// 捐赠弹窗
<template>
  <div>
    <Dialog
      :title="'企业清产核资审批-'+getmode"
      width="1100px"
      :visible="visible"
      :close-on-click-modal="true"
      :destroy-on-close="true"
      :append-to-body="true"
      @close="handleClose"
     
    >
      <div slot="body"   v-loading="loading">
        <el-row :gutter="50">
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="120px"
            label-position="left"
            :disabled="mode=='see'?true:false"
          >
            <el-col :span="8">
              <el-form-item label="单据编号" prop="documentNo">
                <el-input
                  disabled
                  v-model="formData.documentNo"
                  placeholder="自动带入单据编号"
                  clearable
                  :style="{ width: '100%' }"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="填报单位" prop="reportDeptName">
                <el-input
                  v-model="formData.reportDeptName"
                  placeholder="请输入填报单位"
                  clearable
                  :style="{ width: '100%' }"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="清产核资基准日" prop="assetVerificationBaseDate" label-width="125px">
                <el-date-picker
                  v-model="formData.assetVerificationBaseDate"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  :style="{ width: '100%' }"
                  placeholder="请选择日期选择"
                  clearable
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="账面资产总额" prop="totalOriginalBookValue">
                <InputNumber
                  type="decimal"
                  v-model="formData.totalOriginalBookValue"
                  placeholder="请输入账面资产总额"
                  clearable
                  :style="{ width: '100%' }"
                >
                  <template slot="append">元</template>
                </InputNumber>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所有者权益" prop="ownersEquity">
                <InputNumber
                  type="decimal"
                  v-model="formData.ownersEquity"
                  placeholder="请输入所有者权益"
                  clearable
                  :style="{ width: '100%' }"
                >
                  <template slot="append">元</template>
                </InputNumber>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="实收资本" prop="paidinCapital">
                <InputNumber
                  type="decimal"
                  v-model="formData.paidinCapital"
                  placeholder="请输入实收资本"
                  clearable
                  :style="{ width: '100%' }"
                >
                  <template slot="append">元</template>
                </InputNumber>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="资本公积" prop="capitalReserve">
                <InputNumber
                  type="decimal"
                  v-model="formData.capitalReserve"
                  placeholder="请输入资本公积"
                  clearable
                  :style="{ width: '100%' }"
                >
                  <template slot="append">元</template>
                </InputNumber>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="盈余公积" prop="surplusReserve">
                <InputNumber
                  type="decimal"
                  v-model="formData.surplusReserve"
                  placeholder="请输入盈余公积"
                  clearable
                  :style="{ width: '100%' }"
                >
                  <template slot="append">元</template>
                </InputNumber>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="未分配利润" prop="undistributedProfit">
                <InputNumber
                  type="decimal"
                  v-model="formData.undistributedProfit"
                  placeholder="请输入未分配利润"
                  clearable
                  :style="{ width: '100%' }"
                >
                  <template slot="append">元</template>
                </InputNumber>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="其他利润" prop="otherProfits">
                <InputNumber
                  type="decimal"
                  v-model="formData.otherProfits"
                  placeholder="请输入其他利润"
                  clearable
                  :style="{ width: '100%' }"
                >
                  <template slot="append">元</template>
                </InputNumber>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="少数股东权益" prop="minorityEquity">
                <InputNumber
                  type="decimal"
                  v-model="formData.minorityEquity"
                  placeholder="请输入少数股东权益"
                  clearable
                  :style="{ width: '100%' }"
                >
                  <template slot="append">元</template>
                </InputNumber>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="其他权益" prop="otherEquity">
                <InputNumber
                  type="decimal"
                  v-model="formData.otherEquity"
                  placeholder="请输入其他权益"
                  clearable
                  :style="{ width: '100%' }"
                >
                  <template slot="append">元</template>
                </InputNumber>
              </el-form-item>
            </el-col>
            <!-- 文件 -->
          </el-form>
        </el-row>
        <AccessoryList
          v-model="formData.attachmentFileDTOList"
          title="附件列表"
          dict="financial_examinationMean_attach"
          :mode="mode=='see'?'see':'upload'"
          class="m-20"
        />
      </div>
      <!-- 提交按钮 -->
      <div slot="footer">
        <el-button v-if="mode=='see'" @click="closeDialog">关闭</el-button>
        <el-button v-if="mode!='see'" @click="handleClose">取消</el-button>
        <el-button
          v-if="mode!='see'"
          @click="submitForm"
          type="primary"
        >{{ Diaformdata.id == '' ? '提交' : '确定修改' }}</el-button>
      </div>
    </Dialog>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import Uploader from '@/components/Uploader/index.vue'
import { getYear } from '@/utils/cache'
import { serialNo, DeleteExaminationMean, DetailExaminationMean, AddExaminationMean } from '@/api/finance'
import AccessoryList, { Accessory } from './AccessoryFileList.vue'
import { Confirm } from '@/decorators/index'
import InputNumber from '@/components/FormComment/inputNumber.vue'
@Component({
  components: {
    Dialog,
    Uploader,
    AccessoryList,
    InputNumber
  }
})
export default class Container extends Vue {
  @Prop() private visible!: boolean
  @Prop() private Diaformdata!: any
  @Prop({ default: 'add' }) private mode!: 'see' | 'edit' | 'add'
  //   @Prop() private Diaformdata!: {}
  // 文件上传
  private uploaderDlgVisible = false
  private uploaderDlgTitle = '文件上传'
  private uploaderDlgMode = 'upload' //download  upload  see
  private uploadInitList: Array<string> = []
  private currentRow: any = {}
  private uploadtype!: string
  private loading=false
  // upload end
  private formData: any = {
    documentNo: '',
    reportDeptName: '',
    assetVerificationBaseDate: '',
    totalOriginalBookValue: '',
    ownersEquity: '',
    paidinCapital: '',
    capitalReserve: '',
    surplusReserve: '',
    undistributedProfit: '',
    otherProfits: '',
    minorityEquity: '',
    otherEquity: '',
    attachmentFileDTOList: []
  }
  private rules: object = {
    documentNo: [
      {
        required: true,
        message: '自动带入单据编号',
        trigger: 'blur'
      }
    ],
    reportDeptName: [
      {
        required: true,
        message: '请输入填报单位',
        trigger: 'blur'
      }
    ],
    assetVerificationBaseDate: [
      {
        required: true,
        message: '请选择日期选择',
        trigger: 'change'
      }
    ],
    totalOriginalBookValue: [
      {
        required: true,
        message: '请输入账面资产总额',
        trigger: 'blur'
      }
    ],
    ownersEquity: [
      {
        required: true,
        message: '请输入所有者权益',
        trigger: 'blur'
      }
    ],
    paidinCapital: [
      {
        required: true,
        message: '请输入实收资本',
        trigger: 'blur'
      }
    ],
    capitalReserve: [
      {
        required: true,
        message: '请输入资本公积',
        trigger: 'blur'
      }
    ],
    surplusReserve: [
      {
        required: true,
        message: '请输入盈余公积',
        trigger: 'blur'
      }
    ],
    undistributedProfit: [
      {
        required: true,
        message: '请输入未分配利润',
        trigger: 'blur'
      }
    ],
    otherProfits: [
      {
        required: true,
        message: '请输入其他利润',
        trigger: 'blur'
      }
    ],
    minorityEquity: [
      {
        required: true,
        message: '请输入少数股东权益',
        trigger: 'blur'
      }
    ],
    otherEquity: [
      {
        required: true,
        message: '请输入其他权益',
        trigger: 'blur'
      }
    ]
  }
  private accessoryList: Accessory[] = [
    {
      fileName: '企业清产核资工作报告',
      isRequired: true,
      prop: 'attachmentFile1',
      fileList: []
    },
    {
      fileName: '清产核资报表',
      isRequired: true,
      prop: 'attachmentFile2',
      fileList: []
    },
    {
      fileName: '企业年度审计报告或中介机构专项财务审计报告',
      isRequired: true,
      prop: 'attachmentFile3',
      fileList: []
    },
    {
      fileName: '董事会相关决议',
      isRequired: true,
      prop: 'attachmentFile4',
      fileList: []
    },
    {
      fileName: '申报损失的单独做出情况说明',
      isRequired: true,
      prop: 'attachmentFile5',
      fileList: []
    },
    {
      fileName: '损失申报证明材料',
      isRequired: true,
      prop: 'attachmentFile6',
      fileList: []
    },

    {
      fileName: '清产核资情况表',
      isRequired: true,
      prop: 'attachmentFile8',
      fileList: []
    },
    {
      fileName: '其他备查材料',
      isRequired: false,
      prop: 'attachmentFile7',
      fileList: []
    }
  ]
  created() {
     if(this.mode=="see"||this.mode=='edit'){
        this.getdetail()
    }
    this.openDiaHandle()
    // this.formData = Object.assign(this.formData, this.Diaformdata)
    // let investProjectPlanFiling = this.formData
  }
  //
  get getmode() {
    switch (this.mode) {
      case 'see':
        return '查看'
      case 'edit':
        return '编辑'
      default:
        return '新增'
    }
  }
  // 打开弹窗
  private openDiaHandle() {
    try {
      if (this.Diaformdata.id == '' || this.Diaformdata.id == undefined) {
        this.getSerialNo(4)
        ;(this.$refs['elForm'] as any).resetFields()
        return ''
      } else {
        // this.getdetail()
      }
    } catch (error) {
      return ''
    }
  }
  // 获取编辑详情
  private async getdetail() {
    this.loading=true
    try {
      let res: any = await DetailExaminationMean({
        id: this.Diaformdata.id + ''
      })
      if (res.success) {
        this.formData = Object.assign(this.formData,res.data)
        this.loading=false
      }
    } catch (e) {
      this.$emit('changshowDialogAdd', false)
      console.error(e)
        this.loading=false

    }
  }
  private async getSerialNo(id: number) {
    try {
      let res: any = await serialNo({
        index: id
      })
      this.formData.documentNo = res.data
    } catch (e) {
      this.$message.info('获取编号失败')
    }
  }
  private submitForm() {
    if (!this.validateFiles()) return
    ;(this.$refs['elForm'] as any).validate((valid: any) => {
      if (!valid) return
      // TODO 提交表单
      if(this.formData.attachmentFileDTOList.length==0){
        this.$message.warning("请上传附件")
        return
      }
      this.AddLarge()
    })
  }
  ///附件校验 转换
  private validateFiles(): boolean {
    return true
  }
  @Confirm({
    title: '提示',
    content: `是否提交？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'success'
  })
  private async AddLarge() {
    try {
      let params: any = { ...this.formData }
      params.year = getYear()
      // params.lendingPeriod = Number(params.lendingPeriod)
      let res = await AddExaminationMean(params)
      if (res.success) {
        this.$message.success('提交成功')
        this.$emit('changshowDialogAdd', false)
      }
    } catch (e) {
      console.error(e)
    }
  }
  //  文件上传事件
  get uploadable() {
    return this.uploaderDlgMode === 'upload'
  }
  get fileList() {
    return this.currentRow.fileList
  }
  private uploadfile(value: string) {
    this.currentRow.fileList = []
    this.uploadtype = value
    this.uploaderDlgVisible = true
  }
  private lookfile(value: string) {
    this.currentRow = {
      fileName: '附件',
      fileList: [value],
      isRequired: true
    }
    this.uploaderDlgVisible = true
  }
  private onFilesChange(fileList: string[]) {
    this.currentRow.fileList = [...fileList]
  }
  // 文件上传成功写入fromdata
  private handleUploadComplete(fileList: Array<any>) {
    this.$set(this.formData, this.uploadtype + '', fileList[0].url)
    //
  }
  private resetForm() {
    ;(this.$refs['elForm'] as any).resetFields()
  }
  @Confirm({
    title: '提示',
    content: `是否关闭？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private handleClose() {
    // ;(this.$refs['elForm'] as any).resetFields()
    this.$emit('changshowDialogAdd', false)
  }
  private closeDialog(){
    this.$emit('changshowDialogAdd', false)

  }
}
</script>

<style lang="scss" scoped>
::v-deep.el-form-item--medium .el-form-item__label {
  line-height: 20px;
}
::v-deep .el-input.is-disabled .el-input__inner {
  color: #2e2e2e;
  background-color: #fff;
}
</style>

