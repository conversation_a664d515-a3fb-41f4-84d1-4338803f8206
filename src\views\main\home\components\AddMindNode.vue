<template>
  <Dialog width="400px"
    :visible="visible"
    :modal-append-to-body="true"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :append-to-body="false"
    @close="handleClose">
    <!-- 头部 -->
    <div slot="header">
      <span>{{selectedNode.topic}}</span>
    </div>

    <!-- 内容区域 -->
    <div slot="body">
      <el-form :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="0">
        <el-form-item label=""
          prop="values">
          <el-select v-model="ruleForm.values"
            multiple
            filterable
            clearable
            remote
            reserve-keyword
            placeholder="请输入关键词进行搜索"
            :remote-method="remoteMethod"
            :loading="loading"
            class="input-max">
            <el-option v-for="item in options"
              :key="item.id"
              :label="item.name"
              :value="item.id"
              :disabled="item.disabled">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <!-- 底部按钮 -->
    <div slot="footer"
      class="footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary"
        @click="validateForm">确认</el-button>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { ElForm } from 'element-ui/types/form'
import Dialog from '@/components/Dialog/index.vue'

@Component({
  components: {
    Dialog
  }
})
export default class extends Vue {
  @Prop({ default: false }) private visible?: boolean // 弹窗显隐
  @Prop() private selectedNode!: object // 父节点数据
  @Prop() private getNodeData!: any // 父节点所有数据

  private loading = false
  private options: any[] = []
  private nodeDataList: any[] = []
  private ruleForm = {
    values: []
  }
  private rules = {
    values: [{ required: true, message: '请勾选至少企业', trigger: 'change' }]
  }

  // 组件初始化
  private mounted() {
    this.initData()
  }

  // 初始化数据（把父组件传入的结构数据，进行递归平铺，好在 remoteMethod 函数中做排除重复处理）
  private initData() {
    let { data } = this.getNodeData
    let list: any[] = []

    let convertNodeData = function (data: any) {
      if (Array.isArray(data.children) && data.children.length) {
        data.children.forEach((item: any) => {
          list.push({
            id: item.id,
            name: item.topic
          })
          convertNodeData(item)
        })
      }
    }

    if (data && Array.isArray(data.children) && data.children.length) {
      data.children.forEach((item: any) => {
        list.push({
          id: item.id,
          name: item.topic
        })
        convertNodeData(item)
      })
    }

    this.nodeDataList = list
  }

  // 远程搜索（通过父组件的结构数据中要筛选出接口请求数据中一样的数据，然后打上 disabled，这样就不能重复勾选了）
  @Loading('loading')
  private async remoteMethod(query: any) {
    if (query !== '') {
      // await this.xxxxapi

      let data = [
        {
          id: 1,
          name: '公司1'
        },
        {
          id: 2,
          name: '公司2'
        },
        {
          id: 3,
          name: '公司3'
        }
      ]

      if (Array.isArray(data) && data.length) {
        data.forEach((item: any) => {
          let arr = this.nodeDataList.find((itemNode) => {
            return +item.id === +itemNode.id
          })

          if (arr && arr.id) item.disabled = true
        })
      }

      this.options = data
    } else {
      this.options = []
    }
  }

  // 校验必填数据
  private validateForm() {
    ;(this.$refs.ruleForm as ElForm).validate((valid: boolean) => {
      valid && this.submitForm()
    })
  }

  // 确认
  private submitForm() {
    let list: any[] = []
    let values = this.ruleForm.values

    if (values.length) {
      values.forEach((item) => {
        let arr = this.options.find((itemFind) => {
          return +item === +itemFind.id
        })
        if (arr.id) {
          list.push(arr)
        }
      })
    }

    this.updataHandle(list)
    this.handleClose()
  }

  // 触发父组件更新
  private updataHandle(data: any[]) {
    this.$emit('updataHandle', data)
  }

  // 关闭弹窗
  private handleClose() {
    this.$emit('update:visible', false)
  }
}
</script>

<style scoped lang="scss">
.input-max {
  width: 100%;
}
</style>