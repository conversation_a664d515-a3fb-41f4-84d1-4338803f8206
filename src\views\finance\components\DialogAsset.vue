// 财务监管弹窗
<template>
  <div>
    <Dialog
      :title="'企业资产减值准备财务核销备案-'+getmode"
      width="1100px"
      :visible="visible"
      :close-on-click-modal="true"
      :destroy-on-close="true"
      :append-to-body="true"
      @close="handleClose"
    >
      <div slot="body" v-loading="loading" class="assetImpairment-body">
        <el-row :gutter="20">
          <el-form
            ref="elForm"
            :disabled="mode=='see'?true:false"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="80px"
          >
            <el-row>
              <el-col :span="8">
                <el-form-item label="单据编号" prop="documentNo">
                  <el-input
                    disabled
                    v-model="formData.documentNo"
                    placeholder="自动带入单据编号"
                    clearable
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="备案编号" prop="recordNo">
                  <el-input
                    v-model="formData.recordNo"
                    placeholder="请输入备案编号"
                    clearable
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="年度" prop="year">
                  <el-date-picker
                    type="year"
                    v-model="formData.year"
                    format="yyyy"
                    value-format="yyyy"
                    :style="{ width: '100%' }"
                    placeholder="请选择年度"
                    clearable
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="填报单位" prop="reportDeptName">
                  <el-input
                    v-model="formData.reportDeptName"
                    placeholder="请输入填报单位"
                    clearable
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>

             
              <el-col :span="8">
                <el-form-item label="资产核销企业名称" prop="entName">
                  <el-input
                    v-model="formData.entName"
                    placeholder="请输入资产核销企业名称"
                    clearable
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="资产核销企业性质" prop="enterpriseNature">
                  <el-input
                    v-model="formData.enterpriseNature"
                    placeholder="请输入资产核销企业性质"
                    clearable
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
               <el-col :span="8">
                <el-form-item label="经济行为决策机构" prop="decisionMakingBody">
                  <el-input
                    v-model="formData.decisionMakingBody"
                    placeholder="请输入经济行为决策机构"
                    clearable
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="批准文件日期" prop="approvalDate">
                  <el-date-picker
                    v-model="formData.approvalDate"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    :style="{ width: '100%' }"
                    placeholder="请选择批准文件日期"
                    clearable
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                    <el-form-item label="核销不良资产内容" prop="content">
                      <el-input
                        v-model="formData.content"
                        placeholder="请输入核销不良资产内容"
                        :style="{ width: '100%' }"
                      ></el-input>
                    </el-form-item>
                  </el-col>
              <el-col :span="24">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="币种" prop="assetCurrency">
                      <el-select  :style="{ width: '100%' }" v-model="formData.assetCurrency" placeholder="请选择币种" clearable>
                        <el-option
                          v-for="(item, index) in getDictData('issuance_currency')"
                          :key="index"
                          :label="item.label"
                          :value="item.value"
                          :disabled="item.disabled"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item
                      label="其他币种"
                      prop="filler"
                      v-show="formData.assetCurrency == '99'"
                    >
                      <el-input
                        v-model="formData.filler"
                        placeholder="请输入币种类型"
                        clearable
                        :style="{ width: '100%' }"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="货币单位" prop="currencyUnit">
                      <el-select
                        v-model="formData.currencyUnit"
                        placeholder="货币单位"
                        clearable
                        :style="{ width: '100%' }"
                      >
                        <el-option
                          v-for="(item, index) in getDictData('currency_unit')"
                          :key="index"
                          :label="item.label"
                          :value="item.value"
                          :disabled="item.disabled"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
               
                     <el-col :span="8">
                <el-form-item  label="核销不良资产金额" prop="amount">
                  <inputNumber
                  disabled
                    v-model="formData.amount"
                    placeholder="根据不良资产自动带入"
                    clearable
                    :style="{ width: '100%' }"
                  ></inputNumber>
                </el-form-item>
              </el-col>
                </el-row>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                  <el-input
                    v-model="formData.remark"
                    placeholder="请输入备注"
                    clearable
                    type="textarea"
                    maxlength="300"
                    show-word-limit
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- end -->

            <!-- 不良资产类型 -->
            <bad-assets-detail :mode="mode" ref="badAsset" :badList="badAssetList" @setAmount="setAmount" />
            <!-- 文件上传 -->
          </el-form>
        </el-row>

        <AccessoryList
          v-model="formData.attachmentFileDTOList"
          dict="financial_writeoff_attach"
          title="附件列表"
          :mode="mode=='see'?'see':'upload'"
          class="m-20"
        />
      </div>
      <div slot="footer">
        <el-button v-if="mode=='see'" @click="closeDialog">关闭</el-button>
        <el-button v-if="mode!='see'" @click="handleClose">取消</el-button>
        <el-button
          v-if="mode!='see'"
          @click="submitForm"
          type="primary"
        >{{ Diaformdata.id == '' ? '提交' : '确定修改' }}</el-button>
      </div>
    </Dialog>
    <!-- 上传文件 -->
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import * as filterOptions from '@/views/finance/filterOptions'
import badAssetsDetail from './badAssets.vue'
import { serialNo, AddAssetsWriteoff, DetailAssetsWriteoff, DeleteAssetsWriteoff } from '@/api/finance'
// import Uploader from "@/components/Uploader/index.vue"
import Uploader from '@/components/Uploader/index.vue'
import { getYear } from '@/utils/cache'
import AccessoryList, { Accessory } from './AccessoryFileList.vue'
import { Confirm } from '@/decorators/index'
import { BusinessModule } from '@/store/modules/businessDict'
import inputNumber from "@/components/FormComment/inputNumber.vue"

@Component({
  components: {
    Dialog,
    badAssetsDetail,
    Uploader,
    AccessoryList,
    inputNumber
  }
})
export default class Container extends Vue {
  @Prop() private visible!: boolean
  @Prop() private Diaformdata!: any
  @Prop({ default: 'add' }) private mode!: 'see' | 'edit' | 'add'
  //  动态不良资产表格
  private badAssetList = []
  // 文件上传
  private uploaderDlgVisible = false
  private uploaderDlgTitle = '文件上传'
  private uploaderDlgMode = 'upload' //download  upload  see
  private uploadInitList: Array<string> = []
  private currentRow: any = {}
  private uploadtype!: string
  private loading = false
  // upload end
  get getmode() {
    switch (this.mode) {
      case 'see':
        return '查看'
      case 'edit':
        return '编辑'
      default:
        return '新增'
    }
  }
  private rules: object = {
    documentNo: [
      {
        required: false,
        message: '自动带入单据编号',
        trigger: 'blur'
      }
    ],
    recordNo: [
      {
        required: true,
        message: '请输入备案编号',
        trigger: 'blur'
      }
    ],
    year: [
      {
        required: true,
        message: '请输入年度',
        trigger: 'blur'
      }
    ],
    approvalDate: [
      {
        required: true,
        message: '请选择批准文件日期',
        trigger: 'change'
      }
    ],
    decisionMakingBody: [
      {
        required: true,
        message: '请输入经济行为决策机构',
        trigger: 'blur'
      }
    ],
    enterpriseNature: [
      {
        required: true,
        message: '请输入资产核销企业性质',
        trigger: 'blur'
      }
    ],
    entName: [
      {
        required: false,
        message: '请输入资产核销企业名称',
        trigger: 'blur'
      }
    ],
    reportDeptName: [
      {
        required: true,
        message: '请输入填报名称',
        trigger: 'blur'
      }
    ],
    amount: [
      {
        required: true,
        message: '请输入核销不良资产金额',
        trigger: 'blur'
      }
    ],
    assetCurrency: [
      {
        required: true,
        message: '请选择币种',
        trigger: 'change'
      }
    ],
    currencyUnit: [
      {
        required: true,
        message: '货币单位',
        trigger: 'change'
      }
    ],
    content1: [
      {
        required: true,
        message: '请选择下拉选择',
        trigger: 'change'
      }
    ],
    content: [
      {
        required: true,
        message: '请输入核销不良资产内容',
        trigger: 'blur'
      }
    ],
    remark: [
      {
        required: false,
        message: '请输入备注',
        trigger: 'blur'
      }
    ],
    attachment1FileUrls: [
      {
        required: true,
        message: '请上传文件',
        trigger: 'blur'
      }
    ],
    attachment2FileUrls: [
      {
        required: true,
        message: '请上传文件',
        trigger: 'blur'
      }
    ],
    attachment3FileUrls: [
      {
        required: true,
        message: '请上传文件',
        trigger: 'blur'
      }
    ]
  }

  // 币种类型
  // private currencyUnitOptions = filterOptions.currencyUnit
  // 不良资产类型
  private badAssetType = this.getDictData("bad_asset_type ")

  private setAmount(val:number){
    if(this.mode!=='see'){
      this.formData.amount=val

    }
  }
  private formData: any = {
    amount:'',
    documentNo: undefined,
    decisionMakingBody: undefined,
    publisher: undefined,
    referenceNo: undefined,
    issuer: undefined,
    issuanceBondsReason: undefined,
    issuancePurpose: undefined,
    issuanceCurrency: undefined,
    filler: undefined,
    contactPhone: undefined,
    creditEnhancementMeasures: undefined,
    restitutionMeasures: undefined,
    otherSupplements: undefined,
    bondsType: undefined,
    BigDecimal: undefined,
    mainUses: undefined,
    validityStartDate: undefined,
    expiryDate: null,
    issuanceRate: undefined,
    issuanceTimeNumber: undefined,
    issuanceForm: undefined,
    issuanceTarget: undefined,
    attachmentFileDTOList: []
  }
  private badAssetTypeFrom: any = [
    {
      currencyUnit: 1,
      badAssetTypeTable: [
        {
          name: '',
          num: '',
          oldnum: '',
          lumpSum: ''
        }
      ]
    }
  ]
  created() {
    if (this.mode == 'see' || this.mode == 'edit') {
      this.getdetail()
    }
    this.openDiaHandle()
    // this.formData = Object.assign(this.formData, this.Diaformdata)

    // this.badAssetList = this.formData.badAssetsDetailVOList
  }
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  private openDiaHandle() {
    try {
      if (this.Diaformdata.id === '') {
        this.getSerialNo(2)
        ;(this.$refs['elForm'] as any).resetFields()
        return ''
      } else {
        // this.getdetail()
      }
    } catch (error) {
      return ''
    }
  }
  // 获取编辑详情
  private async getdetail() {
    this.loading = true
    try {
      let res: any = await DetailAssetsWriteoff({
        id: this.Diaformdata.id + ''
      })
      if (res.success) {
        this.formData = res.data
        ;(this.$refs.badAsset as any).setData(res.data.badAssetTypeInfoList)
        // this.$forceUpdate()
        this.loading = false
      }
    } catch (e) {
      this.loading = false
      console.error(e)
    }
  }
  private async getSerialNo(id: number) {
    try {
      let res: any = await serialNo({
        index: id
      })
      this.formData.documentNo = res.data
    } catch (e) {
      this.$message.info('获取编号失败')
    }
  }
  @Confirm({
    title: '提示',
    content: `是否确认提交？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private async AddLarge() {
    try {
      let params: any = { ...this.formData }
      params.year = getYear()
      let res = await AddAssetsWriteoff(params)
      if (res.success) {
        this.$message.success(res.msg)
        this.$emit('changshowDialogAdd', false)
      }
    } catch (e) {
      console.error(e)
    }
  }
  private validateFiles(): boolean {
    return true
  }

  private submitForm() {
    
    // if (!this.validateFiles()) return

    ;(this.$refs['elForm'] as any).validate((valid: any) => {
      if (!valid) return
      // TODO 提交表单
    if (!(this.$refs['badAsset'] as any).validateBadForm()) return
    if(this.formData.attachmentFileDTOList.length==0){
        this.$message.warning("请上传附件")
        return
      }
      this.formData.badAssetTypeInfoList = (this.$refs['badAsset'] as any).badAssetTypeFrom
      // Object.assign(this.formData, (this.$refs['badAsset'] as any).badAssetTypeFrom)
      this.AddLarge()
    })
  }
  // 文件上传事件
  get uploadable() {
    return this.uploaderDlgMode === 'upload'
  }
  get fileList() {
    return this.currentRow.fileList
  }
  private uploadfile(value: string) {
    this.$set(this.currentRow, 'fileList', [])
    this.uploadtype = value
    this.uploaderDlgVisible = true
  }

  private onFilesChange(fileList: string[]) {
    this.currentRow.fileList = [...fileList]
  }
  private lookfile(value: string) {
    this.currentRow = {
      fileName: '附件',
      fileList: [value],
      isRequired: true
    }
  }
  private handleUploadComplete(fileList: Array<any>) {
    // this.formData[this.uploadtype] = fileList[0].url
    // Object.assign(this.formData,{})
    this.$set(this.formData, this.uploadtype + '', fileList[0].url)
    //
  }
  private resetForm() {
    ;(this.$refs['elForm'] as any).resetFields()
  }
  @Confirm({
    title: '提示',
    content: `确定关闭？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private handleClose() {
    this.$emit('changshowDialogAdd', false)
    this.resetForm()
  }
  private closeDialog() {
    this.$emit('changshowDialogAdd', false)
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-form-item__label {
  line-height: 20px;
}

::v-deep .el-input.is-disabled .el-input__inner {
  color: #2e2e2e;
  background-color: #fff;
}
</style>

