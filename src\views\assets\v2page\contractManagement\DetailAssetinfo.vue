<template>
  <el-drawer title="查看详情"
    direction="rtl"
    size="60%"
    :modal="false"
    :visible="visible"
    @close="handleClose">
    <div v-loading="loading"
      class="dialog_content">
      <el-tabs v-model="activeName">
        <el-tab-pane label="合同信息"
          name="contract">
          <Contract :detailData="detailData" />
        </el-tab-pane>

        <el-tab-pane label="账单信息"
          name="bill">
          <Bill :contractNo="detailInfo.contractNo" />
        </el-tab-pane>
      </el-tabs>
    </div>

    <div slot="footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-drawer>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { contractDetail } from '@/api/assetsv2'
import Dialog from '@/components/Dialog/index.vue'
import Contract from './components/contract.vue'
import Bill from './components/bill.vue'

export default interface DetailInfo {
  id: string | number
}
@Component({
  components: {
    Dialog,
    Contract,
    Bill
  }
})
export default class extends Vue {
  @Prop() private visible!: boolean
  @Prop() private detailInfo!: any

  private loading = false
  private activeName = 'contract'
  private detailData: any = {
    contractInfo: {},
    renterInfo: {},
    fileList: [],
    lessorInfo: {}
  }

  created() {
    this.getDetail()
  }

  // 获取详情数据
  private async getDetail() {
    this.loading = true
    let res: any = {}
    try {
      res = await contractDetail({
        id: this.detailInfo.id
      })
      if (res.success) {
        this.detailData = res.data
      }
    } catch (e) {
      setTimeout(() => {
        this.handleClose()
      }, 1500)
    } finally {
      this.loading = false
    }
  }

  // 关闭弹窗
  private handleClose() {
    this.$emit('update:visible', false)
    this.$emit('update:visibleif', false)
  }
}
</script>

<style lang="scss" scoped>
.dialog_content {
  min-height: 40vh;
  padding: 0 20px;
}
::v-deep .el-descriptions__header {
  margin-bottom: 10px;
}
::v-deep .el-descriptions__title {
  border-left: 5px solid #2f2f2f;
  padding-left: 5px;
  margin-left: -10px;
}
::v-deep .el-descriptions-item__label.is-bordered-label {
  width: 150px;
}

.btn {
  color: #ce4c4c;
  cursor: pointer;
}

.mode-box {
  .title {
    display: flex;
    align-items: center;
    font-weight: bold;
    margin-bottom: 10px;
    &::before {
      display: inline-block;
      content: ' ';
      height: 23px;
      border-left: 5px solid #2f2f2f;
      padding-left: 5px;
      margin-left: -10px;
    }
  }
}
</style>