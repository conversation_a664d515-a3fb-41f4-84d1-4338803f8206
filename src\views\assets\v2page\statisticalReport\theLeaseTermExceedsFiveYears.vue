<template>
  <el-container class="container"
    direction="vertical">
    <SearchBar :items="searchItems"
      @onSearch="handleSearch"
      v-if="isSearch">
      <el-button v-loading="loadingExport"
        :disabled="!gridData.length"
        type="primary"
        @click="exportHandle">导 出</el-button>
    </SearchBar>

    <Grid ref="grid"
      border
      :remote-url="remoteUrl"
      :columns="columns"
      :show-pagination="true"
      :search-params="searchParams"
      :overflow-tooltip="true"
      @row-click="loaddetail"
      @onLoaded="onLoadedHandle">
      <template slot="contractLeasePeriod"
        slot-scope="scope">
        {{ scope.row.startTime + ' 至 ' + scope.row.endTime }}
      </template>
      <template slot="operatingBar"
        slot-scope="scope">
        <el-button type="text"
          @click.stop="loaddetail(scope.row)">查看</el-button>
      </template>
    </Grid>
  </el-container>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { BusinessModule } from '@/store/modules/businessDict'
import { getAstCompTree } from '@/api/projectInvestment'
import { downloadXls } from '@/utils'
import { ExportOver5year } from '@/api/assetsv2'
import { Loading, Confirm } from '@/decorators'
import Grid from '@/components/Grid/index.vue'
import SearchBar from '@/components/SearchBar/index.vue'
import Uploader from '@/components/Uploader/index.vue'

@Component({
  components: {
    Grid,
    SearchBar,
    Uploader
  }
})
export default class HouseCertificate extends Vue {
  // 文件上传
  private isSearch = false
  private showUploader = false
  private UploaderList = []
  private compTree = []
  private loadingExport = false

  // 详细表格数据
  private appendixsLength!: number
  private Diadetaillist: any = {}
  private gridData: any[] = []
  private visvileDetail = false
  private visvileDetailif = false
  private searchParams: any = {}
  private remoteUrl = '/fht-monitor/ast/report/over5year'
  private searchItems: any[] = [
    {
      type: 'text',
      key: 'keyword',
      placeholder: '编号/房产名称',
      width: '200px'
    },
    {
      type: 'text',
      key: 'lessorName',
      placeholder: '出租方',
      width: '120px'
    },
    {
      type: 'text',
      key: 'renterName',
      placeholder: '承租方',
      width: '120px'
    },
    {
      type: 'select',
      key: 'orgCode',
      placeholder: '直属单位',
      width: '120px',
      options: this.compTree
    },
    {
      type: 'select',
      key: 'assetPurpose',
      placeholder: '房产用途',
      width: '120px',
      options: this.getDictData('asset_purpose')
    }
  ]
  private columns = [
    {
      label: '直属单位',
      minWidth: 120,
      prop: 'orgName'
    },
    {
      prop: 'itemName',
      label: '房产名称',
      minWidth: 120
    },
    {
      label: '房产用途',
      minWidth: 120,
      prop: 'assetPurposeDesc'
    },
    {
      prop: 'coveredArea',
      label: '建筑面积(m²)',
      minWidth: 120
    },
    {
      prop: 'originalValue',
      label: '账面原值（元）',
      minWidth: 120
    },

    {
      prop: 'houseCertNo',
      label: '房产证编号',
      minWidth: 120
    },
    // 承租方联系方式
    {
      prop: 'realEstateCertNo',
      label: '不动产证编号',
      minWidth: 120
    },
    {
      prop: 'landCertNo',
      label: '土地证编号',
      minWidth: 120
    },

    {
      prop: 'useStatusDesc',
      label: '使用情况',
      minWidth: 120
    },
    {
      prop: 'occupancyCoveredArea',
      label: '出租面积(m²)',
      minWidth: 120
    },
    // 承租方联系方式

    {
      prop: 'lessorName',
      label: '出租方',
      minWidth: 120
    },

    {
      prop: 'renterName',
      label: '承租方',
      minWidth: 120
    },
    {
      prop: 'annualRent',
      label: '年租金',
      minWidth: 120
    },
    {
      prop: 'rentPeriod',
      label: '租期',
      minWidth: 120
    },
    // {
    //   prop: 'useStatus',
    //   label: '审核情况',
    //   minWidth: 120
    // },
    {
      prop: 'remark',
      label: '备注',
      minWidth: 120
    }
  ]

  // 初始化
  created() {
    this.initQuery()
    this.getCompTree()
  }

  // 字典
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }

  // 根据链接赋值相关字段
  private initQuery() {
    let { orgCode } = this.$route.query
    if (orgCode) {
      let findIndex = this.searchItems.findIndex((item) => {
        return item.key === 'orgCode'
      })
      this.searchItems[findIndex].value = orgCode
      this.searchParams.orgCode = orgCode
    }
  }

  // 文件详情
  private uploaderDetail(List: any) {
    this.UploaderList = List
    this.showUploader = true
  }

  // 详情附件
  private showUpdaload(list: any) {
    this.uploaderDetail = list
    this.showUploader = true
  }

  //获取机构数组
  private async getCompTree() {
    let options = []
    let res = await getAstCompTree({ parentId: 0, deptCategory: 1 })
    this.isSearch = false
    if (res.success) {
      let options = res.data.map((res: any) => {
        return {
          label: res.deptName,
          value: res.deptCode
        }
      })
      await this.$nextTick(() => {
        Object.assign(this.compTree, options)
        this.isSearch = true
      })
    }
  }

  // 改变详情显示隐藏
  private changeShowDialogDetail(state: boolean) {
    this.visvileDetailif = state
    this.$nextTick(() => {
      this.visvileDetail = state
    })
  }

  // 导出
  @Loading('loadingExport')
  @Confirm({
    title: '提示',
    content: `是否确认导出数据`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private async exportHandle() {
    let res = await ExportOver5year(this.searchParams)
    let time = new Date().getTime()
    downloadXls(res.data, ` 租期超五年_${time}.xlsx`)
    this.$message.success(res.msg || '导出成功')
  }

  // 表格搜索
  private handleSearch(condition: any) {
    this.searchParams = condition
    ;(this.$refs['grid'] as any).refresh()
  }

  //  操作
  private loaddetail(row: any) {
    this.Diadetaillist = row
    this.visvileDetailif = true
    this.$nextTick(() => {
      this.visvileDetail = true
    })
  }

  // 表格数据加载完成后触发
  private onLoadedHandle(data: any) {
    this.gridData = data.records || []
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
}
</style>
