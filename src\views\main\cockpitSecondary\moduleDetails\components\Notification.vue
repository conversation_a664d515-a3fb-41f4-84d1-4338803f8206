/** 通知 */

<template>
  <div class="notification">
    <div class=""></div>
    <div></div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { ProjectProfitabilityRankingData } from '@/views/main/cockpitSecondary/baseData'
import { capitalProfile } from '@/api/cockpit'
import * as echarts from 'echarts'
import { deepClone } from '@/utils'

type EChartsOption = echarts.EChartsOption

@Component
export default class extends Vue {
  private timer: any
  private loading = false
  private maxValue = 100
  private year = ''
  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}
  private tabList = []
  private echartsDatas: any[] = []
  private seriesData: any[] = deepClone(ProjectProfitabilityRankingData)

  private companyList = [
    {
      code: '1',
      name: '城投集团'
    },
    {
      code: '4',
      name: '交投集团'
    },
    {
      code: '2',
      name: '金投集团'
    },
    {
      code: '5',
      name: '轨道集团'
    },
    {
      code: '3',
      name: '水务集团'
    },
    {
      code: '6',
      name: '社发集团'
    }
  ]
  private tabActive: {
    code: string
    name: string
  } = {
    code: '',
    name: ''
  }

  // 获取集团列表
  get getCompanyList() {
    let list: string[] = []
    this.companyList.forEach((item) => {
      list.push(item.name)
    })
    return list
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('ProjectProfitabilityRanking') as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    // this.initData()
    this.initEcharts()
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await capitalProfile()
    if (!Array.isArray(data) || !data.length) return
    this.echartsDatas = data
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 初始化数据
  private initEcharts() {
    let textSize = 22
    let series = this.seriesData.map((item) => {
      // 设置圆角方向
      if (item.value < 0) {
        item.itemStyle = {
          borderRadius: [20, 0, 0, 20]
        }
        item.backgroundStyle = {
          borderRadius: [20, 0, 0, 20]
        }
      }
      return item
    })
    let yeas = ~~this.getMomentTime()
    let companyList = this.getCompanyList
    this.option = {
      color: [
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(239, 149, 43, 0.8)' // 0% 处的颜色
            },
            {
              offset: 1,
              color: 'rgba(245, 216, 18, 0.8)' // 100% 处的颜色
            }
          ]
        },
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: '#7E358B' // 0% 处的颜色
            },
            {
              offset: 1,
              color: '#EC342F' // 100% 处的颜色
            }
          ]
        }
      ],
      axisPointer: {
        show: false
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 103, 177, 1)',
        borderColor: 'rgba(0, 103, 177, 1)',
        textStyle: {
          color: '#fff',
          fontSize: textSize
        }
      },
      legend: {
        show: false
      },
      grid: {
        top: '0%',
        left: '0%',
        right: '6%',
        bottom: '0%',
        containLabel: true
      },
      xAxis: {
        show: false,
        type: 'value',
        axisLabel: {
          color: '#5db0ea',
          fontSize: textSize
        }
      },
      yAxis: {
        type: 'category',
        axisLabel: {
          color: '#5db0ea',
          fontSize: textSize,
          shadowBlur: 10,
          shadowColor: '#0B388F',
          backgroundColor: '#0B388F66',
          borderRadius: 6,
          formatter: (value: any, index: number) => {
            return `{a|0${series.length - index}} {b|${value}}`
          },
          rich: {
            a: {
              fontStyle: 'italic',
              fontWeight: 'bold',
              color: '#4A97F8',
              fontSize: textSize * 1.2,
              lineHeight: textSize * 2,
              padding: 10
            },
            b: {
              fontWeight: 'bold',
              fontSize: textSize * 1,
              lineHeight: textSize * 2,
              color: 'rgba(255, 255, 255, 0.6)',
              padding: 10
            }
          }
        },
        data: companyList.reverse()
      },
      series: [
        {
          name: '资产利用情况',
          type: 'bar',
          stack: 'total',
          selectedMode: 'single',
          barWidth: textSize * 1.8,
          label: {
            show: true,
            fontSize: textSize,
            color: '#fff',
            textBorderColor: 'RGBA(233, 60, 167, 1)',
            position: 'right',
            formatter: ({ data }: any) => {
              return `${data}万元`
            }
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'RGBA(233, 60, 167, 0)'
                  },
                  {
                    offset: 0.4,
                    color: 'RGBA(233, 60, 167, 0.3)'
                  },
                  {
                    offset: 1,
                    color: 'RGBA(233, 60, 167, 1)'
                  }
                ]
              },
              shadowColor: 'RGBA(233, 60, 167, 0.6)',
              shadowBlur: 30
            }
          },
          select: {
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'RGBA(233, 60, 167, 0)'
                  },
                  {
                    offset: 0.4,
                    color: 'RGBA(233, 60, 167, 0.3)'
                  },
                  {
                    offset: 1,
                    color: 'RGBA(233, 60, 167, 1)'
                  }
                ]
              },
              shadowColor: 'RGBA(233, 60, 167, 0.6)',
              shadowBlur: 30
            }
          },
          itemStyle: {
            borderRadius: [0, 20, 20, 0],
            shadowBlur: 30,
            shadowColor: 'rgba(245, 216, 18, 0.6)'
          },
          showBackground: true,
          backgroundStyle: {
            color: 'rgba(54, 116, 214, 0.3)',
            borderRadius: [0, 20, 20, 0]
          },
          data: series
        }
      ]
    }

    this.myChart && this.myChart.setOption && this.myChart && this.myChart.setOption(this.option)
    this.change()
  }
  // 年份切换
  private yearHandler(year: string) {
    this.year = year
    // this.initData()
  }

  private change() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    this.timer = setInterval(() => {
      let index = Math.max(Math.floor(Math.random() * 4), 0)

      this.myChart &&
        this.myChart.dispatchAction({
          type: 'select',
          seriesIndex: 0,
          dataIndex: index
        })
    }, 5000)
  }

  destroyed() {
    clearInterval(this.timer)
  }
}
</script>

<style scoped lang="scss">
#ProjectProfitabilityRanking {
  width: 100%;
  height: 100%;
}
</style>