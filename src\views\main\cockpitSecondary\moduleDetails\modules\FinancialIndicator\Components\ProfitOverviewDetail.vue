<template>
  <Dialog :width="width"
    :visible="visible"
    :close-on-click-modal="closeModal"
    :destroy-on-close="true"
    :append-to-body="true"
    :show-close="false"
    @close="handleClose"
    custom-class="custom-cockipt-dialog-wrap">
    <!-- 公共头部 -->
    <div slot="header"
      class="header-box">
      <div class="mode mode-left">
        <el-select v-model="yearSelect"
          popper-class="custom-cockipt-select-down"
          class="custom-cockipt-select-wrap m-r-10"
          style="width: 100px;"
          size="small"
          placeholder="年份"
          @change="filterData">
          <el-option v-for="item in yearList"
            :key="item"
            :label="item"
            :value="item" />
        </el-select>

        <el-select v-model="idxSelect"
          popper-class="custom-cockipt-select-down"
          class="custom-cockipt-select-wrap m-r-10"
          style="width: 100px;"
          size="small"
          placeholder="指标"
          @change="filterData">
          <el-option v-for="(item, index) in idxList"
            :key="index"
            :label="item.label"
            :value="item.value" />
        </el-select>

        <el-select v-model="typeSelect"
          popper-class="custom-cockipt-select-down"
          class="custom-cockipt-select-wrap m-r-10"
          style="width: 100px;"
          size="small"
          placeholder="维度"
          @change="filterData">
          <el-option v-for="(item, index) in typeList"
            :key="index"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </div>

      <h4 class="mode mode-middel">{{title}}</h4>

      <div class="mode mode-right">
        <Import v-if="getPermission('finance_cockpit_import')"
          :isDownMobel="true"
          :downMobelUrl="getDictData('import_template').find((item) => {
            return item.value === 'fin_zj_gzw_main_indicators'
          }).label"
          downMobelName="市属企业主要指标情况表_模版.xlsx"
          tip="请上传 excel 文件，且单个不超过20M"
          submitUrl="fht-monitor/fin/gzw/main-indicators/import"
          accept=".xls,.xlsx"
          class="exprt-box"
          @updataHandle="filterData" />

        <el-select v-model="monthSelect"
          popper-class="custom-cockipt-select-down"
          class="custom-cockipt-select-wrap m-r-10"
          style="width: 140px;"
          size="small"
          placeholder="数据日期"
          @change="filterData">
          <el-option v-for="item in 12"
            :key="item"
            :label="`数据日期：${item}月`"
            :value="item" />
        </el-select>

        <i class="close el-icon-circle-close"
          @click="handleClose" />
      </div>
    </div>

    <!-- 自定义内容 -->
    <div slot="body"
      class="content-box">
      <div class="refEcharts"
        ref="refEcharts" />
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { bigNumberFormat } from '@/utils/index'
import { mainIndicatorsSummary } from '@/api/cockpit'
import { BusinessModule } from '@/store/modules/businessDict'
import { PermissionModule } from '@/store/modules/permissionDict'
import Dialog from '@/components/Dialog/index.vue'
import Import from '@/components/Import/index.vue'
import * as echarts from 'echarts'

type EChartsOption = echarts.EChartsOption

@Component({
  components: {
    Dialog,
    Import
  }
})
export default class Header extends Vue {
  @Prop() private visible!: boolean // 弹窗显隐
  @Prop() private year!: string // 年份
  @Prop({ default: '标题' }) private title?: string // 标题
  @Prop({ default: '1000px' }) private width?: string // 弹窗宽度
  @Prop({ default: true }) private closeModal?: boolean // 弹窗是否可点击mark关闭

  private detailData: any[] = []
  private yearList: string[] = []
  private idxList: any[] = []
  private typeList: any[] = []
  private yearSelect = ''
  private monthSelect = ''
  private idxSelect = '1'
  private typeSelect = '2'

  // echarts 视图数据
  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private seriesData: any[] = []
  private yAxisData: any[] = []
  private xAxisName = ''
  private xAxisNameTitle = ''
  private option: EChartsOption = {}

  // 权限方法
  get getPermission() {
    return (code: string) => {
      return PermissionModule.dictLaodData.includes(code) || false
    }
  }

  // 获取字典数据
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }

  // 万元数据转化成亿元
  get getBigNumberFormat() {
    return (num: string | number) => {
      return bigNumberFormat(+num || 0).value || 0
    }
  }

  // 组件初始化
  private created() {
    this.yearSelect = this.year
    this.getYearList()
    this.getIdxList()
    this.getTypeList()
  }

  // 初始化
  private mounted() {
    this.$nextTick(() => {
      this.chartDom = this.$refs.refEcharts as HTMLElement
      this.myChart = echarts.init(this.chartDom as HTMLElement)

      this.filterData()
    })
  }

  // 渲染数据
  @Loading('loading')
  private async filterData() {
    let data:any = {}

    await mainIndicatorsSummary({
      year: this.yearSelect,
      index: this.idxSelect,
      type: this.typeSelect,
      month: this.monthSelect
    }).then((res) => {
      data = res.data
    }).catch(() => {
      this.yAxisData = []
      this.seriesData = []
    })
    
    this.detailData = data.month ? data : []
    this.monthSelect = data.month ? data.month : ''
    let dataList: any[] = data.month ? data.list : []

    // 组装数据
    let yAxisData: string[] = []
    let seriesData: any[] = []
    let list: number[] = []
    this.xAxisName = +this.typeSelect === 1 ? '亿元' : '%'
    this.xAxisNameTitle = +this.typeSelect === 1 ? '累计金额' : '增速'

    dataList.forEach((item, index) => {
      if (item.title) {
        yAxisData.unshift(`${index + 1} ${item.label}&&${item.title}`)
      } else {
        yAxisData.unshift(`${index + 1} ${item.label}`)
      }

      if (+this.typeSelect == 1) {
        list.unshift(+this.getBigNumberFormat(item.value))
      } else {
        if (item.title) {
          list.unshift(0)
        } else {
          list.unshift(+item.value || 0)
        }
      }
    })

    seriesData.unshift({
      type: 'bar',
      stack: 'total',
      label: {
        show: true,
        fontSize: 15,
        color: '#fff',
        position: 'right',
        formatter: (parmes: any) => {
          let names = parmes.name.split('&&')

          if (names.length <= 1) {
            let indexName = names[0].split(' ')[0]

            let find = dataList[indexName - 1]

            if(find.valueInfo) {
              return `${find.valueInfo}`
            } else {
              return `${parmes.value} ${this.xAxisName}`
            }
          } else {
            return `${names[1]}`
          }
        }
      },
      emphasis: {
        focus: 'series'
      },
      barWidth: 30,
      data: list
    })

    this.yAxisData = yAxisData
    this.seriesData = seriesData

    this.initEcharts()
  }

  // 渲染 echarts 视图
  private initEcharts() {
    let xAxisName = this.xAxisName
    let xAxisNameTitle = this.xAxisNameTitle
    let yAxisData = this.yAxisData
    let seriesData = this.seriesData
    let tooltip: any = {
      trigger: 'axis',
      formatter: (parmes: any) => {
        let dom = ''
        let data = parmes[0]
        let names = data.name.split('&&')

        if (names.length <= 1) {
          let info = ''
          if(data.value) {
            info = data.value + xAxisName
          } else {
            info = '-'
          }

          dom = `
            <div style="font-size:14px; border-radius:10px; display: flex; flex-direction: column;">
              <div style="font-weight:bold; margin-bottom: 4px;">${names[0]}</div>
              <div style="display: flex; justify-content: space-between;">
                <span style="padding-right: 40px;">${xAxisNameTitle}</span>
                <span>${info}</span>
              </div>
            </div>
          `
        } else {
          dom = `
            <div style="font-size:14px; border-radius:10px; display: flex; flex-direction: column;">
              <div style="font-weight:bold; margin-bottom: 4px;">${names[0]}</div>
              <div style="display: flex; justify-content: space-between;">
                <span>${names[1]}</span>
              </div>
            </div>
          `
        }

        return dom
      }
    }

    this.option = {
      color: ['#5db0ea'],
      tooltip: tooltip,
      grid: {
        top: '4%',
        left: '0%',
        right: '8%',
        bottom: '4%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        axisLabel: {
          fontSize: 16
        },
        axisLine: {
          lineStyle: {
            color: '#5db0ea'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(0, 103, 177, 0.4)'
          }
        }
      },
      yAxis: {
        type: 'category',
        axisLabel: {
          fontSize: 16,
          formatter: (parmes: any) => {
            let names = parmes.split('&&')

            if (names[0].includes('金华市')) {
              return `{b|${names[0]}}`
            } else {
              return `{a|${names[0]}}`
            }
          },
          rich: {
            a: {
              fontSize: 16
            },
            b: {
              color: 'rgba(252, 180, 94, 1)',
              fontSize: 18
            }
          }
        },
        axisLine: {
          lineStyle: {
            color: '#5db0ea'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(0, 103, 177, 0.4)'
          }
        },
        data: yAxisData
      },
      series: seriesData
    }

    this.myChart && this.myChart.setOption(this.option, true)
  }

  // 年份：获取数据
  private getYearList() {
    let list = []
    let yearNow = (this as any).$moment(new Date()).add('year', 0).format('YYYY')
    for (let i = 1; i < 8; i++) {
      let year = (+yearNow + 2) - i
      list.push(String(year))
    }
    this.yearList = list
  }

  // 指标：获取数据
  private getIdxList() {
    let list: any[] = [
      {
        label: '营业收入',
        value: '1'
      },
      {
        label: '利润总额',
        value: '2'
      },
      {
        label: '资产总额',
        value: '3'
      },
      {
        label: '所有者权益',
        value: '4'
      }
    ]

    this.idxList = list
  }

  // 维度：获取数据
  private getTypeList() {
    let list: any[] = [
      {
        label: '增速',
        value: '2'
      },
      {
        label: '累计金额',
        value: '1'
      }
    ]

    this.typeList = list
  }

  // 弹窗：关闭
  private handleClose() {
    this.$emit('update:visible', false)
  }
}
</script>

<style scoped lang="scss">
$color: #2eb6f6;

.refEcharts {
  width: 100%;
  height: 500px;
}

.month {
  height: 32px;
  line-height: 32px;
  font-size: 15px;
  padding: 0 10px;
  border-radius: 4px;
  border: 1px solid $color;
}

.exprt-box {
  margin-right: 10px;
  ::v-deep .el-button {
    color: $color;
    background: none;
    border: 1px solid $color;
  }
}
</style>