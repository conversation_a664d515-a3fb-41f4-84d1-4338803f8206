<template>
  <section class="echarts-dom-wrap">
    <div v-loading="loading" ref="chartDom" class="chartDom" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { assetsReceivablesOverYear } from '@/api/assetsv2'
import { Loading } from '@/decorators'
import * as echarts from 'echarts'
let chartDom = null
let myChart: any = null

@Component({})
export default class extends Vue {
  @Prop() private orgCode!: string // 集团code
  @Prop() private year!: string // 年份

  private loading = false
  private legendData: string[] = []
  private xAxisData: string[] = []
  private seriesData: any[] = []
  private interfaceData = []

  // 监听搜索条件改变
  @Watch('orgCode', { deep: true })
  @Watch('year', { deep: true })
  private watchFormData() {
    this.initData()
  }

  // 组件初始化
  private mounted() {
    this.$nextTick(() => {
      chartDom = this.$refs.chartDom
      myChart = echarts.init(chartDom as HTMLElement)

      this.initData()
    })
  }

  // 获取数据
  @Loading('loading')
  private async initData() {
    this.initEcharts()
  }

  // 初始化echarts组件
  private initEcharts() {
    let list: any = [
      {
        name: '城投集团',
        value: Math.ceil(Math.random() * 100)
      },
      {
        name: '国资运营',
        value: Math.ceil(Math.random() * 100)
      },
      {
        name: '交投集团',
        value: Math.ceil(Math.random() * 100)
      },
      {
        name: '金投集团',
        value: Math.ceil(Math.random() * 100)
      },
      {
        name: '轨道集团',
        value: Math.ceil(Math.random() * 100)
      }
    ]

    let option = {
      title: [
        {
          text: '68',
          x: '46%',
          y: '35%',
          textStyle: {
            fontSize: 42,
            fontWeight: 'normal',
            fontStyle: 'normal',
            color: '#333333'
          }
        },
        {
          text: '合计',
          x: '46%',
          y: '50%',
          textStyle: {
            fontSize: 24,
            fontWeight: 'normal',
            fontStyle: 'normal',
            color: '#666666'
          }
        }
      ],
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      grid: {
        top: '18%',
        left: '2%',
        right: '2%',
        bottom: '0%',
        containLabel: true
      },
      legend: {
        orient: 'vertical',
        x: 'right',
        itemWidth: 14,
        itemHeight: 14,
        align: 'left',
        data: list.map((res: any) => {
          return res.name
        }),
        textStyle: {
          color: '#f2f2f'
        }
      },

      series: [
        {
          name: '母猪数量占比',
          type: 'pie',
          hoverAnimation: false,
          legendHoverLink: false,
          radius: ['42%', '55%'],
          data: list,
          label: {
            normal: {
              formatter: '{b}\n{c}'
            }
          }
        },
        {
          name: '母猪数量占比',
          type: 'pie',
          radius: ['42%', '55%'],
          label: {
            normal: {
              formatter: '{b}\n{c}'
            }
          },
          data: list
        }
      ]
    }

    myChart && myChart.setOption && myChart.setOption(option)
  }
}
</script>

<style scoped lang="scss">
.chartDom {
  width: 100%;
  height: 100%;
}
</style>
