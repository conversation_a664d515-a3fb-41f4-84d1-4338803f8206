<template>
  <Grid
    ref="gridListing"
    show-index-fixed="left"
    :show-index="true"
    :show-pagination="true"
    :columns="cols"
    :remote-url="remoteUrl"
    :search-params="searchParams"
    @row-click="onSee"
  >
    <!-- <template #assetNoSlot="{row}">{{ row.currentAsset.assetNo }}</template> -->
    
    

    <template #operationSlot="{row}">
      <Operation :list="operationList" :row="row" />
    </template>
  </Grid>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { deleteAssetLedger, transactionBack } from '@/api/assets'
import { Confirm } from '@/decorators/index'
import Operation, { OperationItem } from '@/components/Operation/index.vue'
import Uploader from '@/components/Uploader/index.vue'
import { circulationStatusList, processNodeList } from '@/views/assets/filterOptions'
import Grid from '@/components/Grid/index.vue'
import searchBar from '@/components/SearchBar/index.vue'
import { BusinessModule } from '@/store/modules/businessDict'
import { deepClone } from '../../../../../utils'

@Component({
  components: {
    Grid,
    searchBar,
    Uploader,
    Operation
  }
})
export default class extends Vue {
  @Prop() private params!: any
  private searchParams = {
    type: 1,
    keyword:"",
    current:1,
    page:10,
  }
  // 附件数据相关
  private accessoryTitle = '附件列表'
  private appendixsLength!: number
  private accessoryList: string[] = []
  private showUploader = false
  private Diadetaillist: object = []
  private showDialogDetail = false
  private selectionRowList = [] //表格被选中列表
  private searchContent = ''
  private remoteUrl = '/fht-monitor/asset/transaction/page'

  // 全选
  private allChecked = false

  private searchItems = [
    {
      type: 'text',
      key: 'keyword',
      placeholder: '请输入批次号/申请人/坐落/ ',
      width: '330px'
    }
  ]

  // 操作按钮
  private operationList: OperationItem[] = [
    {
      label: '撤回',
      click: this.onRevocation,
      style: 2,
      visible: (row: any) => {
        return row.circulationStatus === "11" && row.status === "1"
      }
    },
    {
      label: '重新发起',
      click: this.onReset,
      style: 2,
      visible: (row: any) => {
        return (
          (row.circulationStatus === "11" && row.status === "0") ||
          row.circulationStatus === "13" ||
          row.circulationStatus === "4"
        )
      }
    },
    {
      label: '查看',
      click: this.onSee,
      style: 3,
      visible: (row: any) => {
        return true
      }
    }
  ]

  private cols = [
    {
      prop:"assetNo",
      label: '资产编号',
      minWidth: 120,
      fixed: 'left'
    },
    {
      prop: 'batchNo',
      label: '申请批次号',
      fixed: 'left',
      minWidth: 160
    },

    {
      prop: 'createTime',
      label: '提交时间',
      minWidth: 160
    },
    {
      prop: 'subjectName',
      label: '标的名称',
      minWidth: 160
    },
    {
      prop: 'circulationStatusDesc',
      label: '业务状态'
    },
    {
      prop: 'location',
      label: '坐落位置',
      minWidth: 240
    },
    {
      prop: 'realEstateCertificateNo',
      label: '不动产证号',
      minWidth: 120
    },
    {
      prop: 'titleDeedNo',
      label: '房产证号',
      minWidth: 120
    },
    {
      prop: 'landCertificateNo',
      label: '土地证号',
      minWidth: 120
    },
    {
      prop: 'planCertificateNo',
      label: '规划证号',
      minWidth: 120
    },
    {
      prop: 'constructionCertificateNo',
      label: '施工证号',
      minWidth: 120
    },
    {
      prop: 'completionCertificateNo',
      label: '竣工证号',
      minWidth: 120
    },
    {
      prop: 'assetCategoryDesc',
      label: '资产类型'
    },
    {
      prop: 'rentListingPrice',
      label: '租金挂牌价'
    },
    {
      prop: 'listingPriceUnitDesc',
      label: '挂牌价单位'
    },
    {
      prop: 'bidSecurity',
      label: '保证金'
    },
    {
      prop: 'rentalArea',
      label: '拟出租面积(平方米)',
      minWidth: 160
    },
    {
      prop: 'useRequirementsDesc',
      label: '房产使用用途要求',
      minWidth: 160
    },
    {
      prop: 'leaseTermDesc',
      label: '租期',
      minWidth: 160
    },
    {
      prop: 'hasRentFreePeriodDesc',
      labelAlign: 'center',
      label: '有无免租期'
    },
    {
      prop: 'rentFreePeriod',
      label: '免租期时间'
    },
    {
      prop: 'inLeaseTermDesc',
      label: '是否包含在租期内',
      labelAlign: 'center',
      minWidth: 160
    },
    {
      label: '操作',
      slotName: 'operationSlot',
      fixed: 'right',
      minWidth: 120,
      labelAlign: 'center'
    }
  ]
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  private getCirculationStatus(status: number) {
    return (
      this.getDictData('circulation_status').find((res: any) => {
        return res.value == status
      })||{label:""}.label
    )
  }

  // 选择事件
  private handleSelectionChange(list: any) {
    this.selectionRowList = list
  }

  private onSee(row: any) {
    this.$emit('detail', row)
  }

  // 撤销
  @Confirm({
    title: '提示',
    content: `是否确认撤销该交易申报？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private async onRevocation(row: any) {
    //
    let res = await transactionBack({ batchNo: row.batchNo })

    if (res.success) {
      this.$message.success(res.msg || '撤回成功！')
      this.refresh()
    }
  }

  // 重新发起, 与新增逻辑一样，只是有老数据
  @Confirm({
    title: '提示',
    content: `是否确认重新发起该交易申报？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private onReset(row: any) {
    this.$emit('reset', row.id)
  }

  // 刷新表格
  public refresh() {
    let grid = this.$refs['gridListing'] as any
        
    this.searchParams=(this.searchParams,this.params)
    grid.refresh(true)
  }
}
</script>

<style lang="scss" scoped>
</style>
