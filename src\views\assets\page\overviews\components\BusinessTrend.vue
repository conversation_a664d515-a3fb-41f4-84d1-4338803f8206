<template>
  <section class="echarts-dom-wrap">
    <div v-loading="loading"
      ref="chartDom"
      class="chartDom"
      :class="{'hide': !interfaceData.length}" />

    <el-empty description="暂无数据"
      class="empty-none-data"
      :class="{'none': interfaceData.length}" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { assetStatusRatio } from '@/api/cockpit'
import * as echarts from 'echarts'

let chartDom = null
let myChart: any = null

@Component({})
export default class extends Vue {
  @Prop() private orgCode!: string // 集团code
  @Prop() private year!: string // 年份

  private loading = false
  private seriesData: any[] = []
  private interfaceData: any[] = []

  // 监听搜索条件改变
  @Watch('orgCode', { deep: true })
  @Watch('year', { deep: true })
  private watchFormData() {
    this.initData()
  }

  // 组件初始化
  private mounted() {
    this.$nextTick(() => {
      chartDom = this.$refs.chartDom
      myChart = echarts.init(chartDom as HTMLElement)

      this.initData()
    })
  }

  // 获取数据
  @Loading('loading')
  private async initData() {
    let { data } = await assetStatusRatio({
      orgCode: this.orgCode,
      year: +this.year
    })

    let list: any[] = []
    if (Array.isArray(data) && data.length) {
      this.interfaceData = data

      data.forEach((item) => {
        list.push({
          name: item.useStatus,
          value: +item.count
        })
      })
    }

    // 组装数据
    this.seriesData = list
    this.initEcharts()
  }

  // 初始化echarts组件
  private initEcharts() {
    let seriesData = this.seriesData

    let option = {
      tooltip: {
        trigger: 'item',
        valueFormatter: function (value: string) {
          return value + ' 处'
        }
      },
      legend: { show: false },
      grid: {
        top: '0%',
        left: '1%',
        right: '1%',
        bottom: '0%',
        containLabel: true
      },
      series: [
        {
          name: '经营性资产',
          type: 'pie',
          radius: '80%',
          itemStyle: {
            borderRadius: 0,
            borderColor: '#fff',
            borderWidth: 2
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            show: true,
            position: 'outside',
            fontSize: 14,
            color: '#333',
            formatter: ({ data }: { data: any }) => {
              return `{a|${data.name}} \n {b|${data.value}}`
            },
            rich: {
              a: {
                fontSize: 14,
                color: '#333'
              },
              b: {
                fontSize: 14,
                color: '#999'
              }
            }
          },
          data: seriesData
        },
        {
          type: 'pie',
          radius: '80%',
          itemStyle: {
            borderRadius: 0,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: true,
            position: 'inside',
            formatter: (parmes: any) => {
              return parmes.percent ? `${parmes.percent}%` : '0%'
            },
            color: '#333',
            fontSize: 14
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          data: seriesData
        }
      ]
    }

    myChart && myChart.setOption && myChart.setOption(option)
  }
}
</script>

<style scoped lang="scss">
.chartDom {
  width: 100%;
  height: 100%;
}
</style>