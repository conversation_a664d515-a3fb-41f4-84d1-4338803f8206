<!-- 房产申请表 -->
<template>
  <el-container class="table-wrapper"
    direction="vertical">
    <div class="title">
      <span>房产申请表</span>
      <div class="btn-bar">
        <el-button type="primary"
          @click="onAdd">添加</el-button>
        <!-- <el-button type="primary" @click="onImport">导入</el-button> -->
      </div>
    </div>
    <!-- 表格 -->
    <Grid ref="grid"
      :columns="cols"
      :show-selection="true"
      :show-pagination="true"
      :overflow-tooltip="true"
      :show-index="true"
      show-index-fixed="left"
      :data="houseTableList"
      @selection-change="handleSelectionChange"
      @select-all="handleSelectionChange">
      <template #areaSlot="{ row }">{{ row.city }} {{ row.county }}</template>

      <template #operationSlot="{ row }">
        <Operation :list="operationList"
          :row="row" />
      </template>

      <div slot="bottomLeft">
        <el-button type="primary"
          @click="onBatchDelete">删除</el-button>
      </div>
    </Grid>

    <!-- 房产表单 -->
    <AssetFormCom v-if="houseFormDialogVisivle"
      :visible.sync="houseFormDialogVisivle"
      :initData="currentRow"
      :mode="houseFormDialogMode"
      @success="onSuccess"
      :houseTableList="houseTableList" />
  </el-container>
</template>

<script lang="ts">
import { Component, Emit, Model, Vue, Watch } from 'vue-property-decorator'
import Grid from '@/components/Grid/index.vue'
import Operation, { OperationItem } from '@/components/Operation/index.vue'
import AssetFormCom, { AssetForm } from './AssetForm.vue'
import { BusinessModule } from '@/store/modules/businessDict'
import {
  assetTypeList,
  constructionAreaUnitList,
  otherRightTyleList,
  priceUnitList,
  subjectStatusTyleList,
  titleDeedTypeList
} from '@/views/assets/filterOptions'

export interface AsseetForm {
  assetCategory: number | '' // 资产类别,1:房屋,2:土地,3:房屋+土地
  assetCategoryDesc: string
  assetNo: string // 资产编号	string
  assignUser: number | '' // 分配人员
  assignUserName: string // 分配人员	string
  auditFailReason: string // 审核失败原因	string
  auditPassTime: string // 审核通过时间	string
  auditStatus: number | '' //	审核状态，0：草稿,1:待审核,1：审核成功,2：审核失败
  auditStatusDesc: string //
  batchNo: string // 申请批次号
  bidSecurity: number | '' // 竞拍保证金(万)
  circulationStatus: number | '' //	流转状态，0：草稿,1；审核中，2：未认领，3：已认领，4：挂牌，5：撤牌，6：摘牌，7：成交，8：已中止，9：已终结
  circulationStatusDesc: string //
  city: string // 市	string
  completionCertificateNo: string // 竣工证号	string
  constructionArea: number | '' //	建筑面积
  constructionAreaUnit: number | string //	建筑面积单位，1:平方米，2：亩，3：其它
  constructionCertificateNo: string // 施工证号	string
  county: string // 区（县）	string
  createDept: number | '' //	创建部门
  dealStatus: number | '' //	成交状态：0:未成交,1:已成交,2：已中止，3:已终结,4：已归档
  dealStatusDesc: string //		string
  expiryDate: string // 到期日	string
  hasRentFreePeriod: number | '' // 有无免租期,1-是 0-否
  hasRentFreePeriodDesc: string //		string
  houseDesignPurpose: string // 房屋设计用途	string
  houseStatus: string // 房屋当前状态	string
  id: number | '' //		integer
  inLeaseTerm: number | '' //	是否包含在租期内,1-是 0-否
  inLeaseTermDesc: string // 		string
  isPriorityLease: number | '' //	是否有优先承租权，1-是 0-否
  isPriorityLeaseDesc: string // 		string
  landCertificateNo: string // 土地证号	string
  leaseTerm: string // 租期	string
  listingFailReason: string // 挂牌失败原因	string
  listingPassTime: string // 挂牌时间	string
  listingPriceUnit: number | '' //	挂牌价单位，详见字典ListingPriceUnitEnum
  listingPriceUnitDesc: string // 		string
  listingStatus: number | '' // 	挂牌状态，1：已挂牌,2：撤牌，3：摘牌，4:挂牌失败
  listingStatusDesc: string // 		string
  location: string // 房屋坐落	string
  markupRange: number | '' //	加价幅度(元/次)
  originalLessee: string // 原承租人名称	string
  otherConstructionAreaUnit: string // 其它建筑面积单位	string
  otherRights: number | '' //	其他权利情况，0：无，1：抵押，2：共有，9：其它
  otherRightsDesc: string // 其他权利说明	string
  otherUseRequirements: string // 其它房产使用用途要求	string
  paymentRequirements: string // 租金支付要求	string
  planCertificateNo: string // 规划证号	string
  province: string // 省	string
  realEstateCertificateNo: string // 不动产证号	string
  remark: string // 备注	string
  rentFreePeriod: string // 免租期时间	string
  rentListingPrice: string // 租金挂牌价	string
  rentalArea: string //	出租面积(可租赁面积)	string
  status: number | '' //	有效状态，1-正常，0-作废
  statusDesc: string // 		string
  subjectAddress: string // 出租标的地址	string
  subjectName: string // 标的名称	string
  subjectStatus: number | '' //标的状态，1：空置，2：使用，9：其它
  subjectStatusDesc: string // 		string
  titleDeedNo: string // 房产证号	string
  useRequirements: number | '' //	房产使用用途要求，详见字典PropertyUseRequirementsEnum
  useRequirementsDesc: string

  projectName: string // 项目名称
  projectContactAddress: string // string 项目联系地址
  tenantUnsolicited: number | '' // number 未征集到意向承租方选项
  agreedWorkingDay: string // number 约定工作日
  extensionPeriod: number | '' //  延期周期
}

@Component({
  components: {
    Grid,
    Operation,
    AssetFormCom
  }
})
export default class Project extends Vue {
  @Model('change')
  private houseTableList!: AssetForm[]

  @Emit('change')
  private onHouseTableListChange(list: AssetForm[]) {
    return [...list]
  }

  private houseFormDialogVisivle = false
  private houseFormDialogMode = 'see'
  private currentRow: any = {}

  private checkedList: AssetForm[] = []

  // 后期取字典
  private subjectStatusTyleList: any = this.getDictData('subject_status')
  private titleDeedTypeList: any = this.getDictData('listing_price_unit')
  private assetTypeList: any = this.getDictData('asset_category')
  private constructionAreaUnitList: any = this.getDictData('construction_area_unit')
  private otherRightTyleList: any = this.getDictData('other_rights')
  private priceUnitList: any = this.getDictData('listing_price_unit')
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  private cols = [
    {
      prop: 'subjectName',
      label: '标的名称',
      minWidth: 120,
      fixed: 'left'
    },
    {
      prop: 'location',
      label: '坐落地址',
      minWidth: 120
    },
    {
      prop: 'subjectStatus',
      label: '标的状态',
      minWidth: 120,
      renderStatus: (status: any): [string, string] => {
        const statusMap = ['info', 'success', 'warning']
        let current = this.subjectStatusTyleList.find((res: any) => {
          return res.value == status
        })
        return [statusMap[0] || 'warning', current.label]
      },
      filterOptions: this.subjectStatusTyleList
    },
    // {
    //   prop: 'expiryDate',
    //   label: '使用截止时间',
    //   minWidth: 120
    // },
    {
      prop: 'assetNo',
      label: '资产编号',
      minWidth: 120
    },
    // {
    //   prop: 'areaSlot',
    //   label: '所在区域*',
    //   minWidth: 120
    // },
    // {
    //   prop: '',
    //   label: '所在乡镇',
    //   minWidth: 120
    // },
    {
      prop: 'location',
      label: '房屋坐落',
      minWidth: 120
    },
    {
      prop: 'realEstateCertificateNo',
      label: '不动产证号',
      minWidth: 120
    },
    {
      prop: 'titleDeedNo',
      label: '房产证号',
      minWidth: 120
    },
    {
      prop: 'landCertificateNo',
      label: '土地证号',
      minWidth: 120
    },
    {
      prop: 'planCertificateNo',
      label: '规划证号',
      minWidth: 120
    },
    {
      prop: 'constructionCertificateNo',
      label: '施工证号',
      minWidth: 120
    },
    {
      prop: 'completionCertificateNo',
      label: '竣工证号',
      minWidth: 120
    },
    {
      prop: 'assetCategory',
      label: '资产类型',
      minWidth: 120,
      renderStatus: (status: any): [string, string] => {
        const statusMap = ['info', 'success', 'warning']
        let current = this.assetTypeList.find((res: any) => {
          return res.value == status
        })
        return ['warning', current.label]
      },
      filterOptions: this.assetTypeList
    },
    {
      prop: 'houseStatus',
      label: '房屋当前状态',
      minWidth: 120
    },
    {
      prop: 'constructionArea',
      label: '建筑面积',
      minWidth: 120
    },
    {
      slotName: 'constructionAreaUnit',
      label: '建筑面积单位',
      minWidth: 120,
      renderStatus: (status: any, row: AssetForm): [any, any] => {
        let current = this.constructionAreaUnitList.find((res: any) => {
          return res.value == row.constructionAreaUnit
        })

        return ['warning', current.label]
      },
      filterOptions: this.constructionAreaUnitList
    },
    {
      prop: 'houseDesignPurpose',
      label: '房屋设计用途',
      minWidth: 120
    },
    {
      prop: 'otherRights',
      label: '其他权利情况',
      minWidth: 120,
      renderStatus: (status: any, row: AssetForm): [string, string] => {
        const statusMap = ['info', 'success']
        let current = this.otherRightTyleList.find((item: any) => item.value === status) || { label: row.otherRightsDesc }
        return [statusMap[status] || 'warning', current.label]
      },
      filterOptions: this.otherRightTyleList
    },
    {
      prop: 'isPriorityLeaseDesc',
      label: '是否优先承租权情况 ',
      minWidth: 160,
      renderStatus: (status: any, row: AssetForm): [string, string] => {
        const statusMap = ['error', 'success']
        return [statusMap[status], status ? '是' : '否']
      },
      filterOptions: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    },
    {
      prop: '',
      label: '是否分别计价',
      minWidth: 120,
      renderStatus: (status: any, row: AssetForm): [string, string] => {
        const statusMap = ['error', 'success']
        return [statusMap[status], status ? '是' : '否']
      },
      filterOptions: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    },
    {
      prop: 'rentListingPrice',
      label: '租金挂牌价',
      minWidth: 120
    },
    {
      prop: 'listingPriceUnit',
      label: '挂牌价单位',
      minWidth: 120,
      renderStatus: (status: any, row: AssetForm): [string, string] => {
        let current = this.priceUnitList.find((item: any) => item.value === status) || { label: '' }
        return ['success', current.label]
      },
      filterOptions: this.priceUnitList
    },
    {
      prop: 'rentalArea',
      label: '拟出租面积(平方米)',
      minWidth: 160
    },
    {
      prop: 'paymentRequirements',
      label: '租金支付要求',
      minWidth: 120
    },
    {
      slot: 'useRequirementsDescSlot',
      label: '房产使用用途要求',
      minWidth: 140
    },
    {
      prop: 'leaseTerm',
      label: '租期',
      minWidth: 120
    },
    {
      prop: 'hasRentFreePeriod',
      label: '有无免租期',
      minWidth: 120,
      renderStatus: (status: any, row: AssetForm): [string, string] => {
        const statusMap = ['error', 'success']
        return [statusMap[status], status ? '有' : '无']
      },
      filterOptions: [
        {
          label: '有',
          value: 1
        },
        {
          label: '无',
          value: 0
        }
      ]
    },
    // {
    //   prop: 'rentFreePeriod',
    //   label: '免租期时间',
    //   minWidth: 120
    // },
    {
      prop: 'inLeaseTermSlot',
      label: '是否包含在租期内',
      minWidth: 120,
      renderStatus: (status: any, row: AssetForm): [string, string] => {
        const statusMap = ['error', 'success']
        return [statusMap[status], status ? '是' : '否']
      },
      filterOptions: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    },
    {
      prop: 'bidSecurity',
      label: '竞拍保证金(万)',
      minWidth: 120
    },
    {
      prop: 'markupRange',
      label: '加价幅度(元/次)',
      minWidth: 120
    },

    {
      prop: '',
      label: '操作',
      width: 145,
      slotName: 'operationSlot',
      fixed: 'right',
      labelAlign: 'center'
    }
  ]

  // 操作按钮
  private operationList: OperationItem[] = [
    {
      label: '修改',
      style: 2,
      click: this.onEdit,
      visible: (row: any) => {
        return true
      }
    },
    {
      label: '查看',
      click: this.onSee,
      style: 3,
      visible: (row: any) => {
        return true
      }
    }
  ]

  // 是否是企业端
  get isCompany() {
    return true
  }

  private onSee(row: any) {
    // 查看
    this.houseFormDialogMode = 'see'
    this.currentRow = row
    this.houseFormDialogVisivle = true
  }

  private onEdit(row: any) {
    // 编辑
    this.houseFormDialogMode = 'edit'
    this.currentRow = row
    this.houseFormDialogVisivle = true
  }

  // 新增
  private onAdd() {
    this.houseFormDialogMode = 'add'
    this.houseFormDialogVisivle = true
  }

  // 导入
  private onImport() {
    //
  }

  private onSuccess(houseForm: AssetForm) {
    if (this.houseFormDialogMode === 'add') {
      let houseTableList = [...this.houseTableList]
      houseForm.id = new Date().getTime()
      this.houseTableList.push(houseForm)
      this.$message.success('添加成功')
      // this.onHouseTableListChange(this.houseTableList)
    } else if (this.houseFormDialogMode === 'edit') {
      this.houseTableList.forEach((item: any, index: number) => {
        if (item.id == houseForm.id) {
          this.houseTableList.splice(index, 1, houseForm)
          // this.onHouseTableListChange(this.houseTableList)
        }
      })
    }
  }

  private handleSelectionChange(checkedList: AssetForm[]) {
    this.checkedList = [...checkedList]
  }

  // 批量删除
  private onBatchDelete() {
    if (!this.checkedList.length) {
      this.$message.warning('请选择需要删除的房产交易表！')
      return
    }

    this.$confirm(`此操作将删除 <strong><i>${this.checkedList.length}</i></strong> 条房产交易表, 是否继续?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      dangerouslyUseHTMLString: true,
      type: 'warning'
    })
      .then(() => {
        let tableList = [...this.houseTableList]
        let filterList = tableList.filter((item) => {
          for (let index in this.checkedList) {
            if (this.checkedList[index] === item) {
              return false
            }
          }
          return true
        })
        this.onHouseTableListChange(filterList)
        this.checkedList = []
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
      })
      .catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
  }
}
</script>

<style lang="scss" scoped>
.table-wrapper {
  height: 100%;
  .title {
    // height: 48px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 10px 0 16px;
    span {
      font-size: 16px;
      font-weight: 700;
      color: #303133;
    }
  }

  ::v-deep .el-table__body-wrapper {
    // min-height: 200px;
    height: 200px;
  }
}
</style>