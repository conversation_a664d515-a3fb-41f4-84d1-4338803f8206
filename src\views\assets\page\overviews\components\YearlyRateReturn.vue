<template>
  <section class="echarts-dom-wrap">
    <div v-loading="loading"
      ref="chartDom"
      class="chartDom"
      :class="{'hide': !interfaceData.length}" />

    <el-select v-model="searchValue"
      placeholder="请选择区域"
      class="search-box">
      <el-option v-for="item in getDictData('areaCode')"
        :key="item.value"
        :label="item.label"
        :value="item.value">
      </el-option>
    </el-select>

    <el-empty description="暂无数据"
      class="empty-none-data"
      :class="{'none': interfaceData.length}" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { BusinessModule } from '@/store/modules/businessDict'
import { assetsRentTrendsZone } from '@/api/assetsv2'
import { Loading, Throttle } from '@/decorators'
import * as echarts from 'echarts'

let chartDom = null
let myChart: any = null

@Component({})
export default class extends Vue {
  @Prop() private orgCode!: string // 集团code
  @Prop() private year!: string // 年份

  private loading = false
  private searchValue = ''
  private legendData: string[] = []
  private xAxisData: string[] = []
  private seriesData: any[] = []
  private interfaceData: any[] = []
  private options: any[] = [
    {
      label: 'aaa',
      value: 1
    },
    {
      label: 'bbb',
      value: 2
    }
  ]

  // 获取字典数据
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }

  // 监听搜索条件改变
  @Watch('orgCode', { deep: true })
  @Watch('year', { deep: true })
  private watchFormData() {
    this.initData()
  }

  // 区域改变时触发
  @Watch('searchValue', { deep: true })
  private watchSearchValue() {
    this.initData()
    this.$emit('emitChangeArea', this.searchValue)
  }

  // 数据初始化
  private created() {
    let codeList = this.getDictData('areaCode')
    if (Array.isArray(codeList) && codeList.length) {
      this.searchValue = codeList[0].value
    }
  }

  // 组件初始化
  private mounted() {
    this.$nextTick(() => {
      chartDom = this.$refs.chartDom
      myChart = echarts.init(chartDom as HTMLElement)

      this.initData()
    })
  }

  // 获取数据
  @Loading('loading')
  private async initData() {
    let { data } = await assetsRentTrendsZone({
      districtCode: this.searchValue,
      orgCode: this.orgCode,
      year: this.year
    })

    this.interfaceData = data || []

    // let data = [
    //   {
    //     name: '城投集团',
    //     list: [
    //       {
    //         label: '商圈A',
    //         value: 100
    //       },
    //       {
    //         label: '商圈B',
    //         value: 45
    //       },
    //       {
    //         label: '商圈C',
    //         value: 67
    //       },
    //       {
    //         label: '商圈D',
    //         value: 129
    //       }
    //     ]
    //   }
    // ]

    // 组装数据
    let legendData: string[] = []
    let xAxisData: string[] = []
    let seriesData: any[] = []

    Array.isArray(data) &&
      data.forEach((item) => {
        legendData.push(item.name)

        let data: number[] = []
        let obj = {}

        if (Array.isArray(item.list) && item.list.length) {
          item.list.forEach((itemList: { value: string }) => {
            data.push(+itemList.value)
          })

          obj = {
            name: item.name,
            type: 'bar',
            barWidth: 20,
            label: {
              show: true,
              position: 'top'
            },
            data: data,
            tooltip: {
              valueFormatter: function (value: number) {
                return value + ' 万'
              }
            }
          }
        }

        seriesData.push(obj)
      })

    if (Array.isArray(data) && data.length) {
      Array.isArray(data[0].list) &&
        data[0].list.forEach((item: { label: string }) => {
          xAxisData.push(item.label)
        })
    }

    this.legendData = legendData
    this.xAxisData = xAxisData
    this.seriesData = seriesData
    this.initEcharts()
  }

  // 初始化echarts组件
  private initEcharts() {
    let legendData = this.legendData
    let xAxisData = this.xAxisData
    let seriesData = this.seriesData

    let option = {
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        top: '12%',
        left: '2%',
        right: '1%',
        bottom: '0%',
        containLabel: true
      },
      legend: {
        data: legendData
      },
      xAxis: [
        {
          type: 'category',
          data: xAxisData,
          axisPointer: {
            type: 'shadow'
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: '万',
          min: 0,
          axisLabel: {
            formatter: '{value}'
          }
        }
      ],
      series: seriesData
    }

    myChart && myChart.setOption && myChart.setOption(option)
  }
}
</script>

<style scoped lang="scss">
.chartDom {
  width: 100%;
  height: 100%;
}

.search-box {
  position: absolute;
  top: -35px;
  right: 0;
  z-index: 2;
}
</style>