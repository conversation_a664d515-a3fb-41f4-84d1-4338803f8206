<template>
  <el-input class="money-input"
    v-bind="$attrs"
    v-on="$listeners"
    type="text"
    oninput="value=value.replace(/[^\d.]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3').replace(/^\./g, '')"
    onblur="value.slice(-1) == '.'?value = value.slice(0,value.length - 1):''">
    >
    <slot name="prepend"
      slot="prepend" />
    <slot name="append"
      slot="append" />

    <template v-if="prepend"
      slot="prepend">{{ prepend }}</template>
    <i slot="suffix">{{ suffix }}</i>
  </el-input>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

@Component
export default class extends Vue {
  @Prop() private suffix!: string
  @Prop() private prepend!: string
}
</script>

<style lang="scss">
.money-input {
  .el-input__suffix-inner {
    line-height: 32px;
    font-size: 12px;
  }
}
</style>
