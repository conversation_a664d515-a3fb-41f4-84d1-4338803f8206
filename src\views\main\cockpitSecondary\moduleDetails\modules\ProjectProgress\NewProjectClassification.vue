/**
  组件描述:  项目分类占比
*/
<template>
  <section v-loading="loading"
    class="project-classification">
    <div class="halo" />

    <div class="info">
      <div class="info__legend">
        <div class="info__legend-item"
          v-for="(item, index) in seriesData"
          :key="item.indicatorName">
          <div class="legend-item__top">
            <span class="ws">
              <span class="point"
                :style="{ background: colorList[index] }"></span>
              <span class="name">{{ item.name }}</span>
            </span>
            <span class="number"
              :style="{ color: colorList[index] }">{{ item.num }}个</span>
          </div>
        </div>
      </div>
    </div>

    <div class="chart-wrapper">
      <ProportionOfRing chartId="ProjectClassification"
        unit='万元'
        :seriesData="seriesData"
        :individuationOptions="individuationOptions"
        :ringWidth="0" />
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import ProportionOfRing from '@/views/main/cockpitSecondary/charts/ProportionOfRing.vue'
import { EChartsOption } from 'echarts'
import { getProjectTypeRate } from '@/api/cockpit'
import { Loading } from '@/decorators'

@Component({
  components: {
    ProportionOfRing
  }
})
export default class extends Vue {
  @Prop({ default: '0' }) private activeTabs!: string

  @Watch('activeTabs', { immediate: true })
  private activeTabsChange() {
    this.params.orgCode = this.activeTabs
    this.fetchData()
  }

  private loading = false

  // 参数
  private params: any = {
    orgCode: '0',
    year: String(new Date().getFullYear())
  }

  private readonly colorList = ['#3AD4FE', '#FF7A27', '#FEDE02', '#03E791', '#E04290']
  private seriesData: any[] = []
  private totalInvestment = 0

  get individuationOptions(): EChartsOption {
    return {
      color: this.colorList,
      legend: {
        show: false
      },
      title: [
        {
          text: '总投资(万元)',
          top: '52%',
          left: '50%',
          textAlign: 'center',
          textStyle: {
            fontSize: 34,
            fontWeight: 'bold',
            color: '#fff'
          }
        },
        {
          text: this.totalInvestment + '',
          top: '38%',
          left: '50%',
          textAlign: 'center',
          textStyle: {
            fontSize: 42,
            fontWeight: 'bold',
            color: '#FFC62C',
            fontFamily: 'PangMenZhengDao'
          }
        }
      ],
      series: [
        {
          center: ['50%', '50%'],
          radius: ['60%', '80%'],
          label: {
            show: false
          }
        }
      ]
    }
  }

  mounted() {
    this.listenerDate()
  }

  // 监听时间变化
  private listenerDate() {
    this.$bus.$on('BusYearTimeChange', (year: string) => {
      this.params.year = year
      this.fetchData()
    })

    this.$bus.$on('BusMoonTimeChange', (month: string) => {
      this.params.month = month
      this.fetchData()
    })
  }

  // 获取数据
  @Loading('loading')
  private async fetchData() {
    let { data } = await getProjectTypeRate(this.params)

    let dataList: any[] = []
    data.forEach((item: any) => {
      dataList.push({
        name: item.indicatorName,
        value: +item.indicatorValue,
        rate: +item.indicatorRate,
        num: +item.indicatorCount
      })
    })

    this.seriesData = dataList

    this.totalInvestment = this.seriesData.reduce((total: number, current) => (total += +current.value), 0)
    this.totalInvestment = +this.totalInvestment.toFixed(2)
  }
}
</script>

<style scoped lang="scss">
.project-classification {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  box-sizing: border-box;
  .chart-wrapper {
    width: 50%;
    height: 100%;
  }

  .info {
    width: 50%;
    height: 100%;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;

    &__legend {
      align-self: flex-start;
      width: 100%;
      padding: 0 30px;
      box-sizing: border-box;
      border-radius: 10px;
      background: rgba($color: #093091, $alpha: 1);
    }

    &__legend-item {
      font-size: 36px;
      margin: 20px 0;
      span {
        margin-right: 8px;
      }
      .legend-item {
        &__top {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .ws {
            display: flex;
            align-items: center;
          }

          .point {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
          }
          .name {
            font-weight: 400;
          }
          .rate {
            font-weight: bold;
          }
        }
        &__bottom {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .sum {
            font-family: 'PangMenZhengDao';
            font-weight: normal;
            font-size: 44px;
            i {
              font-size: 30px;
              margin-left: 6px;
            }
          }
          .up {
            color: #ff368b;
            font-family: 'digital-7';
          }
          .down {
            color: #42ff5b;
            font-family: 'digital-7';
          }
        }
      }
    }
  }

  .halo {
    position: absolute;
    top: 199px;
    right: -192px;
    width: 390px;
    height: 390px;
    transform: translate(-50%, -50%);
    background: url('../../../images/halo.png');
    background-size: 100% 100%;
    will-change: width, height, transform;
    //animation: move 20s linear forwards infinite;
  }
}
</style>