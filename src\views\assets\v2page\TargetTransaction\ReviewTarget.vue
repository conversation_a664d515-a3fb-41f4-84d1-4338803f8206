<template>
  <Dialog title="标的审核" width="95%" :visible="visible" @close="handleClose">
    
    <div slot="body" v-loading="loading" class="dialog_content">
      <div class="review_content" v-if="showReview">
        <div class="review_content_box">
          <ImgLists :detaildataInfo="detaildataInfo" />
        </div>
        <div class="review_content_box">
          <el-tabs v-model="activeName">
            <el-tab-pane label="标的信息" name="1">
              <detailtargetInfo :detail="detaildataInfo"></detailtargetInfo>
            </el-tab-pane>
            <el-tab-pane label="出租方信息" name="2">
              <detailLessorInfo :detail="detaildataInfo"></detailLessorInfo>
            </el-tab-pane>
            <el-tab-pane label="承租方资格条件" name="3">
              <detailLesseeInfo :detail="detaildataInfo"></detailLesseeInfo>
            </el-tab-pane>
            <!-- <el-tab-pane label="审核与结果" name="4">
              <DetailReviewResults></DetailReviewResults>
            </el-tab-pane> -->
          </el-tabs>
        </div>
      </div>
      <DialogReview @handleClose="handleClose" :detaildata="detaildata" :detail="detaildataInfo" :visible.sync="reviewVisible" v-if="reviewVisible" />
    </div>
    <div slot="footer">
      <el-button type="" @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="reviewBtn">审核</el-button>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import detailtargetInfo from './components/DetailTargetInfo.vue'
import detailLessorInfo from './components/DetailLessorInfo.vue'
import detailLesseeInfo from './components/DetailLesseeInfo.vue'
import DetailReviewResults from './components/DetailReviewResults.vue'
import { targetDetail } from '@/api/assetsv2'
import DialogReview from './components/Dialogreview.vue'
import ImgLists from './components/ImgList.vue'
@Component({
  components: {
    Dialog,
    detailtargetInfo,
    detailLessorInfo,
    detailLesseeInfo,
    DetailReviewResults,
    ImgLists,
    DialogReview
  }
})
export default class extends Vue {
  @Prop() private visible!: boolean
  @Prop() private detaildata!: any
  @Prop() private mode!: any
  private detaildataInfo = {}
  private loading = false
  private showReview = false
  private reviewVisible = false
  private activeName = '1'
  private handleClose() {
    this.$emit('update:visible', false)
  }
  mounted() {
    if (this.mode == 'see') {
      this.getDetail()
    }
  }
  // 点击审核按钮
  private reviewBtn() {
    //
    this.reviewVisible = true
  }
  private async getDetail() {
    try {
      this.loading = true
      let res = await targetDetail({
        id: this.detaildata.id
      })
      if (res.success) {
        this.detaildataInfo = res.data
        this.loading = false
        this.showReview = true
      }
    } catch (e) {
      this.$emit('update:visible', false)
      this.loading = false
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog_content {
  padding: 0 !important;
  min-height: 40vh;
}
.review_content {
  width: 100%;
  height: 100%;
  height: 75vh;
  display: flex;
  // justify-content: ;
}
.review_content_box {
  // width: 100%;
  height: 100%;
  max-width: 50%;
  max-height: 100%;
  overflow-y: auto;
  flex: 1;
  margin: 0 5px;
}
::v-deep.el-tab-pane {
  height: calc(100% - 110px);
  overflow-y: auto;
}
</style>
