 <!-- 资产减值备案 -->
<template>
  <el-container class="container"
    direction="vertical">
    <search-bar :items="searchItems"
      @onSearch="handleSearch">
      <div>
        <el-button type="primary"
          @click="AddForm">新增</el-button>
      </div>
    </search-bar>
    <Grid @row-click="loaddetail"
      ref="grid"
      :columns="cols"
      :remote-url="remoteUrl"
      :show-pagination="true"
      :search-params="searchParams">
      <template slot="documentNo"
        slot-scope="scope">
        <span>{{ scope.row.documentNo }}</span>
      </template>
      <template slot="state"
        slot-scope="scope">
        <span v-if="scope.row.state == 1">审批中</span>
        <span v-else-if="scope.row.state == 2">审批通过</span>
        <span v-else-if="scope.row.state == 3"
          style="color: #df7575">审批不通过</span>
      </template>
      <!-- 决策机构 -->
      <!-- <div v-for="(item,index) in cols" :key="index" slot="item.slotName" slot-scope="scope">
            <span v-if="'filterOptions' in item">{{ scope.row[item.prop] }}</span>
          </div> -->
      <template slot="issuanceCurrency"
        slot-scope="scope">
        <span v-if="scope.row.state == 1">人民币</span>
        <span v-else-if="scope.row.state == 2">美元</span>
        <span v-else-if="scope.row.state == 3">其他</span>
      </template>
      <template slot="operatingBar"
        slot-scope="scope">
        <div class="operatingBar_content">
          <el-button type="text"
            @click.stop="loaddetail(scope.row)">查看</el-button>
          <el-button type="text"
            @click.stop="loofinfo(scope.row)">编辑</el-button>

        </div>
      </template>
    </Grid>
    <!-- 查看 -->
    <Dialog-add :visible.sync="showDialogAdd"
      :mode="DialogMode"
      v-if="showDialogAdd"
      @changshowDialogAdd="changeShowDialogAdd"
      :Diaformdata="Diaformdata" />
    <!-- 查看详情 -->
  </el-container>

</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import Grid from '@/components/Grid/index.vue'
import DialogAdd from '../components/DialogAsset.vue'
import { AssetList } from '../date'
import { deleteFormData } from '@/api/finance'
import searchBar from '@/components/SearchBar/index.vue'
import { deepClone } from '../../../utils'

@Component({
  components: {
    Grid,
    DialogAdd,
    searchBar
  }
})
export default class Container extends Vue {
  private detailFrom = {}
  private Diadetaillist: object[] = [] //查看详情列表
  private Diaformdata: any = {}
  private showDialogAdd = false
  private showDetailAsset = false
  private searchParams = {} //表格搜索条件
  private searchContent = ''
  private remoteUrl = '/fht-monitor/fin/assetsWriteoff/page'
  private data: object[] = AssetList
  private DialogMode = ''
  private searchItems = [
    {
      type: 'text',
      key: 'keyword',
      placeholder: '请输入关键字进行查询',
      width: '200px'
    }
  ]
  private cols = [
    {
      label: '备案编号',
      prop: 'recordNo',
      minWidth: 180
    },
    {
      label: '单据编号',
      prop: 'documentNo',
      slotName: 'documentNo',
      minWidth: 160
    },

    {
      label: '年度',
      prop: 'year',
      minWidth: 110
    },
    {
      label: '填报单位',
      prop: 'reportDeptName',
      minWidth: 110
    },
    // {
    //   label: '资产核销企业名称',
    //   prop: 'reportDeptName',
    //   minWidth: 120
    // },
    {
      label: '资产核销企业性质',
      prop: 'enterpriseNature',
      minWidth: 120
    },
    {
      label: '经济行为决策机构',
      prop: 'decisionMakingBody',
      minWidth: 120
    },
    {
      label: '批准文件日期',
      prop: 'approvalDate',
      minWidth: 100
    },

    {
      label: '核销不良资产金额',
      prop: 'amount',
      minWidth: 120
    },
    {
      label: '币种',
      prop: 'currency',
      minWidth: 80
    },
    {
      label: '货币单位',
      prop: 'currencyUnitDesc',
      minWidth: 80
    },
    {
      label: '不良资产内容',
      prop: 'content',
      minWidth: 120
    },
    {
      label: '创建人',
      prop: 'createUserName',
      minWidth: 80
    },
    {
      label: '创建时间',
      prop: 'createTime',
      minWidth: 80
    },
    {
      label: '操作',
      prop: 'operatingBar',
      slotName: 'operatingBar',
      fixed: 'right',
      minWidth: 80
    }
  ]
  // search搜索
  private handleSearch(condition: any) {
    this.searchParams = condition
    let grid: any = this.$refs.grid
    grid.refresh()
  }
  // 新增按钮
  private AddForm() {
    this.DialogMode = 'add'
    this.Diaformdata = {
      id: ''
    }
    this.showDialogAdd = true
  }
  // 改变显示隐藏
  private changeShowDialogAdd(state: boolean) {
    if (state == false) {
      ;(this.$refs.grid as Grid).refresh(true)
    }
    this.showDialogAdd = state
  }
  private changeShowDetail(state: boolean) {
    this.showDetailAsset = state
  }
  //点击查看
  private loofinfo(row: object) {
    this.DialogMode = 'edit'
    this.Diaformdata = row
    this.showDialogAdd = true
  }
  // 查看
  private loaddetail(row: any) {
    this.Diaformdata = row
    this.DialogMode = 'see'
    this.showDialogAdd = true
  }
  // 删除
  private async deleteinfo() {
    // 拉取接口远程数据
    let params = {}
    try {
      let res = await deleteFormData(params)
      if (res.success) {
        // this.option = res.data
      }
    } catch (e) {
      //
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
}
</style>
