/** 柱形图组 */

<template>
  <section class="bar-chart-gounp">
    <div v-for="(item, index) in chartTypeList"
      :key="index"
      class="chart">
      <BarChart :chartId="`${companyId}${item.code}${index}`"
        :seriesData="item.series"
        :individuationOptions="getIndividuationOptions(item)"
        :xData="item.xData" />
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import BarChart from '@/views/main/cockpitSecondary/charts/BarCharts.vue'
import { EChartsOption } from 'echarts'

interface ChartType {
  label: string
  code: string
  xData: string[]
  color: string
  series: number[]
}

@Component({
  components: {
    BarChart
  }
})
export default class extends Vue {
  @Prop() private companyId!: string

  private chartTypeList: ChartType[] = [
    {
      label: '重要人事任免事项',
      code: '0',
      xData: ['推荐人数', '提拔人数', '轮岗人数', '免职人数', '辞职人数', '辞退人数'],
      color: '#E6B607',
      series: this.getData(6)
    },
    {
      label: '重大项目安排事项',
      code: '1',
      color: '#35AAF6',
      xData: ['产权转让', '对外投资', '机构调整', '其他重要事项'],
      series: this.getData(4)
    },
    {
      label: '重大决策事项',
      code: '2',
      color: '#2D5AE5',
      xData: ['年度投资', '大宗货物', '重大工程', '服务项目'],
      series: this.getData(4)
    },
    {
      label: '大额资金运作事项',
      code: '3',
      color: '#EC342F',
      xData: ['年度投资', '大宗货物', '重大工程', '服务项目'],
      series: this.getData(4)
    }
  ]

  private getIndividuationOptions(item: ChartType): EChartsOption {
    return {
      title: {
        text: item.label,
        left: 'center',
        bottom: '3%',
        textStyle: {
          color: '#fff',
          fontSize: 36
        }
      },
      grid: {
        top: '20%',
        bottom: '15%'
      },
      yAxis: {
        nameTextStyle: {
          padding: [40, 0]
        }
      }
    }
  }

  private getData(num = 5, upperLimit = 100) {
    let data: number[] = new Array(num).fill(0)
    return data.map(() => Math.ceil(Math.random() * upperLimit))
  }
}
</script>

<style scoped lang="scss">
.bar-chart-gounp {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .chart {
    width: 23%;
    height: 520px;
    // margin-bottom: 30px;
  }
  .chart:first-child {
    width: 30%;
  }

  .chart:nth-child(2) {
    width: 25%;
  }

  .title {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
    color: #fff;
  }
}
</style>




