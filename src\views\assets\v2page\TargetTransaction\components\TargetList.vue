<template>
  <el-container class="table-wrapper" direction="vertical">
    <Title title="标的名称">
      <el-button type="primary" @click="addTargrt" v-if="this.$attrs.mode !== 'reset'">新增标的</el-button>
    </Title>
    <!-- 表格 -->

    <Grid
      ref="grid"
      :columns="cols"
      :show-selectiSearchAstListon="true"
      :show-pagination="false"
      :overflow-tooltip="true"
      :show-index="true"
      show-index-fixed="left"
      :data="targrtList"
      style="min-height:200px"
    >
      <template #subjectStatusSlot="{ row }">
        {{ getDictValue("subjectStatus",row.subjectStatus) }}
      </template>
      <template #priceUnitSlot="{ row }">
                     {{ getDictValue("subjectPriceUnit",row.subjectStatus) }}

      </template>
      <template #priorityLeaseSlot="{ row }">
        <div>{{ row.priorityLease=='1'?'是':"否" }}</div>
      </template>
      <template #leaseTermSlot="{ row }">
         {{ getDictValue("subjectLeaseTerm",row.subjectStatus) }}
      </template>
      <template #operationSlot="{ row }">
        <Operation :list="operationList" :row="row" />
      </template>
    </Grid>
    <target-list-form
      @addForm="addForm"
      @updatedForm="updatedForm"
      v-bind="$attrs"
      v-if="addVisible"
      :visible.sync="addVisible"
      :targrtList="targrtList"
      :mode="FormMode"
      :formData="formData"
    />
  </el-container>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import Title from '@/views/assets/components/Title/index.vue'
import Grid from '@/components/Grid/index.vue'
import Operation, { OperationItem } from '@/components/Operation/index.vue'
import { Confirm } from '@/decorators/index'
import TargetListForm from './TargetListForm.vue'
import { deepClone } from '../../../../../utils'
import { BusinessModule } from '@/store/modules/businessDict'
@Component({
  components: {
    Title,
    Grid,
    Operation,
    TargetListForm
  }
})
export default class extends Vue {
  private titleName = '标的列表'
  private targrtList: any = []
  private mode = 'see'
  //   新增标的
  private addVisible = false
  private FormMode = 'add'
  private formData: any = {}
  get operationList(): OperationItem[] {
    return [
      {
        label: '查看',
        click: this.onSee,
        style: 2,
        visible: (row: any) => {
          return true
        }
      },
      {
        label: '删除',
        click: this.onDelete,
        style: 2,
        visible: (row: any) => {
          return this.$attrs.mode !== 'reset'
        }
      },
      {
        label: '编辑',
        click: this.onEdit,
        style: 2,
        visible: (row: any) => {
          return true
        }
      }
    ]
  }
  private cols = [
    {
      label: '标的名称',
      prop: 'subjectName',
      minWidth:"120px"
    },
    {
      slotName: 'subjectStatusSlot',
      label: '标的状态',
      prop: 'subjectStatus',
    },
    // {
    //   label: '租金挂牌价',
    //   prop: 'rentPrice'
    // },
    {
      label: '面积（㎡）',
      prop: 'rentArea'
    },
    {
      label: '租金挂牌价',
      prop: 'rentPrice'
    },
    {
      label: '挂牌价单位',
      prop: 'priceUnit',
      minWidth: '80px',
      slotName: 'priceUnitSlot'
    },
    {
      label: '是否有优先承租权',
      slotName: 'priorityLeaseSlot',
      prop: 'priorityLease',
      minWidth: '120px'
    },
    {
      label: '租期',
      slotName: 'leaseTermSlot',
      prop: 'leaseTerm',
      minWidth: '120px'
    },
    {
      slotName: 'operationSlot',
      label: '操作'
    }
  ]
  private addForm(data: any) {
    data.aid = new Date().getTime()
    this.targrtList.push(data)
    this.$message.success('添加成功')
    this.addVisible = false
  }
  private updatedForm(data: any) {
    if (this.targrtList.length > 0) {
      this.targrtList.forEach((item: any, index: number) => {
        if (item.aid == data.aid) {
          this.targrtList.splice(index, 1, data)
          this.$message.success('更新成功')
          this.addVisible = false
        }
      })
    }
  }
  // 获取资产列表
  // private get
  //   新增按钮
  private addTargrt() {
    this.FormMode = 'add'
    this.addVisible = true
  }
  mounted() {
    if (this.$attrs.mode == 'reset') {
      this.targrtList = Object.assign(this.targrtList, (this.$attrs.detaildata as any).subjectList)
      // this.lesseeQualificationsForm = (this.$attrs.detailInfo as any).leaseCond
    }
  }
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  // 筛选对应值
  private getDictValue(key:string,values: any = '') {
    return this.getDictData(key).find((res: any) => {
      return res.value == values
    }).label
  }
  //   查看
  private onSee(row: any) {
    //
    this.formData = deepClone(row)
    this.FormMode = 'see'
    this.addVisible = true
  }
  //   编辑
  private onEdit(row: any) {
    //
    this.formData = deepClone(row)
    this.FormMode = 'edit'
    this.addVisible = true
  }
  @Confirm({
    title: '提示',
    content: `是否确认删除该标的？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private onDelete(row: any) {
    if (this.targrtList.length > 0) {
      this.targrtList.forEach((item: any, index: number) => {
        if (item.aid == row.aid) {
          this.targrtList.splice(index, 1)
        }
      })
    }
  }
}
</script>
