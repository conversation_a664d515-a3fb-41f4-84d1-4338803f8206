/**
  组件描述:  新增年度计划投资弹框
*/
<template>
  <Dialog title="投资项目计划备案-新增"
    width="980px"
    :visible="visible"
    :close-on-click-modal="true"
    :destroy-on-close="true"
    :append-to-body="true"
    @close="closeDlg">
    <div slot="body"
      v-loading="loading">
      <el-form ref="RecordsForm"
        :rules="recordsFormRules"
        :model="investProjectPlanFiling"
        inline
        label-width="80px">
        <el-form-item label="备案编号"
          prop="id">
          <el-input v-model="investProjectPlanFiling.id"
            disabled
            placeholder="保存后自动生成" />
        </el-form-item>
        <el-form-item label="填报单位">
          <el-input v-model="investProjectPlanFiling.orgName"
            readonly />

          <!-- <el-link style="padding:0 30px 0 15px;"
            :underline="false">{{investProjectPlanFiling.orgName}}</el-link> -->
        </el-form-item>
        <el-form-item label="年份"
          prop="year">
          <el-date-picker v-model="investProjectPlanFiling.year"
            type="year"
            value-format="yyyy"
            :clearable="true" />
        </el-form-item>
      </el-form>

      <div>
        <PlanTable ref="PlanTable"
          :loading="loading"
          :id="investProjectPlanFiling.id"
          mode="add" />
      </div>
      <div style="margin-top:60px;">
        <AccessoryList v-model="investProjectPlanFiling.fileList"
          mode="upload"
          dict="invest_plan_file_type" />
      </div>
    </div>
    <div slot="footer">
      <el-button @click="closeDlg">取 消</el-button>
      <el-button class="primary-buttom"
        type="primary"
        :loading="loading"
        @click="submitForm">提 交</el-button>
    </div>
  </Dialog>
</template>
<script lang="ts">
import { Component, Emit, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import { Loading } from '@/decorators'
import { ElForm } from 'node_modules/element-ui/types/form'
import { createPlanIni, createProject, getCompTree, userInfo } from '@/api/projectInvestment'
import PlanTable from '../../components/PlanTable/PlanGrid.vue'
import Uploader from '../../components/Uploader/index.vue'
import { AgentProjectForm } from '../../components/ProjectForm/AgentProjectForm.vue'
import { CompanyForm } from '../../components/ProjectForm/CompanyProjectForm.vue'
import { GovForm } from '../../components/ProjectForm/GovProjectForm.vue'
import AccessoryList from '@/views/assets/components/TradeDialog/components/AccessoryFileList.vue'
import { Confirm } from '@/decorators/index'
import { getLocalStorage } from '@/utils/cache'
@Component({
  components: {
    Dialog,
    Uploader,
    PlanTable,
    AccessoryList
  }
})
export default class extends Vue {
  @Prop() private visible!: boolean
  @Prop() private title!: string

  private loading = false
  private compTree = []
  private tableList: {
    investProjectAgentDataExlVO: AgentProjectForm[]
    investProjectDataExlVO: CompanyForm[]
    investProjectGovDataExlVO: GovForm[]
  } = {
    investProjectAgentDataExlVO: [], // 代建
    investProjectDataExlVO: [], // 企业
    investProjectGovDataExlVO: [] // 政府
  }

  private investProjectPlanFiling: any = {
    orgId: '',
    orgName: '',
    id: '',
    fileList: [],
    year: String(new Date().getFullYear() - 1),
    planType: 1, //计划备案类型 1-正常 2-调整
    projectDataList: []
  }

  private recordsFormRules = {
    id: [{ required: false, trigger: 'blur', message: '备案编号不能为空' }],
    orgId: [{ required: true, trigger: 'blur', message: '填报单位' }],
    year: [{ required: true, trigger: 'blur', message: '年份不能为空' }]
  }

  // 弹窗关闭
  @Emit('close')
  private onClose() {
    //
  }

  created() {
    // this.createPlanIni()
    this.getCompTree()
    this.getuserinfo()
    let userInfo: any = getLocalStorage('saber-userInfo')
    let deptId = JSON.parse(userInfo).content.dept_id
  }

  // 获取备案编号
  @Loading('loading')
  private async createPlanIni() {
    let res = await createPlanIni(1)
    if (res.success) {
      // this.investProjectPlanFiling.id = res.data.id
    }
  }
  @Loading('loading')
  private async getuserinfo() {
    try {
      let res = await userInfo({})
      if (res.success) {
        this.investProjectPlanFiling.orgId = res.data.deptId
        this.investProjectPlanFiling.orgName = res.data.deptName
        // this.data= res.data.records
      }
    } catch (e) {
      //
    }
  }
  //获取机构数组
  private async getCompTree() {
    let options = []
    let res = await getCompTree({})
    if (res.success) {
      options = res.data
    }
    this.compTree = options
  }
  // @Loading('loading')
  @Confirm({
    title: '提示',
    content: `是否确认提交信息？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'success'
  })
  private async submitForm() {
    // 基础信息校验
    ;(this.$refs.RecordsForm as ElForm).validate((valid: boolean) => {
      if (!valid) return

      // 附件校验
      // if (!this.validateFiles()) return
      let planTable = this.$refs.PlanTable as any
      this.investProjectPlanFiling.projectDataList = planTable.tableList
      // 计划表校验
      this.onSave()
      // planTable.validate().then(() => {
      //   let tableList = planTable.getTableList()
      //   // 如果都为空
      //   this.tableList = tableList
      // })
    })
  }

  // 保存
  private async onSave() {
    this.investProjectPlanFiling.projectDataList.forEach((item: any, index: number) => {
      if (this.investProjectPlanFiling.planType == 1) {
        //  新增提交时修改id为空
        item.id = ''
      }
    })
    let res = await createProject(this.investProjectPlanFiling)

    if (res.success) {
      this.$message.success(res.msg || '保存成功!')
      this.$emit('update:visible', false)
      this.$emit('success')
    }
  }

  // 附件校验
  private validateFiles(): boolean {
    let investProjectPlanFiling = this.investProjectPlanFiling
    // 按url形式
    // for (let index in this.accessoryList) {
    //   let accessory = this.accessoryList[index]
    //   let fileUrl = accessory.fileList.map((item) => item.url).join(',')
    //   this.investProjectPlanFiling[accessory.prop as keyof typeof investProjectPlanFiling] = fileUrl
    //   if (!fileUrl && accessory.isRequired) {
    //     this.$message.warning(`请上传${accessory.fileName}！`)
    //     return false
    //   }
    // }
    return true
  }

  // 关闭弹窗
  @Confirm({
    title: '提示',
    content: `是否关闭弹窗？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private closeDlg() {
    this.$emit('update:visible', false)
    this.onClose()
  }
}
</script>

<style lang="scss" scoped>
.uploader-list {
  display: flex;
  flex-wrap: wrap;
  ::v-deep .uploader-wrapper {
    margin: 12px 24px;
  }
}
</style>

