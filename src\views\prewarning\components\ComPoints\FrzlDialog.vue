<template>
  <Dialog width="1200px"
    :visible="visible"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :append-to-body="true"
    @close="handleClose">
    <!-- 自定义标题 -->
    <div slot="header"
      class="header-box">
      <div class="title">查看详情</div>
    </div>

    <!-- 内容区域 -->
    <div v-loading="loading"
      slot="body"
      class="body-box">
      <iframe :src="src"
        ref="iframe"
        width="100%"
        height="100%"
        frameborder="0"
        :allowfullscreen="false" />
    </div>

    <!-- 底部按钮 -->
    <div slot="footer"
      class="footer-box">
      <el-button @click="handleClose">关 闭</el-button>
    </div>
  </Dialog>
</template>

<script>
import Dialog from '@/components/Dialog/index.vue'
import { adminRouterUrl } from '@/utils/index'

export default {
  components: { Dialog },

  data() {
    return {
      loading: false,
      visible: false,
      src: ''
    }
  },

  methods: {
    handleOpen(row) {
      console.log('🚀 传递的参数', row)
      this.src = adminRouterUrl(`/business/assets/filingManagement/viewcoment?menuType=view&rowDataId=${row.reserve}`)
      this.visible = true
    },

    handleClose() {
      this.visible = false
    }
  }
}
</script>

<style scoped lang="scss">
.body-box {
  width: 100%;
  height: 700px;
}
</style>

