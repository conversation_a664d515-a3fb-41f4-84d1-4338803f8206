/**
  组件描述: 项目投资概况
*/
<template>
  <section v-loading="loading"
    class="new-project">
    <div class="new-project__item"
      v-for="(item, index) in indexList"
      :key="index">
      <span>{{ item.indicatorName }}</span>
      <span :style="{
        color: index === 3 ? '#FFC62C' : '#40EEFF'
      }">
        {{ item.indicatorValue}}{{ index === 3 ? '%' : '' }}
      </span>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import { getNewProject, ProjectProgressDataItem, ProjectProgressParams } from '@/api/cockpit'
import { Loading } from '@/decorators'

@Component
export default class extends Vue {
  @Prop({ default: '0' }) private activeTabs!: string

  @Watch('activeTabs', { immediate: true })
  private activeTabsChange() {
    this.params.orgCode = this.activeTabs
    this.fetchData()
  }

  private loading = false

  // 参数
  private params: any = {
    orgCode: '0',
    year: String(new Date().getFullYear()),
    projectProperty: ''
  }

  // 指标列表
  private indexList: ProjectProgressDataItem[] = []

  mounted() {
    this.listenerDate()
    this.$bus.$on('projectInvestmentTypeChange', (projectProperty: string) => {
      this.params.projectProperty = projectProperty
      this.fetchData()
    })
  }

  // 监听时间变化
  private listenerDate() {
    this.$bus.$on('BusYearTimeChange', (year: string) => {
      this.params.year = year
      this.fetchData()
    })

    this.$bus.$on('BusMoonTimeChange', (month: string) => {
      this.params.month = month
      this.fetchData()
    })
  }

  // 请求数据
  @Loading('loading')
  private async fetchData() {
    let res = await getNewProject(this.params)
    if (res.success) {
      this.indexList = [...res.data]
    }
  }
}
</script>


<style scoped lang="scss">
.new-project {
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 100%;
  &__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 36px;
    span:first-child {
      color: #fff;
      font-weight: 400;
    }

    span:last-child {
      font-size: 48px;
      font-weight: normal;
      font-family: 'PangMenZhengDao';
      margin-top: 30px;
    }
  }
}
</style>