@font-face {
  font-family: "digital-7";
  src: url('./digital-7.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "Alibaba-PuHuiTi-Bold";
  src: url('./Alibaba-PuHuiTi-Bold.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "ALiHanYiZhiNengHeiTi-2";
  src: url('./ALiHanYiZhiNengHeiTi-2.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "PangMenZhengDao";
  src: url('./庞门正道标题体.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "FZZZHONGHJW";
  src: url('./FZZZHONGHJW.TTF') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "fsGB2312";
  src: url('./fsGB2312.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "fzxbsjt";
  src: url('./fzxbsjt.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "ht";
  src: url('./ht.TTF') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "kt";
  src: url('./kt.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "zwfs";
  src: url('./zwfs.TTF') format('truetype');
  font-weight: normal;
  font-style: normal;
}