/* 二级页面公共标题组件 */

<template>
  <section class="cockipt-vice-title">
    <h4 class="title">{{title}}</h4>
    <i v-if="isThrow"
      class="details"
      @click="toDetaileHandle" />
    <i v-if="isRotuerThrow"
      class="details"
      @click="routerHandle" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

type typeBizObj = {
  bizAppCode: string
  detailsPath: string
}

@Component
export default class extends Vue {
  @Prop() private title!: string // 标题
  @Prop({ default: false }) private isThrow?: boolean // 是否需要详情箭头
  @Prop({ default: false }) private isRotuerThrow?: boolean // 是否跳转业务模块箭头
  @Prop({
    default: () => {
      return {
        bizAppCode: '',
        detailsPath: ''
      }
    }
  })
  routerObj?: typeBizObj // 如果 isRotuerThrow 为 true，需要设置跳转的业务路由参数

  // 点击箭头
  private toDetaileHandle() {
    this.$emit('toDetaileHandle')
  }

  // 点击箭头，跳转到相关业务系统
  routerHandle() {
    let { detailsPath, bizAppCode } = this.routerObj as typeBizObj

    this.$bus.$emit('BustoBusiness', {
      code: bizAppCode,
      path: detailsPath
    })
  }
}
</script>

<style scoped lang="scss">
.cockipt-vice-title {
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 40px;
  box-sizing: border-box;
  justify-content: space-between;

  @keyframes keyViceTitle {
    0% {
      opacity: 0;
      transform: translateX(100px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  h4,
  p {
    margin: 0;
  }

  .title {
    height: 80px;
    font-size: 50px;
    font-style: italic;
    font-weight: normal;
    font-family: 'FZZZHONGHJW';
    padding-right: 120px;
    background: url('../images/mode_title.png') no-repeat right bottom;
    background-size: auto 100%;
    animation: keyViceTitle 5s ease;
  }

  .details {
    position: relative;
    z-index: 10;
    width: 80px;
    height: 30px;
    background: url('../images/thow_more.png') no-repeat center center;
    background-size: 100%;
    transition: 0.5s;
    cursor: pointer;
    &:hover {
      transform: translateX(10px);
    }
  }
}

.cockipt-mode-vice-title {
  position: absolute;
  width: 100%;
  color: #63f1ff;
  font-size: 38px;
  font-weight: bold;
  margin: 0;
  padding-left: 34px;
  background: url('../images/threw.png') no-repeat left -4px;
  background-size: 24px 100%;
}
</style>