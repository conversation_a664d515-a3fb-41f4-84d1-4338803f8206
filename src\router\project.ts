/* 项目管理 */

import Layout from '@/layouts/public/index.vue'

export const routes = [
  {
    path: '/project',
    name: '项目管理',
    component: Layout,
    redirect: "/project/home",
    children: [
      {
        path: 'home',
        name: '项目看板',
        component: () => import('@/views/project/home/<USER>')
      },
      {
        path: 'investment',
        name: '投资看板',
        component: () => import('@/views/project/investment/index.vue')
      },
      {
        path: 'home',
        name: '项目地图',
        component: () => import('@/views/project/home/<USER>')
      }
    ]
  }
]

export default routes