<template>
  <section class="contract-detail-wrap">
    <el-descriptions v-if="detailData.contractInfo"
      :column="2"
      :colon="true"
      labelClassName="descriptions_label"
      size="medium"
      title="基本信息"
      class="m-b-20"
      border>
      <el-descriptions-item
        label="合同编号">{{detailData.contractInfo.contractNo}}</el-descriptions-item>
      <el-descriptions-item label="合同周期"
        :span="3">
        <span v-if="detailData.contractInfo.startTime && detailData.contractInfo.endTime">
          {{detailData.contractInfo.startTime + "至" + detailData.contractInfo.endTime}}
        </span>
      </el-descriptions-item>
      <el-descriptions-item label="期租金">
        <span v-if="detailData.contractInfo.rentFee">
          {{detailData.contractInfo.rentFee !== null ? detailData.contractInfo.rentFee + "元/" + detailData.contractInfo.rentUnitDesc : ""}}
        </span>
      </el-descriptions-item>
      <el-descriptions-item
        label="合同状态">{{detailData.contractInfo.contractStatusDesc}}</el-descriptions-item>
      <el-descriptions-item
        label="合同类型">{{detailData.contractInfo.contractTypeDesc}}</el-descriptions-item>
      <el-descriptions-item label="签约日期">{{detailData.contractInfo.signTime}}</el-descriptions-item>
      <el-descriptions-item
        label="交易方式">{{detailData.contractInfo.tradeTypeDesc}}</el-descriptions-item>
      <el-descriptions-item
        label="总金额（元）">{{detailData.contractInfo.totalFee}}</el-descriptions-item>
      <el-descriptions-item
        label="	总押金（元）">{{detailData.contractInfo.totalDeposit}}</el-descriptions-item>
      <el-descriptions-item
        label="总押金（元）">{{detailData.contractInfo.totalDeposit}}</el-descriptions-item>
      <el-descriptions-item
        label="总实收（元）">{{detailData.contractInfo.totalPaidFee}}</el-descriptions-item>
    </el-descriptions>

    <div class="mode-box m-t-15 m-b-20">
      <div class="title">资产信息</div>
      <el-table :data="detailData.itemList"
        border
        size="medium"
        style="width: 100%"
        @row-click="assetsRowClick">
        <el-table-column label="资产名称"
          prop="itemName"
          :show-overflow-tooltip="true">
          <template slot-scope="{row}">
            <span class="btn">{{row.itemName}}</span>
          </template>
        </el-table-column>
        <el-table-column label="所属集团"
          prop="orgName"
          :show-overflow-tooltip="true" />
        <el-table-column label="直属单位"
          prop="bizName"
          :show-overflow-tooltip="true" />
        <el-table-column label="子资产编号"
          prop="itemNo"
          :show-overflow-tooltip="true" />
        <el-table-column label="资产地址"
          prop="address"
          :show-overflow-tooltip="true" />
        <el-table-column label="出租面积(m²)"
          prop="coveredArea"
          :show-overflow-tooltip="true" />
      </el-table>
    </div>

    <el-descriptions v-if="detailData.lessorInfo"
      :colon="true"
      :column="2"
      title="出租方信息"
      size="medium"
      border
      class="m-t-15 m-b-20">
      <el-descriptions-item label="出租方名称"
        :span="2">{{detailData.lessorInfo.lessorName}}</el-descriptions-item>
      <el-descriptions-item label="统一代码"
        :span="2">{{detailData.lessorInfo.lessorCardNo}}</el-descriptions-item>
      <el-descriptions-item
        label="法人代表">{{detailData.lessorInfo.lessorLegalName}}</el-descriptions-item>
      <el-descriptions-item
        label="法人电话">{{detailData.lessorInfo.lessorLegalPhone}}</el-descriptions-item>
      <el-descriptions-item
        label="联系人名称">{{detailData.lessorInfo.lessorContactName}}</el-descriptions-item>
      <el-descriptions-item
        label="联系电话">{{detailData.lessorInfo.lessorContactPhone}}</el-descriptions-item>
      <el-descriptions-item label="联系地址"
        :span="2">{{detailData.lessorInfo.lessorContactAddress}}</el-descriptions-item>
    </el-descriptions>

    <el-descriptions v-if="detailData.renterInfo"
      :colon="true"
      :column="2"
      class="m-t-15 m-b-20"
      title="承租方信息"
      size="medium"
      border>
      <el-descriptions-item label="承租方名称"
        :span="2">{{detailData.renterInfo.renterName}}</el-descriptions-item>
      <el-descriptions-item label="联系电话"
        :span="2">{{detailData.renterInfo.renterPhone}}</el-descriptions-item>
      <el-descriptions-item
        label="类型">{{detailData.renterInfo.renterTypeDesc}}</el-descriptions-item>
      <el-descriptions-item
        label="证件类型">{{detailData.renterInfo.renterCardTypeDesc}}</el-descriptions-item>
      <el-descriptions-item
        label="证件号">{{detailData.renterInfo.renterCardNo}}</el-descriptions-item>
      <el-descriptions-item
        label="地址">{{detailData.renterInfo.renterAddress}}</el-descriptions-item>
      <el-descriptions-item v-if="detailData.renterInfo.renterType=='2'"
        label="企业代表名称">{{detailData.renterInfo.renterLegalName}}</el-descriptions-item>
      <el-descriptions-item v-if="detailData.renterInfo.renterType=='2'"
        label="企业代表身份证号">{{detailData.renterInfo.renterLegalCardNo}}</el-descriptions-item>
      <el-descriptions-item v-if="detailData.renterInfo.renterType=='2'"
        label="企业代表联系方式">{{detailData.renterInfo.renterLegalPhone}}</el-descriptions-item>
    </el-descriptions>

    <AccessoryList title="合同文件"
      v-model="detailData.fileList"
      mode="see" />

    <!-- 弹窗：详情 -->
    <DetailCom v-if="visibleDetailDia"
      :visible.sync="visibleDetailDia"
      :id="detailRow.id"
      :itemId="detailRow.itemId" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import AccessoryList from '@/views/assets/components/TradeDialog/components/AccessoryFileList.vue'

@Component({
  components: {
    AccessoryList,
    DetailCom: () => import('@/views/assets/page/manage/components/DetailCom.vue')
  }
})
export default class extends Vue {
  @Prop({
    default: () => {
      return {
        contractInfo: {},
        renterInfo: {},
        fileList: [],
        lessorInfo: {}
      }
    }
  })
  private detailData?: object

  private loading = false
  private visibleDetailDia = false
  private detailRow = {}

  // 资产信息，查看详情
  private assetsRowClick(item: any) {
    if (item.id && item.itemId) {
      this.detailRow = item
      this.visibleDetailDia = true
    } else {
      console.error('id 和 itemId 参数错误')
    }
  }
}
</script>
