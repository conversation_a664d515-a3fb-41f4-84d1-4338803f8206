/**
  组件描述:  新增年度计划投资弹框
*/
<template>
  <Dialog title="投资项目计划备案-调整"
    width="980px"
    :visible="visible"
    :close-on-click-modal="true"
    :destroy-on-close="true"
    :append-to-body="true"
    @close="closeDlg">
    <div class="header"
      slot="header">
      <span>投资项目计划备案-调整</span>
      <div class="tip">
        <i class="el-icon-info"></i>
        温馨提示：调整备案将中止原有流程
      </div>
    </div>
    <div slot="body">
      <div class="header-form">
        <el-form ref="RecordsForm"
          :rules="recordsFormRules"
          :model="investProjectPlanFiling"
          inline
          label-width="100px">
          <el-form-item label="备案编号"
            prop="planNo">
            <el-select v-model="investProjectPlanFiling.planNo"
              :multiple="false"
              filterable
              remote
              placeholder="请输入关键词"
              :remote-method="featchProject"
              :loading="loadingsearch"
              @change="handleSelect">
              <el-option v-for="item in planNoOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"></el-option>
            </el-select>
            <!-- <el-autocomplete
              v-model="investProjectPlanFiling.planNo"
              :fetch-suggestions="featchProject"
              :disabled="mode=='add'?false:true"
              placeholder="请输入项目名称/项目编码"
              @select="handleSelect"
            ></el-autocomplete>-->
          </el-form-item>
          <el-form-item label="填报单位"
            prop="orgName">
            <el-input v-model="investProjectPlanFiling.orgName"
              disabled />
          </el-form-item>
          <el-form-item label="年份"
            prop="year">
            <el-date-picker v-model="investProjectPlanFiling.year"
              disabled
              type="year"
              value-format="yyyy"
              :clearable="true" />
          </el-form-item>
        </el-form>
      </div>

      <div class="plan-table-wrapper">
        <!-- <PlanTable
          ref="PlanTable"
          :loading="loading"
          :recordNumber="investProjectPlanFiling.recordNumber"
          :list="investProjectPlanFiling.filingDetailList"
          mode="edit"
        />-->
        <PlanTable ref="PlanTable"
          :list="investProjectPlanFiling.filingDetailList"
          :loading="loading"
          :recordNumber="investProjectPlanFiling.id"
          :mode="mode" />
      </div>

      <!-- <AccessoryList :list="oldAccessoryList"
        title="原附件列表"
        mode="download"
      class="m-20" />-->

      <!-- 审批流 -->
      <!-- <AccessoryList :list="accessoryList"
        title="附件列表"
        mode="upload"
      class="m-80" />-->
      <div style="margin-top:60px;"
        v-if="!loading">
        <AccessoryList v-model="investProjectPlanFiling.fileList"
          :mode="mode=='see'?'see':'upload'"
          dict="invest_plan_file_type"
          class="m-20" />
      </div>
    </div>
    <div slot="footer">
      <el-button v-if="mode!=='see'"
        @click="closeDlg">取消</el-button>
      <el-button v-else
        @click="closeDialog">关闭</el-button>
      <el-button v-if="mode!='see'"
        class="primary-buttom"
        type="primary"
        :loading="loading"
        @click="submitForm">提交</el-button>
    </div>
  </Dialog>
</template>
<script lang="ts">
import { Component, Emit, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import { Loading } from '@/decorators'
import { ElForm } from 'node_modules/element-ui/types/form'
import { createPlanIni, createProject, selectPlanFiling } from '@/api/projectInvestment'
import PlanTable from '../../components/PlanTable/PlanGrid.vue'
import Uploader from '../../components/Uploader/index.vue'
import { AgentProjectForm } from '../../components/ProjectForm/AgentProjectForm.vue'
import { CompanyForm } from '../../components/ProjectForm/CompanyProjectForm.vue'
import { GovForm } from '../../components/ProjectForm/GovProjectForm.vue'
import AuditFlow from '@/views/projectInvestment/components/AuditFlow.vue'
import AccessoryList, { Accessory } from '@/views/assets/components/TradeDialog/components/AccessoryFileList.vue'
import { Confirm } from '@/decorators/index'
import { investPLanInfo } from '@/api/projectInvestment'
@Component({
  components: {
    Dialog,
    Uploader,
    PlanTable,
    AuditFlow,
    AccessoryList
  }
})
export default class extends Vue {
  @Prop() private visible!: boolean
  @Prop() private title!: string
  @Prop({ default: () => ({}) }) private detail!: any
  @Prop({ default: 'see' }) private mode!: any

  private loading = false
  private fileList: any = []
  private tableList: {
    investProjectAgentDataExlVO: AgentProjectForm[]
    investProjectDataExlVO: CompanyForm[]
    investProjectGovDataExlVO: GovForm[]
  } = {
    investProjectAgentDataExlVO: [], // 代建
    investProjectDataExlVO: [], // 企业
    investProjectGovDataExlVO: [] // 政府
  }
  private loadingsearch = false
  private investProjectPlanFiling: any = {
    orgPath: '',
    id: '',
    fileList: [],
    year: String(new Date().getFullYear() - 1),
    planType: 2, //计划备案类型 1-正常 2-调整
    projectDataList: []
  }

  private recordsFormRules = {
    planNo: [{ required: true, trigger: 'change', message: '备案编号不能为空' }],
    orgPath: [{ required: true, trigger: 'blur', message: '填报单位' }],
    year: [{ required: true, trigger: 'blur', message: '年份不能为空' }]
  }
  private planNoOptions = []
  // 弹窗关闭
  @Emit('close')
  private onClose() {
    //
  }

  created() {
    if (this.mode == 'see') {
      // 查看
      // 查看获取远程数据
      this.investProjectPlanFiling = this.detail
      this.getdetail()
    } else if (this.mode == 'add') {
      // 新增
      // this.investProjectPlanFiling = Object.assign({}, this.detail)
      // let investProjectPlanFiling = this.investProjectPlanFiling
    }
  }
  private async getdetail() {
    this.loading = true
    try {
      let res = await investPLanInfo({
        id: this.investProjectPlanFiling.id
      })
      if (res.success) {
        this.investProjectPlanFiling = Object.assign(this.investProjectPlanFiling, res.data)
        // this.$forceUpdate()
        this.investProjectPlanFiling.fileList = Object.assign(this.investProjectPlanFiling.fileList, res.data.fileList)
        // this.$forceUpdate()
        this.fileList = [...this.investProjectPlanFiling.fileList]
        this.loading = false
      }
    } catch (e) {
      this.loading = false
    }
  }
  private async featchProject(queryString: string) {
    let res = await selectPlanFiling({ current: 1, planType: 1, size: 999, keyword: queryString })
    if (res.success) {
      let list = await res.data.map((item: any) => {
        return Object.assign(
          {
            label: item.year + '-' + item.planNo,
            value: item.year + '-' + item.planNo
          },
          item
        )
      })
      this.planNoOptions = list
      // await cb(list)
    }
  }

  private async handleSelect(val: any) {
    let row: any = this.planNoOptions.find((item: any) => {
      return item.label == val
    })
    this.investProjectPlanFiling = await Object.assign(this.investProjectPlanFiling, row)

    await this.getdetail()
    // this.$set(this.investProjectPlanFiling,"filingDetailList",data.filingDetailList)
    // this.$set(this.investProjectPlanFiling.fileList,0,data.fileList[1])
    // this.investProjectPlanFiling = Object.assign({}, data)
  }

  @Loading('loading')
  private async submitForm() {
    // 基础信息校验
    ;(this.$refs.RecordsForm as ElForm).validate((valid: boolean) => {
      if (!valid) return

      // 附件校验
      // if (!this.validateFiles()) return
      let planTable = this.$refs.PlanTable as any
      this.investProjectPlanFiling.projectDataList = planTable.tableList
      // 计划表校验
      this.onSave()
      // planTable.validate().then(() => {
      //   let tableList = planTable.getTableList()
      //   // 如果都为空
      //   this.tableList = tableList
      // })
    })
  }

  // 保存
  @Confirm({
    title: '提示',
    content: `是否确认提交？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private async onSave() {
    this.investProjectPlanFiling.projectDataList.forEach((item: any) => {
      if (this.investProjectPlanFiling.planType == 1) {
        //  新增提交时修改id为空
        item.id = ''
      }
    })
    this.investProjectPlanFiling.planType = 2
    this.investProjectPlanFiling.id = ''
    let res = await createProject(this.investProjectPlanFiling)

    if (res.success) {
      this.$message.success(res.msg || '保存成功!')
      this.$emit('update:visible', false)
      this.$emit('success')
    }
  }

  // 附件校验
  private validateFiles(): boolean {
    return true
  }

  // 关闭弹窗
  @Confirm({
    title: '提示',
    content: `是否关闭？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private closeDlg() {
    this.$emit('update:visible', false)
    // this.onClose()
  }
  private closeDialog() {
    this.$emit('update:visible', false)
    // this.onClose()
  }
}
</script>

<style lang="scss" scoped>
.header {
  display: flex !important;
  align-items: center;
  .tip {
    font-weight: normal;
    font-size: 12px;
    color: #e6a23c;
    margin-left: 26px;
  }
}
.uploader-list {
  display: flex;
  flex-wrap: wrap;
  ::v-deep .uploader-wrapper {
    margin: 12px 24px;
  }
}

::v-deep .el-date-editor.el-input {
  width: 160px !important;
}

::v-deep .el-autocomplete .el-input {
  width: 250px;
}
.header-form {
  display: flex;
  width: 100%;
}
</style>

