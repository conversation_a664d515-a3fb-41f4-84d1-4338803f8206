/** 重大项目数量 */

<template>
  <div class="wrapper">
    <div id="ProjectNumber" />
    <img ref="img"
      src="../../../images/bar.png"
      alt=""
      srcset="">
    <img ref="img2"
      src="../../../images/bar2.png"
      alt=""
      srcset="">
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { ProjectNumberData } from '@/views/main/cockpitSecondary/baseData'
import { capitalComponent, capitalProfile } from '@/api/cockpit'
import * as echarts from 'echarts'
import { deepClone } from '@/utils'
import CommonModuleWrapper from '@/views/main/cockpitSecondary/components/CommonModuleWrapper.vue'

type EChartsOption = echarts.EChartsOption

type dataType = {
  value: number
  name: string
}

@Component({
  components: {
    CommonModuleWrapper
  }
})
export default class extends Vue {
  private timer: any
  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}
  private seriesData: any[] = [0, 0, 0, 0]
  private echartsDatas: any[] = []
  private realData: any = {
    ALL: [20, 16, 8, 27],
    CT: [6, 5, 3, 9],
    KT: [5, 5, 2, 7],
    SW: [5, 1, 1, 4],
    JT: [2, 1, 1, 3],
    GD: [1, 2, 0, 2],
    SF: [1, 2, 1, 2]
  }

  get currentTabCode(): any {
    return this.$route.query ? this.$route.query.id || '' : ''
  }

  // 筛选数据
  private filterData() {
    this.seriesData = this.realData[this.currentTabCode]
    this.initEcharts()
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('ProjectNumber') as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    this.filterData()
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await capitalProfile()
    if (!Array.isArray(data) || !data.length) return
    this.echartsDatas = data
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 初始化数据
  private initEcharts() {
    let textSize = 16
    let series = this.seriesData
    let yeas = ~~this.getMomentTime()
    let imgDom = this.$refs['img'] as HTMLImageElement
    let imgDom2 = this.$refs['img2'] as HTMLImageElement
    this.option = {
      color: [
        {
          image: imgDom2, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串
          repeat: 'repeat'
        }
      ],
      title: {
        text: '重大决策事项',
        top: 0,
        textStyle: {
          color: '#fff',
          fontSize: 26
        }
      },
      legend: {
        show: true
      },
      axisPointer: {
        show: false
      },
      grid: {
        left: '0%',
        right: '0%',
        top: '30%',
        bottom: '0%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 103, 177, 1)',
        borderColor: 'rgba(0, 103, 177, 1)',
        textStyle: {
          color: '#fff',
          fontSize: textSize
        }
      },
      xAxis: {
        type: 'category',

        axisLabel: {
          fontSize: textSize * 1,
          fontWeight: 'bold',
          hideOverlap: false,
          interval: 0
        },
        axisLine: {
          lineStyle: {
            color: '#5db0ea'
          }
        },
        data: ['年度投资', '大宗货物', '重大工程', '服务项目']
      },
      yAxis: {
        show: false,
        type: 'value'
      },
      series: [
        {
          selectedMode: 'single',
          label: {
            show: true,
            position: 'top',
            color: '#5db0ea',
            fontSize: textSize * 1.8
          },
          itemStyle: {
            borderRadius: [textSize * 0.3, textSize * 0.3, 0, 0],
            shadowColor: '#258AFF66',
            shadowBlur: 30
          },
          emphasis: {
            itemStyle: {
              color: {
                image: imgDom, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串
                repeat: 'repeat'
              },
              shadowColor: '#EE9D0066',
              shadowBlur: 30
            },
            label: {
              fontSize: textSize * 2.4,
              color: '#EE9D00'
            }
          },
          select: {
            itemStyle: {
              color: {
                image: imgDom, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串
                repeat: 'repeat'
              },
              shadowColor: '#EE9D0066',
              shadowBlur: 30
            },
            label: {
              fontSize: textSize * 2.4,
              color: '#EE9D00'
            }
          },
          showBackground: true,
          backgroundStyle: {
            color: '#081C5866',
            borderRadius: [textSize * 0.3, textSize * 0.3, 0, 0]
          },
          barWidth: textSize * 2.5,
          data: series,
          type: 'bar'
        }
      ]
    }

    this.myChart && this.myChart.setOption && this.myChart && this.myChart.setOption(this.option)
    this.change()
  }

  private change() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    this.timer = setInterval(() => {
      let index = Math.max(Math.floor(Math.random() * 4), 0)

      this.myChart &&
        this.myChart.dispatchAction({
          type: 'select',
          seriesIndex: 0,
          dataIndex: index
        })
    }, 5000)
  }

  destroyed() {
    clearInterval(this.timer)
  }
}
</script>

<style scoped lang="scss">
#ProjectNumber {
  width: 100%;
  height: 100%;
}

.wrapper {
  position: relative;
  width: 320px;
  height: 240px;
  overflow: hidden;
}

img {
  position: absolute;
  top: -100px;
  left: -100px;
}
</style>