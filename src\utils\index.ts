export * from './replaceOss'
/*
 * 常用的工具函数
 */

/* 深拷贝 */
export const deepClone = (source: any) => {
  if (!source || typeof source !== 'object') {
    return source
  }
  const targetObj: any = source.constructor === Array ? [] : {}
  for (const keys in source) {
    if (Object.prototype.hasOwnProperty.call(source, keys)) {
      if (source[keys] && typeof source[keys] === 'object') {
        targetObj[keys] = source[keys].constructor === Array ? [] : {}
        targetObj[keys] = deepClone(source[keys])
      } else {
        targetObj[keys] = source[keys]
      }
    }
  }
  return targetObj
}

/**
 * @description 判断数据类型
 * @param val 需要判断类型的数据
 * @returns {string} 数据类型
 */
export const isType = (val: any): string => {
  if (val === null) return 'null'
  if (typeof val !== 'object') return typeof val
  else
    return Object.prototype.toString
      .call(val)
      .slice(8, -1)
      .toLocaleLowerCase()
}

/**
 * @desc 对象深度面试
 * @param {object} obj1 对象1
 * @param {object} obj2 对象2
 * @return {object} 返回一个新对象
 */
export const deepMerge = (obj1: Record<string, any>, obj2: Record<string, any>) => {
  if (isType(obj1) !== 'object' || isType(obj2) !== 'object') {
    return deepClone(obj2)
  }

  const resultObj: Record<string, any> = deepClone(obj1)

  for (let prop in obj2) {
    let val1 = obj1[prop]
    let val2 = obj2[prop]
    let type1 = isType(val1)
    let type2 = isType(val2)

    if (val1) {
      // 如果val2是对象
      if (type2 === 'object') {
        if (type2 === 'object') {
          // 如果都是对象就递归
          resultObj[prop] = deepMerge(val1, val2)
        } else {
          resultObj[prop] = deepClone(val2)
        }
        // 如果val2是数组
      } else if (type2 === 'array') {
        if (type2 === 'array') {
          //  如果都是数组就逐个对比
          let tempArr: any = []
          for (let index in val2) {
            tempArr[index] = deepMerge(val1[index], val2[index])
          }
          resultObj[prop] = tempArr
        } else {
          resultObj[prop] = deepClone(val2)
        }
      } else {
        resultObj[prop] = deepClone(val2)
      }
    } else {
      resultObj[prop] = deepClone(val2)
    }
  }

  return resultObj
}

/* 防抖函数 */
export const debounce = (func: Function, wait = 100, immediate?: boolean) => {
  let timeout: any, args: null, context: null, timestamp: number, result: boolean
  const later = function () {
    // 防抖函数，判断时间的间隔
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔last小于设定时间间隔wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function (this: any, ...args: any) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }
    return result
  }
}

// 进入全屏模式
export function windowFullScreen(dom: any) {
  let element: any = dom.documentElement || document.documentElement

  if (element) {
    if (element.requestFullscreen) {
      element.requestFullscreen()
    } else if (element.mozRequestFullScreen) {
      element.mozRequestFullScreen()
    } else if (element.webkitRequestFullScreen) {
      element.webkitRequestFullScreen()
    } else if (element.msRequestFullscreen) {
      element.msRequestFullscreen()
    }
  }
}

// 退出全屏模式
export function windowExitFullScreen() {
  let windowDom = document as any

  if (windowDom.exitFullscreen) {
    windowDom.exitFullscreen()
  } else if (windowDom.msExitFullscreen) {
    windowDom.msExitFullscreen()
  } else if (windowDom.mozCancelFullScreen) {
    windowDom.mozCancelFullScreen()
  } else if (windowDom.webkitCancelFullScreen) {
    windowDom.webkitCancelFullScreen()
  }
}

// 判断窗口是否处于全屏模式
export function isWindowFull() {
  let dom = document as any
  let isFull =
    dom.mozFullScreen ||
    dom.fullScreen ||
    dom.webkitIsFullScreen ||
    dom.webkitRequestFullScreen ||
    dom.mozRequestFullScreen ||
    dom.msFullscreenEnabled

  if (isFull === undefined) isFull = false
  return isFull
}

// 读取全局css变量
export function getGlobalCss(cssName: string) {
  return getComputedStyle(document.documentElement).getPropertyValue(cssName)
}

// 微软的在线预览office附件
export function viewOffice(src: string) {
  window.open(`https://view.officeapps.live.com/op/view.aspx?src=${src}`)
}

// 万元转化成 亿元
export function bigNumberFormat(value: number) {
  let num = 0

  if (!+value) {
    num = 0
  } else {
    num = Math.round((value / 10000) * 100) / 100
  }

  return {
    value: num,
    unit: '亿元'
  }
}

// 解析url
export function queryString(locationURL: any) {
  if (locationURL.split('?').length <= 1) {
    return {}
  }
  let params = locationURL.split('?')[1]
  let param = params.split('&')
  let obj: any = {}
  for (let i = 0; i < param.length; i++) {
    let paramsA = param[i].split('=')
    let key = paramsA[0]
    let value = paramsA[1]
    obj[key] = value
  }
  return obj
}

// 动态插入css
export const loadStyle = (url: string) => {
  const link = document.createElement('link')
  link.type = 'text/css'
  link.rel = 'stylesheet'
  link.href = url
  const head = document.getElementsByTagName('head')[0]
  head.appendChild(link)
}


/**
 * 下载excel
 * @param {blob} fileArrayBuffer 文件流
 * @param {String} filename 文件名称
 */
export const downloadXls = (fileArrayBuffer: any, filename: string) => {
  let data = new Blob([fileArrayBuffer], { type: 'application/vnd.ms-excel,charset=utf-8' })
  let windows = window as any

  if (typeof windows.chrome !== 'undefined') {
    // Chrome
    let link = document.createElement('a');
    link.href = window.URL.createObjectURL(data);
    link.download = filename;
    link.click();
  } else if (typeof windows.navigator.msSaveBlob !== 'undefined') {
    // IE
    let blob = new Blob([data], { type: 'application/force-download' });
    windows.navigator.msSaveBlob(blob, filename);
  } else {
    // Firefox
    let file = new File([data], filename, { type: 'application/force-download' });
    windows.open(URL.createObjectURL(file));
  }
}


// 如果用 iframe 嵌入 admin 模块的页面，需要获取域名
export const adminRouterUrl = (url: string) => {
  let env = process.env
  let baseAdminUrl = ''

  if (env.NODE_ENV === 'development') {
    // 开发环境
    console.log('开发环境')
    baseAdminUrl = `http://localhost:1888/#`
  } else if (env.NODE_ENV === 'production') {
    if (window.location.origin.includes('szjg.gzw.jinhua.gov.cn')) {
      // 生产环境1
      console.log('生产环境1')
      baseAdminUrl = `https://szjg.gzw.jinhua.gov.cn:6443/#`
    } else if (window.location.origin.includes('*************')) {
      // 生产环境2
      console.log('生产环境2')
      baseAdminUrl = `http://*************:9990/#`
    } else {
      // 生产环境3
      console.log('生产环境3')
      baseAdminUrl = `//dpm1-jh-fht-admin.mdguanjia.com/#`
    }
  }

  return `${baseAdminUrl}${url}`
}



// 转大地坐标系（传前两个参数表示从百度坐标转，传后两个参数表示从高德坐标转）
export const coordinateTransformation: any = (
  bdLon: number | undefined,
  bdLat: number | undefined,
  marsLon: number | undefined,
  marsLat: number | undefined
) => {
  const marsPoint = { lon: 0, lat: 0 };
  if (bdLon && bdLat) {
    const xPi = (3.141592653589793 * 3000.0) / 180.0;
    //百度坐标转成火星坐标
    const x = bdLon - 0.0065;
    const y = bdLat - 0.006;
    const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * xPi);
    const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * xPi);
    marsPoint.lon = z * Math.cos(theta);
    marsPoint.lat = z * Math.sin(theta);
  } else if (marsLon && marsLat) {
    marsPoint.lon = marsLon;
    marsPoint.lat = marsLat;
  } else {
    console.error('coordinateTransformation方法传参错误！')
    return { lng: undefined, lat: undefined };
  }

  function transformLon(x: number, y: number) {
    const PI = 3.141592653589793;
    let ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
    ret += ((20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0) / 3.0;
    ret += ((20.0 * Math.sin(x * PI) + 40.0 * Math.sin((x / 3.0) * PI)) * 2.0) / 3.0;
    ret += ((150.0 * Math.sin((x / 12.0) * PI) + 300.0 * Math.sin((x / 30.0) * PI)) * 2.0) / 3.0;
    return ret;
  }

  function transformLat(x: number, y: number) {
    const PI = 3.141592653589793;
    let ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
    ret += ((20.0 * Math.sin(6.0 * x * PI) + 20.0 * Math.sin(2.0 * x * PI)) * 2.0) / 3.0;
    ret += ((20.0 * Math.sin(y * PI) + 40.0 * Math.sin((y / 3.0) * PI)) * 2.0) / 3.0;
    ret += ((160.0 * Math.sin((y / 12.0) * PI) + 320 * Math.sin((y * PI) / 30.0)) * 2.0) / 3.0;
    return ret;
  }

  function delta(lon: number, lat: number) {
    const PI = 3.141592653589793;
    const a = 6378245.0;
    const ee = 0.006693421622965943;
    let dLon = transformLon(lon - 105.0, lat - 35.0);
    let dLat = transformLat(lon - 105.0, lat - 35.0);
    const radLat = (lat / 180.0) * PI;
    let magic = Math.sin(radLat);
    magic = 1 - ee * magic * magic;
    const sqrtMagic = Math.sqrt(magic);
    dLon = (dLon * 180.0) / ((a / sqrtMagic) * Math.cos(radLat) * PI);
    dLat = (dLat * 180.0) / (((a * (1 - ee)) / (magic * sqrtMagic)) * PI);
    return {
      lon: dLon,
      lat: dLat,
    };
  }

  //把火星坐标GCJ02转地球坐标系WGS84
  const gcjLon = marsPoint.lon;
  const gcjLat = marsPoint.lat;
  const d = delta(gcjLon, gcjLat);

  return {
    lng: gcjLon - d.lon,
    lat: gcjLat - d.lat,
  };
};

