<template>
  <div>
    <!-- 查看 -->
    <TradeDetailDialog v-if="tradeDetailDialogVisible"
      :initData="currentRow"
      :assetId="DetailAssetID"
      :visible.sync="tradeDetailDialogVisible" />
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { DetailAsset } from '@/api/assets'
import TradeDetailDialog from '@/views/assets/components/TradeDialog/TradeDetailDialog.vue'
import { queryString } from '@/utils/index'
import { BusinessModule } from '@/store/modules/businessDict'

@Component({
  components: { TradeDetailDialog }
})
export default class extends Vue {
  private currentRow = {}
  private tradeDetailDialogVisible = true
  private DetailAssetID = ''
  private DetailModule = ''

  // 数据初始化
  private async created() {
    let queryStringData = queryString(window.location.href)

    this.DetailAssetID = queryStringData.id
    this.DetailModule = queryStringData.module
    this.getBusinessModule()
  }

  // 获取对应字典数据
  private async getBusinessModule() {
    await BusinessModule.getDictLoad(this.DetailModule)
  }
}
</script>

<style lang="scss" scoped>
</style>