// 大额资金
<template>
  <el-container class="container"
    direction="vertical">
    <search-bar :items="searchItems"
      @onSearch="handleSearch">
      <div>
        <el-button type="primary"
          @click="AddForm">新增</el-button>
      </div>
    </search-bar>
    <Grid @row-click="loaddetail"
      :columns="cols"
      :remote-url="remoteUrl"
      ref="grid"
      :show-pagination="true"
      :search-params="searchParams">
      <template slot="documentNo"
        slot-scope="scope">
        <span>{{ scope.row.documentNo }}</span>
      </template>
      <template slot="state"
        slot-scope="scope">
        <span v-if="scope.row.state == 1">审批中</span>
        <span v-else-if="scope.row.state == 2">审批通过</span>
        <span v-else-if="scope.row.state == 3"
          style="color: #df7575">审批不通过</span>
      </template>
      <!-- 决策机构 -->
      <!-- <div v-for="(item,index) in cols" :key="index" slot="item.slotName" slot-scope="scope">
            <span v-if="'filterOptions' in item">{{ scope.row[item.prop] }}</span>
          </div> -->
      <template slot="issuanceCurrency"
        slot-scope="scope">
        <span v-if="scope.row.state == 1">人民币</span>
        <span v-else-if="scope.row.state == 2">美元</span>
        <span v-else-if="scope.row.state == 3">其他</span>
      </template>
      <template slot="operatingBar"
        slot-scope="scope">
        <div class="operatingBar_content">
          <el-button type="text"
            @click.stop="loaddetail(scope.row)">查看</el-button>
          <el-button type="text"
            @click.stop="loofinfo(scope.row)">编辑</el-button>

          <!-- <el-button type="text" slot="reference">删除</el-button> -->
        </div>
      </template>
    </Grid>
    <Dialog-large :mode="DialogMode"
      :Diaformdata="Diaformdata"
      v-if="showDialogAdd"
      :visible.sync="showDialogAdd"
      @changshowDialogAdd="changeShowDialogAdd" />
    <!-- 查看详情 -->
    <DetailAsset :visible="showDetailAsset"
      :list="Diadetaillist"
      @changeShowDetail="changeShowDetail"
      :fileList="pageData.attachmentFileDTOList"
      dict="financial_largeLending_attach"></DetailAsset>
  </el-container>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import Grid from '@/components/Grid/index.vue'
import DialogLarge from '../components/Dialoglarge.vue'
import { DataDebissuance } from '../date'
import { equityRelationship } from '../filterOptions'
import DetailAsset from '../components/DetailAsset.vue'
import searchBar from '@/components/SearchBar/index.vue'
import { priceUnitList } from '../../assets/filterOptions'
import { deepClone } from '../../../utils'

@Component({
  name: 'LargeFunds',
  components: {
    Grid,
    DialogLarge,
    DetailAsset,
    searchBar
  }
})
export default class Container extends Vue {
  private pageData = {}
  private showDialogAdd = false
  private searchParams = {} //表格搜索条件
  private DialogMode = ''
  private remoteUrl = '/fht-monitor/fin/largeAmountCapitalLending/page'
  private showDetailAsset = false //显示详情弹窗
  private Diadetaillist: object[] = [] //详情列表
  private Diaformdata = {} //详情列表
  private data: object[] = DataDebissuance
  private searchItems = [
    {
      type: 'text',
      key: 'keyword',
      placeholder: '请输入关键字进行查询',
      width: '200px'
    }
  ]
  private cols = [
    {
      label: '单据编号',
      prop: 'documentNo',
      slotName: 'documentNo',
      minWidth: 150
    },
    {
      label: '公司名称',
      prop: 'companyName',
      minWidth: 90
    },
    {
      label: '公司地址',
      prop: 'companyAddress',
      minWidth: 110
    },
    {
      label: '法定代表人',
      prop: 'legalRepresentative',
      minWidth: 110
    },
    {
      label: '所有者权益(元)',
      prop: 'ownersEquity',
      minWidth: 110
    },
    {
      label: '董事会决议文号',
      prop: 'boardResolutionNum',
      minWidth: 110
    },
    {
      label: '债务人',
      prop: 'debtor',
      minWidth: 100
    },
    {
      label: '与债务人关系',
      prop: 'equityRelationshipDesc',
      minWidth: 100
    },
    {
      label: '持股比例',
      prop: 'shareholdingRatio',
      minWidth: 80
    },
    {
      label: '出借资金额度',
      prop: 'fundsAmount',
      minWidth: 100
    },
    {
      label: '出借资金期限',
      prop: 'lendingPeriod',
      minWidth: 100
    },
    {
      label: '出借资金利率',
      prop: 'lendingRate',
      minWidth: 80
    },
    {
      label: '出借资金项目概况',
      prop: 'projectDesc',
      minWidth: 120
    },
    {
      label: '累计出借资金',
      prop: 'accumulatedFundsAmount',
      minWidth: 100
    },
    {
      label: '备注',
      prop: 'remark',
      minWidth: 80
    },
    {
      label: '创建人',
      prop: 'createUserName',
      minWidth: 80
    },
    {
      label: '创建日期',
      prop: 'createTime',
      minWidth: 80
    },
    {
      label: '操作',
      prop: 'operatingBar',
      slotName: 'operatingBar',
      fixed: 'right',
      minWidth: 80
    }
  ]
  // search搜索
  private handleSearch(condition: any) {
    this.searchParams = condition
    let grid: any = this.$refs.grid
    grid.refresh()
  }
  // 新增按钮
  private AddForm() {
    this.DialogMode = 'add'
    this.Diaformdata = {
      id: ''
    }
    this.showDialogAdd = true
  }
  // 改变显示隐藏
  private changeShowDialogAdd(state: boolean) {
    this.showDialogAdd = state
    if (state == false) {
      ;(this.$refs.grid as Grid).refresh(true)
    }
  }
  // 改变详情显示隐藏
  private changeShowDetail(state: boolean) {
    this.showDetailAsset = state
  }
  //编辑
  private loofinfo(row: any) {
    this.DialogMode = 'edit'
    this.Diaformdata = row

    this.showDialogAdd = true
  }
  //查看
  private loaddetail(row: any) {
    this.Diaformdata = deepClone(row)
    this.DialogMode = 'see'
    this.showDialogAdd = true
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
}
</style>
