/** 指数总览 样式1 */

<template>
  <div :class="['index-overview']">
    <div v-for="(item, index) in indexList"
      :key="index"
      :style="{
          width: `${itemWidth || 340}px`,
          height: `${itemHeight || 180}px`
        }"
      @click="onItemClick(index, item)"
      :class="['index-overview__item', currentIndex === index && 'index-overview__item--checked']">
      <div class="item__top"
        :style="{fontSize: `${fontSize}px` }">
        <CountTo :startVal='0'
          :decimals="item.decimals !== undefined ? decimals : 2"
          :endVal="+item[realPropName['value']]"
          :duration='2000' />
        <span style="margin-left: 6px;"
          :style="{fontSize: `${fontSize * 0.6}px` }">{{ item[realPropName['unit']] }}</span>
      </div>
      <div class="item__bottom">
        <div :style="{fontSize: `${fontSize * 0.45}px` }">{{ item[realPropName['label']] }}</div>
        <div
          :class="['item__percent', +item[realPropName['percent']] > 0 && 'item__percent--up', +item[realPropName['percent']] < 0 && 'item__percent--down']"
          :style="{fontSize: `${fontSize * 0.8}px` }">
          <img :style="{width: `${fontSize * 0.5}px` }"
            class="icon"
            v-if="+item[realPropName['percent']] > 0"
            src="../images/thows.png" />
          <img :style="{width: `${fontSize * 0.5}px` }"
            class="icon"
            v-if="+item[realPropName['percent']] < 0"
            src="../images/thowx.png" />
          <span>{{ item[realPropName['percent']] }}%</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator'
import CountTo from 'vue-count-to'
import { bigNumberFormat } from '@/utils'

export interface IndexItem {
  label: string
  value: string | number
  percent: string | number
  unit: string
}

@Component({
  components: {
    CountTo
  }
})
export default class extends Vue {
  @Prop({ default: () => [] }) indexList!: IndexItem[]
  @Prop({ default: () => ({}) }) propMap!: IndexItem
  @Prop({ default: 50 }) private fontSize!: number
  @Prop() private itemWidth!: number
  @Prop() private itemHeight!: number
  @Prop() private currentIndex!: number
  @Prop() private decimals!: number

  @Watch('indexList', { deep: true })
  private onIndexListChange() {
    this.realList = [...this.indexList]
    this.realList.forEach((item) => {
      this.bigNumberFormat(item)
    })
  }
  private realPropName: IndexItem = {
    label: 'label',
    value: 'value',
    percent: 'percent',
    unit: 'unit'
  }

  private realList: any[] = []

  created() {
    this.realPropName = Object.assign(this.realPropName, this.propMap)
    this.realList = [...this.indexList]
    this.realList.forEach((item) => {
      !item.noFormat && this.bigNumberFormat(item)
    })
  }

  // 万元转化成 亿元、万亿元
  bigNumberFormat(item: any) {
    let k = 10000
    let value = +item[this.realPropName['value']]
    if (value < k) {
      return
    }

    let newValue = Math.abs(value) * k
    let sizes = ['', '万', '亿元', '万亿元']
    let i

    if (newValue < k) {
      item.value = newValue + ''
      item.unit = ''
    } else {
      i = Math.floor(Math.log(newValue) / Math.log(k))
      item.value = (newValue / Math.pow(k, i)).toFixed(2)
      item.unit = sizes[i]
    }

    let zhValue = String(item.value).split('.')
    if (zhValue && zhValue[1] === '00') item.value = zhValue[0]
    if (+value < 0) item.value = `-${item.value}`

    if (String(newValue).length < 9) item.value = String((+item.value / 10000).toFixed(2))

    if (!item.value) item.value = '0'
    if (!item.unit) item.unit = ''
  }

  private onItemClick(index: any, item: IndexItem) {
    this.$emit('itemClick', index, item)
  }
}
</script>

<style scoped lang="scss">
.index-overview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;

  &__item {
    position: relative;
    width: 380px;
    height: 150px;
    background: url('../images/iconbg.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    padding-bottom: 20px;
    color: #fff;
    cursor: pointer;
    &--checked,
    &:hover {
      background: url('../images/iconbg2.png') no-repeat;
      background-size: 100% 100%;
      .item__bottom {
        color: #fff;
      }
    }
    .item {
      &__top,
      &__bottom {
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
      }

      &__top {
        height: 60%;
        font-size: 72px;
        padding-top: 32px;
        font-family: 'PangMenZhengDao';
        display: flex;
        align-items: flex-end;
      }
      &__bottom {
        height: 40%;
        font-size: 32px;
        line-height: 30px;
        white-space: nowrap;
        padding-top: 10px;
      }
      &__percent {
        font-family: 'digital-7';
        margin-left: 8px;
        font-size: 32px;
        display: flex;
        align-items: center;
        img {
          width: 14px;
          margin-right: 3px;
        }
        &--up {
          color: #ec342f;
        }
        &--down {
          color: #00bc94;
        }
      }
    }
  }
}
</style>