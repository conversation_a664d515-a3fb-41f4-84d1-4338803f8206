/**
  组件描述: 年度计划投资情况
*/
<template>
  <section v-loading="loading"
    class="planned-investment">
    <DoubleYChart chartId="PlannedInvestment"
      :seriesData="seriesData"
      :individuationOptions="individuationOptions"
      :xData="yearList" />
  </section>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import DoubleYChart from '@/views/main/cockpitSecondary/charts/DoubleYChart.vue'
import { EChartsOption } from 'echarts'
import { getPlanCompletedYear, ProjectProgressDataItem } from '@/api/cockpit'
import { Loading } from '@/decorators'

@Component({
  components: {
    DoubleYChart
  }
})
export default class extends Vue {
  @Prop({ default: '0' }) private activeTabs!: string

  @Watch('activeTabs', { immediate: true })
  private activeTabsChange() {
    this.params.orgCode = this.activeTabs
    this.fetchData()
  }

  private loading = false

  // 参数
  private params: any = {
    orgCode: '0',
    year: String(new Date().getFullYear()),
    projectCategory: ''
  }

  private yearList: string[] = []

  private seriesData: {
    data1: number[]
    data2: number[]
  } = {
    data1: [],
    data2: []
  }

  // 个性化配置
  get individuationOptions(): EChartsOption {
    return {
      color: [
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: '#C538F522' // 0% 处的颜色
            },
            {
              offset: 0.3,
              color: '#C538F599' // 30% 处的颜色
            },
            {
              offset: 1,
              color: '#C538F5' // 100% 处的颜色
            }
          ]
        },
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: '#47EFFF22' // 0% 处的颜色
            },
            {
              offset: 0.3,
              color: '#47EFFF99' // 30% 处的颜色
            },
            {
              offset: 1,
              color: '#47EFFF' // 100% 处的颜色
            }
          ]
        }
      ],
      yAxis: [
        {
          name: '金额（亿元）'
        },
        {
          nameTextStyle: {
            fontSize: 0
          }
        }
      ],
      legend: {
        show: false
      },
      tooltip: {
        // 取消 wrapper
        backgroundColor: '#00000000',
        shadowColor: '#00000000',
        borderWidth: 0,
        formatter: (params: any) => {
          let { name, dataIndex } = params
          let value = this.seriesData['data1'][dataIndex] || 0
          let rate = this.seriesData['data2'][dataIndex] || 0
          return `
            <div style="border-radius:8px;border:2px solid #0C3EB6; padding: 18px 12px; display: flex;flex-direction: column; background: linear-gradient(0deg, rgba(0, 103, 177, 1), rgba(0, 103, 177, 1));">
              <span>${name}年</span>
              <span>总投资额 : <span>${value}亿元</span></span>
              </span>
            </div>
          `
        }
      },
      series: [
        {},
        {
          symbol: 'none',
          lineStyle: {
            shadowColor: 'rgba(0, 0, 0, 0.5)',
            shadowBlur: 10
          }
        }
      ]
    }
  }

  mounted() {
    this.listenerDate()
    this.$bus.$on('plansInvestmentTypeChange', (code: string) => {
      this.params.projectCategory = code.trim() || ''
      this.fetchData()
    })
  }

  // 监听时间变化
  private listenerDate() {
    this.$bus.$on('BusYearTimeChange', (year: string) => {
      this.params.year = year
      this.fetchData()
    })

    this.$bus.$on('BusMoonTimeChange', (month: string) => {
      this.params.month = month
      this.fetchData()
    })
  }

  // 获取数据
  @Loading('loading')
  private async fetchData() {
    let res = await getPlanCompletedYear(this.params)
    if (res.success) {
      this.processingData(res.data)
    }
  }

  // 处理数据
  private processingData(data: ProjectProgressDataItem[]) {
    // 年份从大到小
    data = data.sort((a, b) => +a.year - +b.year)
    this.yearList = data.map((item) => item.year)
    this.seriesData.data1 = data.map((item) => +item.indicatorValue)
    // this.seriesData.data2 = data.map((item) => +item.indicatorRate)
  }
}
</script>


<style scoped lang="scss">
.planned-investment {
  width: 100%;
  height: 100%;
}
</style>