<template>
<el-container>
  <div v-show="assetOccupancyTypeVO!=[]" id="compositeRightIndex"
       style="height: 170px; width: 180px" ></div>
       <div v-show="assetOccupancyTypeVO==[]">暂无数据</div>
       </el-container>
</template>
<script lang="ts">
import { Component, Prop, Vue,Watch } from 'vue-property-decorator'
import * as echarts from 'echarts'
@Component({
  name: 'Container',
  components: {}
})

export default class Container extends Vue {
  
  @Prop() private assetOccupancyTypeVO: any
  @Watch('assetOccupancyTypeVO', { deep: true })
  private changidEvent() {

    this.assetOccupancyTypeVO.forEach((item: { num: number; occupancyType: string; status: string },index:number) => {
          this.chartData[index]=item.num
          this.chartName[index]=item.occupancyType
    })
    
    this.initChart()
  }
  private chartDom: HTMLElement | null = null
  private myChart: any = null

  private chartData = [0]
  private chartName = ['无',]
  // private chartData = [18, 28, 15]
  // private chartName = ['教育行业', '餐饮行业', '金融行业']

  private  mounted() {
    // await this.initChart()
  }

  private initChart() {
    this.chartDom = document.getElementById('compositeRightIndex') as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)
    let option: any = ''

    option = {


      backgroundColor: '#fff',
      grid: {
        left: '2%',
        right: '10%',
        bottom: '10%',
        top: '0%',
        containLabel: true
      },
      xAxis: [
        {
          show: false
        },
        {
          show: false
        }
      ],
      yAxis: {
        type: 'category',
        inverse: true,
        show: false
      },

      series: [
        //亮色条 百分比
        {
          show: true,
          type: 'bar',
          barGap: '-100%',
          barWidth: '20%',
          z: 2,
          itemStyle: {
            normal: {
              color: '#DF7575'
            }
          },
          label: {
            normal: {
              show: true,
              textStyle: {
                color: '#303133',
                fontSize: 12,
                fontWeight: 'bold'
              },
              position: 'right',

              formatter: this.chartName
            }
          },
          data: this.chartData
        },
        //年份
        {
          show: true,
          type: 'bar',
          xAxisIndex: 1, //代表使用第二个X轴刻度
          barGap: '-100%',
          barWidth: '10%',
          itemStyle: {
            normal: {
              barBorderRadius: 200,
              color: 'transparent'
            }
          },
          label: {
            normal: {
              show: true,
              position: [0, '-20'],
              textStyle: {
                fontSize: 12,
                color: '#909399'
              },
              formatter: (data: any) => {
                return this.chartName[data.dataIndex]
              }
            }
          },
          data: this.chartData
        }
      ]
    }
   
    option && this.myChart.setOption(option)&& this.myChart.resize()
  }
}
</script>

<style lang="scss" scoped>
</style>
