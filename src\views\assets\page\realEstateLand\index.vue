     <!--  房产土地信息-->
<template>
  <el-container class="content"
    direction="vertical">
    <Search-bar :items="searchItems"
      @onSearch="handleSearch">
      <!-- <el-button type="primary">导入</el-button> -->
      <el-button type="primary">导出</el-button>
      <el-button @click="gridRefresh"
        type="info">刷新</el-button>
      <!-- <el-button>删除</el-button> -->
    </Search-bar>
    <Grid show-index="true"
      show-index-fixed="left"
      @select="selectionRow"
      :overflow-tooltip="true"
      show-selection="true"
      :columns="cols"
      @row-click="loaddetail"
      ref="grid"
      :remote-url="remoteUrl"
      :show-pagination="true"
      :search-params="searchParams">
      <template slot="operatingBar"
        slot-scope="scope">
        <div class="operatingBar_content">
          <el-button type="text"
            @click="loaddetail(scope.row)">查看</el-button>
        </div>
      </template>
    </Grid>
    <!--  -->
    <Detail :list="Diadetaillist"
      @changeShowDetail="changeShowDialogDetail"
      v-if="showDialogDetail"
      :visible="showDialogDetail"
      :data="detailRow"
      @showUpdaload="showUpdaload" />
    <Uploader v-model="accessoryList"
      :title="accessoryTitle"
      :uploadable="false"
      :is-private="false"
      :show-cover="false"
      :visible.sync="showUploader" />
  </el-container>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import Grid from '@/components/Grid/index.vue'
import Uploader from '@/components/Uploader/index.vue'

// import { DataDebissuance } from '../date'
import SearchBar from '@/components/SearchBar/index.vue'
import Detail from './Detail.vue'
import { Confirm } from '@/decorators/index'
import { deleteAssetLedger } from '@/api/assets'
@Component({
  components: {
    Grid,
    SearchBar,
    Detail,
    Uploader
  }
})
export default class Container extends Vue {
  private Diadetaillist: object = []
  private showDialogDetail = false
  private searchParams = {
    assetNo: '',
    address: ''
  } //表格搜索条件
  // 文件上传参数
  private accessoryTitle = '附件列表'
  private accessoryList: string[] = []
  private showUploader = false
  private selectionRowList = [] //表格被选中列表
  private detailRow!: {}
  private searchContent = ''
  private remoteUrl = '/fht-monitor/asset/houseLandInfoList'
  private searchItems = [
    {
      type: 'text',
      key: 'keyword',
      placeholder: '坐落/编号',
      width: '100px'
    },

  ]
  private cols = [
    // {
    //   label: '序号',
    //   prop: 'index',
    //   slotName: 'index',
    //   minWidth: 150
    // },
    {
      label: '直属单位',
      prop: 'companyName',
      minWidth: 160,
      searchKey: 'state',
      fixed: 'left',
      overflowTooltip: true
    },
    {
      label: '系统编号',
      prop: 'systemNumber',
      minWidth: 110,
      fixed: 'left'
    },
    {
      label: '企业资产编号',
      prop: 'assetNo',
      minWidth: 100,
      overflowTooltip: true,
      fixed: 'left'
    },
    {
      label: '资产名称',
      prop: 'assetName',
      minWidth: 110,
      fixed: 'left'
    },
    {
      label: '房屋坐落',
      prop: 'address',
      minWidth: 110,
      fixed: 'left'
    },
    {
      label: '所在城市',
      prop: 'city',
      minWidth: 180
    },
    {
      label: '所在区域',
      prop: 'district',
      minWidth: 120
    },
    {
      label: '所在乡镇',
      prop: 'town',
      minWidth: 120
    },
    {
      label: '资产类型',
      prop: 'assetType',
      minWidth: 120
    },
    {
      label: '资产来源',
      prop: 'source',
      minWidth: 120
    },
    {
      label: '出让年份',
      prop: 'contractDate',
      minWidth: 120
    },

    {
      label: '产权证类型',
      prop: 'certificateType',
      minWidth: 100
    },
    {
      label: '产权证号',
      prop: 'certificateNumber',
      minWidth: 180
    },
    {
      label: '产权人',
      prop: 'propertyOwner',
      minWidth: 80
      // filterOptions: filterOptions.assetCurrency
    },
    {
      label: '房产性质',
      prop: 'property',
      minWidth: 80
    },

    {
      label: '建筑面积(m²)',
      prop: 'constructionArea',
      minWidth: 160
    },
    {
      label: '所在楼层/总楼层',
      prop: 'currentFloors',
      minWidth: 120
    },
    {
      label: '房屋结构',
      prop: 'houseStructure',
      minWidth: 80
    },
    {
      label: '计量单位',
      prop: 'unit',
      minWidth: 100
    },
    {
      label: '业态情况',
      prop: 'bizStatus',
      minWidth: 120
    },
    // {
    //   label: '有效期',
    //   prop: 'decisionMakingBody',
    //   minWidth: 80
    // },

    {
      label: '土地证号',
      prop: 'landCertificateNum',
      minWidth: 130
    },
    {
      label: '土地面积(m²)',
      prop: 'landArea',

      minWidth: 90
    },
    {
      label: '土地用途',
      prop: 'landUse',

      minWidth: 80
    },

    {
      label: '产权情况',
      prop: 'propertyRights',
      minWidth: 90
    },
    {
      label: '使用情况',
      prop: 'useStatus',
      minWidth: 90
    },
    {
      label: '使用面积(m²)',
      prop: 'useArea',
      minWidth: 100
    },
    {
      label: '房屋情况',
      prop: 'housingSituation',
      minWidth: 90
    },
    {
      label: '账面原值',
      prop: 'originalValue',
      slotName: 'originalValue',
      minWidth: 90
    },
    {
      label: '核算方法',
      prop: 'accountingMethod',
      minWidth: 90
    },
    {
      label: '市场公允价值(元)',
      prop: 'newValuation',
      minWidth: 110
    },
    {
      label: '资产净值(元)',
      prop: 'assetValue',
      minWidth: 90
    },
    {
      label: '是否抵押',
      prop: 'whetherMortgage',
      minWidth: 90
    },
    {
      label: '资产形成时间',
      prop: 'assetFormationTime',
      minWidth: 90
    },
    {
      label: '备注',
      prop: 'remark',
      minWidth: 90
    },
    {
      label: '操作',
      prop: 'operatingBar',
      slotName: 'operatingBar',
      fixed: 'right',
      minWidth: 50
    }
  ]
  // 详情附件
  private showUpdaload(list: []) {
    this.accessoryList = list
    this.showUploader = true
  }
  // 顶部删除按钮
  @Confirm({
    title: '提示',
    content: `是否删除所有选中的合同？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private deleteFromBtn() {
    if (this.selectionRowList.length == 0) {
      this.$message.info('请选择你要删除的项')
      return ''
    }
    this.selectionRowList.forEach((item: any) => {
      let res: any = deleteAssetLedger(item.assetNumber)
    })
    this.$message.success('删除成功!')

    this.gridRefresh()
  }
  //  grid 多选触发
  private selectionRow(list: any) {
    this.selectionRowList = list
  }
  // search搜索
  private handleSearch(condition: any) {
    this.searchParams = condition
    this.gridRefresh()
  }
  // 刷新表格
  private gridRefresh() {
    ;(this.$refs['grid'] as any).refresh()
  }
  // 改变详情显示隐藏
  private changeShowDialogDetail(state: boolean) {
    this.showDialogDetail = state
  }
  //  操作
  private loaddetail(row: any) {
    this.detailRow = row
    // 被排除数组
    let Nolist = ['附件资料', '房屋图片', '备注']
    let list: { title: string; label: string }[] = []

    this.cols.forEach((item: any, index: number) => {
      if (index != this.cols.length - 1 && !Nolist.includes(item.label)) {
        let label: any = ''
        if (!item.filterOptions) {
          label = row[item.prop]
        } else {
          label = item.filterOptions.find((item: any) => {
            return (row[item.prop] = item.value)
          })

          label = label.label
        }
        list.push({
          title: item.label,
          label: label
        })
        label = null
      }
    })
    this.Diadetaillist = list
    this.showDialogDetail = true
  }
}
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
  background: #f0f2f5;
}
</style>
