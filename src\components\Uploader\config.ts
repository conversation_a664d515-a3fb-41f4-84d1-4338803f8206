import { UserModule } from '@/store/modules/user'
import { getSignUrl, getSignUrls } from '@/api/public'
import { getLocalStorage } from '@/utils/cache'
export const getToken = () => {
  // return UserModule.token
  
  let activeMenuId = getLocalStorage('saber-token') || ''

  return JSON.parse(activeMenuId).content
}

export const getSignSingle = getSignUrl

export const getSignMulti = getSignUrls

export const FileSize=10 //文件大小，M