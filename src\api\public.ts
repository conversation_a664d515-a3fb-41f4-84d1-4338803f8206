import request from './request'
import { RemoteResponse } from '@/types'

/**
 *@param { boolean } isPrivate true 私有， false公有
 */
export const getOssToken = (isPrivate = false): Promise<RemoteResponse> => {
  // 获取上传图片token
  return request('/fht-base/mdc/oss/getSTSToken', {
    fileType: isPrivate ? 2 : 1
  })
}

/**
 * @param { string } fileName 文件名
 * @param { boolean } isPrivate true 私有， false公有
 */
export const getSignUrl = (fileName: string, isPrivate = false): Promise<RemoteResponse> => {
  return request('/fht-admin/oss/endpoint/put-file', {
    fileName,
    fileType: isPrivate ? 2 : 1,
    zipFlag: false
  })
}

/**
 * @param { Array<string> } fileNames 多个文件名
 * @param { boolean } isPrivate true 私有， false公有
 */
export const getSignUrls = (filenames: Array<string>, isPrivate = false): Promise<RemoteResponse> => {
  return request('/fht-admin/oss/endpoint/file-link', {
    fileNameList: filenames,
    fileType: isPrivate ? 2 : 1,
    zipFlag: false
  })
}

/**
 * 布局容器公共头部
 */
export const getHeaderMenu = (params: any = {}): Promise<RemoteResponse> => {
  return request('/fht-admin/menu/top-menu', params, {
    method: 'GET'
  })
}

/**
 * 获取系统消息通知
 */
export const getNoticeList = (params: any = {}): Promise<RemoteResponse> => {
  return request('/fht-admin/message/my/topList', params, {
    method: 'GET'
  })
}

/**
 * 布局容器公共侧边栏
 */
export const getAsideMenu = (topMenuId: string): Promise<RemoteResponse> => {
  return request(
    `/fht-admin/menu/routes?topMenuId=${topMenuId}`,
    {},
    {
      method: 'GET'
    }
  )
}

// 用户登录
export const loginByUsername = ({ username, password }: { username: string; password: string }): Promise<RemoteResponse> => {
  return request(
    `/fht-auth/oauth/token?tenantId=000000&username=${username}&password=${password}&grant_type=captcha&scope=all&type=account`,
    {},
    {
      method: 'POST'
    }
  )
}

// 刷新token
export const refreshToken = (refreshToken: string) => {
  return request(
    '/fht-auth/oauth/token',
    {
      tenantId: '000000',
      refreshToken: refreshToken,
      grantType: 'refresh_token',
      scope: 'all'
    },
    {
      headers: {
        'Tenant-Id': '000000'
      }
    }
  )
}

// 获取业务系统字典数据
export const infaceBusinessDictData = (params: any): Promise<RemoteResponse> => {
  return request(
    `/fht-admin/dict-biz/tree-by-code?bizAppCode=${params.bizAppCode}`,
    {},
    {
      method: 'GET'
    }
  )
}

// 获取所有模块、按钮等权限
export const infacePermissionDictData = (params: any = {}): Promise<RemoteResponse> => {
  return request('/fht-admin/menu/buttons', params, {
    method: 'GET'
  })
}
// 获取用户信息
export const getUserInfo = (params: any = {}): Promise<RemoteResponse> => {
  return request('/fht-admin/info', params, {
    method: 'GET'
  })
}

// 获取数据字典
export const getDictBizData = (params: any = {}): Promise<RemoteResponse> => {
  return request('/fht-admin/dict-biz/dictionary', params, {
    method: 'GET'
  })
}

// 获取下级机构列表
export const getOrgCodeList = (params: any = {}): Promise<RemoteResponse> => {
  return request(
    '/fht-admin/dept/child-list',
    {
      parentId: 0,
      isGroup: 1,
      deptCategory: 1
    },
    {
      method: 'GET'
    }
  )
}

// 获取集团数据
export const getGroupList = (params: any = {}): Promise<RemoteResponse> => {
  return request(
    '/fht-monitor/ds/common/group-list',
    params,
    {
      method: 'POST'
    }
  )
}
// 企业监管企业数
export const getOrgTreeByEnt = (): Promise<RemoteResponse> => {
  return request(
    '/fht-monitor/ent/supervise-tree/basic',
    {},
    {
      method: 'POST'
    }
  )
}

// 获取单位树
export const getOrgTree = (params: any = {}): Promise<RemoteResponse> => {
  return request('/fht-admin/dept/tree', params, {
    method: 'GET'
  })
}

// 注销账号
export const logUserUnable = (params: any = {}): Promise<RemoteResponse> => {
  return request(
    '/fht-admin/unable',
    params,
    {
      method: 'POST'
    }
  )
}

// 附件加签
export const getReadOnlySignUrl = (params: any = {}): Promise<RemoteResponse> => {
  return request(
    '/fht-admin/oss/endpoint/getReadOnlySignUrl',
    params,
    {
      method: 'POST'
    }
  )
}

