/* 指数、预警 */

<template>
  <section class="index-warning-wrap">
    <CompositeIndex class="m-b-50" />
    <DebtWarning />
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import DebtWarning from '@/views/main/cockpitcren/components/OnePage/DebtWarning.vue'
import CompositeIndex from '@/views/main/cockpitcren/components/OnePage/CompositeIndex.vue'

@Component({
  components: {
    DebtWarning,
    CompositeIndex
  }
})
export default class extends Vue {}
</script>

<style scoped lang="scss">
.index-warning-wrap {
  position: relative;
  width: 100%;
  height: 1010px;
  background: url('../../../cockpitcren/images/panel_bg1.png') no-repeat left top;
  background-size: 100% 100%;
  .m-b-64 {
    margin-bottom: 64px;
  }
}
</style>