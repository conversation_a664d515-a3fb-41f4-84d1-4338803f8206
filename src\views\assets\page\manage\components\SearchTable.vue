<template>
  <section class="search-table-wrap">
    <!-- 头部搜索 -->
    <el-form :inline="true"
      :model="formData"
      ref="ruleForm"
      label-width="84px"
      class="form-box">
      <!-- <el-form-item label="关键字" prop="keyword">
        <el-input v-model.trim="formData.keyword" clearable placeholder="请输入资产坐落、资产编号" class="mode-input" />
      </el-form-item> -->

      <el-form-item label="所属集团"
        prop="orgCode">
        <el-select v-model="formData.orgCode"
          clearable
          placeholder="请选择"
          class="mode-input">
          <el-option v-for="item of compTree"
            :key="item.deptCode"
            :label="item.deptName"
            :value="item.deptCode" />
        </el-select>

        <!-- <el-cascader
          :disabled="getPermission('assets_manage_company58')"
          v-model="orgIdList"
          :props="{ label: 'deptName', value: 'deptCode' }"
          :options="compTree"
          :show-all-levels="false"
          clearable
          placeholder="请选择"
          class="mode-input"
          @change="changeOrgId"
        /> -->
      </el-form-item>

      <el-form-item label="直属单位"
        prop="bizCodeList">
        <orgTree v-model="formData.bizCodeList"
          ref="orgTree"
          width="235"
          class="mode-input" />
      </el-form-item>

      <el-form-item label="子资产编号"
        prop="itemNo">
        <el-input v-model.trim="formData.itemNo"
          clearable
          placeholder="请输入"
          class="mode-input" />
      </el-form-item>

      <el-form-item label="资产名称"
        prop="assetName">
        <el-input v-model.trim="formData.assetName"
          clearable
          placeholder="请输入"
          class="mode-input" />
      </el-form-item>

      <!-- <el-form-item label="资产用途" prop="keyword">
        <el-input v-model.trim="formData.keyword" clearable placeholder="请输入资产坐落、资产编号" class="mode-input" />
      </el-form-item> -->

      <el-form-item label="资产用途"
        prop="assetPurpose">
        <el-select v-model="formData.assetPurpose"
          clearable
          placeholder="请选择"
          class="mode-input">
          <el-option v-for="item of getDictData('asset_purpose')"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="经营类别"
        prop="manageType">
        <el-select v-model="formData.manageType"
          clearable
          placeholder="请选择"
          class="mode-input">
          <el-option v-for="item of getDictData('ast_manage_type')"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="使用状态"
        prop="useStatus">
        <el-select v-model="formData.useStatus"
          clearable
          placeholder="请选择"
          class="mode-input">
          <el-option v-for="item of getDictData('asset_use_status')"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="建筑面积"
        prop="coveredAreaStart">
        <InputNumber v-model.trim="formData.coveredAreaStart"
          clearable
          placeholder="最小"
          type="integer"
          class="mode-min-input"
          @change="changecoveredArea" />
      </el-form-item>

      <el-form-item label=""
        prop="coveredAreaEnd">
        <InputNumber style="margin-left:10px"
          v-model.trim="formData.coveredAreaEnd"
          clearable
          placeholder="最大"
          type="decimal"
          class="mode-min-input"
          @change="changecoveredArea" />
      </el-form-item>

      <!-- <el-form-item label="资产原值"
        prop="originalValueStart">
        <InputNumber v-model.trim="formData.originalValueStart"
          clearable
          placeholder="最小"
          type="decimal"
          class="mode-min-input"
          @change="changeoriginalValue" />
      </el-form-item> -->

      <el-form-item label=""
        prop="originalValueEnd">
        <InputNumber style="margin-left:10px"
          v-model.trim="formData.originalValueEnd"
          clearable
          placeholder="最大"
          type="decimal"
          class="mode-min-input"
          @change="changeoriginalValue" />
      </el-form-item>

      <el-form-item label="产权证号"
        prop="cerNo">
        <el-input v-model.trim="formData.certNo"
          clearable
          placeholder="房产证/土地证/不动产证号"
          class="mode-input" />
      </el-form-item>
      <!-- 
      <el-form-item label="房产证" prop="hasHouseCert">
        <el-select v-model="formData.hasHouseCert" clearable placeholder="请选择" class="mode-input">
          <el-option v-for="item of optionDocuments" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="土地证" prop="hasLandCert">
        <el-select v-model="formData.hasLandCert" clearable placeholder="请选择" class="mode-input">
          <el-option v-for="item of optionDocuments" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="不动产证" prop="hasRealEstateCert">
        <el-select v-model="formData.hasRealEstateCert" clearable placeholder="请选择" class="mode-input">
          <el-option v-for="item of optionDocuments" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="资产用途"
        prop="assetBizStatus">
        <el-select v-model="formData.assetBizStatus"
          clearable
          placeholder="请选择"
          class="mode-input">
          <el-option v-for="(item,index) of getDictData('asset_biz_status')"
            :key="index"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="安全等级" prop="safetyLevel">
        <el-select v-model="formData.safetyLevel" clearable placeholder="请选择" class="mode-input">
          <el-option v-for="item of getDictData('asset_quality_leve')" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item> -->

      <!-- <el-form-item label="闲置时间"
        prop="safetyLevel">
        <el-select v-model="formData.safetyLevel"
          clearable
          placeholder="请选择"
          class="mode-input">
          <el-option v-for="item of getDictData('endpoint')"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item> -->

      <el-form-item label=" ">
        <el-button type="primary"
          icon="el-icon-search"
          @click="searchForm">查 询</el-button>
        <el-button icon="el-icon-refresh"
          @click="resetForm">重 置</el-button>
        <!-- <el-button :loading="loadingExport" @click="exportForm">导 出</el-button> -->
      </el-form-item>
    </el-form>

    <!-- 表格内容 -->
    <VxeGrid ref="VxeGrid"
      :tableData="formData"
      :assetType="assetType"
      @updateStatistics="updateStatistics"
      @handleClickAdd="handleClickAdd"
      @handleClick="handleClick" />

    <!-- 弹窗：新增标的 -->
    <AddTarget :visible.sync="addVisible"
      v-if="addVisible"
      mode="add"
      :assetData="detailRow" />

    <!-- 弹窗：详情 -->
    <DetailCom v-if="visibleDetailDia"
      :visible.sync="visibleDetailDia"
      :id="detailRow.id"
      :itemNo="detailRow.itemNo"
      :itemId="detailRow.itemId" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import orgTree from '@/components/FormComment/orgTree.vue'
import { ElForm } from 'element-ui/types/form'
import { BusinessModule } from '@/store/modules/businessDict'
import { getAstCompTree } from '@/api/projectInvestment'
import { assetsInfoExport } from '@/api/assetsv2'
import { Loading } from '@/decorators'
import { downloadXls } from '@/utils'
import { PermissionModule } from '@/store/modules/permissionDict'
import Dialog from '@/components/Dialog/index.vue'
import InputNumber from '@/components/FormComment/inputNumber.vue'
import DetailCom from '@/views/assets/page/manage/components/DetailCom.vue'
import AddTarget from '@/views/assets/v2page/TargetTransaction/AddTargetTransaction.vue'
import VxeGrid from '@/views/assets/page/manage/components/VxeGrid.vue'

@Component({
  components: {
    Dialog,
    DetailCom,
    InputNumber,
    AddTarget,
    VxeGrid,
    orgTree
  }
})
export default class extends Vue {
  @Prop() private assetType!: number // 房产 and 土地
  @Prop({ default: 0 }) private tableTotal?: any[] // 分页数据总量
  @Prop({
    default: () => {
      return []
    }
  })
  private tableData?: any[] // 表格数据
  // 常量数据
  private optionDocuments = Object.freeze([
    {
      label: '有',
      value: 1
    },
    {
      label: '无',
      value: 2
    }
  ])

  private compTree = []
  private orgIdList = []
  private loadingExport = false

  // 获取字典数据
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }

  // 获取字典项label
  get getDictLabelData() {
    return (type: string, value: string) => {
      if (this.getDictData(type).length) {
        let obj = this.getDictData(type).find((item: any) => {
          return +item.value === +value
        })

        return obj ? obj.label : ''
      } else {
        return ''
      }
    }
  }

  // 获取相关证件label
  get getDocumentsLable() {
    return (value: number) => {
      let obj = this.optionDocuments.find((item) => {
        return +item.value === +value
      })

      if (obj) {
        return obj.label
      } else {
        return '无'
      }
    }
  }

  private loadingTable = false
  private visibleDetailDia = false
  private addVisible = false
  private detailRow = {}
  private page = {
    current: 1,
    size: 20
  }
  private formData = {
    assetPurpose: '',
    bizCodeList: [],
    cerNo: '',
    coveredAreaEnd: '',
    coveredAreaStart: '',
    itemNo: '',
    assetName: '',
    manageType: '',
    orgCode: '',
    originalValueEnd: '',
    originalValueStart: '',
    useStatus: '',
    orgtree: [],
    certNo: ''
  }

  // 组件初始化
  private mounted() {
    Object.assign(this.formData, { ...this.$route.query })
    this.getCompTree()
    this.searchForm()
  }
  private changeTab() {
    this.$nextTick(() => {
      this.searchForm()
    })
  }
  // 导出
  @Loading('loadingExport')
  private async exportForm() {
    let objData = Object.assign(
      {
        assetType: this.assetType
      },
      this.formData
    )

    let res = await assetsInfoExport(objData)
    let text = +this.assetType === 1 ? '房产列表' : '土地列表'
    let time = new Date().getTime()

    downloadXls(res.data, `${text}_${time}.xlsx`)

    this.$message.success(res.msg || '导出成功')
  }

  // 获取机构数组
  private async getCompTree() {
    let { data } = await getAstCompTree({ parentId: 0, deptCategory: 1 })
    this.compTree = data || []
  }

  // 所属单位改变，赋值 formData.orgCode
  private changeOrgId(val: string) {
    this.formData.orgCode = val
  }

  // 搜索
  private searchForm() {
    ;(this.$refs.VxeGrid as any).resetPage()
    ;(this.$refs.VxeGrid as any).getlist()
  }
  private updateStatistics(data: any) {
    this.$emit('updateStatistics', data)
  }
  // 当前页改变
  private handleCurrentChange(val: number) {
    this.$emit('changeTableData', this.page)
  }

  // 闲置时间：对比两个值的大小
  private changeoriginalValue() {
    if (!this.formData.originalValueStart || !this.formData.originalValueEnd) return

    if (+this.formData.originalValueStart > +this.formData.originalValueEnd) {
      this.$message.warning('最小资产原值不得大于最大资产原值')
      this.formData.originalValueStart = ''
      this.formData.originalValueEnd = ''
    }
  }
  // 闲置时间：对比两个值的大小
  private changecoveredArea() {
    if (!this.formData.coveredAreaStart || !this.formData.coveredAreaEnd) return

    if (+this.formData.coveredAreaStart > +this.formData.coveredAreaEnd) {
      this.$message.warning('最小建筑面积不得大于最大建筑面积')
      this.formData.coveredAreaStart = ''
      this.formData.coveredAreaEnd = ''
    }
  }

  // 重置
  private resetForm() {
    ;(this.$refs.ruleForm as ElForm).resetFields()
    ;(this.$refs.VxeGrid as any).resetPage()
    ;(this.$refs.orgTree as any).clearHandle()

    this.formData.orgCode = ''
    this.formData.bizCodeList = []
    this.formData.certNo = ''
    this.orgIdList = []
    this.$nextTick(() => {
      this.$emit('changeTableData', this.formData)
    })
  }

  // 操作
  private handleClick(type: string, row: any) {
    switch (type) {
      case 'see': // 查看
        this.detailRow = row
        if (!row.id) {
          this.$message.warning('该处资产不存在')
        } else {
          this.visibleDetailDia = true
        }
        break
    }
  }

  // 新增标的
  private handleClickAdd(row: { id: string }) {
    this.detailRow = row
    if (!row.id) {
      this.$message.warning('该处资产不存在')
    } else {
      this.addVisible = true
    }
  }

  // 权限方法
  get getPermission() {
    return (code: string) => {
      return PermissionModule.dictLaodData.includes(code) || false
    }
  }
}
</script>

<style scoped lang="scss">
.search-table-wrap {
  position: relative;
  .form-box {
    margin-bottom: 14px;
    padding: 10px 0 0;
    border-radius: 4px;
    background: rgb(172 196 221 / 10%);
    border: 1px solid rgb(172 196 221 / 20%);
    .el-form-item {
      margin-bottom: 10px;
    }
    .mode-input {
      width: 236px;
    }
    .mode-min-input {
      width: 108px;
    }
  }
  .pagination-box {
    margin-top: 10px;
    text-align: right;
  }
}
</style>
