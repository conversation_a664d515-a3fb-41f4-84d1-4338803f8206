<template>
  <Dialog width="600px"
    :visible="visible"
    :modal-append-to-body="true"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :append-to-body="true"
    @close="handleClose">
    <!-- 标题 -->
    <div slot="header">
      <div>{{title}}</div>
    </div>

    <!-- 内容区域 -->
    <div slot="body">
      <el-form :model="ruleForm"
        :rules="rules"
        :hide-required-asterisk="true"
        label-position="left"
        ref="ruleForm"
        label-width="90px"
        class="form-content">
        <el-form-item label-width="0"
          prop="dealContent">
          <el-input v-model.trim="ruleForm.dealContent"
            :rows="5"
            clearable
            type="textarea"
            placeholder="请输入处置意见"
            maxlength="200"
            show-word-limit />
        </el-form-item>
      </el-form>
    </div>

    <!-- 底部按钮 -->
    <div slot="footer"
      class="footer-box">
      <el-button :loading="loading"
        type="primary"
        @click="validateForm">确 认</el-button>
      <el-button @click="handleClose">取 消</el-button>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { ElForm } from 'element-ui/types/form'
import { disposeBatchDealResp } from '@/api/prewarning'
import Dialog from '@/components/Dialog/index.vue'

@Component({
  components: {
    Dialog
  }
})
export default class extends Vue {
  @Prop({ default: false }) private visible?: boolean // 弹窗显隐
  @Prop() private checkedList!: string[] // 选中数据

  private loading = false
  private title = '批量处置情况反馈'
  private ruleForm = {
    dealContent: ''
  }
  private rules = {
    dealContent: [{ required: true, message: '请输入处置意见', trigger: 'blur' }]
  }

  // 校验必填数据
  private validateForm() {
    ;(this.$refs.ruleForm as ElForm).validate((valid: boolean) => {
      valid && this.submitForm()
    })
  }

  // 提交数据
  @Loading('loading')
  private async submitForm() {
    let objData = Object.assign(
      {
        idList: this.checkedList
      },
      this.ruleForm
    )

    let res = await disposeBatchDealResp(objData)
    this.$message.success(res.msg || '提交成功')
    this.updataHandle()
    this.handleClose()
  }

  // 触发父组件更新
  private updataHandle() {
    this.$emit('updataHandle')
  }

  // 关闭弹窗
  private handleClose() {
    this.$emit('update:visible', false)
  }
}
</script>