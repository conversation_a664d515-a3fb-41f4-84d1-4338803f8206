/* 弹窗嵌套 iframe 高度自适应 */

<template>
  <Dialog :width="width"
    :visible="visible"
    :close-on-click-modal="true"
    :destroy-on-close="true"
    :append-to-body="true"
    custom-class="custom-view-iframe-wrap"
    @close="handleClose">
    <div v-if="!isHeader"
      slot="header">
      {{title}}
    </div>

    <div slot="body">
      <div class="body-box">
        <iframe :src="src"
          width="100%"
          height="100%"
          frameborder="0"
          allowfullscreen />
      </div>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'

@Component({
  name: 'Login',
  components: {
    Dialog
  }
})
export default class Header extends Vue {
  @Prop() private visible!: boolean // 是否显示
  @Prop() private src!: string // iframe 地址
  @Prop({ default: '' }) private title?: string // 标题
  @Prop({ default: '800px' }) private width?: string // 宽度
  @Prop({ default: false }) private isHeader?: boolean // 是否显示标题

  // 关闭弹窗
  private handleClose() {
    this.$emit('update:visible', false)
  }
}
</script>

<style scoped lang="scss">
.rule-form-box {
  .icon-user {
    font-size: 16px;
  }
}

.body-box {
  height: 700px;
  overflow-x: auto;
  overflow-y: hidden;
}
</style>