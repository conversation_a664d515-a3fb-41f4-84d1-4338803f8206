/* 发展指数 */

<template>
  <section class="composite-index-wrap">
    <el-row :span="24"
      :gutter="24"
      class="hegith100">
      <el-col :span="7"
        class="module-box">
        <div class="mode-content pis-relative mode-bg1 cockipt-approach-left-top"
          style="height:840px;">
          <!-- 数据改变提示层 -->
          <img v-if="visibleFlicker"
            class="cockipt-bg-flicker"
            src="@/views/main/cockpitcren/images/panel_bg1_cg.png" />

          <StatusIndex title="盈利能力"
            v-loading="loadingFinancial"
            :echartsData="getFinancialInterfaceData('盈利能力')" />
        </div>
        <div class="mode-content pis-relative mode-bg2 cockipt-approach-left-bottom"
          style="height:550px;">
          <!-- 数据改变提示层 -->
          <img v-if="visibleFlicker"
            class="cockipt-bg-flicker"
            src="@/views/main/cockpitcren/images/panel_bg2_cg.png" />

          <StatusIndex title="经营增长"
            gridBootm="8%"
            v-loading="loadingFinancial"
            :echartsData="getFinancialInterfaceData('经营增长')" />
        </div>
      </el-col>
      
      <el-col :span="10"
        class="module-box">
        <div class="mode-content mode-bg2 mode-custom-middle cockipt-approach-middel-top">
          <!-- 各集团tabs  -->
          <CommonTabs :orgCode="tabActive"
            module="CompositeIndex"
            class="cockipt-approach-middel-top m-b-20"
            @commonTabsHandle="commonTabsHandle" />

          <div class="mode-content pis-relative mode-bg2"
            style="height:780px;">
            <!-- 发展指数  -->
            <!-- 数据改变提示层 -->
            <img v-if="visibleFlicker"
              class="cockipt-bg-flicker"
              src="@/views/main/cockpitcren/images/panel_bg2_cg.png" />

            <CompositeIndex v-loading="loadingPerformance"
              :year="year"
              :orgCode="tabActive"
              :orgName="tabActiveName"
              :echartsData="performanceData"
              @scoreTabsHandle="scoreTabsHandle" />
          </div>
        </div>
        <div class="mode-content pis-relative mode-bg3 cockipt-approach-middel-bottom"
          style="height:510px;">
          <!-- 数据改变提示层 -->
          <img v-if="visibleFlicker"
            class="cockipt-bg-flicker"
            src="@/views/main/cockpitcren/images/panel_bg2_cg.png" />

          <StatusIndex title="持续发展"
            gridBootm="11%"
            v-loading="loadingFinancial"
            :echartsData="getFinancialInterfaceData('持续发展')" />
        </div>
      </el-col>

      <el-col :span="7"
        class="module-box">
        <div class="mode-content pis-relative mode-bg1 cockipt-approach-right-top"
          style="height:840px;">
          <!-- 数据改变提示层 -->
          <img v-if="visibleFlicker"
            class="cockipt-bg-flicker"
            src="@/views/main/cockpitcren/images/panel_bg1_cg.png" />

          <StatusIndex title="风险防控"
            v-loading="loadingFinancial"
            :echartsData="getFinancialInterfaceData('风险防控')" />
        </div>
        <div class="mode-content pis-relative mode-bg2 cockipt-approach-right-bottom"
          style="height:550px;">
          <!-- 数据改变提示层 -->
          <img v-if="visibleFlicker"
            class="cockipt-bg-flicker"
            src="@/views/main/cockpitcren/images/panel_bg2_cg.png" />

          <StatusIndex title="资产运营"
            gridBootm="8%"
            v-loading="loadingFinancial"
            :echartsData="getFinancialInterfaceData('资产运营')" />
        </div>
      </el-col>
    </el-row>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { financialIndicator, performanceEvaluation } from '@/api/cockpit'
import { Loading } from '@/decorators'
import CommonTabs from '@/views/main/cockpitSecondary/components/CommonActiveTabs.vue'
import CompositeIndex from '@/views/main/cockpitSecondary/moduleDetails/modules/CompositeIndex/CompositeIndex.vue'
import StatusIndex from '@/views/main/cockpitSecondary/moduleDetails/modules/CompositeIndex/StatusIndex.vue'

@Component({
  components: {
    CommonTabs,
    StatusIndex,
    CompositeIndex
  }
})
export default class extends Vue {
  private year = ''
  private moon = ''
  private tabActive = ''
  private tabActiveName = ''
  private scoreActive = 1
  private loadingFinancial = false
  private loadingPerformance = false
  private visibleFlicker = false
  private financialData = {}
  private performanceData = {}

  // 获取财务各模块数据
  get getFinancialInterfaceData() {
    return (type: string) => {
      if (Array.isArray(this.financialData) && this.financialData.length) {
        let list = this.financialData.filter((item) => {
          return type === item.tag
        })

        if (Array.isArray(list) && list.length) {
          return list[0].list || []
        } else {
          return [0]
        }
      } else {
        return []
      }
    }
  }

  // 数据初始化
  private created() {
    this.year = String((this as any).$moment().format('YYYY'))

    // 如果链接上带有集团code，需要让tabs选中该集团
    let { query} = this.$route
    if(query && query.orgCode) {
      this.tabActive = (query.orgCode as string) || '0'
    }
    // end
  }

  // 组件初始化
  private mounted() {
    // 时间年份改变
    this.$bus.$on('BusYearTimeChange', (year: string) => {
      let yearDeep = ''
      yearDeep = year


      this.year = yearDeep
      this.initDataHandle()
    })

    // 时间月份改变
    this.$bus.$on('BusMoonTimeChange', (data: string) => {
      this.moon = data
      this.initDataHandle()
    })
  }

  // 获取财务各模块数据接口
  @Loading('loadingFinancial')
  private async financialInterfaceData() {
    let { data } = await financialIndicator({
      year: this.year,
      month: this.moon,
      type: +this.scoreActive,
      orgCode: this.tabActive
    })

    this.financialData = data || {}
  }

  // 获取发展指数模块数据接口
  @Loading('loadingPerformance')
  private async performanceEvaluationData() {
    let { data } = await performanceEvaluation({
      year: this.year,
      month: this.moon,
      type: +this.scoreActive,
      orgCode: this.tabActive
    })

    this.performanceData = data || {}
  }

  // 各集团模块切换
  private commonTabsHandle(code: string, name: string) {
    this.tabActive = code
    this.tabActiveName = name
    this.initDataHandle()
  }

  // 执行数据加载
  private initDataHandle() {
    this.flickerHandle()
    this.financialInterfaceData()
    this.performanceEvaluationData()
  }

  // 中间各评分值tabs切换触发数据更新
  private scoreTabsHandle(data: number) {
    this.scoreActive = data
    this.financialInterfaceData()
  }

  // 触发背景闪烁效果
  private flickerHandle() {
    this.visibleFlicker = true
    setTimeout(() => {
      this.visibleFlicker = false
    }, 3000)
  }
}
</script>

<style scoped lang="scss">
.composite-index-wrap {
  position: relative;
  height: 100%;

  .hegith100 {
    height: 100%;
  }
  .bg-none {
    background: none !important;
  }

  .module-box {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    box-sizing: border-box;
    @extend .hegith100;

    .mode-content {
      font-size: 30px;
      padding: 60px 70px;
    }
    .mode-bg1 {
      background: url('../../../../cockpitcren/images/panel_bg1.png') no-repeat left top;
      background-size: 100% 100%;
    }
    .mode-bg2 {
      background: url('../../../../cockpitcren/images/panel_bg2.png') no-repeat left top;
      background-size: 100% 100%;
    }
    .mode-bg3 {
      background: url('../../../../cockpitcren/images/panel_bg2.png') no-repeat left top;
      background-size: 100% 100%;
    }
    .mode-custom-middle {
      height: 780px;
      padding: 20px 0;
      background: none;
    }
  }
}
</style>




