// 大额资金出借弹窗
<template>
  <div>
    <Dialog
      :title="'报表-导入'"
      width="1100px"
      :visible="visible"
      :close-on-click-modal="true"
      :destroy-on-close="true"
      :append-to-body="true"
      @close="handleClose"
    >
      <div slot="body" class="p-20">
        <el-row :gutter="20">
          <el-form
            ref="elForm"
            :model="formData"
            :disabled="mode == 'see' ? true : false"
            :rules="rules"
            size="medium"
            label-width="110px"
            label-position="center"
          >
   
            <el-col :span="24">
              <el-form-item label="公司名称" prop="companyName">
                <el-input v-model="formData.companyName" placeholder="请输入公司名称" clearable :style="{ width: '100%' }"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="报表类型 " prop="companyAddress">
                <el-input v-model="formData.companyAddress" placeholder="请输入公司地址" clearable :style="{ width: '100%' }"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="上报人" prop="debtor">
                <el-input
                  v-model="formData.debtor"
                  placeholder="请输入法定代表人"
                  clearable
                  :style="{ width: '100%' }"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="上报时间" prop="legalRepresentative">
                <el-input
                  v-model="formData.legalRepresentative"
                  placeholder="请输入法定代表人"
                  clearable
                  :style="{ width: '100%' }"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <div>
          <AccessoryList
            v-model="formData.attachmentFileDTOList"
            title="报表"
            dict="financial_largeLending_attach"
            :mode="mode == 'see' ? 'see' : 'upload'"
            class="m-20"
          />
        </div>
      </div>

      <div slot="footer">
        <el-button v-if="mode == 'see'" @click="closeDialog">关闭</el-button>
        <el-button v-if="mode != 'see'" @click="handleClose">取消</el-button>
        <el-button v-if="mode != 'see'" @click="submitForm" type="primary">{{ Diaformdata.id == '' ? '提交' : '确定修改' }}</el-button>
      </div>
    </Dialog>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import { serialNo, AddLargeAmountCapitalLending, DetailLargeAmountCapitalLending } from '@/api/finance'
import { equityRelationship } from '../filterOptions'
import { getYear } from '@/utils/cache'
import AccessoryList, { Accessory } from './AccessoryFileList.vue'
import { Confirm } from '@/decorators/index'
import inputNumber from '@/components/FormComment/inputNumber.vue'
import { BusinessModule } from '@/store/modules/businessDict'

@Component({
  components: {
    Dialog,
    AccessoryList,
    inputNumber
  }
})
export default class Container extends Vue {
  @Prop() private visible!: boolean
  @Prop() private Diaformdata!: any
  @Prop({ default: 'add' }) private mode!: 'see' | 'edit' | 'add'
  // 文件上传
  private uploaderDlgVisible = false
  private uploaderDlgTitle = '文件上传'
  private uploaderDlgMode = 'upload' //download  upload  see
  private uploadInitList: Array<string> = []
  private currentRow: any = {}
  private uploadtype!: string
  // upload end
  private rules: object = {
    documentNo: [
      {
        required: true,
        message: '自动带入单据编号',
        trigger: 'blur'
      }
    ],
    companyName: [
      {
        required: true,
        message: '请输入公司名称',
        trigger: 'blur'
      }
    ],
    companyAddress: [
      {
        required: true,
        message: '请输入公司地址',
        trigger: 'blur'
      }
    ],
    legalRepresentative: [
      {
        required: true,
        message: '请输入法定代表人',
        trigger: 'blur'
      }
    ],
    ownersEquity: [
      {
        required: true,
        message: '请输入所有者权益',
        trigger: 'blur'
      }
    ],
    boardResolutionNum: [
      {
        required: true,
        message: '请输入董事会决议文号',
        trigger: 'blur'
      }
    ],
    debtor: [
      {
        required: true,
        message: '请输入债务人',
        trigger: 'blur'
      }
    ],
    equityRelationship: [
      {
        required: true,
        message: '请输入债务人关系',
        trigger: 'change'
      }
    ],
    shareholdingRatio: [
      {
        required: true,
        message: '请输入持股比例',
        trigger: 'blur'
      }
    ],
    fundsAmount: [
      {
        required: true,
        message: '请输入出借资金额度',
        trigger: 'blur'
      }
    ],
    lendingPeriod: [
      {
        required: true,
        message: '请输入出借期限',
        trigger: 'blur'
      }
    ],
    lendingRate: [
      {
        required: true,
        message: '请输入出借利率',
        trigger: 'blur'
      }
    ],
    projectDesc: [
      {
        required: true,
        message: '请输入出借资金项目概况',
        trigger: 'blur'
      }
    ],
    accumulatedFundsAmount: [
      {
        required: true,
        message: '请输入累计出借资金',
        trigger: 'blur'
      }
    ],
    remark: [
      {
        required: false,
        message: '请输入累计出借资金',
        trigger: 'blur'
      }
    ]
  }

  private equityRelationshipOptions: object = equityRelationship
  //   private formData = this.Diaformdata
  private formData: any = {
    documentNo: '11',
    companyName: '',
    companyAddress: '',
    legalRepresentative: '',
    ownersEquity: '11',
    boardResolutionNum: '11',
    debtor: '',
    equityRelationship: '1',
    shareholdingRatio: '11',
    fundsAmount: '1',
    lendingPeriod: '1',
    lendingRate: '1',
    projectDesc: '1',
    accumulatedFundsAmount: '1',
    remake: '',
    year: '2022',
    attachmentFileDTOList: []
  }

  private issuanceTimeNumberOptions: object[] = [
    {
      label: '一次',
      value: 1
    },
    {
      label: '多次',
      value: 2
    }
  ]
  private issuanceFormOptions: object[] = [
    {
      label: '境内',
      value: 1
    },
    {
      label: '境外',
      value: 2
    }
  ]
  private issuanceTargetOptions: object[] = [
    {
      label: '机构',
      value: 1
    },
    {
      label: '自然人',
      value: 2
    },
    {
      label: '其他',
      value: 3
    }
  ]
  created() {
    if (this.mode == 'see' || this.mode == 'edit') {
      this.getdetail()
    }
    this.openDiaHandle()
    this.formData = Object.assign(this.formData, this.Diaformdata)
    let investProjectPlanFiling = this.formData
  }
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  get getmode() {
    switch (this.mode) {
      case 'see':
        return '查看'
      case 'edit':
        return '编辑'
      default:
        return '新增'
    }
  }
  // 打开弹窗
  private openDiaHandle() {
    try {
      if (this.Diaformdata.id === '') {
        this.getSerialNo(5)
        ;(this.$refs['elForm'] as any).resetFields()
        return ''
      } else {
        // this.getdetail()
      }
    } catch (error) {
      return ''
    }
  }
  // 获取编辑详情
  private async getdetail() {
    try {
      let res: any = await DetailLargeAmountCapitalLending({
        id: this.Diaformdata.id + ''
      })
      if (res.success) {
        res.data.year = res.data.year + ''
        res.data.guaranteePeriod = res.data.guaranteePeriod + ''
        this.formData = res.data
      }
    } catch (e) {
      console.error(e)
    }
  }
  private async getSerialNo(id: number) {
    try {
      let res: any = await serialNo({
        index: id
      })
      this.formData.documentNo = res.data
    } catch (e) {
      this.$message.info('获取编号失败')
    }
  }
  //  文件上传事件
  get uploadable() {
    return this.uploaderDlgMode === 'upload'
  }
  get fileList() {
    return this.currentRow.fileList
  }
  private uploadfile(value: string) {
    this.currentRow.fileList = []
    this.uploadtype = value
    this.uploaderDlgVisible = true
  }
  private lookfile(value: string) {
    this.currentRow = {
      fileName: '附件',
      fileList: [value],
      isRequired: true
    }
    this.uploaderDlgVisible = true
  }
  private onFilesChange(fileList: string[]) {
    this.currentRow.fileList = [...fileList]
  }
  // 文件上传成功写入fromdata
  private handleUploadComplete(fileList: Array<any>) {
    // this.formData[this.uploadtype] = fileList[0].url
    // Object.assign(this.formData,{})
    this.$set(this.formData, this.uploadtype + '', fileList[0].url)
    //
  }
  @Confirm({
    title: '提示',
    content: `是否提交？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private async AddLarge() {
    try {
      let params: any = { ...this.formData }
      params.year = getYear()
      params.lendingPeriod = Number(params.lendingPeriod)
      let res = await AddLargeAmountCapitalLending(params)
      if (res.success) {
        this.$message.success('提交成功')
        this.$emit('changshowDialogAdd', false)
      }
    } catch (e) {
      console.error(e)
    }
  }
  closeDialog() {
    this.$emit('changshowDialogAdd', false)
  }
  ///附件校验 转换
  private validateFiles(): boolean {
    return true
  }
  private submitForm() {
    if (!this.validateFiles()) return
    ;(this.$refs['elForm'] as any).validate((valid: any) => {
      if (!valid) return
      // TODO 提交表单
      if (this.formData.attachmentFileDTOList.length == 0) {
        this.$message.warning('请上传附件')
        return
      }
      this.AddLarge()
    })
  }
  private resetForm() {
    ;(this.$refs['elForm'] as any).resetFields()
  }
  @Confirm({
    title: '提示',
    content: `是否关闭？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private handleClose() {
    this.$emit('changshowDialogAdd', false)
  }
}
</script>

<style lang="scss" scoped>
::v-deep.el-form-item--medium .el-form-item__label {
  line-height: 20px;
}
::v-deep .el-input.is-disabled .el-input__inner {
  color: #2e2e2e;
  background-color: #fff;
}
</style>
