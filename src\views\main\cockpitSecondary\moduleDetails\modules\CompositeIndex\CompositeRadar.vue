/* 发展指数 */

<template>
  <section class="Composite-radar-wrap">
    <div class="count-box"
      @click="visibleDetail = true">
      <CountTo v-if="String(count).split('.').length === 1"
        :decimals="0"
        :startVal="0"
        :endVal="+count"
        :duration="1500"
        class="count"
        :class="textColor" />
      <CountTo v-else
        :decimals="2"
        :startVal="0"
        :endVal="+count"
        :duration="1500"
        class="count"
        :class="textColor" />
    </div>

    <div class="echarts-box"
      ref="refEcharts" />

    <!-- 弹窗：发展指数趋势图 -->
    <DialogCom v-if="visibleDetail"
      :visible.sync="visibleDetail"
      :closeModal="false"
      :year="year"
      :code="orgCode"
      :title="`${getActiveName(orgName)} 发展指数趋势图`"
      width="1000px">
      <IndexRunChart :year="year"
        :code="orgCode" />
    </DialogCom>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { echartConfigure } from '@/views/main/cockpitcren/baseData'
import { deepClone } from '@/utils'
import * as echarts from 'echarts'
import CountTo from 'vue-count-to'
import DialogCom from '@/views/main/cockpitcren/components/Public/DialogCom.vue'
import IndexRunChart from '@/views/main/cockpitcren/components/TwoPage/Components/IndexRunChart.vue'

type EChartsOption = echarts.EChartsOption

@Component({
  components: {
    CountTo,
    DialogCom,
    IndexRunChart
  }
})
export default class extends Vue {
  @Prop() private orgCode?: string // 集团code
  @Prop() private orgName?: string // 集团名称
  @Prop() private year?: string // 年份
  @Prop() private echartsData!: any // 渲染数据

  private count = 0
  private visibleDetail = false
  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private legendData: any[] = []
  private seriesData: any[] = []
  private indicatorData: any[] = []
  private option: EChartsOption = {}
  private colorSource = ['']
  private textColor = ''
  private sourceList = Object.freeze([
    {
      label: '优秀',
      textColor: 'excellent',
      color: '#00F6FF'
    },
    {
      label: '良好',
      textColor: 'good',
      color: '#FFEA00'
    },
    {
      label: '平均',
      textColor: 'qualified',
      color: '#FF7F18'
    },
    {
      label: '较低',
      textColor: 'lower',
      color: '#903ff9'
    },
    {
      label: '较差',
      textColor: 'range',
      color: '#E72A73'
    }
  ])

  // 箭头数据变化，渲染视图
  @Watch('echartsData', { deep: true })
  private changeEchartsData() {
    this.initData()
  }

  // 动态设置弹窗标题
  get getActiveName() {
    return (orgName: string) => {
      let name = orgName === '国资总况' ? '国企' : orgName
      return name
    }
  }

  // 组件初始化
  private mounted() {
    this.chartDom = this.$refs.refEcharts as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)
  }

  // 请求数据接口
  private initData() {
    let data = deepClone(this.echartsData)

    // 组装echarts格式数据
    if (!data) return

    this.count = +data.num

    // 组装数据
    let dataList = data.list || []
    let indicatorData: any[] = []
    let seriesData: number[] = []

    Array.isArray(dataList) &&
      dataList.forEach((item) => {
        seriesData.push(+item.num)
        indicatorData.push({
          name: item.label,
          max: 100
        })
      })

    this.indicatorData = indicatorData
    this.seriesData = seriesData
    this.legendData = this.sourceList.map((item) => {
      return item.label
    })

    // 匹配颜色
    let findItem = this.sourceList.find((item) => {
      return item.label === data.name
    })

    if (findItem) {
      this.colorSource = [findItem.color]
      this.textColor = findItem.textColor
    }

    this.initEcharts()
  }

  // 初始化数据
  private initEcharts() {
    let indicatorData = this.indicatorData
    let seriesData = this.seriesData
    let legendData = this.legendData
    let textColor = this.colorSource[0]
    let tooltipData: any = Object.assign(
      {
        trigger: 'item'
      },
      echartConfigure.tooltipBody
    )

    let color = ['#3eeeff', '#FFEA00', '#FF7F18', '#903ff9', '#E72A73'].concat(this.colorSource)

    let stageList = [
      {
        name: '优秀',
        symbol: 'none',
        value: [100, 100, 100, 100, 100]
        // areaStyle: {
        //   color: new echarts.graphic.RadialGradient(0.1, 0.6, 1, [
        //     {
        //       color: 'rgba(0, 246, 255, 0.5)',
        //       offset: 0
        //     },
        //     {
        //       color: 'rgba(0, 246, 255, 0.5)',
        //       offset: 1
        //     }
        //   ])
        // }
      },
      {
        name: '良好',
        symbol: 'none',
        value: [80, 80, 80, 80, 80]
      },
      {
        name: '平均',
        symbol: 'none',
        value: [60, 60, 60, 60, 60]
      },
      {
        name: '较低',
        symbol: 'none',
        value: [40, 40, 40, 40, 40]
      },
      {
        name: '较差',
        symbol: 'none',
        value: [20, 20, 20, 20, 20]
      }
    ]

    this.option = {
      color: color,
      tooltip: tooltipData,
      legend: {
        show: false,
        selectedMode: false,
        bottom: '8%',
        itemGap: 100,
        itemWidth: 20,
        itemHeight: 20,
        data: legendData,
        textStyle: {
          color: '#fff',
          fontSize: 34,
          padding: [0, 0, 0, 8]
        }
      },
      grid: {
        left: '0%',
        right: '0%',
        bottom: '0%',
        top: '0%',
        containLabel: true
      },
      radar: {
        radius: '70%',
        axisLabel: {
          show: false,
          fontSize: 14,
          color: '#fff',
          fontWeight: 'bold'
        },
        axisName: {
          color: '#fff',
          fontWeight: 'bold',
          fontSize: 32
        },
        axisLine: {
          lineStyle: {
            color: '#3eeeff'
          }
        },
        splitLine: {
          lineStyle: {
            color: ['#062675']
          }
        },
        splitArea: {
          areaStyle: {
            color: ['#062675']
          }
        },
        indicator: indicatorData
      },
      series: [
        {
          type: 'radar',
          data: [
            ...stageList,
            {
              name: '发展指数',
              symbol: 'rect',
              value: seriesData,
              label: {
                show: true,
                fontSize: 24,
                color: textColor,
                formatter: (data: any) => {
                  let labels = ''

                  if (+data.dimensionIndex === 0) {
                    if (+data.value >= 0 && +data.value <= 20) {
                      labels = `{a1|${data.value}}` // 较差
                    } else if (+data.value > 20 && +data.value <= 40) {
                      labels = `{a2|${data.value}}` // 较低
                    } else if (+data.value > 40 && +data.value <= 60) {
                      labels = `{a3|${data.value}}` // 平均
                    } else if (+data.value > 60 && +data.value <= 80) {
                      labels = `{a4|${data.value}}` // 良好
                    } else if (+data.value > 80 && +data.value <= 100) {
                      labels = `{a5|${data.value}}` // 优秀
                    } else {
                      labels = `{a|${data.value}}`
                    }
                  } else if (+data.dimensionIndex === 1) {
                    if (+data.value >= 0 && +data.value <= 20) {
                      labels = `{b1|${data.value}}` // 较差
                    } else if (+data.value > 20 && +data.value <= 40) {
                      labels = `{b2|${data.value}}` // 较低
                    } else if (+data.value > 40 && +data.value <= 60) {
                      labels = `{b3|${data.value}}` // 平均
                    } else if (+data.value > 60 && +data.value <= 80) {
                      labels = `{b4|${data.value}}` // 良好
                    } else if (+data.value > 80 && +data.value <= 100) {
                      labels = `{b5|${data.value}}` // 优秀
                    } else {
                      labels = `{b|${data.value}}`
                    }
                  } else if (+data.dimensionIndex === 2) {
                    if (+data.value >= 0 && +data.value <= 20) {
                      labels = `{c1|${data.value}}` // 较差
                    } else if (+data.value > 20 && +data.value <= 40) {
                      labels = `{c2|${data.value}}` // 较低
                    } else if (+data.value > 40 && +data.value <= 60) {
                      labels = `{c3|${data.value}}` // 平均
                    } else if (+data.value > 60 && +data.value <= 80) {
                      labels = `{c4|${data.value}}` // 良好
                    } else if (+data.value > 80 && +data.value <= 100) {
                      labels = `{c5|${data.value}}` // 优秀
                    } else {
                      labels = `{c|${data.value}}`
                    }
                  } else if (+data.dimensionIndex === 3) {
                    if (+data.value >= 0 && +data.value <= 20) {
                      labels = `{d1|${data.value}}` // 较差
                    } else if (+data.value > 20 && +data.value <= 40) {
                      labels = `{d2|${data.value}}` // 较低
                    } else if (+data.value > 40 && +data.value <= 60) {
                      labels = `{d3|${data.value}}` // 平均
                    } else if (+data.value > 60 && +data.value <= 80) {
                      labels = `{d4|${data.value}}` // 良好
                    } else if (+data.value > 80 && +data.value <= 100) {
                      labels = `{d5|${data.value}}` // 优秀
                    } else {
                      labels = `{d|${data.value}}`
                    }
                  } else if (+data.dimensionIndex === 4) {
                    if (+data.value >= 0 && +data.value <= 20) {
                      labels = `{e1|${data.value}}` // 较差
                    } else if (+data.value > 20 && +data.value <= 40) {
                      labels = `{e2|${data.value}}` // 较低
                    } else if (+data.value > 40 && +data.value <= 60) {
                      labels = `{e3|${data.value}}` // 平均
                    } else if (+data.value > 60 && +data.value <= 80) {
                      labels = `{e4|${data.value}}` // 良好
                    } else if (+data.value > 80 && +data.value <= 100) {
                      labels = `{e5|${data.value}}` // 优秀
                    } else {
                      labels = `{e|${data.value}}`
                    }
                  }

                  return labels
                },
                rich: {
                  a: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 0]
                  },
                  a1: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 0],
                    color: '#fb3f3f'
                  },
                  a2: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 0],
                    color: '#903ff9'
                  },
                  a3: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 0],
                    color: '#ff7f18'
                  },
                  a4: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 0],
                    color: '#ffea00'
                  },
                  a5: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 0],
                    color: '#00f6ff'
                  },

                  b: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 60, 0, 0]
                  },
                  b1: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 60, 0, 0],
                    color: '#fb3f3f'
                  },
                  b2: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 60, 0, 0],
                    color: '#903ff9'
                  },
                  b3: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 60, 0, 0],
                    color: '#ff7f18'
                  },
                  b4: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 60, 0, 0],
                    color: '#ffea00'
                  },
                  b5: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 60, 0, 0],
                    color: '#00f6ff'
                  },

                  c: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 100, -20, 0]
                  },
                  c1: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 100, -20, 0],
                    color: '#fb3f3f'
                  },
                  c2: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 100, -20, 0],
                    color: '#903ff9'
                  },
                  c3: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 100, -20, 0],
                    color: '#ff7f18'
                  },
                  c4: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 100, -20, 0],
                    color: '#ffea00'
                  },
                  c5: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 100, -20, 0],
                    color: '#00f6ff'
                  },

                  d: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, -20, 100]
                  },
                  d1: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, -20, 100],
                    color: '#fb3f3f'
                  },
                  d2: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, -20, 100],
                    color: '#903ff9'
                  },
                  d3: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, -20, 100],
                    color: '#ff7f18'
                  },
                  d4: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, -20, 100],
                    color: '#ffea00'
                  },
                  d5: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, -20, 100],
                    color: '#00f6ff'
                  },

                  e: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 40]
                  },
                  e1: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 40],
                    color: '#fb3f3f'
                  },
                  e2: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 40],
                    color: '#903ff9'
                  },
                  e3: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 40],
                    color: '#ff7f18'
                  },
                  e4: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 40],
                    color: '#ffea00'
                  },
                  e5: {
                    fontSize: 26,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 40],
                    color: '#00f6ff'
                  }
                }
              },
              lineStyle: {
                width: 6
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(62, 238, 255, 0.6)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(62, 238, 255, 0.2)'
                  }
                ])
              }
            }
          ]
        }
      ],
      animationDelay: function (idx) {
        return idx * 500
      }
    }

    this.myChart && this.myChart.setOption(this.option, true)

    // 点击事件
    this.myChart.on('click', () => {
      this.visibleDetail = true
    })
  }
}
</script>

<style scoped lang="scss">
.Composite-radar-wrap {
  $color: #0195e2;
  $colorActive: #40eeff;

  $yx: #00f6ff;
  $lh: #ffea00;
  $pj: #ff7f18;
  $jd: #903ff9;
  $jc: #fb3f3f;

  position: relative;
  height: 100%;
  .count-box {
    position: absolute;
    top: 312px;
    z-index: 10;
    width: 100%;
    color: $colorActive;
    font-size: 50px;
    font-weight: normal;
    font-family: 'PangMenZhengDao';
    text-align: center;
    .count {
      text-shadow: 6px 6px 0px #333;
      -webkit-text-stroke: 1px #eee;
    }
    .excellent {
      color: $yx;
    }
    .good {
      color: $lh;
    }
    .qualified {
      color: $pj;
    }
    .lower {
      color: $jd;
    }
    .range {
      color: $jc;
    }
  }

  .echarts-box {
    width: 100%;
    height: 100%;
    transform: translateY(-48px);
  }
}
</style>


