<template>
  <section class="manControl-ast-details-wrap">
    <h4 class="title-box">产权信息</h4>
    <el-descriptions size="medium"
      border>
      <el-descriptions-item :span="2" label="不动产证号">{{detailData.realEstateCertNo}}</el-descriptions-item>
      <el-descriptions-item :labelStyle="{padding: '0'}" :contentStyle="{padding: '0'}">
        <template slot="label">
          <div style="padding: 10px;border-bottom: 1px solid #EBEEF5;">建筑面积（㎡）</div>
          <div style="padding: 10px 10px 10px">土地面积（㎡）</div>
        </template>
          <div style="height: 42px;border-bottom: 1px solid #EBEEF5;padding-left: 10px;display: flex;align-items: center;">{{detailData.realEstateArea}}</div>
          <div style="height: 42px;padding-left: 10px;display: flex;align-items: center;">{{detailData.realEstaLandArea}}</div>
      </el-descriptions-item>
      <el-descriptions-item label="房产证号"
        :span="2">{{detailData.houseCertNo}}</el-descriptions-item>
      <el-descriptions-item label="建筑面积（㎡）">{{detailData.houseArea}}</el-descriptions-item>
      <el-descriptions-item label="土地证号"
        :span="2">{{detailData.landCertNo}}</el-descriptions-item>
      <el-descriptions-item label="土地面积（㎡）">{{detailData.landArea}}</el-descriptions-item>

      <el-descriptions-item label="产权人">{{detailData.propertyOwner}}</el-descriptions-item>
      <el-descriptions-item label="使用年限">{{detailData.useTerm}}</el-descriptions-item>
      <el-descriptions-item label="开始时间">{{detailData.startDate}}</el-descriptions-item>
      <el-descriptions-item label="结束时间">{{detailData.endDate}}</el-descriptions-item>
      <el-descriptions-item label="产权类型">
        <span v-if="detailData.certCat == 1">甲方自有</span>
        <span v-if="detailData.certCat == 2">受乙委托</span>
      </el-descriptions-item>
      <el-descriptions-item label="附件">
        <el-button v-if="Array.isArray(detailData.fileUrl) && detailData.fileUrl.length"
          type="text"
          size="medium"
          style="padding: 0;"
          @click="visibelFile = true">查看附件({{+detailData.fileUrl.length}})</el-button>
      </el-descriptions-item>
      <el-descriptions-item label="备注"
        :span="3">{{detailData.remark}}</el-descriptions-item>
    </el-descriptions>

    <!-- 查看附件 -->
    <Uploader v-if="Array.isArray(detailData.fileUrl)"
      v-model="detailData.fileUrl"
      title="查看附件"
      url-key="value"
      name-key="label"
      :visible.sync="visibelFile"
      :uploadable="false"
      :is-private="false"
      :show-cover="false" />
  </section>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { astInfoDetail } from '@/api/assets'
import Uploader from '@/components/Uploader/index.vue'

@Component({
  components: {
    Uploader
  }
})
export default class extends Vue {
  @Prop() private rowData!: {
    id: number | string
  }

  private visibelFile = false
  private detailData = {}

  // 初始化
  private created() {
    this.initData()
  }

  // 获取详情数据
  private async initData() {
    let data = await astInfoDetail({
      id: this.rowData.id
    })

    this.detailData = data.data
  }
}
</script>

<style scoped lang="scss">
.manControl-ast-details-wrap {
  .title-box {
    margin: 0;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    &::before {
      display: inline-block;
      content: ' ';
      width: 4px;
      height: 14px;
      margin-right: 6px;
      background: #ce4c4c;
    }
  }
}


</style>