/* 项目投资监管 */

<template>
  <section class="invest-supervision-wrap">
    <TitleCom title="项目投资监管"
      module="ProjectProgress" />

    <div v-loading="loading"
      class="content-box">
      <div class="mode title-box">
        <span class="text-ellipsis-1">项目名称</span>
        <span>年度</span>
        <span>预算/亿元</span>
        <span>执行/亿元</span>
        <span>执行率</span>
      </div>

      <ul ref="tableList"
        class="ul-box scrollbar-none"
        @mouseenter="mouseenterHandle"
        @mouseleave="mouseleaveHandle">
        <li v-for="(item, index) of tableData"
          :key="index"
          :class="{'active': +index === +currentIndex}"
          class="mode"
          @click="openDetail(item)">
          <span class="text-ellipsis-1"
            :title="item.projectName">{{item.projectName}}</span>
          <span class="text-ellipsis-1"
            :title="item.year">{{item.year}}</span>
          <span class="text-ellipsis-1"
            :title="getBigNumberFormat(item.budgetAmount)">{{getBigNumberFormat(item.budgetAmount)}}</span>
          <span class="text-ellipsis-1"
            :title="getBigNumberFormat(item.executeAmount)">{{getBigNumberFormat(item.executeAmount)}}</span>
          <span class="text-ellipsis-1"
            :title="`${getEcuteRate(item.executeRate)}%`">{{getEcuteRate(item.executeRate)}}%</span>
        </li>
      </ul>
    </div>

    <!-- 详情弹窗 -->
    <CockiptDialog v-if="visibleDetail"
      :visible.sync="visibleDetail"
      :year="year"
      :closeModal="false"
      :code="tabActive.code"
      :columns="columns"
      :remoteUrl="remoteUrl"
      :searchParams="searchParams"
      title="项目投资监管" />
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { isWindowFull, bigNumberFormat } from '@/utils'
import { projectInvest } from '@/api/cockpit'
import { Loading } from '@/decorators'
import TitleCom from '@/views/main/cockpitcren/components/Public/TitleCom.vue'
import CockiptDialog from '@/views/main/cockpitcren/components/Public/CockiptDialog.vue'

type typeTabItem = {
  id: string
  code: string
  name: string
}

@Component({
  components: {
    TitleCom,
    CockiptDialog
  }
})
export default class extends Vue {
  private year = ''
  private timer: any
  private listHeight = 80
  private currentIndex = 0
  private middleIndex = 2
  private loading = false
  private listDom: any = {}
  private tableData: any[] = []
  private tabActive: typeTabItem = {
    id: '',
    code: '',
    name: ''
  }

  // 详情表格数据
  private visibleDetail = false
  private remoteUrl = '/fht-monitor/ds/home/<USER>/projectInvestNew'
  private searchParams = {
    year: '',
    companyCode: 0
  }
  private columns = [
    {
      prop: 'projectName',
      label: '项目名称',
      minWidth: 110
    },
    {
      prop: 'eventNo',
      label: '建设性质'
    },
    {
      prop: 'budgetAmount',
      label: '预算金额'
    },
    {
      prop: 'executeAmount',
      label: '执行金额'
    },
    {
      prop: 'executeRate',
      label: '执行率'
    },
    {
      prop: 'ruleName',
      label: '项目地址',
      minWidth: 120
    },
    {
      prop: 'bizName',
      label: '土地面积',
      minWidth: 100
    },
    {
      prop: 'xxx',
      label: '建筑总面积',
      minWidth: 100
    },
    {
      prop: 'xxxx',
      label: '开始时间',
      minWidth: 100
    },
    {
      prop: 'xxxx',
      label: '计划竣工'
    },
    {
      prop: 'xxxx',
      label: '项目周期'
    },
    {
      prop: 'xxxx',
      label: '建设工期'
    },
    {
      prop: 'xxxx',
      label: '负责人'
    },
    {
      prop: 'xxxx',
      label: '联系电话',
      minWidth: 100
    }
  ]

  // 判断窗口是否处于全屏模式
  get getIsWindowFull() {
    return isWindowFull
  }

  // 万元数据转化成亿元
  get getBigNumberFormat() {
    return (num: string | number) => {
      return bigNumberFormat(+num).value || 0
    }
  }

  // 转化执行率
  get getEcuteRate() {
    return (num: number) => {
      let count = Number((+num).toFixed(2))
      return Math.floor(count)
    }
  }

  // 组件初始化
  private mounted() {
    this.listDom = this.$refs['tableList'] as HTMLDivElement
    this.year = String((this as any).$moment(new Date()).add('year', 0).format('YYYY') - 1)

    // 中间各tabs切换触发数据更新
    this.$bus.$on('BusTabsChange', (data: typeTabItem) => {
      this.tabActive = data
      this.clearTimer()
      this.initData()
    })

    // 时间改变时触发数据更新
    this.$bus.$on('BusYearTimeChange', (data: string) => {
      this.clearTimer()
      this.yearHandel(data)
    })
  }

  // 初始化数据
  @Loading('loading')
  private async initData() {
    let { data } = await projectInvest({
      current: 1,
      size: 100,
      year: this.year,
      companyCode: +this.tabActive.code
    })

    if (Array.isArray(data.records)) {
      this.tableData = data.records || []
    } else {
      this.tableData = data || []
    }

    this.scrollTable()
  }

  // 年份切换
  private yearHandel(year: string) {
    this.year = year
    this.initData()
  }

  // 触发 tabs 切换
  private tabsHandel(item: typeTabItem) {
    this.tabActive = item
  }

  // 打开详情页面
  private openDetail(item: any) {
    this.searchParams.companyCode = +this.tabActive.code
    this.searchParams.year = this.year
    this.visibleDetail = true
  }

  // 开启定时器，让 tablist 滚动起来
  private scrollTable() {
    let dataLen = this.tableData.length
    if (!dataLen || dataLen <= this.middleIndex) return

    this.timer = setInterval(() => {
      this.currentIndex++
      if (this.currentIndex > this.middleIndex && this.currentIndex < dataLen) {
        this.listDom.scrollTo(0, (this.currentIndex - this.middleIndex) * this.listHeight)
      } else if (this.currentIndex === dataLen) {
        this.currentIndex = 0
        this.listDom.scrollTo(0, 0)
      }
    }, 8000)
  }

  // 列表鼠标移入，停止动画
  private mouseenterHandle() {
    clearInterval(this.timer)
    this.timer = null
  }

  // 列表鼠标移出，开始动画
  private mouseleaveHandle() {
    this.scrollTable()
  }

  // 清除定时器，还原相关数据
  private clearTimer() {
    clearInterval(this.timer)
    this.timer = null
    this.currentIndex = 0
    this.listDom.scrollTo(0, 0)
  }

  // 组件销毁
  private destroyed() {
    this.clearTimer()
  }
}
</script>

<style scoped lang="scss">
.invest-supervision-wrap {
  position: relative;
  width: 100%;
  height: 610px;
  background: url('../../images/panel_bg2.png') no-repeat left top;
  background-size: 100% 100%;

  .title-box {
    display: flex;
    color: #00e8fa;
    padding: 10px;
    justify-content: space-between;
  }

  .mode {
    font-size: 32px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    span {
      flex: 1;
      display: inline-block !important;
      vertical-align: middle;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      text-align: center;
      &:nth-child(1) {
        width: 32%;
        flex: none;
        text-align: left;
      }
    }
  }

  .ul-box {
    height: 318px;
    overflow-y: auto;
    scroll-behavior: smooth;
    &::-webkit-scrollbar {
      display: none;
    }
    li {
      height: 80px;
      line-height: 80px;
      padding: 0 10px;
      border-radius: 8px;
      box-sizing: border-box;
      cursor: pointer;
    }

    .active {
      background: rgba($color: #387aff, $alpha: 0.5);
      color: #ffba00;
    }
  }
}
</style>