/** 水球图表 */

<template>
  <div ref="WaterChartDom"
    class="content-box"
    :style="{width: `${width}px`,height: `${height}px`}"></div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import * as echarts from 'echarts'
import 'echarts-liquidfill'
type EChartsCoreOption = echarts.EChartsCoreOption

@Component
export default class extends Vue {
  @Prop({ default: 100 }) width!: number
  @Prop({ default: 100 }) height!: number
  @Prop({ default: '' }) title!: string

  private echartsDom!: HTMLElement
  private myChart: any = {}

  get rate() {
    return this.title
  }

  mounted() {
    this.echartsDom = this.$refs.WaterChartDom as HTMLElement
    this.myChart = echarts.init(this.echartsDom)
    this.intiCharts()
  }

  private intiCharts() {
    let value = 0.5
    let data = [0.5, 0.5, 0.5]
    let option: EChartsCoreOption = {
      title: [
        {
          text: this.rate + '%',
          left: '50%',
          bottom: '54%',
          textAlign: 'center',
          textStyle: {
            color: '#40EEFF',
            fontSize: this.width / 6,
            textAlign: 'center',
            fontWeight: 'normal',
            fontFamily: 'PangMenZhengDao'
          }
        }
      ],
      series: [
        {
          type: 'liquidFill',
          radius: '60%',
          z: 6,
          center: ['50%', '40%'],
          color: [
            {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0.5,
                  color: 'rgba(64, 238, 255, 0.3)'
                },
                {
                  offset: 0.2,
                  color: 'rgba(64, 238, 255, 0.2)'
                },
                {
                  offset: 0,
                  color: 'rgba(64, 238, 255, 0.1)'
                }
              ],
              globalCoord: false
            }
          ],
          data: [+this.rate / 100, +this.rate / 100, +this.rate / 100],
          backgroundStyle: {
            borderWidth: 1,
            color: 'transparent'
          },
          label: {
            normal: {
              formatter: ''
            }
          },
          outline: {
            show: true,
            itemStyle: {
              borderWidth: 0
            },
            borderDistance: 0
          }
        },
        {
          name: '第二层边',
          type: 'pie',
          z: 3,
          radius: ['0%', '63%'],
          center: ['50%', '40%'],
          hoverAnimation: false,
          itemStyle: {
            normal: {
              label: {
                show: false
              }
            }
          },
          data: [
            {
              value: 100,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: '#072775'
                    },
                    {
                      offset: 1,
                      color: '#072775'
                    }
                  ])
                }
              }
            },
            {
              value: 0,
              itemStyle: {
                normal: {
                  color: 'transparent'
                }
              }
            }
          ]
        },
        {
          name: '最外层边',
          type: 'pie',
          z: 1,
          radius: ['0%', '68%'],
          center: ['50%', '40%'],
          hoverAnimation: false,
          itemStyle: {
            normal: {
              label: {
                show: false
              }
            }
          },
          data: [
            {
              value: 100,
              itemStyle: {
                color: '#58D9F9'
              }
            },
            {
              value: 0,
              itemStyle: {
                normal: {
                  color: '#58D9F9'
                }
              }
            }
          ]
        }
      ]
    }

    this.myChart.setOption && this.myChart.setOption(option)
  }
}
</script>