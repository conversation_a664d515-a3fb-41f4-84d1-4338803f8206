import request from './request'
import { RemoteResponse } from '@/types'

export const getPrewarningRules = (): Promise<RemoteResponse> => {
  return request('/fht-monitor/alert/rule/page')
}

export const getPrewarningEvents = (params: object): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/ewm/unDisposed/page', params)
}

export const removePrewarningEvent = (params: { id: number }): Promise<RemoteResponse> => {
  return request('/fht-monitor/alert/event/erase', params)
}

export const addPrewarningEvent = (params: object): Promise<RemoteResponse> => {
  return request('/fht-monitor/alert/event/add', params)
}
//获取预警点详情列表
export const getAlertDetail = (params: object): Promise<RemoteResponse> => {
  return request('/fht-monitor/ewm/rule/detail', params)
}
//获取预警点详情指标
export const alertTmetricDict = (params: object = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/ewm/metrics/level/list', params)
}
// 更新新增预警点
export const saveOrUpdate = (params: object): Promise<RemoteResponse> => {
  // return request('/fht-monitor/alert/pointrecord/saveOrUpdate', params)
  return request('/fht-monitor/ewm/rule/save', params)
}
// 历史变更记录
export const getpointrecordhistory = (params: object): Promise<RemoteResponse> => {
  return request('/fht-monitor/alert/pointrecordhistory/page', params)
}
// 更新预警状态
export const prewarningUpdateStatus = (params: object): Promise<RemoteResponse> => {
  return request('/fht-monitor/ewm/rule/status/update', params)
}
// 预警详情接口
export const debtWarningDetail = (params: { id: number }): Promise<RemoteResponse> => {
  return request('/fht-monitor/ewm/event/detail', params)
}

// 点阅状态变更
export const updateClickStatus = (params: object): Promise<RemoteResponse> => {
  return request('/fht-monitor/alert/event/updateClickStatus', params)
}

// 下发整改通知
export const issuedRectification = (params: object): Promise<RemoteResponse> => {
  return request('/fht-monitor/ewm/event/trace/submit', params)
}

// 预警处置详情
export const getAlertDisposeInfo = (params: object): Promise<RemoteResponse> => {
  return request('/fht-monitor/ewm/event/audit/detail', params)
}

// 预警短信发送记录
export const getSmsSendList = (params: object): Promise<RemoteResponse> => {
  return request('/fht-monitor/alert/contact/policy/getSmsSendList', params)
}

// 处置情况-核实
export const disposeDealResp = (params: object): Promise<RemoteResponse> => {
  return request('/fht-monitor/ewm/event/audit', params)
}

// 处置情况-批量核实
export const disposeBatchDealResp = (params: object): Promise<RemoteResponse> => {
  return request('/fht-monitor/ewm/event/batch-audit', params)
}

// 反馈
export const doFeedback = (params: object): Promise<RemoteResponse> => {
  return request('/fht-monitor/ewm/event/feedback', params)
}

// --------***预警事件****------------
/**
 * 
 * @param params 
 *  预警业务数量top10
 */
export const getPperationalWarningsTop = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/alert/event/getEventClass', params)
}
/**
 * @param params 
 *  集团年度预警数量
 */
export const getEventStatistics = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/alert/event/getEventStatistics', params)
}
/**
 * @param params 
 *  预警事件列表配置（预警分类）
 */
export const getDictWarningeventlist = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/alert/pointtype/page', params)
}

// 驾驶舱大屏-三重一大：智慧预警
export const realtimeWarning = (params: any): Promise<RemoteResponse> => {
  let obj = Object.assign(
    params
  )
  return request('/fht-monitor/alert/event/page', obj)
}

// 预警数量
export const getAlertNotice = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/alert/event/getAlertNotice', params)
}

// 各类型预警情况
export const getAlertSummary = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/ewm/summary', params)
}

// 各类型预警情况(资产)
export const getEwmSummaryAst = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/ewm/summary-for-ast', params)
}

// 预警名称分组统计处理情况
export const getPrewarningSummary = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/ewm/handle-sum', params)
}

// 预警事件列表导出
export const getPrewarningExport = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ewm/event/export', params, {
    responseType: 'blob'
  })
}

// 数据看板顶部指标
export const getTopData = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ewm/summary/topData', params)
}

// 预警分布
export const getTypeDistribute = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ewm/summary/typeDistribute', params)
}

// 资产经营预警分布
export const getAssetDistribute = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ewm/summary/assetDistribute', params)
}

// 融资信息预警分布
export const getFinDistribute = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ewm/summary/finDistribute', params)
}

// 集团年度预警数量
export const groupByOrg = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ewm/summary/groupByOrg', params)
}

// 年度预警数量
export const monthHistory = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ewm/summary/monthHistory', params)
}

// 融资借款还款明细（子企业）
export const getFinancialDetailCapital = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/fin/capital/borrow/page', params)
}

