/* 三重一大，重大项目安排事项 */

<template>
  <section v-loading="loading"
    class="trinity-majorpeople-wrap">
    <h4 class="cockipt-mode-vice-title">重大项目安排事项</h4>

    <div v-if="listData.length"
      class="content-box">
      <div v-for="(item, index) of listData"
        :key="index"
        class="mode">
        <img v-if="index <= 3"
          :src="require(`@/views/main/cockpitcren/images/tubiao${index+1}.png`)" />
        <img v-else
          :src="require(`@/views/main/cockpitcren/images/tubiao1.png`)" />
        <div class="info">
          <p>{{item.name}}</p>
          <CountTo :decimals="2"
            :startVal='0'
            :endVal='item.value'
            :duration='2000'
            class="counto" />
          <i v-if="item.name.indexOf('数量') > -1">个</i>
          <i v-if="item.name.indexOf('笔数') > -1">笔</i>
          <i v-if="item.name.indexOf('金额') > -1">亿元</i>
        </div>
      </div>
    </div>

    <div v-else
      class="custom-data-none">暂无数据</div>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { majorProjectCount } from '@/api/cockpit'
import { Loading } from '@/decorators'
import CountTo from 'vue-count-to'

type typeTabItem = {
  id: string
  code: string
  name: string
}

@Component({
  components: {
    CountTo
  }
})
export default class extends Vue {
  private loading = false
  private year = ''
  private interfaceData: any[] = []
  private listData: Array<typeTabItem> = []
  private tabActive: typeTabItem = {
    id: '',
    code: '',
    name: ''
  }

  // 组件初始化
  private mounted() {
    this.year = String((this as any).$moment(new Date()).add('year', 0).format('YYYY') - 1)
    this.initData()

    // 中间各tabs切换触发数据更新
    this.$bus.$on('BusTabsChange', (data: typeTabItem) => {
      this.tabActive = data
      this.filterEchartsData()
    })

    // 时间改变时触发数据更新
    this.$bus.$on('BusYearTimeChange', (data: string) => {
      this.year = data
      this.initData()
    })
  }

  // 获取数据
  @Loading('loading')
  private async initData() {
    let { data } = await majorProjectCount({
      year: this.year,
      companyCode: +this.tabActive.code
    })

    this.interfaceData = data as any[]
    this.filterEchartsData()
  }

  // 筛选相关数据
  private filterEchartsData() {
    let list: any[] = []
    let filterData = this.interfaceData.filter((item) => {
      return +item.companyCode === +this.tabActive.code
    })

    filterData.forEach((item: { itemName: string; itemValue: string }) => {
      list.push({
        name: item.itemName,
        value: +item.itemValue
      })
    })

    this.listData = list
  }
}
</script>

<style scoped lang="scss">
.trinity-majorpeople-wrap {
  position: relative;
  height: 100%;
  box-sizing: border-box;
  padding-top: 20px;
  p {
    margin: 0;
  }
  .content-box {
    display: flex;
    align-items: center;
    flex-flow: wrap;
    padding: 70px 0 20px;
    .mode {
      position: relative;
      display: flex;
      align-items: center;
      height: 134px;
      width: 375px;
      padding: 14px 20px;
      box-sizing: border-box;
      border-radius: 10px;
      border: 1px solid #009cf0;
      background: linear-gradient(rgba($color: #00287e, $alpha: 0.9), rgba($color: #009cf0, $alpha: 0.6));
      img {
        width: 80px;
        margin-top: 30px;
        margin-right: 30px;
      }
      .info {
        margin-top: 12px;
        p {
          font-size: 34px;
        }
        span {
          color: #3eeeff;
          font-size: 52px;
          line-height: 40px;
          font-weight: normal;
          font-family: 'PangMenZhengDao';
        }
        i {
          position: absolute;
          right: 10px;
          bottom: 14px;
          color: #3eeeff;
          font-size: 30px;
        }
      }
    }
    & .mode:nth-child(1) {
      margin-bottom: 30px;
      margin-right: 45px;
    }
    & .mode:nth-child(2) {
      margin-bottom: 30px;
    }
    & .mode:nth-child(3) {
      margin-right: 45px;
    }
  }

  .custom-data-none {
    text-align: left !important;
  }
}
</style>