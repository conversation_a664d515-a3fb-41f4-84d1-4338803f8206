<template>
  <section class="financialIndicator-assets-rateinfo-wrap">
    <div v-for="(item, index) of seriesData"
      :key="index"
      class="mode-box"
      @click="routerHandle">
      <h6 class="title">{{item.label}}</h6>
      <p class="info">
        <CountTo :decimals="getDecimals(String(item.value))"
          :startVal='0'
          :endVal='+item.value'
          :duration='3000'
          suffix="%"
          class="rote" />
        <span>
          <template v-if="+item.rote>0">
            <img src="@/views/main/cockpitSecondary/images/thows.png" />
            <i class="s">{{getValueDecimals(item.rote)}}%</i>
          </template>
          <template v-if="+item.rote<0">
            <img src="@/views/main/cockpitSecondary/images/thowx.png" />
            <i class="x">{{getValueDecimals(item.rote)}}%</i>
          </template>
        </span>
      </p>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { deepClone } from '@/utils'
import { getLocalStorage } from '@/utils/cache'
import { countoDecimals, valueDecimals } from '@/views/main/cockpitSecondary/baseData'
import CountTo from 'vue-count-to'

@Component({
  components: {
    CountTo
  }
})
export default class extends Vue {
  @Prop() private echartsData!: any // 渲染数据

  private seriesData: any[] = []

  // 根据传入的值，判断 CountTo 组件保留几位小数
  get getDecimals() {
    return (count: string) => {
      return countoDecimals(count)
    }
  }

  // 根据传入的值，如果是小数，则去掉小数点后面的 .00
  get getValueDecimals() {
    return (count: string) => {
      return valueDecimals(count)
    }
  }

  // 数据变化，渲染视图
  @Watch('echartsData', { deep: true })
  private changeEchartsData() {
    this.initData()
  }

  // 跳转到相关业务系统
  routerHandle() {
    this.$bus.$emit('BustoBusiness', {
      code: 'finance',
      path: '/business/finance/index'
    })
  }

  // 组装数据，渲染视图
  private initData() {
    let data = deepClone(this.echartsData)
    this.seriesData = data
  }
}
</script>

<style scoped lang="scss">
.financialIndicator-assets-rateinfo-wrap {
  $colorText: #00ffff;
  $colorThows: #ff6267;
  $colorThowx: #00a92b;

  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 10px 24px 0 0;
  h6,
  p {
    margin: 0;
  }

  .mode-box {
    width: 500px;
    height: 172px;
    overflow: hidden;
    cursor: pointer;
    background: url('../../../images/assets_rateinfo_bg.png') no-repeat center center;
    background-size: 100% 100%;
    .title {
      height: 80px;
      line-height: 110px;
      font-size: 40px;
      text-align: center;
      font-weight: bold;
      margin-bottom: 6px;
    }
    .info {
      text-align: center;
      padding: 0 10px;
      .rote {
        color: $colorText;
        font-size: 50px;
        font-weight: normal;
        font-family: 'PangMenZhengDao';
      }
      img {
        width: 22px;
        margin-right: 4px;
      }
      i {
        font-size: 36px;
        font-weight: normal;
        font-family: 'digital-7';
      }
      .s {
        color: $colorThows;
      }
      .x {
        color: $colorThowx;
      }
    }
  }
}
</style>