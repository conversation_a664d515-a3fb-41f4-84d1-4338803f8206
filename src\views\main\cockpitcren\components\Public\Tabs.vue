/* * prop[! type]：'assets' | 'company'----tabs数据类型 * emit[@tabsHandel]：tabs 切换后触发父组件事件，并传入选中数据 */

<template>
  <section class="tabs-content-wrap">
    <el-tabs v-model="activeMode"
      type="card"
      :stretch="true"
      @tab-click="changeTabs">
      <el-tab-pane v-for="(item, index) of tabList"
        :key="index"
        :label="item.name"
        :data-index="String(item.code)"
        :name="String(item.code)" />
    </el-tabs>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { deepClone } from '@/utils'
import {
  assetsTypes,
  companyList,
  projectProgressList,
  dateUnitList,
  projectInvestmentTypeList,
  plansInvestmentTypeList,
  earlyWarningList
} from '@/views/main/cockpitcren/baseData'

@Component
export default class extends Vue {
  [x: string]: any
  @Prop({
    validator: (value: any) => {
      return ['assets', 'company', 'projectProgress', 'date', 'projectInvestment', 'plansInvestment', 'earlyWarning'].indexOf(value) !== -1
    }
  })
  readonly type!: string // tabs 数据类型（assets：资产数据；company：集团数据）

  private time = 20000
  private timer: any = null
  private activeMode = ''
  private acitveIndex = 0
  private tabList: any[] = []

  // 组件初始化
  private mounted() {
    if (this.type === 'assets') {
      this.$bus.$on('BusTabsChange', (data: { code: number; name: string }) => {
        this.$emit('tabsComsHandel', data)
      })
      return
    }

    if (this.type === 'company') {
      this.$bus.$on('BusTabsChange', (data: { code: number; name: string }) => {
        this.activeMode = data.code + ''
        this.$emit('tabsHandel', data)
      })
      return
    }
  }

  // 初始化数据
  private created() {
    let list = []
    if (!this.type) return
    switch (this.type) {
      case 'assets':
        this.tabList = deepClone(assetsTypes)
        break
      case 'company':
        this.tabList = deepClone(companyList)
        break
      // 项目进度
      case 'projectProgress':
        this.tabList = deepClone(projectProgressList)
        break
      case 'date':
        this.tabList = deepClone(dateUnitList)
        break
      // 项目投资tabs类型
      case 'projectInvestment':
        this.tabList = deepClone(projectInvestmentTypeList)
        break
      // 计划投资tabs类型
      case 'plansInvestment':
        this.tabList = deepClone(plansInvestmentTypeList)
        break
      // 预警类型
      case 'earlyWarning':
        this.tabList = deepClone(earlyWarningList)
        break
    }

    if (this.tabList[0]) {
      this.activeMode = this.tabList[0].code
      this.$emit('tabsHandel', this.tabList[0])
    }

    if (this.type === 'assets') this.cycleTabs()
  }

  // 切换tab
  private changeTabs() {
    // 资产tab
    if (this.type === 'assets') {
      clearInterval(this.timer)
      this.timer = null

      setTimeout(() => {
        this.cycleTabs()
      }, this.timer)

      let activeItem = {}

      for (let i = 0; i < this.tabList.length; i++) {
        if (this.tabList[i].code === this.activeMode) {
          this.acitveIndex = i
          activeItem = this.tabList[i]
          break
        }
      }
      this.$emit('tabsHandel', activeItem)
      return
    }

    // 集团tab
    if (this.type === 'company') {
      let activeItem = this.tabList.filter((item: { code: string }) => {
        return +item.code === +this.activeMode
      })
      this.$emit('tabsHandel', activeItem[0])
      return
    }

    // 默认返回
    let activeItem = this.tabList.filter((item: { code: string }) => {
      return item.code === this.activeMode
    })[0]
    this.$emit('tabsHandel', activeItem)
  }

  // 定时器，循环切换（type = assets 时触发）
  private cycleTabs() {
    // clearInterval(this.timer)
    // this.timer = null
    // for (let i = 0; i < this.tabList.length; i++) {
    //   if (this.tabList[i].code === this.activeMode) {
    //     this.acitveIndex = i
    //     break
    //   }
    // }
    // this.timer = setInterval(() => {
    //   this.acitveIndex += 1
    //   if (this.acitveIndex > this.tabList.length - 1) {
    //     this.activeMode = ''
    //     this.acitveIndex = 0
    //   }
    //   this.activeMode = this.tabList[this.acitveIndex].code
    //   this.changeTabs()
    // }, this.time)
  }

  // 组件销毁
  private destroyed() {
    clearInterval(this.timer)
    this.timer = null
  }
}
</script>

<style scoped lang="scss">
$colorActive: #3be5f8;

.tabs-content-wrap {
  text-align: center;
  overflow: hidden;
  border-radius: 8px;
  ::v-deep .el-tabs {
    .el-tabs__header {
      margin: 0;

      border: none !important;
      .el-tabs__nav {
        border: none !important;
      }
      .el-tabs__item {
        height: 64px;
        line-height: 50px;
        color: #fff;
        font-size: 36px;
        padding: 0 !important;
        border: none !important;
      }
      .is-active {
        color: $colorActive;
        border-bottom: 4px solid $colorActive !important;
      }
    }
  }
}
</style>
