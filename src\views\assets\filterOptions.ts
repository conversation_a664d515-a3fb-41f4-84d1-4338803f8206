/**
 * 产权证类型:1-不动产证号，2-房产证号，3-土地证号，4-林权证号，5-其它
 */
export const FilTercertificateType: object[] = [
  {
    value: 1,
    label: '不动产证号'
  },
  {
    value: 2,
    label: '房产证号'
  },
  {
    value: 3,
    label: '土地证号'
  },
  {
    value: 4,
    label: '林权证号'
  },
  {
    value: 5,
    label: '其它'
  }
]
/**
 * 房屋结构,1-砖混,2-泥木,3-砖木,4-木,5-钢混,6-混合,7-其它
 */

export const FilterHouseStructure: object[] = [
  {
    value: 1,
    label: '砖混'
  },
  {
    value: 2,
    label: '泥木'
  },
  {
    value: 3,
    label: '砖木'
  },
  {
    value: 4,
    label: '木'
  },
  {
    value: 5,
    label: '钢混'
  },
  {
    value: 6,
    label: '混合'
  },
  {
    value: 5,
    label: '其它'
  }
]
/**
 * 	房屋情况,1-现存,2-危房,3-已拆除,4-部分拆除,5-其它
 */
export const FilterHousingSituation: object[] = [
  {
    value: 1,
    label: '现存'
  },
  {
    value: 2,
    label: '危房'
  },
  {
    value: 3,
    label: '已拆除'
  },
  {
    value: 4,
    label: '部分拆除'
  },
  {
    value: 5,
    label: '其它'
  }
]
/**
 * 土地用途 1-城镇住宅用地,2-商服用地,3-工业用地,4-科教用地,
 * 5-仓储用地,6-批发零售用地,7-其它商服用地,8-住宿餐饮用地,9-机关团体用地,10-其它
 */
export const FilterLandUse: object[] = [
  {
    value: 1,
    label: '城镇住宅用地'
  },
  {
    value: 2,
    label: '商服用地'
  },
  {
    value: 3,
    label: '工业用地'
  },
  {
    value: 4,
    label: '科教用地'
  },
  {
    value: 5,
    label: '仓储用地'
  },
  {
    value: 6,
    label: '批发零售用地'
  },
  {
    value: 7,
    label: '其它商服用地'
  },
  {
    value: 8,
    label: '住宿餐饮用地'
  },
  {
    value: 9,
    label: '机关团体用地'
  },
  {
    value: 10,
    label: '其它'
  }
]
/**
 * 权利性质 1-出让,2-划拨,3-其它
 */
export const FilterNatureRights: object[] = [
  {
    value: 1,
    label: '出让'
  },
  {
    value: 2,
    label: '划拨'
  },
  {
    value: 3,
    label: '其它'
  }
]
/**
 * 	获取方式, 1-划入，2-购置，3-调拨，4-置换，5-拆赔，
 * 6-自建，7-改制，8-代管，9-待入账，10-其它
 */
export const FilterSource: object[] = [
  {
    value: 1,
    label: '划入'
  },
  {
    value: 2,
    label: '购置'
  },
  {
    value: 3,
    label: '调拨'
  },
  {
    value: 4,
    label: '置换'
  },
  {
    value: 5,
    label: '拆赔'
  },
  {
    value: 6,
    label: '自建'
  },
  {
    value: 7,
    label: '改制'
  },
  {
    value: 8,
    label: '代管'
  },
  {
    value: 9,
    label: '待入账'
  },
  {
    value: 10,
    label: '其它'
  }
]
/**
 * 是否抵押 ,1-是,0-否
 */
export const FilterWhetherMortgage: object[] = [
  {
    value: 1,
    label: '是'
  },
  {
    value: 2,
    label: '否'
  }
]
/**
 * 资产类型 1-房产 2-土地
 */
export const FilterAssetType: object[] = [
  {
    value: 1,
    label: '房产'
  },
  {
    value: 2,
    label: '土地'
  }
]

/**
 * 使用情况，1-出租，2-闲置，3-征收，4-出售，5-自用
 * ，5-公开拍租，6-协议出租，7-其它
 */
export const FilterUseStatus: object[] = [
  {
    value: 1,
    label: '出租'
  },
  {
    value: 2,
    label: '闲置'
  },
  {
    value: 3,
    label: '征收'
  },
  {
    value: 4,
    label: '出售'
  },
  {
    value: 5,
    label: '自用'
  },
  {
    value: 6,
    label: '公开拍租'
  },
  {
    value: 7,
    label: '协议出租'
  },
  {
    value: 8,
    label: '其它'
  }
]
/**
 * 	房屋类别,1-投资性房产,2-固定资产房产,3-存货房产
 */
export const FilterPropertyType: object[] = [
  {
    value: 1,
    label: '投资性房产'
  },
  {
    value: 2,
    label: '固定资产房产'
  },
  {
    value: 3,
    label: '存货房产'
  }
]

// 业务状态
export const processNodeList = [
  {
    value: 1,
    label: '待核实'
  },
  {
    value: 2,
    label: '审核中'
  },
  {
    value: 3,
    label: '未认领'
  },
  {
    value: 4,
    label: '已认领'
  },
  {
    value: 5,
    label: '挂牌'
  },
  {
    value: 6,
    label: '撤牌'
  },
  {
    value: 7,
    label: '摘牌'
  },
  {
    value: 8,
    label: '成交'
  },
  {
    value: 9,
    label: '已中止'
  },
  {
    value: 10,
    label: '已终结'
  }
]

// 决策类型 列表
export const decisionTypeList = [
  {
    label: '无',
    value: 0
  },
  {
    label: '股东会决议',
    value: 1
  },
  {
    label: '董事会决议',
    value: 2
  },
  {
    label: '总经理办公会决议',
    value: 3
  },
  {
    label: '党委会决定',
    value: 4
  },
  {
    label: '批复',
    value: 5
  },
  {
    label: '其他',
    value: 9
  }
]

// 价格单位
export const priceUnitList = [
  {
    label: '元/平方米/天',
    value: 1
  },
  {
    label: '元/月',
    value: 2
  },
  {
    label: '元/年',
    value: 3
  },
  {
    label: '万/平方米/月',
    value: 4
  },
  {
    label: '万元/年',
    value: 5
  },
  {
    label: '万元（租期内总租金）',
    value: 6
  },
  {
    label: '元/平方米/天（首年）',
    value: 7
  },
  {
    label: '元/平方米/月（首年）',
    value: 8
  },
  {
    label: '元',
    value: 9
  },
  {
    label: '万元',
    value: 10
  },
  {
    label: '其他',
    value: 11
  }
]

// 经济性质
export const economicNatureList = [
  {
    label: '国资监管机构/政府部门',
    value: 1
  },
  {
    label: '国有独资公司（企业）/国有全资企业',
    value: 2
  },
  {
    label: '国有控股企业',
    value: 3
  },
  {
    label: '国有事业单位，国有社团等',
    value: 4
  },
  {
    label: '国有实际控制企业',
    value: 5
  },
  {
    label: '国有参股企业',
    value: 6
  },
  {
    label: '集体',
    value: 7
  },
  {
    label: '私营',
    value: 8
  },
  {
    label: '外资企业',
    value: 9
  },
  {
    label: '其他',
    value: 99
  }
]

// 未征集到意向承租方选项
export const tenantUnsolicitedTypeList = [
  {
    label: '信息发布终结',
    value: 1
  },
  {
    label: '按约定工作日为周期延期延长信息披露，直至征集到意向承租方',
    value: 2
  },
  {
    label: '按约定工作日为周期延长信息披露',
    value: 3
  },
  {
    label: '变更公告内容，重新申请信息发布',
    value: 4
  }
]

// 标的状态 1：空置，2：使用，9：其它
export const subjectStatusTyleList = [
  {
    label: '空置',
    value: 1
  },
  {
    label: '使用',
    value: 2
  },
  {
    label: '其它',
    value: 3
  }
]

// 产权证类型
export const titleDeedTypeList = [
  {
    label: '不动产证号',
    value: 1
  },
  {
    label: '房产证号',
    value: 2
  },
  {
    label: '土地证号',
    value: 3
  },
  {
    label: '林权证号',
    value: 4
  },
  {
    label: '规划证号',
    value: 5
  },
  {
    label: '施工证号',
    value: 6
  },
  {
    label: '竣工正号',
    value: 7
  }
]

// 资产类别
export const assetTypeList = [
  {
    label: '房屋',
    value: 1
  },
  {
    label: '土地',
    value: 2
  }
]

// 建筑面积单位
export const constructionAreaUnitList = [
  {
    label: '平方米',
    value: 1
  },
  {
    label: '亩',
    value: 2
  }
]

// 其他权利情况 0：无，1：抵押，2：共有，9：其它
export const otherRightTyleList = [
  {
    label: '无',
    value: 0
  },
  {
    label: '抵押',
    value: 1
  },
  {
    label: '共有',
    value: 2
  }
]

// 0：草稿,1；审核中，2：未认领，3：已认领，4：挂牌，5：撤牌，6：摘牌，7：成交，8：已中止，9：已终结
export const circulationStatusList: any = {
  0: '草稿',
  1: '审核中',
  2: '未认领',
  3: '已认领',
  4: '挂牌',
  5: '撤牌',
  6: '摘牌',
  7: '成交',
  8: '已中止',
  9: '已终结'
}
