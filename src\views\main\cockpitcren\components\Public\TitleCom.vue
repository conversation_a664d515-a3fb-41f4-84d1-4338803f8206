<template>
  <section class="cockipt-vice-title">
    <h4 class="title">
      <span>{{title}}</span>
      <div v-if="isFullScreen">
        <i v-show="!isModeFull"
          class="full el-icon-full-screen"
          @click="fullScreenHandle(true)" />
        <i v-show="isModeFull"
          class="full el-icon-news"
          @click="fullScreenHandle(false)" />
      </div>
    </h4>
    <i v-if="isThrow"
      class="details"
      @click="jumpToDetailePage" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { isWindowFull } from '@/utils'

@Component
export default class extends Vue {
  @Prop() private title!: string // 标题
  @Prop({ default: true }) private isThrow?: boolean // 是否需要详情箭头
  @Prop({ default: false }) private isFullScreen?: boolean // 是否需要全屏功能
  @Prop({ default: '' }) private module?: string // 要跳转的详情页面，没有则触发emit事件
  @Prop({ default: '0' }) private orgCode?: string // 要跳转的详情页面，定位到哪个集团上

  private isModeFull = false

  // 判断窗口是否处于全屏模式
  get getIsWindowFull() {
    return isWindowFull
  }

  // 进入全屏/退出全屏
  private fullScreenHandle(isFull: boolean) {
    this.isModeFull = isFull

    this.$emit('fullScreenHandle', isFull)
  }

  // 跳转详情页面
  private jumpToDetailePage() {
    if (this.module) {
      this.$router.push({
        name: 'moduleDetail',
        query: { 
          module: this.module,
          orgCode: this.orgCode
        },
        params: { isWindowFull: this.getIsWindowFull() }
      })
    } else {
      this.$emit('titleThowHandle')
    }
  }
}
</script>

<style scoped lang="scss">
.cockipt-vice-title {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  justify-content: space-between;

  @keyframes keyViceTitle {
    0% {
      opacity: 0;
      transform: translateX(100px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  h4,
  p {
    margin: 0;
  }

  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
    font-size: 50px;
    font-style: italic;
    font-weight: normal;
    font-family: 'FZZZHONGHJW';
    padding-right: 100px;
    background: url('../../images/mode_title.png') no-repeat right bottom;
    background-size: auto 100%;
    animation: keyViceTitle 5s ease;
  }

  .details {
    position: relative;
    z-index: 10;
    width: 80px;
    height: 30px;
    background: url('../../images/thow_more.png') no-repeat center center;
    background-size: 100%;
    transition: 0.5s;
    cursor: pointer;
    &:hover {
      transform: translateX(10px);
    }
  }

  .full {
    font-size: 40px;
    margin-left: 20px;
    cursor: pointer;
    &:hover {
      color: #3eeeff;
    }
  }
}
</style>

<style lang="scss">
.cockipt-mode-vice-title {
  position: absolute;
  width: 100%;
  color: #63f1ff;
  font-size: 38px;
  font-weight: bold;
  margin: 0;
  padding-left: 34px;
  background: url('../../images/threw.png') no-repeat left -4px;
  background-size: 24px 100%;
}
</style>