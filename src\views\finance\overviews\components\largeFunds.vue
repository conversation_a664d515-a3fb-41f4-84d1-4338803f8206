<template>
  <section class="echarts-dom-wrap">
    <div v-loading="loading"
      ref="chartDom"
      class="chartDom"
      :class="{ hide: !interfaceData.length }" />

    <el-empty description="暂无数据"
      class="empty-none-data"
      :class="{ none: interfaceData.length }" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { assetsOverYear } from '@/api/assetsv2'
import { Loading } from '@/decorators'
import * as echarts from 'echarts'

let chartDom = null
let myChart: any = null

@Component({})
export default class extends Vue {
  @Prop() private orgCode!: string // 集团code
  @Prop() private year!: string // 年份

  private loading = false
  private yAxisData: string[] = []
  private seriesData: any[] = []
  private interfaceData = []

  // 监听搜索条件改变
  @Watch('orgCode', { deep: true })
  @Watch('year', { deep: true })
  private watchFormData() {
    this.initData()
  }

  // 组件初始化
  private mounted() {
    this.$nextTick(() => {
      chartDom = this.$refs.chartDom
      myChart = echarts.init(chartDom as HTMLElement)

      this.initData()
    })
  }

  // 获取数据
  @Loading('loading')
  private async initData() {
    let { data } = await assetsOverYear({
      orgCode: this.orgCode,
      year: this.year
    })

    this.interfaceData = data || []

    // let data = [
    //   {
    //     year: '2019',
    //     occupancyRate: 77,
    //     vacancyRate: 23
    //   },
    // ]

    // 组装数据
    let yAxisData: string[] = []
    let vacancyList: number[] = []
    let occupancyList: number[] = []
    Array.isArray(data) &&
      data.forEach((item) => {
        yAxisData.push(item.year)
        vacancyList.push(+item.vacancyRate)
        occupancyList.push(+item.occupancyRate)
      })

    this.yAxisData = yAxisData
    this.seriesData = [
      {
        name: '出租率',
        type: 'bar',
        stack: 'total',
        label: {
          show: true
        },
        data: occupancyList
      },
      {
        name: '空置率',
        type: 'bar',
        stack: 'total',
        label: {
          show: true
        },
        data: vacancyList
      }
    ]
    this.initEcharts()
  }

  // 初始化echarts组件
  private initEcharts() {
    let yAxisData = this.yAxisData
    let seriesData = this.seriesData
    let list: any = [
      {
        name: '城投集团',
        value: Math.ceil(Math.random() * 100),
        num: Math.ceil(Math.random() * 100)
      },
      {
        name: '国资运营',
        value: Math.ceil(Math.random() * 100),
        num: Math.ceil(Math.random() * 100)
      },
      {
        name: '交投集团',
        value: Math.ceil(Math.random() * 100),
        num: Math.ceil(Math.random() * 100)
      },
      {
        name: '金投集团',
        value: Math.ceil(Math.random() * 100),
        num: Math.ceil(Math.random() * 100)
      },
      {
        name: '轨道集团',
        value: Math.ceil(Math.random() * 100),
        num: Math.ceil(Math.random() * 100)
      }
    ]
    let colors = ['#5793f3', '#d14a61', '#675bba']

    let option = {
      title: {
        text: ''
      },
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: '5%',
        right: '10%',
        bottom: '2%',
        top: '8%',
        containLabel: true
      },
      legend: {},

      xAxis: {
        type: 'category',
        data: list.map((res: any) => {
          return res.name
        })
      },
      yAxis: [
        {
          type: 'value',
          min: 0,
          max: 100,
          nameRotate: 90,
          nameGap: 50,
          nameLocation: 'middle',
          axisLine: {
            lineStyle: {
              color: colors[0]
            }
          },
          axisLabel: {
            formatter: '{value}万'
          }
        },
        {
          name: '数量',
          type: 'value',
          min: 0,
          max: 25,
          nameRotate: 90,
          nameGap: 50,
          nameLocation: 'middle',
          axisLine: {
            lineStyle: {
              color: colors[2]
            }
          }
        }
      ],
      series: [
        {
          name: '大额资金出借(万)',
          type: 'bar',
          data: list.map((res: any) => {
            return res.value
          }),

          barWidth: '40%',
          label: {
            show: true,
            position: 'top'
          }
        },
        {
          name: '数量',
          type: 'line',
          symbol: 'circle',
          smooth: true,
          data: list.map((res: any) => {
            return res.num
          }),
          label: {
            show: true,
            position: 'top'
          }
        }
      ]
    }

    myChart && myChart.setOption && myChart.setOption(option)
  }
}
</script>

<style scoped lang="scss">
.chartDom {
  width: 100%;
  height: 100%;
}
</style>
