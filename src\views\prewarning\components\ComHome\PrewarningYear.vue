<template>
  <div class="graph-year">
    <graph-title title="集团年度预警数量" />
    <div ref="graph"
      class="graph"></div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { getEventStatistics } from '@/api/prewarning'
import * as echarts from 'echarts'
import GraphTitle from './GraphTitle.vue'

@Component({
  components: {
    GraphTitle
  }
})
export default class PrewarningYear extends Vue {
  private async getEventStatistics() {
    try {
      let res = await getEventStatistics({})
    } catch (e) {
      //
    }
  }
  private option = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '红色预警',
        type: 'line',
        symbol: 'circle',
        stack: 'Total',
        showSymbol: false,
        data: [120, 132, 101, 134, 90, 230, 210, 120, 132, 101, 134, 90],
        itemStyle: {
          color: '#FF8888'
        }
      },
      {
        name: '黄色预警',
        type: 'line',
        symbol: 'circle',
        showSymbol: false,
        stack: 'Total',
        data: [220, 182, 191, 234, 290, 330, 310, 120, 132, 101, 134, 90],
        itemStyle: {
          color: '#FFE29B'
        }
      }
    ]
  }

  private async mounted() {
    await this.getEventStatistics()
    let chart = await echarts.init(this.$refs.graph as any)
    chart.setOption(this.option)
    let count = 0
    setInterval(() => {
      chart.dispatchAction({
        type: 'showTip',
        seriesIndex: 0,
        dataIndex: count
      })
      if (count === 11) {
        count = 0
      } else {
        ++count
      }
    }, 2000)
  }
}
</script>

<style lang="scss" scoped>
.graph-year {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  .graph {
    flex: 1;
  }
}
</style>