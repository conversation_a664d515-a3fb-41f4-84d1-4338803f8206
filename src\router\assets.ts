/* 资产监管 */

import Layout from '@/layouts/public/index.vue'

export const routes = [
  {
    path: '/assets',
    name: '资产监管',
    component: Layout,
    redirect: "/asstes/overviews",
    children: [
      {
        path: 'home',
        name: '资产首页',
        component: () => import('@/views/assets/page/home/<USER>')
      },
      {
        path: 'overviews',
        name: '数据看板',
        component: () => import('@/views/assets/page/overviews/index.vue')
      },
      {
        path: 'moduledetail',
        name: '资产驾驶舱',
        props: (route: any) => ({ moduleName: route.query.module }),
        meta: { title: '资产驾驶舱', hideTab: true, hasAside: true },
        component: () => import('@/views/main/cockpitSecondary/moduleDetails/index.vue')
      },
      {
        path: 'distributed',
        name: '资产地图',
        // component: () => import('@/views/assets/page/distributed/index.vue')
        component: () => import('@/views/assets/page/distributed/indexTdt.vue')
      },
      {
        path: 'manage',
        name: '资产运营',
        component: () => import('@/views/assets/page/manage/index.vue')
      },
      {
        path: 'manControl',
        name: '资产管理',
        component: () => import('@/views/assets/page/manControl/index.vue')
      },
      {
        path: 'realEstateLand',
        name: '房产土地信息',
        component: () => import('@/views/assets/page/realEstateLand/index.vue')
      },
      {
        path: 'propertyRights',
        name: '资产信息',
        component: () => import('@/views/assets/page/propertyRights/index.vue'),
        // redirect: '/assets/propertyRights/houseCertificate',
        // children: [
        //   {
        //     path: 'houseCertificate',
        //     name: '房产证',
        //     component: () => import('@/views/assets/page/propertyRights/HouseCertificate.vue')
        //   },
        //   {
        //     path: 'landCertificate',
        //     name: '土地证书',
        //     component: () => import('@/views/assets/page/propertyRights/LandCertificate.vue')
        //   },
        //   {
        //     path: 'immovableCertificate',
        //     name: '不动产证书',
        //     component: () => import('@/views/assets/page/propertyRights/ImmovableCertificate.vue')
        //   }
        // ]
      },
      {
        path: 'LeaseContract',
        name: '租赁合同管理',
        component: () => import('@/views/assets/page/LeaseContract/index.vue')
      },
      {
        path: 'tradeManagement',
        name: '资产交易管理',
        component: () => import('@/views/assets/page/tradeManagement/index.vue')
      },
      {
        path: 'organization',
        name: '组织架构管理',
        component: () => import('@/views/assets/page/organization/index.vue')
      },
      {
        path: 'tradeReporting',
        name: '资产交易申报',
        component: () => import('@/views/assets/page/tradeReporting/index.vue')
      },
      // v2  page router 
      {
        path: 'Subjectmanagement',
        name: '标的管理',
        component: () => import('@/views/assets/v2page/Subjectmanagement/index.vue')
      },
      {
        path: 'contractManagement',
        name: '合同管理',
        component: () => import('@/views/assets/v2page/contractManagement/index.vue')
      },
      {
        path: 'targetTransaction',
        name: '合同管理',
        component: () => import('@/views/assets/v2page/TargetTransaction/index.vue')
      },

      {
        path: 'astSummary',
        name: '交易汇总',
        component: () => import('@/views/assets/page/astSummary/index.vue')
      },
      {
        path: 'astDisposal',
        name: '资产处置',
        component: () => import('@/views/assets/page/astDisposal/index.vue')
      },
      {
        path: 'prewarningSummary',
        name: '预警汇总',
        component: () => import('@/views/assets/page/prewarningSummary/index.vue')
      },

      {
        path: 'prewarningEvents',
        name: '预警记录',
        meta: {
          moduleType: 1 // 预警事件，用来区分是否筛选出资产部分的参数
        },
        component: () => import('@/views/prewarning/events.vue')
      },

      {
        path: 'rentArrears',
        name: '租金欠缴预警',
        component: () => import('@/views/assets/v2page/warningRecord/rentArrears.vue')
      },
      {
        path: 'idleAssets',
        name: '资产闲置预警',
        component: () => import('@/views/assets/v2page/warningRecord/idleAssets.vue')
      },
      {
        path: 'unregistered',
        name: '长租未备案预警',
        component: () => import('@/views/assets/v2page/warningRecord/unregistered.vue')
      },

      {
        path: 'priorFiling',
        name: '事前备案',
        component: () => import('@/views/assets/page/priorFiling/index.vue')
      },

      {
        path: 'postFiling',
        name: '事后备案',
        component: () => import('@/views/assets/page/postFiling/index.vue')
      },
      // 报表
      {
        path: 'rentArrearsOnemonth',
        name: '租金欠缴超一个月',
        component: () => import('@/views/assets/v2page/statisticalReport/rentArrearsOnemonth.vue')
      },
      {
        path: 'theLeaseTermExceedsFiveYears',
        name: '租期超五年',
        component: () => import('@/views/assets/v2page/statisticalReport/theLeaseTermExceedsFiveYears.vue')
      },
      {
        path: 'limitToMoreThanThreeMonths',
        name: '闲置超三月',
        component: () => import('@/views/assets/v2page/statisticalReport/limitToMoreThanThreeMonths.vue')
      },
      {
        path: 'nonMobilityTransactionTable',
        name: '非公开交易表',
        component: () => import('@/views/assets/v2page/statisticalReport/nonMobilityTransactionTable.vue')
      },
      {
        path: 'nonAssessmentBalanceSheet',
        name: '非评估资产表',
        component: () => import('@/views/assets/v2page/statisticalReport/nonAssessmentBalanceSheet.vue')
      },
      {
        path: 'propertySummary',
        name: '房产监管汇总表',
        component: () => import('@/views/assets/v2page/statisticalReport/propertySummary.vue')
      },

      // 备案管理
      {
        path: 'filingManagement/beforehand',
        name: '事前备案',
        component: () => import('@/views/assets/v2page/filingManagement/beforehand.vue')
      },
      {
        path: 'filingManagement/afterwards',
        name: '事后备案',
        component: () => import('@/views/assets/v2page/filingManagement/afterwards.vue')
      },
      {
        path: 'filingManagement/system',
        name: '制度备案',
        component: () => import('@/views/assets/v2page/filingManagement/system.vue')
      },
    ]
  }
]

export default routes