<template>
  <el-container class="container"
    direction="vertical">
    <SearchBar v-if="isSearch"
      :items="searchItems"
      @onSearch="handleSearch">
      <el-button v-loading="loadingExport"
        :disabled="!gridData.length"
        type="primary"
        @click="exportHandle">导 出</el-button>
    </SearchBar>

    <Grid ref="grid"
      :show-pagination="true"
      :overflow-tooltip="true"
      :remote-url="remoteUrl"
      :columns="columns"
      :search-params="searchParams"
      @onLoaded="onLoadedHandle" />
  </el-container>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { BusinessModule } from '@/store/modules/businessDict'
import { getAstCompTree } from '@/api/projectInvestment'
import { downloadXls } from '@/utils'
import { ExportFree1Month } from '@/api/assetsv2'
import { Loading, Confirm } from '@/decorators'
import Grid from '@/components/Grid/index.vue'
import SearchBar from '@/components/SearchBar/index.vue'

@Component({
  components: {
    Grid,
    SearchBar
  }
})
export default class HouseCertificate extends Vue {
  private isSearch = false
  private compTree = []
  private loadingExport = false

  // 详细表格数据
  private appendixsLength!: number
  private Diadetaillist: any = {}
  private gridData: any[] = []
  private visvileDetail = false
  private visvileDetailif = false
  private searchParams: any = {}
  private remoteUrl = '/fht-monitor/ast/report/unpaid-one-month'
  private searchItems: any[] = [
    {
      type: 'text',
      key: 'keyword',
      placeholder: '编号/房产名称/出租方/承租方',
      width: '300px'
    },
    {
      type: 'select',
      key: 'orgCode',
      placeholder: '直属单位',
      width: '150px',
      options: this.compTree
    }
  ]
  private columns = [
    {
      label: '直属单位',
      prop: 'orgName'
    },
    {
      label: '出租方',
      prop: 'lessorName'
    },
    {
      label: '资产编号',
      prop: 'itemNo'
    },
    {
      label: '费用类型',
      prop: 'billTypeDesc'
    },
    {
      label: '资产名称',
      prop: 'itemName'
    },
    {
      label: '资产地址',
      prop: 'location'
    },
    {
      label: '出租面积(m²)',
      prop: 'totalRentArea'
    },
    {
      label: '租期',
      prop: 'period'
    },
    {
      label: '租金逾期时间',
      prop: 'deadlineDate'
    },
    {
      label: '逾期天数',
      prop: 'overDueDay'
    },
    {
      label: '欠缴金额(元)',
      prop: 'unpaidFee'
    },
    {
      label: '承租方',
      prop: 'renterName'
    }
  ]

  created() {
    this.initQuery()
    this.getCompTree()
  }

  // 获取字典数据
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }

  // 根据链接赋值相关字段
  private initQuery() {
    let { orgCode } = this.$route.query
    if (orgCode) {
      let findIndex = this.searchItems.findIndex((item) => {
        return item.key === 'orgCode'
      })
      this.searchItems[findIndex].value = orgCode
      this.searchParams.orgCode = orgCode
    }
  }

  //获取机构数组
  private async getCompTree() {
    let options = []
    let res = await getAstCompTree({ parentId: 0, deptCategory: 1 })
    this.isSearch = false
    if (res.success) {
      let options = res.data.map((res: any) => {
        return {
          label: res.deptName,
          value: res.deptCode
        }
      })
      await this.$nextTick(() => {
        Object.assign(this.compTree, options)
        this.isSearch = true
      })
    }
  }

  // 改变详情显示隐藏
  private changeShowDialogDetail(state: boolean) {
    this.visvileDetailif = state
    this.$nextTick(() => {
      this.visvileDetail = state
    })
  }

  // 导出
  @Loading('loadingExport')
  @Confirm({
    title: '提示',
    content: `是否确认导出数据`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private async exportHandle() {
    let res = await ExportFree1Month(this.searchParams)
    let time = new Date().getTime()
    downloadXls(res.data, ` 租金欠缴超一个月_${time}.xlsx`)
    this.$message.success(res.msg || '导出成功')
  }

  // 列表搜索
  private handleSearch(condition: any) {
    this.searchParams = condition
    ;(this.$refs['grid'] as any).refresh()
  }

  // 表格数据加载完成后触发
  private onLoadedHandle(data: any) {
    this.gridData = data.records || []
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
}
</style>
