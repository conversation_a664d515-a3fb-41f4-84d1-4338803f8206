/** 指数总览 */

<template>
  <div class="index-overview">
    <div v-for="(item, index) in tabsList"
      :key="index"
      class="index-overview__item">
      <div class="item__top">
        <template v-if="+item.value">
          <CountTo :startVal='0'
            :endVal='+bigNumberFormat(item).value'
            :decimals="item.unit === '处' ? 0 : 2"
            :duration='2000' />
          <span class="unit">{{ bigNumberFormat(item).unit }}</span>
        </template>

        <span v-else>-</span>
      </div>
      <div class="item__bottom">
        <div>{{ item.label }}</div>
        <div
          :class="['item__percent', item.percent > 0 && 'item__percent--up', item.percent < 0 && 'item__percent--down']">
          <img class="icon"
            v-if="item.percent > 0"
            src="../../images/thows.png" />
          <img class="icon"
            v-if="item.percent < 0"
            src="../../images/thowx.png" />
          <span>{{ item.percent }}%</span>
        </div>
      </div>

    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import CommonModuleWrapper from '@/views/main/cockpitSecondary/components/CommonModuleWrapper.vue'
import CountTo from 'vue-count-to'
import { bigNumberFormat } from '@/utils'
import { Loading } from '@/decorators'
import { capitalProfile } from '@/api/cockpit'
import { companyList } from '../../baseData'

export interface IndexItem {
  label: string
  value: string | number
  percent: number
  unit: string
}

@Component({
  components: {
    CommonModuleWrapper,
    CountTo
  }
})
export default class extends Vue {
  @Watch('currentCompanyId', { immediate: true })
  private onCurrentCompanyIdChange() {
    this.filterData()
  }

  private currentYear = '2022'
  private currentCompanyIndex = '0'
  private dataList: any[] = []
  private companyList = companyList
  private indexList: IndexItem[] = [
    {
      label: '资产总额',
      value: '1200',
      percent: 0,
      unit: '亿元'
    },
    {
      label: '资产净额',
      value: '1220',
      percent: 0,
      unit: '亿元'
    },
    {
      label: '资产总数',
      value: '9312',
      percent: 0,
      unit: '处'
    },
    {
      label: '营业收入',
      value: '776.4',
      percent: 0,
      unit: '亿元'
    },
    {
      label: '利润总额',
      value: '734',
      percent: 0,
      unit: '亿元'
    }
  ]

  private tabsList: any[] = [
    {
      label: '资产总额',
      value: '',
      percent: 0,
      unit: '亿元',
      prop: 'asset_amount'
    },
    {
      label: '资产净额',
      value: '',
      percent: 0,
      unit: '亿元',
      prop: 'asset_net'
    },
    {
      label: '资产总数',
      value: '',
      percent: 0,
      unit: '处',
      prop: 'asset_count'
    },
    {
      label: '营业收入',
      value: '',
      percent: 0,
      unit: '亿元',
      prop: 'turnover'
    },
    {
      label: '利润总额',
      value: '',
      percent: 0,
      unit: '万元',
      prop: 'profit_amount'
    }
  ]

  get currentCompanyId(): any {
    return this.$route.query ? this.$route.query.id || '' : ''
  }

  get bigNumberFormat() {
    return (item: any) => {
      return item.unit === '%' || item.unit === '处'
        ? {
            value: item.value,
            unit: item.unit
          }
        : bigNumberFormat(item.value)
    }
  }

  created() {
    this.$bus.$on('BusChangeCascader', (data: string[]) => {
      this.currentYear = data[0]
      this.filterData()
    })
    this.capitalProfile()
    this.filterData()
  }

  // 获取财务指标
  private async capitalProfile() {
    let res = await capitalProfile()
    if (res.success) {
      this.dataList = res.data || []
      this.filterData()
    }
  }

  private filterData() {
    // 筛选出当前年份数据
    let currentYearList = this.dataList.filter((item) => item.year === +this.currentYear)

    // 筛选出 tabsList 数据
    this.tabsList.forEach((tab) => {
      // 筛选出国资总况数据
      let currentCompany: any = this.companyList.find((item) => item.id === this.currentCompanyId) || {
        code: '0'
      }
      let currentCompanyData = currentYearList.find((item) => item.itemCode === tab.prop && item.companyCode === currentCompany.code) || {
        itemDesc: '0.00',
        itemName: '',
        itemValue: ''
      }
      tab.value = tab.unit === '%' ? (+currentCompanyData.itemValue).toFixed(2) : currentCompanyData.itemValue
      tab.percent = +(+currentCompanyData.itemDesc * 100).toFixed()
    })
  }
}
</script>

<style scoped lang="scss">
.index-overview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 158px;
  // margin-top: 20px;
  // background: #666;
  &__item {
    position: relative;
    width: 280px;
    height: 180px;
    background: url('../../images/iconbg.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    // padding: 10px 0;
    color: #fff;
    cursor: pointer;
    &:hover {
      background: url('../../images/iconbg2.png') no-repeat;
      background-size: 100% 100%;
    }
    .item {
      &__top,
      &__bottom {
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
      }

      &__top {
        height: 92px;
        font-size: 50px;
        display: flex;
        align-items: flex-end;
        font-weight: normal;
        font-family: 'PangMenZhengDao';
        .unit {
          margin-left: 8px;
          font-size: 26px;
        }
      }
      &__bottom {
        font-size: 30px;
        color: rgba(255, 255, 255, 0.8);
        vertical-align: middle;
        white-space: nowrap;
        padding-top: 28px;
      }
      &__percent {
        font-family: 'digital-7';
        margin-left: 10px;
        font-size: 30px;
        display: flex;
        align-items: center;
        img {
          width: 20px;
          margin-right: 3px;
        }
        &--up {
          color: #ff6267;
        }
        &--down {
          color: #00bc94;
        }
      }
    }
  }
}
</style>