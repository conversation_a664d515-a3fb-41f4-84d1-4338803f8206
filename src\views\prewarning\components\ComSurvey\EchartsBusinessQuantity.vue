<template>
  <section class="echarts-dom-wrap">
    <div v-loading="loading"
      ref="chartDom"
      class="chartDom"
      :class="{'hide': !interfaceData.length}" />

    <el-empty description="暂无数据"
      class="empty-none-data"
      :class="{'none': interfaceData.length}" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { getEventStatistics } from '@/api/prewarning'
import { Loading } from '@/decorators'
import * as echarts from 'echarts'

let chartDom = null
let myChart: any = null

@Component({})
export default class extends Vue {
  @Prop() private year!: string // 年份
  @Prop() private isAssets?: false // 判断是否是资产中的“预警汇总”模块

  private loading = false
  private xAxisData: string[] = []
  private legendData: string[] = []
  private seriesData: any[] = []
  private interfaceData: any[] = []

  // 监听搜索条件改变
  @Watch('year', { deep: true })
  private watchFormData() {
    this.initData()
  }

  // 组件初始化
  private mounted() {
    this.$nextTick(() => {
      chartDom = this.$refs.chartDom
      myChart = echarts.init(chartDom as HTMLElement)

      this.initData()
    })
  }

  // 获取数据
  @Loading('loading')
  private async initData() {
    // let { data } = await getEventStatistics({
    //   year: this.year
    // })

    let data: any[] = [
      {
        name: '红色预警',
        list: [
          {
            name: '资产',
            value: 12
          },
          {
            name: '负债',
            value: 8
          },
          {
            name: '欠租',
            value: 6
          },
          {
            name: '合同',
            value: 10
          },
          {
            name: '收款',
            value: 9
          }
        ]
      },
      {
        name: '黄色预警',
        list: [
          {
            name: '资产',
            value: 2
          },
          {
            name: '负债',
            value: 4
          },
          {
            name: '欠租',
            value: 6
          },
          {
            name: '合同',
            value: 11
          },
          {
            name: '收款',
            value: 3
          }
        ]
      }
    ]

    this.interfaceData = data || []

    // 组装数据
    let legendData: string[] = []
    let xAxisData: string[] = []
    let seriesData: any[] = []
    let list: string[] = []

    Array.isArray(data) &&
      data.forEach((item) => {
        legendData.push(item.name)

        let datas: number[] = []
        if (Array.isArray(item.list) && item.list.length) {
          list = []
          item.list.forEach((itemList: { name: string; value: number }) => {
            list.push(itemList.name)
            datas.push(+itemList.value)
          })
        }

        seriesData.push({
          name: item.name,
          type: 'bar',
          stack: 'Total',
          barWidth: 30,
          tooltip: {
            valueFormatter: function (value: number) {
              return value + ' 个'
            }
          },
          label: {
            show: true
          },
          data: datas
        })
      })

    xAxisData = list

    this.legendData = legendData
    this.xAxisData = xAxisData
    this.seriesData = seriesData
    this.initEcharts()
  }

  // 初始化echarts组件
  private initEcharts() {
    let xAxisDaa = this.xAxisData
    let legendData = this.legendData
    let seriesData = this.seriesData

    let option = {
      color: ['#D54941', '#E99C22'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        valueFormatter: function (value: string) {
          return value + ' 个'
        }
      },
      grid: {
        top: '14%',
        left: '1%',
        right: '4%',
        bottom: '0%',
        containLabel: true
      },
      legend: {
        data: legendData
      },
      xAxis: [
        {
          type: 'category',
          data: xAxisDaa,
          label: {
            show: true,
            position: 'top'
          },
          axisPointer: {
            type: 'shadow'
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: '数量',
          nameTextStyle: {
            color: '#999',
            fontSize: 12,
            align: 'left'
          }
        }
      ],
      series: seriesData
    }

    myChart && myChart.setOption && myChart.setOption(option)
  }
}
</script>

<style scoped lang="scss">
.chartDom {
  width: 100%;
  height: 100%;
}
</style>
