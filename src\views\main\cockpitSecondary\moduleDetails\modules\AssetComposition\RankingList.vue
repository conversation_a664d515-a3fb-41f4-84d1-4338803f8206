<template>
  <div id="ProjectProfitabilityRanking" />
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { ProjectProfitabilityRankingData, echartConfigure } from '@/views/main/cockpitSecondary/baseData'
import { capitalProfile } from '@/api/cockpit'
import * as echarts from 'echarts'
import { bigNumberFormat, deepClone } from '@/utils'

type EChartsOption = echarts.EChartsOption

@Component
export default class extends Vue {
  @Prop() private seriesData!: any[]
  @Prop() private companyList!: any[]
  @Prop({ default: '万元' }) private unit!: string

  private timer: any
  private loading = false
  private maxValue = 100
  private year = ''
  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}
  private tabList = []
  private echartsDatas: any[] = []

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('ProjectProfitabilityRanking') as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    // this.initData()
    this.initEcharts()
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await capitalProfile()
    if (!Array.isArray(data) || !data.length) return
    this.echartsDatas = data
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 初始化数据
  private initEcharts() {
    let textSize = 22
    let series = this.seriesData.map((item) => {
      // 设置圆角方向
      if (item.value < 0) {
        item.itemStyle = {
          borderRadius: [20, 0, 0, 20]
        }
        item.backgroundStyle = {
          borderRadius: [20, 0, 0, 20]
        }
      }
      return item
    })
    let yeas = ~~this.getMomentTime()
    this.option = {
      color: [
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(239, 149, 43, 0.2)' // 0% 处的颜色
            },
            {
              offset: 1,
              color: 'rgba(239, 109, 43, 1)' // 100% 处的颜色
            }
          ]
        },
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: '#7E358B' // 0% 处的颜色
            },
            {
              offset: 1,
              color: '#EC342F' // 100% 处的颜色
            }
          ]
        }
      ],
      axisPointer: {
        show: false
      },
      title: {
        text: '前十大工程排名',
        top: 10,
        left: 10,
        textStyle: {
          color: '#fff',
          fontWeight: 'bold',
          fontSize: 40
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 103, 177, 1)',
        borderColor: 'rgba(0, 103, 177, 1)',
        textStyle: {
          color: '#fff',
          fontSize: textSize
        }
      },
      legend: {
        show: false
      },
      grid: {
        top: '14%',
        left: '0%',
        right: '0%',
        bottom: '0%',
        containLabel: true
      },
      xAxis: {
        show: false,
        type: 'value',
        axisLabel: {
          color: '#5db0ea',
          fontSize: textSize
        },
        max: (value: { max: number }) => {
          return value.max * (1 + 0.2)
        }
      },
      yAxis: {
        type: 'category',
        axisLabel: {
          color: '#5db0ea',
          fontSize: textSize,
          shadowBlur: 10,
          shadowColor: '#0B388F',
          backgroundColor: '#0B388F66',
          borderRadius: 6,
          formatter: (value: any, index: number) => {
            return `{a|${series.length - index === 10 ? '' : '0'}${series.length - index}} {b|${value}}`
          },
          rich: {
            a: {
              fontStyle: 'italic',
              fontWeight: 'bold',
              color: '#4A97F8',
              fontSize: textSize * 1.4,
              lineHeight: textSize * 1.4
              //padding: 4
            },
            b: {
              fontWeight: 'bold',
              fontSize: textSize * 1.4,
              lineHeight: textSize * 1.4,
              color: 'rgba(255, 255, 255, 0.6)'
              //padding: 4
            }
          }
        },
        data: this.companyList
      },
      series: [
        {
          name: '资产利用情况',
          type: 'bar',
          stack: 'total',
          selectedMode: 'single',
          barWidth: 16,
          label: {
            show: true,
            fontSize: textSize * 1.6,
            fontWeight: 'bold',
            color: '#fff',
            textBorderColor: 'RGBA(233, 60, 167, 1)',
            position: 'right',
            formatter: ({ data }: any) => {
              let formatData =
                this.unit === '%'
                  ? {
                      value: data,
                      unit: '%'
                    }
                  : bigNumberFormat(+(+data).toFixed(2))
              return `{a|${formatData.value}}{b|${formatData.unit || this.unit}}`
            },
            rich: {
              a: {
                fontSize: 38,
                color: '#fff',
                fontWeight: 'bold',
                opacity: 0.8,
                shadowBlur: 10,
                shadowColor: '#000',
                fontFamily: 'digital-7'
              },
              b: {
                fontSize: 30,
                padding: 4,
                fontWeight: 'bold',
                shadowBlur: 10,
                shadowColor: '#000'
              }
            }
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'RGBA(233, 60, 167, 0)'
                  },
                  {
                    offset: 0.4,
                    color: 'RGBA(233, 60, 167, 0.3)'
                  },
                  {
                    offset: 1,
                    color: 'RGBA(233, 60, 167, 1)'
                  }
                ]
              },
              shadowColor: 'RGBA(233, 60, 167, 0.6)',
              shadowBlur: 30
            }
          },
          select: {
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'RGBA(233, 60, 167, 0)'
                  },
                  {
                    offset: 0.4,
                    color: 'RGBA(233, 60, 167, 0.3)'
                  },
                  {
                    offset: 1,
                    color: 'RGBA(233, 60, 167, 1)'
                  }
                ]
              },
              shadowColor: 'RGBA(233, 60, 167, 0.6)',
              shadowBlur: 30
            }
          },
          itemStyle: {
            borderRadius: [0, 20, 20, 0],
            shadowBlur: 0,
            shadowColor: 'rgba(245, 216, 18, 0.6)'
          },
          showBackground: true,
          backgroundStyle: {
            color: 'rgba(54, 116, 214, 0.3)',
            borderRadius: [0, 20, 20, 0]
          },
          data: series
        }
      ]
    }

    this.myChart && this.myChart.setOption && this.myChart && this.myChart.setOption(this.option)
    this.change()
  }
  // 年份切换
  private yearHandler(year: string) {
    this.year = year
    // this.initData()
  }

  private change() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    this.timer = setInterval(() => {
      let index = Math.max(Math.floor(Math.random() * 4), 0)

      this.myChart &&
        this.myChart.dispatchAction({
          type: 'select',
          seriesIndex: 0,
          dataIndex: index
        })
    }, 5000)
  }

  destroyed() {
    clearInterval(this.timer)
  }
}
</script>

<style scoped lang="scss">
#ProjectProfitabilityRanking {
  width: 100%;
  height: 500px;
}
</style>