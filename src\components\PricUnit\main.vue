<template>
  <section class="crud-unit-wrap">
    <el-select v-model="unit"
      :size="size"
      placeholder="请选择"
      @change="changUnit">
      <el-option v-for="item of dataList"
        :key="item.value"
        :label="item.label"
        :value="item.value" />
    </el-select>
  </section>
</template>

<script>
export default {
  props: {
    // v-bind 绑定默认值
    value: {
      type: [Number, String],
      default: ''
    },

    // 显示数据类型（默认万元）
    type: {
      type: Number,
      default: 2
    },

    // 按钮尺寸
    size: {
      type: String,
      default: 'mini'
    }
  },
  data() {
    return {
      dataList: [],
      unit: this.value
    }
  },
  watch: {
    value(val) {
      this.unit = val
    }
  },
  created() {
    switch (+this.type) {
      case 1:
        this.dataList = [
          {
            label: '单位：元',
            value: 1
          },
          {
            label: '单位：万元',
            value: 2
          },
          {
            label: '单位：亿元',
            value: 3
          }
        ]
        break
      case 3:
        this.dataList = [
          {
            label: '单位：元',
            value: 1
          },
          {
            label: '单位：万元',
            value: 2
          }
        ]
        break
      case 2:
        this.dataList = [
          {
            label: '单位：万元',
            value: 2
          },
          {
            label: '单位：亿元',
            value: 3
          }
        ]
        break
      default:
        this.dataList = [
          {
            label: '万元',
            value: 2
          },
          {
            label: '亿元',
            value: 3
          }
        ]
        break
    }
  },
  methods: {
    // 切换单位时触发父组件更新 updataHandle 事件
    changUnit(value) {
      this.$emit('updataHandle', value)
    }
  }
}
</script>

<style scoped lang="scss">
.crud-unit-wrap {
  display: inline-block;
  width: 120px;
  margin-right: 8px;
}
</style>
