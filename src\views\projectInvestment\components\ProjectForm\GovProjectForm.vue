<template>
  <Dialog title="政府投资项目计划"
    width="1000px"
    :visible="visible"
    @close="handleClose"
    @open="changeList">
    <div slot="body">
      <el-form ref="ProjectForm"
        :rules="projectFormRules"
        :model="projectForm"
        inline
        label-width="100px">
        <el-form-item label="备案编号"
          prop="recordNumber">
          <el-input disabled
            placeholder="自动生成"
            v-model="recordsForm.recordNumber" />
        </el-form-item>
        <el-form-item label="填报单位"
          prop="createDept">
          <el-input v-model="recordsForm.createDept" />
        </el-form-item>
        <el-form-item label="年份"
          prop="year">
          <el-date-picker v-model="recordsForm.year"
            type="year"
            value-format="yyyy"
            :clearable="true" />
        </el-form-item>
      </el-form>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { ElForm } from 'node_modules/element-ui/types/form'
import { Component, Prop, Vue } from 'vue-property-decorator'

export interface GovForm {
  nextYearlyCentralBudgetSpecial: string // 本年中央预算内专项
  nextYearlyCityFiscalFunds: string // 本年市级财政性资金
  nextYearlyDebt: string // 本年负债资金
  nextYearlyDistrictFiscalFunds: string // 本年区级财政性资金
  nextYearlyOtherFunds: string // 本年其他资金
  nextYearlyPlannedBudgetAmount: string // 本年财务数
  nextYearlyPlannedInvestment: string // 本年计划投资额
  nextYearlyProgressTarget: string // 建设进度目标
  nextYearlyProvincialFiscalFunds: string // 本年省级财政性资金
  projectAddress: string // 建设地址
  projectCategory: string // 类型
  projectCode: string // 项目编号
  projectContent: string // 项目规模及主要内容
  projectFullName: string // 项目名称
  projectProperty: string // 项目分类(经营/功能)
  remark: string // 备注
  startEndDate: string // 建设起止年限
  thisYearlyAccumulatedBudgetAmount: string // 预计头年年底累计完成投资财务数
  thisYearlyAccumulatedInvestment: string // 预计头年年底累计完成投资
  totalInvestment: string // 总投资额
  year: string // 本年计划年份
}

@Component
export default class extends Vue {
  @Prop({}) private visible!: boolean
  public projectForm: GovForm = {
    nextYearlyCentralBudgetSpecial: '',
    nextYearlyCityFiscalFunds: '',
    nextYearlyDebt: '',
    nextYearlyDistrictFiscalFunds: '',
    nextYearlyOtherFunds: '',
    nextYearlyPlannedBudgetAmount: '',
    nextYearlyPlannedInvestment: '',
    nextYearlyProgressTarget: '',
    nextYearlyProvincialFiscalFunds: '',
    projectAddress: '',
    projectCategory: '',
    projectCode: '',
    projectContent: '',
    projectFullName: '',
    projectProperty: '',
    remark: '',
    startEndDate: '',
    thisYearlyAccumulatedBudgetAmount: '',
    thisYearlyAccumulatedInvestment: '',
    totalInvestment: '',
    year: ''
  }

  private projectFormRules = {}

  public validate(): Promise<boolean> {
    let projectForm = this.$refs['ProjectForm'] as ElForm
    return projectForm.validate()
  }
}
</script>


<style scoped lang="scss">
</style>