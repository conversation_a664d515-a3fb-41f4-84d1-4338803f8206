<template>
  <section v-loading="loading"
    class="survey-summer-wrap"
    :class="{'survey-summer-multi-wrap' : isMultiLine}">
    <div v-if="title"
      class="title-box">
      <h4>{{title}}</h4>
    </div>

    <div v-if="seriesData.length"
      class="summary-box">
      <div v-for="(item, index) of seriesData"
        :key="index"
        class="mode"
        @click.stop="clickHandle(item)">
        <i v-if="isIcon"
          class="icon-document el-icon-document-copy" />

        <div v-if="isTb"
          class="tb">
          <i v-if="item[propTb] > 0"
            class="el-icon-top top">{{ item[propTb]}} <span>%</span></i>
          <i v-else-if="item[propTb] < 0"
            class="el-icon-top down">{{ item[propTb]}} <span>%</span></i>
        </div>

        <div class="info">
          <strong>
            <span>{{ item[propTotal]}}</span>
            <i v-if="propUnitObj.unit">{{ propUnitObj.unit }}</i>
            <i v-else>{{ item[propUnit] }}</i>
          </strong>
          <div class="text">
            <span>{{ item[propName]}}</span>
            <i v-if="isRouter"
              class="el-icon-arrow-right" />
          </div>
        </div>
      </div>
    </div>

    <div v-else
      class="none-data">暂无数据</div>
  </section>
</template>

<script>
import _ from 'lodash'
import request from '@/api/request.ts'

export default {
  props: {
    // 是否多行展示（数据量比较多时使用多行展示）
    isMultiLine: {
      type: Boolean,
      default: false
    },

    // 直接渲染提供的数据
    data: {
      type: Array,
      default: () => {
        return []
      }
    },

    // 展示标题
    title: {
      type: String,
      default: ''
    },

    // 请求接口
    url: {
      type: String,
      default: ''
    },

    // 请求接口方式
    method: {
      type: String,
      default: 'post'
    },

    // 请求参数（改变时会触发接口重新请求，达到更新数据的作用）
    params: {
      type: Object,
      default: () => {
        return {}
      }
    },

    // 是否展示同比数据
    isTb: {
      type: Boolean,
      default: false
    },

    // 是否展示icon图标
    isIcon: {
      type: Boolean,
      default: true
    },

    // 数据返回的同比对应的 key
    propTb: {
      type: String,
      default: 'tb'
    },

    // 数据返回的统计总数对应的 key
    propTotal: {
      type: String,
      default: 'total'
    },

    // 数据返回的单位对应的 key
    propUnit: {
      type: String,
      default: 'unit'
    },

    // 数据返回的说明文案对应的 key
    propName: {
      type: String,
      default: 'name'
    },

    // 是否直接显示传入的单位（它的展示优先级高于 propUnit）
    propUnitObj: {
      type: Object,
      default: () => {
        return {
          unit: ''
        }
      }
    },

    // 模块是否有点击事件，如果有就会显示小箭头
    isRouter: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      seriesData: []
    }
  },
  watch: {
    params: {
      handler() {
        this.initData()
      },
      deep: true,
      immediate: false
    },
    data: {
      handler() {
        this.seriesData = JSON.parse(JSON.stringify(this.data))
      },
      deep: true,
      immediate: false
    }
  },
  mounted() {
    if (this.url) {
      this.initData()
    } else {
      this.seriesData = JSON.parse(JSON.stringify(this.data))
    }
  },
  methods: {
    // 初始化数据
    initData: _.debounce(
      function () {
        if (!this.url) return

        let requestData = {}

        if (this.method === 'post') {
          requestData = {
            url: this.url.trim(),
            method: this.method,
            data: this.params
          }
        } else {
          requestData = {
            url: this.url.trim(),
            method: this.method,
            params: this.params
          }
        }

        this.loading = true
        request(requestData)
          .then((res) => {
            let data = res.data.data

            if (Array.isArray(data) && data.length) {
              this.seriesData = data

              this.$emit('updataHandle', data)
            }
          })
          .finally(() => {
            this.loading = false
          })
      },
      300,
      {
        leading: true,
        trailing: false
      }
    ),

    // 点击模块触发事件
    clickHandle(item) {
      this.$emit('clickHandle', item)
    }
  }
}
</script>

<style scoped lang="scss">
.survey-summer-wrap {
  position: relative;
  // padding: 16px 6px 0;
  h4,
  h6,
  p {
    margin: 0;
  }

  .title-box {
    display: flex;
    align-items: center;
    color: #303133;
    font-size: 18px;
    margin-bottom: 10px;
    h4 {
      display: flex;
      align-items: center;
      font-weight: 400;
      &::before {
        display: inline-block;
        content: ' ';
        width: 6px;
        height: 17px;
        margin-right: 10px;
        background: #ce4c4c;
      }
    }
  }

  .summary-box {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .mode {
      position: relative;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: end;
      min-width: 100px;
      margin-right: 10px;
      padding: 10px 14px;
      color: #fff;
      border-radius: 8px;
      box-sizing: border-box;
      cursor: pointer;
      height: 70px;
      &:nth-child(1n) {
        background: linear-gradient(180deg, #ffe5e5 0%, #fff4f4 100%);
      }
      &:nth-child(2n) {
        background: linear-gradient(180deg, #f3d6e3 0%, #f5e4ec 100%);
      }
      &:nth-child(3n) {
        background: linear-gradient(180deg, #e9e5ff 0%, #f6f4ff 100%);
      }
      &:nth-child(4n) {
        background: linear-gradient(180deg, #dae1ff 0%, #eff2ff 100%);
      }
      &:nth-child(5n) {
        background: linear-gradient(180deg, #cee6d9 0%, #e9f5ee 100%);
      }
      &:nth-child(6n) {
        background: linear-gradient(180deg, #ffeeda 0%, #fff9ef 100%);
      }
      &:nth-child(7n) {
        background: linear-gradient(180deg, #d6eaf6 0%, #ecf9f9 100%);
      }

      &:nth-last-child(1) {
        margin-right: 0;
      }
      .icon {
        font-size: 26px;
      }
      .icon-document {
        position: absolute;
        left: 7px;
        top: 20px;
        opacity: 0.5;
        font-size: 50px !important;
      }
      .tb {
        position: absolute;
        left: 5px;
        top: 2px;
        font-size: 12px;
        color: #5e5e5e;
        .top {
          color: #f56c6c;
        }
        .down {
          color: #67c23a;
        }
      }
      .info {
        color: #909399;
        font-size: 14px;
        text-align: right;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        align-items: center;

        strong {
          display: inline-block;
          color: #303133;
          font-size: 28px;
          font-weight: 500;
          margin-right: 20px;
          i {
            font-size: 16px;
            font-style: normal;
            font-weight: normal;
            margin-left: 4px;
          }
        }
      }
      .text {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        text-align: right;
        span {
          font-size: 16px;
        }
      }
    }
  }
}

.survey-summer-multi-wrap {
  .summary-box {
    flex-wrap: wrap;
    justify-content: flex-start;
    .mode {
      flex: none;
      min-width: 186px;
      margin-bottom: 10px;
      strong {
        font-size: 18px !important;
      }
      .icon-document {
        font-size: 40px !important;
      }
    }
  }
}
</style>
