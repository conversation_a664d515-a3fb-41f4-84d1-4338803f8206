/* 资产运营 api */

import request from './request'
import { RemoteResponse } from '@/types'

// 资产分布
export const assetsXxxxx = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/xxxxxx', params)
}
// *****************资产首页，分布地图********************
// 地图锚点列表（楼宇）
export const assetInformationbuild = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/asset/assetInfobuild', params)
}
// 地图锚点列表（住宅）
export const assetInformationHouse = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/asset/map/detail', params)
}
// 首页数据列表
export const frontPageList = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/asset/statistic/list', params)
}
// 首页地图表
export const assetMapPoints = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/asset/map/list', params)
}

//
// ***************土地房产、 租赁合同********************
// 房产土地详情
export const houseLandInforDetail = (params = {}): Promise<RemoteResponse> => {
  return request('fht-monitor/asset/houseLandInforDetail', params)
}
// 删除租赁合同
export const deleteAssetLedger = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/asset/deleteAssetLedger', params)
}
// 删除土地房产
export const deleteAssetHouse = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/asset/deleteAssetLedger', params)
}
// 产权详情
export const PropertyDetail = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/asset/property/detail', params)
}
// 产权详情
export const LeaseDetail = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/asset/lease/detail', params)
}
// 产权详情

/**
 * 资产交易申报
 */

// 撤回
export interface TransactionBackParams {
  assetNo?: string
  batchNo?: string
}
export const transactionBack = (params: TransactionBackParams): Promise<RemoteResponse> => {
  return request('/fht-monitor/asset/transaction/back', params)
}
/**
 * 
 * @param code 
 * @returns 
 * code=00，取得第一级
 */
export const getAreaCode = (code: string): Promise<RemoteResponse> => {
  return request(`/fht-admin/region/lazy-tree?parentCode=${code}`, {}, {
    method: "GET"
  })
}
/**
 * @returns 
 * assetId	资产ID		false	
assignUserId	分配人员		false	
assignUserName	分配人员名称		false	
 */
export const transactionAssign = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/subject/lease/assign', params)
}
/**
 * @returns 
 * 用户人员列表
 */
export const getUserList = (params: any): Promise<RemoteResponse> => {
  return request('/fht-admin/user-list', params, {
    method: "GET"
  })
}
/**
 * @returns 
 * 发送审核要求
 */
export const SendTransaction = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/asset/transaction/verify', params)
}
/**
 * @returns 
 * 资产详情
 */
export const DetailAsset = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/asset/transaction/asset/detail', params)
}
/**
 * @returns 
 * 资产页面查询已入库房产土地,远程搜索
 */
export const ListBaseAsset = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/asset/transaction/listBaseAsset', params,)
}
/**
 * @returns 
 * 账单信息
 */
export const getAstBillList = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/contract/billList', params,)
}


/************************* 资产管理 ***************************/

// 资产导出
export const astInfoExport = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/cert/astInfo/export', params, {
    responseType: 'blob'
  })
}

// 资产详情
export const astInfoDetail = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/cert/astInfo/detail', params)
}

// 资产明细
export const astItemList = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/cert/ast-item/list', params)
}


