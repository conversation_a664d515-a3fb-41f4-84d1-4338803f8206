<!-- 各类业务预警数量 -->
<template>
  <div class="graph-category">
    <graph-title title="各类业务预警数量(Top 10)"
      :is-rect="true" />
    <div ref="graph"
      class="graph"></div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import * as echarts from 'echarts'
import GraphTitle from './GraphTitle.vue'

@Component({
  components: {
    GraphTitle
  }
})
export default class PrewarningCategory extends Vue {
  private mounted() {
    this.init()
  }

  private init() {
    let textSize = 12
    let textColor = '#999'
    let chart = echarts.init(this.$refs.graph as any)

    let option = {
      color: [
        {
          type: 'linear',
          colorStops: [
            {
              offset: 0,
              color: '#FF8888' // 0% 处的颜色
            },
            {
              offset: 1,
              color: '#FF8888' // 100% 处的颜色
            }
          ]
        },
        {
          type: 'linear',
          colorStops: [
            {
              offset: 0,
              color: '#FFD879' // 0% 处的颜色
            },
            {
              offset: 1,
              color: '#FFD879' // 100% 处的颜色
            }
          ]
        }
      ],
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#fff',
        textStyle: {
          color: '#2F3038',
          fontSize: textSize
        }
      },
      yAxis: {
        show: true,
        type: 'value',
        axisLabel: {
          color: '#aeaeae',
          fontSize: textSize
        }
      },
      xAxis: {
        type: 'category',
        axisLabel: {
          color: '#aeaeae',
          fontSize: textSize
        },
        data: ['资产', '负债', '欠租', '合同', '收款', '租金', '承租', '闲置', '财务', '欠租']
      },
      series: [
        {
          name: '红色预警',
          type: 'bar',
          stack: 'total',
          barWidth: textSize,
          label: {
            show: false,
            fontSize: textSize
          },
          emphasis: {
            focus: 'series'
          },
          itemStyle: {
            borderRadius: [0, 0, 0, 0]
          },
          showBackground: false,
          backgroundStyle: {
            color: '#FF8888'
          },
          data: [100, 200, 300, 400, 500, 600, 500, 400, 300, 200]
        },
        {
          name: '黄色预警',
          type: 'bar',
          stack: 'total',
          barWidth: textSize,
          label: {
            show: false,
            fontSize: textSize
          },
          emphasis: {
            focus: 'series'
          },
          itemStyle: {
            borderRadius: [20, 20, 0, 0]
          },
          data: [200, 300, 400, 500, 600, 700, 600, 500, 400, 300]
        }
      ]
    }
    chart.setOption(option as any)
  }
}
</script>

<style lang="scss" scoped>
.graph-category {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  .graph {
    flex: 1;
  }
}
</style>