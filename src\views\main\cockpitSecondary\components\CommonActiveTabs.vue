<template>
  <section class="common-tabs-default-wrap">
    <el-tabs v-model="activeCode"
      ref="tabs"
      type="card"
      :stretch="true"
      @tab-click="changeCompanyTabs">
      <el-tab-pane v-for="item of orgCodeList"
        :key="item.id"
        :label="item.orgName"
        :name="item.orgCode" />
    </el-tabs>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { getGroupList } from '@/api/public'

@Component
export default class extends Vue {
  @Prop({ default: false }) private typeStyle?: boolean // 样式
  @Prop({ default: '0' }) private orgCode?: string // 默认选中项
  @Prop({ default: '' }) private module?: string // 模块名称

  private activeCode = ''
  private activeName = ''
  private orgCodeList: any[] = []

  // 组件初始化
  private mounted() {
    this.getCommonList()
  }

  // tabs：获取各个集团数据
  private async getCommonList() {
    let { data } = await getGroupList()

    if (Array.isArray(data) && data.length) {
      if(this.orgCode) {
        let find = data.find((item) => {
          return item.orgCode == this.orgCode
        })

        if(find && find.orgCode) {
          this.activeCode = find.orgCode
          this.activeName = find.orgName
        } else {
          this.activeCode = data[0].orgCode
          this.activeName = data[0].orgName
        }
      } else {
        this.activeCode = data[0].orgCode
        this.activeName = data[0].orgName
      }

      // 如果需要定位到某一个集体，那么需要根据不同模块手动设置tabs滑块滑动到最后一个位置
      // 正确做法（没实现）：根据传入的 orgCode 自动定位到该选项所在位置，或者直接把tabs滑动到最后面
      let findIndex = data.findIndex((item) => {
        return item.orgCode == this.orgCode
      })

      if(findIndex > 7) {
        const tabsNav = (this.$refs.tabs as any).$el.querySelector('.el-tabs__nav')
        if (module && tabsNav) {
          setTimeout(() => {
            if(this.module == 'CompositeIndex' ) tabsNav.style.transform = `translateX(-323px)`;
            if(this.module == 'RealEstate' ) tabsNav.style.transform = `translateX(-59px)`;
            if(this.module == 'FinancialIndicator' ) tabsNav.style.transform = `translateX(-467px)`;
            if(this.module == 'Liabilities' ) tabsNav.style.transform = `translateX(-323px)`;
            if(this.module == 'ProjectProgress' ) tabsNav.style.transform = `translateX(-337px)`;
            if(this.module == 'DebtWarning' ) tabsNav.style.transform = `translateX(-323px)`;
          }, 1000);
        }
      }
      // end
    
      this.orgCodeList = data
    }

    this.$nextTick(() => {
      this.$emit('commonTabsHandle', this.activeCode, this.activeName)
      this.$bus.$emit('BusCompanyTabs', this.activeCode, this.activeName)
    })
  }

  // 各个集团 tabs 切换触发
  private changeCompanyTabs() {
    let find = this.orgCodeList.find((item) => {
      return item.orgCode === this.activeCode
    })

    if (find) this.activeName = find.orgName

    this.$emit('commonTabsHandle', this.activeCode, this.activeName)
    this.$bus.$emit('BusCompanyTabs', this.activeCode, this.activeName)
  }
}
</script>

<style scoped lang="scss">
.common-tabs-default-wrap {
  $color: #4a97f8;
  $bg: #3eeeff;
  $activeColor: #021d5c;

  margin: 12px 0;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid rgba($color: $color, $alpha: 1);

  ::v-deep .el-tabs {
    width: 100%;
    overflow: hidden;
    .el-tabs__header {
      margin: 0;
      border-bottom: 1px solid $color;
      .el-tabs__nav-wrap {
        padding: 0 30px;
        .el-tabs__nav-next,
        .el-tabs__nav-prev {
          font-size: 30px;
          color: #fff;
          line-height: 72px;
          border: 1px solid #4a97f8;
        }
        .el-tabs__nav-next {
          border-left: none;
        }
        .el-tabs__nav-prev {
          border-right: none;
        }
        .el-icon-arrow-left,
        .el-icon-arrow-right {
          font-weight: bold;
        }
      }
      .el-tabs__nav {
        border-color: $color;
      }
      .el-tabs__item {
        color: #fff;
        font-size: 34px;
        border-color: $color;
        font-family: 'Alibaba-PuHuiTi-Bold';
        &:hover {
          border: 1px solid rgba($color: $color, $alpha: 1);
          box-shadow: 0 0 30px 0px inset rgba($color: $color, $alpha: 0.6);
        }
      }
      .is-active {
        color: $activeColor !important;
        background: $bg;
        box-shadow: none !important;
        border: 1px solid rgba($color: $bg, $alpha: 1) !important;
      }
    }
    .el-tabs__item {
      height: auto;
      padding: 14px 10px;
    }
  }
}
</style>