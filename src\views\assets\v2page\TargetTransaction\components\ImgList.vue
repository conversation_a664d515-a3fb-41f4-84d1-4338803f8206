<template>
  <div style="width:100%;height:100%">
    <!-- <span class="file_tree">地址</span> -->
    <!-- <el-popover class="file_tree" placement="top-start" width="" trigger="hover">
      <el-tree
        :data="fileNameTree"
        :props="fileTreeProp"
        accordion
        @node-click="handleNodeClick"
        node-key="id"
        ref="tree"
        :default-expanded-keys="expandedKeys"
      ></el-tree>
      <el-button style="font-size:16px" type="text" slot="reference">{{ Img.label }}文件名称</el-button>
    </el-popover> -->
    <el-popover class="file_tree" placement="top-start" width="" trigger="hover">
      <el-tree
        :data="fileNameTree"
        :props="fileTreeProp"
        accordion
        @node-click="handleNodeClick"
        node-key="id"
        ref="tree"
        :default-expanded-keys="expandedKeys"
      ></el-tree>
      <el-button style="font-size:16px" type="text" slot="reference">{{ Img.originalFileName||"附件查看" }}</el-button>
    </el-popover>
    <iframe
      v-if="src.split('.')[src.split('.').length - 1] == 'pdf'"
      marginwidth="0"
      framespacing="0"
      marginheight="0"
      allowtransparency="true"
      class="iframe"
      style="width:100%;height:100%"
      @load="loadFrame"
      :src="src"
      frameborder="0"
    ></iframe>
    <el-image style="width:100%;height:100%" fit="scale-down" v-else :src="src" alt=""></el-image>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
//  import Pdfh5 from "pdfh5";
@Component({
  components: {}
})
export default class extends Vue {
  private src=".p"
  // src
  @Prop() private detaildataInfo?: any
  private pdfh5: any = ''
  private fileNameTree: any = [
    {
      id: 1,
      label: '标的基本信息附件',
      originalFileName: '标的基本信息附件',
      children: []
    },
    {
      id: 2,
      label: '出租方信息附件',
      originalFileName: '出租方信息附件',
      children: []
    },
    {
      id: 3,
      label: '交易条件与承租方条件',
      originalFileName: '交易条件与承租方条件',
      children: []
    }
  ]
  private fileTreeProp = {
    children: 'children',
    label: 'originalFileName'
  }
  private expandedKeys: number[] = []
  // 通过/驳回状态判断
  private ImgSrc = ''
  private Img: any = {}
  // 附件列表
  private ImgLists: any = []
  created() {
    //    	  this.pdfh5 = new Pdfh5("#demo", {
    // 	// pdfurl: "http://fh-mjgy-test.oss-cn-hangzhou.aliyuncs.com/upload/20220811/a36eb2656e9afb11ad0a6a7e096e432b.pdf"
    //   });
  }
  mounted () {
    this.fileNameTree[1].children=this.detaildataInfo.lessor.fileList
    this.fileNameTree[2].children=this.detaildataInfo.lessor.fileList
    this.fileNameTree[0].children=this.detaildataInfo.fileList
    // this.src=
    this.handleNodeClick( this.fileNameTree[0].children[0])    
  }
  private loadFrame() {
    //  let imgs:any = document.getElementsByTagName('img');
    //  imgs.setAttribute("style","width:100vw;");
  }
  private handleNodeClick(data: any) {
    if(data.children==undefined){
    this.Img=data
    this.src=data.url||"."
    }
  }
}
</script>

<style lang="scss" scoped>
.iframe {
  .img {
    width: 40vw;
  }
}
.file_tree {
  position: absolute;
  top: 10px;
  left: 300px;
}
</style>
