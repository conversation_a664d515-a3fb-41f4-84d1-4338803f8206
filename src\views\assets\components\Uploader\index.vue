<template>
  <el-dialog
    :title="title || '查看'"
    width="700px"
    :visible="visible"
    :close-on-click-modal="true"
    :append-to-body="true"
    @open="handleOpen"
    @close="closeDlg"
  >
    <slot />
    <viewer :images="dataList" @inited="inited">
      <!-- <div v-if="isAttachment">
        <ul class="uploader attachments">
          <li v-for="(item, index) in dataList" :key="index" class="uploader-item">
            <div class="uploader-item-inner no-cover">
              <template v-if="item[urlKey]">
                <img :src="getAttachImgUrl(item)" />
                <div class="attachment-title">{{ item[nameKey] }}</div>
                <div class="uploader-item-actions">
                  <a
                    v-if="judgeFileTypeIsWordExcelPPT(item[urlKey])"
                    :href="`https://view.officeapps.live.com/op/view.aspx?src=${encodeUrl(item[urlKey])}`"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <i class="el-icon-view" />
                  </a>
                  <i
                    v-else-if="judgeFileTypeIsImage(item[urlKey])"
                    class="el-icon-view"
                    @click="handlePreview(index)"
                  />
                  <a v-else :href="item[urlKey]" target="_blank" rel="noopener noreferrer">
                    <i class="el-icon-view" />
                  </a>
                  <i v-if="uploadable" class="el-icon-delete" @click="handleRemove(index)" />
                </div>
              </template>
              <el-progress
                v-else
                :width="92"
                type="circle"
                :percentage="item.progress"
                color="#409EFF"
              ></el-progress>
            </div>
          </li>
          <template v-if="uploadable">
            <el-upload
              ref="upload"
              v-show="dataList.length < maxLimit"
              class="uploader-handler uploader-handler-no-tag"
              multiple
              :headers="aosHeader"
              :action="aosActionUrl"
              :accept="isAttachment ? '.jpg,.jpeg,.png,.pdf,.xls,.xlsx,.doc,.docx,.ppt,.pptx,.numbers,.key' : '.jpg,.jpeg,.png'"
              :show-file-list="false"
              :on-success="handleAOSSuccess"
              :on-progress="handleAOSProgress"
              :on-error="handleAOSError"
              :before-upload="beforeAvatarUpload"
              :data="aosData"
              :on-exceed="handleExceed"
            >
              <i class="el-icon-plus uploader-handler-icon"></i>
            </el-upload>
          </template>
        </ul>
      </div>-->
      <div>
        <draggable v-model="dataList" element="ul" class="uploader">
          <li v-for="(item, index) in dataList" :key="index" class="uploader-item">
            <div :class="showCover ? 'uploader-item-inner' : 'uploader-item-inner no-cover'">
              <template v-if="item[urlKey]">
                <!-- <img
                  :src="isPrivate  ? item[urlKey] : item[urlKey] + '?x-oss-process=image/resize,w_600'"
                />-->
                <img :src="getAttachImgUrl(item)" />
                <div class="uploader-item-actions">
                  <i class="el-icon-zoom-in" @click="handlePreview(index)" />
                  <i v-if="uploadable" class="el-icon-delete" @click="handleRemove(index)" />
                </div>
              </template>
              <el-progress v-else :width="92" type="circle" :percentage="item.progress" color="#409EFF" />
            </div>
            <el-tooltip class="file_tag_name" effect="dark" :content="item.name" placement="bottom">
              <el-button type="text">{{ item.name }}</el-button>
            </el-tooltip>
            <el-select
              v-if="uploaderTags && uploaderTags.length > 0"
              class="uploader-tag"
              v-model="dataList[index].tag"
              placeholder="选择文件类型"
              :disabled="!uploadable"
            >
              <el-option v-for="(tag, pos) in uploaderTags" :label="tag.label" :value="tag.value" :key="pos" />
            </el-select>
          </li>
        </draggable>
        <template v-if="uploadable">
          <el-upload
            ref="upload"
            v-show="dataList.length < maxLimit"
            multiple
            :headers="aosHeader"
            :class="uploaderTags && uploaderTags.length > 0 ? 'uploader-handler' : 'uploader-handler uploader-handler-no-tag'"
            :action="aosActionUrl"
            :accept="getAccept()"
            :show-file-list="false"
            :data="aosData"
            :before-upload="beforeAvatarUpload"
            :on-success="handleAOSSuccess"
            :on-progress="handleAOSProgress"
            :on-error="handleAOSError"
            :on-exceed="handleExceed"
          >
            <i class="el-icon-plus uploader-handler-icon" />
          </el-upload>
        </template>
      </div>
      <!-- <el-empty v-else
      description="暂无数据" />-->
    </viewer>

    <!-- 温馨提示 -->
    <div v-if="uploadable && !this.$attrs['is-tips']">
      <p class="uploader-tip">温馨提示：</p>
      <p v-if="isAttachment" class="uploader-tip">{{ uploaderTip || '' }}</p>
      <p v-else-if="maxLimit > 1" class="uploader-tip">{{ uploaderTip || '' }}</p>
      <p v-else class="uploader-tip">{{ uploaderTip || '' }}</p>
      <h5 v-if="isAttachment == true && isImg == false">
        {{ `目前最多支持${maxLimit}张，支持JPG/JPEG/PNG/PDF/WORD/EXCEL，单个附件大小不能超过${FileSize}M!支持批量上传。` }}
      </h5>
      <h5 v-if="isAttachment == false && isImg == false">
        {{ `目前最多支持${maxLimit}张，支持JPG/JPEG/PNG/PDF/WORD，单个附件大小不能超过 ${FileSize}M!支持批量上传。` }}
      </h5>
      <h5 v-if="isImg == true">{{ `目前最多支持${maxLimit}张，支持JPG/JPEG/PNG, 单个附件大小不能超过${FileSize}M!支持批量上传。` }}</h5>
    </div>
    <!-- <div v-if="uploadable && !this.$attrs['is-tips']">
      <p class="uploader-tip">温馨提示：</p>
      <p
        v-if="isAttachment"
        class="uploader-tip"
      >{{ uploaderTip||'' + `目前最多支持${maxLimit}张，支持JPG/JPEG/PNG/PDF/WORD/EXCEL，可以拖动进行排序，支持批量上传。` }}</p>
      <p
        v-else-if="maxLimit > 1"
        class="uploader-tip"
      >{{ uploaderTip||'' + `目前最多支持${maxLimit}张，支持JPG/JPEG/PNG，可以拖动图片进行排序，支持批量上传。` }}</p>
      <p v-else class="uploader-tip">{{ uploaderTip ||""+ `目前最多支持${maxLimit}张，支持JPG/JPEG/PNG。` }}</p>
      <h5 v-if="maxLimitTip">{{`目前最多支持${maxLimit}张，支持JPG/JPEG/PNG/PDF/WORD/EXCEL，可以拖动进行排序，支持批量上传。` }}</h5>

    </div>-->
    <slot name="tips" />

    <!-- 底部按钮 -->
    <span slot="footer" class="uploader-footer clearfix">
      <slot name="footer" />
      <el-button type="primary" class="right" :disabled="uploading" @click="handleDetermine">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop, Model, Emit } from 'vue-property-decorator'
import draggable from 'vuedraggable'
import { ElUpload } from 'element-ui/types/upload'
import { getSignSingle, getSignMulti, getToken, FileSize } from './config'
import { deepClone } from '@/utils'

export interface UploaderTag {
  label: string
  value: string
}

interface OssToken {
  accessKeyId: string
  accessKeySecret: string
  securityToken: string
  region: string
  endpoint: string
  bucket: string
}

interface UploaderItem {
  name?: string
  link?: string
  url?: string
  originalUrl?: string
  tag?: UploaderTag
  progress?: number
  id?: string
  originalName?: string
  value?: string
  attachId?: string
  tempFileName?: string
  originalFileName?: string
}

const MAX_IMAGE_SIZE = 10 * 1000 * 1000 // 文件大小

@Component({
  name: 'Uploader',
  components: {
    draggable
  }
})
export default class extends Vue {
  @Prop({ default: '图片上传' }) private title!: string // 标题
  @Prop({ default: true }) private uploadable!: boolean // 是否允许上传
  @Prop() private uploaderTags?: Array<UploaderTag> // 图片标签
  @Prop() visible!: boolean // 弹窗可见性
  @Prop({ default: 10 }) private maxLimit!: number // 最大上传数目
  @Prop() private uploaderTip!: string // 温馨提示文案
  @Prop({ default: false }) private showCover!: boolean // 是否显示封面图片
  @Prop() private isPrivate!: boolean // bucket是否私有读写，私有则初始化需要url加签
  @Prop({ default: 'url' }) private urlKey!: string // 图片key名称, 默认url
  @Prop({ default: false }) private isImg!: boolean // 是否只有图片
  @Prop() private isAttachment!: boolean
  @Prop({ default: 'name' }) private nameKey!: string // 附件名称的键名，默认name
  // @Prop({ default: 'primary' }) private btntype!: string // 按钮类型
  @Prop({ default: 'viewer' }) private openMethod!: string // 是否以新悬浮窗口的方式打开,viewer/windown

  @Model('uploadComplete', { type: Array })
  initList!: Array<string | UploaderItem>

  @Emit('onPreview')
  private onPreview(url: unknown) {
    return url
  }

  @Emit('onClose')
  private onClose() {
    return
  }

  private dataList: Array<UploaderItem> = []
  private viewer: any
  private ossToken?: OssToken // 上传图片需要的oss token
  private pendingUploadFiles: File[] = []
  private cacheSigned: any = {} // 缓存授权过的链接，防止重复发请求
  private uploading = false
  private attachmentSuffixes = ['.jpg', '.jpeg', '.png', '.pdf', '.xls', '.xlsx', '.doc', '.docx', '.ppt', '.pptx', '.numbers', '.key']
  private imageSuffixes = ['.png', '.jpg', '.jpeg', '.pdf',".doc",".docx"]
  private onlyImageSuffixes = ['.png', '.jpg', '.jpeg']
  private aosActionUrl = `${process.env.VUE_APP_URL}fht-admin/api/base/upload`
  private aosHeader = {}
  private aosData = { fileType: this.isPrivate ? 2 : 1, tag: '',bizType:2 } //bizType :用来区别是否对外或者对内
  private fileNumber = 0 // 文件个数
  private FileSize = FileSize //文件大小
  private async handleOpen() {
    this.aosHeader = {
      Authorization: `Basic c2FiZXI6c2FiZXJfc2VjcmV0`,
      'Fht-Auth': `bearer ${getToken()}`
    }
    let list = this.initList ? JSON.parse(JSON.stringify(this.initList)) : []
    let data: any = []
    let url // 文件url
    if (!this.isPrivate) {
      for (let item of list) {
        if (typeof item === 'string') {
          data.push({
            [this.urlKey]: item,
            originalUrl: item
          })
        } else if (typeof item === 'object') {
          item.originalUrl = item[this.urlKey]
          data.push(item)
        }
      }
    } else {
      let waitingSignArr: any = []
      let index = 0
      for (let item of list) {
        if (typeof item === 'string') {
          url = item
        } else if (typeof item === 'object') {
          url = item[this.urlKey] || item.url
        }
        // 兼容后端私有的图片已授权的情况
        if (url.indexOf('?') > -1) {
          let original = url.split('?')[0]
          this.cacheSigned[original] = url
          if (typeof item === 'string') {
            list[index] = original
          } else if (typeof item === 'object') {
            item[this.urlKey] = original
          }
        } else if (!this.cacheSigned[url]) {
          waitingSignArr.push(url)
        }
        ++index
      }
      if (waitingSignArr.length > 0) {
        let res = await getSignMulti(waitingSignArr, this.isPrivate)
        if (res.success) {
          res.data.forEach((val: string, index: number) => {
            // 加入缓存
            this.cacheSigned[waitingSignArr[index]] = val
          })
        }
      }
      for (let item of list) {
        if (typeof item === 'string') {
          data.push({
            [this.urlKey]: this.cacheSigned[item],
            originalUrl: item
          })
        } else if (typeof item === 'object') {
          item.originalUrl = item[this.urlKey]
          item[this.urlKey] = this.cacheSigned[item[this.urlKey]]
          data.push(item)
        }
      }
    }
    this.dataList = data || []
    this.fileNumber = this.dataList.length
  }

  beforeDestroy() {
    this.viewer = null
  }

  private getAttachImgUrl(item: UploaderItem) {
    if (this.isAttachment || this.ispdf(item.url)) {
      let url = item.originalUrl || ''
      if (url.search(/.jpg|.jpeg|.png/) > -1) {
        return item[this.urlKey as keyof UploaderItem]
      } else if (url.search(/.doc|.docx/) > -1) {
        return require('@/assets/images/public/ic_doc.png')
      } else if (url.search(/.xls|.xlsx/) > -1) {
        return require('@/assets/images/public/ic_excel.png')
      } else if (url.search(/.ppt|.pptx/) > -1) {
        return require('@/assets/images/public/ic_excel.png')
      } else {
        return require('@/assets/images/public/ic_other.png')
      }
    } else {
      return item[this.urlKey as keyof UploaderItem]
      // return
    }
  }
  get ispdf() {
    const reg = new RegExp(/(.png)|(.jpg)|(.jpeg)/g)
    return (val: any) => {
      if (reg.test(val)) {
        return true
      } else {
        return true
      }
    }
  }
  private inited(viewer: any) {
    this.viewer = viewer
  }
  private getAccept() {
    if (this.isAttachment && !this.isImg) {
      // img +pdf
      return this.attachmentSuffixes.join(',')
    } else if (this.isAttachment && this.isImg) {
      // 只有图片
      return this.onlyImageSuffixes.join(',')
    } else {
      return this.imageSuffixes.join(',')
    }
  }
  // 判断是否是 图片
  private judgeFileTypeIsImage(url: string) {
    const reg = new RegExp(/(.png)|(.jpg)|(.jpeg)/g)
    return reg.test(url)
  }

  // 判断是否是 Word Excel PPT
  private judgeFileTypeIsWordExcelPPT(url: string) {
    const reg = new RegExp(/(.ppt)|(.pptx)|(.xlsx)|(.xls)|(.doc)|(.docx)/g)
    return reg.test(url)
  }

  // 编码 url,使用 encodeURIComponent， 因为要对整个 url 编码
  private encodeUrl(url: string) {
    return encodeURIComponent(url)
  }
  // 上传文件之前校验文件大小
  private beforeAvatarUpload(file: any) {
    if (this.fileNumber >= this.maxLimit) {
      this.handleExceed()
      return false
    }
    const isLt = file.size / 1024 / 1024 < FileSize
    if (!isLt) {
      this.$message.error(`上传附件大小不能超过 ${FileSize}M!`)
    } else {
      this.fileNumber += 1
    }
    return isLt
  }
  // 生成上传图片名称
  private getRandomFileName() {
    let random = ('000000' + Math.floor(Math.random() * 999999)).slice(-6)
    let date = new Date()
    return `${date.getFullYear()}${date.getMonth() + 1}${date.getDate()}${date.getHours()}${date.getMinutes()}${date.getSeconds()}${random}`
  }

  // 点击确认
  private handleDetermine() {
    if (this.uploading) {
      return
    }
    // 标签必填校验
    // if (!this.isAttachmentVerification()) return

    let hasTag = this.uploaderTags && this.uploaderTags.length > 0
    let data = []
    let previewUrl: unknown = ''
    if (this.dataList.length > 0) {
      let list = deepClone(this.dataList)
      previewUrl = list[0][this.urlKey as keyof UploaderItem]
      for (let item of list) {
        if (hasTag || this.isAttachment) {
          Object.assign(item, {
            [this.urlKey]: item.originalUrl
          })
          delete item.originalUrl
          data.push(JSON.parse(JSON.stringify(item)))
        } else {
          data.push(this.isPrivate ? item.originalUrl : item[this.urlKey as keyof UploaderItem])
        }
      }
    }
    this.onPreview(previewUrl)
    this.$emit('uploadComplete', data)

    // 如果 isClose 参数为 true，没有上传图片数据的话，就不关闭弹窗
    if (this.$attrs['isClose'] && !data.length) return
    this.reset()
    this.closeDlg()
  }
  //  对文件进行必传和标签选择校验
  private isAttachmentVerification() {
    let list: any = deepClone(this.dataList)
    let taglist: any = deepClone(this.uploaderTags)
    // 对标签是否选择校验

    if (taglist == undefined || list.length == 0 || !this.uploadable) return true
    if (list.length > 0) {
      let istage: any = list.find((item: any) => {
        return item.tag === '' || item.tag === undefined
      })
      if (istage !== undefined) {
        this.$message.error(`(${istage.name})未选择附件类型`)
        return false
      }
    }
    for (let i = 0; i < taglist.length; i++) {
      let isupload: any = list.find((item: any) => {
        return taglist[i].value == item.tag
      })
      // 不存在某一个附件
      if (isupload == undefined && taglist[i].isRequired == '1') {
        this.$message.error(`请上传${taglist[i].label}`)
        return false
      }
    }

    return true
  }

  // 点击关闭
  private closeDlg() {
    // 对附件校验
    this.$emit('update:visible', false)
    this.onClose()
  }

  private handlePreview(index: number) {
    let dataList = this.dataList[index] || {}

    //  用新窗口模式打开新窗口
    if (this.openMethod == 'windown') {
      window.open(dataList.url, dataList.name, 'top=10,left=10,width=600,height=400')
    } else {
      let list = dataList.url && dataList.url.split('.')

      if (!Array.isArray(list)) return
      if (['png', 'jpg', 'jpeg'].indexOf(list[list.length - 1]) == -1) {
        window.open(dataList.url, '__blank__')
      } else {
        this.viewer.view(index)
      }
      // if(list[list.length]=="")
    }
  }

  private handleRemove(index: number) {
    this.dataList.splice(index, 1)
    this.fileNumber -= 1
  }

  private async handleAOSSuccess(response: { data: any }, file: File, fileList: Array<File>) {
    // 私有桶需要加签
    let url: string = response.data.link
    // 兼容后端返回的地址已加签
    if (this.isPrivate && url.indexOf('?') === -1) {
      let arr = url.split('/')
      let res = await getSignSingle(arr[arr.length - 1], true)
      if (res.success) {
        this.cacheSigned[url] = res.data
        this.dataList.push({
          [this.nameKey]: file.name.substring(0, 40),
          [this.urlKey]: response.data.url,
          originalUrl: response.data.url,
           tempFileName:response.data.tempFileName,
          link: response.data.link,
          id: response.data.id,
          attachId: response.data.attachId,
          originalFileName: response.data.originalFileName,
          name: response.data.originalFileName
        })
      }
    } else {
      this.dataList.push({
        [this.nameKey]: file.name.substring(0, 40),
        [this.urlKey]: response.data.url,
        originalUrl: response.data.url,
         tempFileName:response.data.tempFileName,
        link: response.data.link,
        id: response.data.id,
        attachId: response.data.attachId,
        originalFileName: response.data.originalFileName,
        name: response.data.originalFileName
      })
    }
  }

  private handleAOSProgress(event: object, file: File, fileList: Array<File>) {
    //
  }

  private handleAOSError(error: object, file: File, fileList: Array<File>) {
    this.fileNumber -= 1
    this.$message.warning('上传失败')
    console.error(error)
  }

  private handleExceed() {
    this.$message.warning('上传文件个数超出限制！')
  }

  public reset() {
    let uploadRef = this.$refs.upload as ElUpload
    uploadRef && uploadRef.clearFiles()
  }
}
</script>

<style lang="scss" scoped>
.uploader {
  margin: 0 0 20px 0;
  padding: 0;
  display: initial;

  li {
    display: inline-block;
    background-color: #fff;
    width: 122px;
    margin: 0 8px 8px 0;
    cursor: move;

    .uploader-item-inner {
      position: relative;
      display: block;
      border-radius: 6px;
      border: 1px solid #c0ccda;
      width: 100%;
      height: 92px;
      overflow: hidden;
      text-align: center;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .uploader-item-actions {
        position: absolute;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0);
        display: flex;
        justify-content: center;
        align-items: center;

        i {
          display: none;
          color: #fff;
          cursor: pointer;
          font-size: 20px;
          margin: 0 8px;
        }

        &:hover {
          background-color: rgba(0, 0, 0, 0.5);

          i {
            display: inline-block;
          }
        }
      }
    }

    .uploader-tag {
      margin-top: 8px;
    }
  }

  li:first-child .uploader-item-inner {
    &::before {
      content: '封面';
      position: absolute;
      right: -20px;
      top: -3px;
      width: 65px;
      height: 30px;
      background: #4680ff;
      text-align: center;
      transform: rotate(45deg);
      box-shadow: 0 1px 1px #4680ff;
      font-size: 12px;
      line-height: 36px;
      color: #fff;
      z-index: 1;
    }

    &.no-cover::before {
      display: none;
    }
  }
  &.attachments {
    li {
      cursor: auto;
      .attachment-title {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        color: #fff;
        font-size: 12px;
        line-height: 20px;
        text-align: center;
        background: rgba(0, 0, 0, 0.5);
        padding: 0 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.uploader-footer {
  .left {
    float: left;
  }
  .right {
    float: right;
  }
}

.uploader-handler {
  display: inline-block;
  border-radius: 6px;
  border: 1px dashed #c0ccda;
  width: 122px;
  height: 130px;
  vertical-align: top;
  text-align: center;
  margin: 0 8px 8px 0;

  .uploader-handler-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100%;
    height: 10%;
    line-height: 130px;
    text-align: center;
  }

  &:hover {
    border: 1px dashed #66b1ff;
  }
}

.uploader-handler-no-tag {
  height: 92px;
  .uploader-handler-icon {
    line-height: 92px;
  }
}

.uploader-tip {
  margin: 0;
  font-size: 16px;
  letter-spacing: 3px;
  color: #606266;

  &:nth-of-type(1) {
    margin-top: 10px;
  }
}
.file_tag_name {
  margin-top: 10px;
  padding: 5px;
  overflow: hidden;
  max-width: 100%;
}
.el-dialog__wrapper {
  // z-index:6666 !important;
}
</style>
<style>
/* .el-popper[x-placement^=bottom]{
  z-index: 9669 !important;
} */
</style>
