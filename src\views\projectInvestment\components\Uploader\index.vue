/**
  组件描述: 上传组件
*/
<template>
  <section class="uploader-wrapper">
    <div class="title">
      <span>{{ title }}</span>
      <span v-if="mode === 'upload'"
        class="uploader-btn"
        @click="onClick"> <i class="el-icon-upload"></i> 点击上传</span>
    </div>
    <div class="uploader-btn"></div>
    <div class="file-list">
      <span v-for="(file, index) in fileList"
        :key="index">
        <el-link type="primary"
          :href="file">{{ file }}</el-link>
      </span>
    </div>

    <!-- 导入 -->
    <uploader :title="`${title}${mode === 'upload' ? '上传' : '查看'}`"
      :visible.sync="uploaderDlgVisible"
      :init-list="fileList"
      :uploadable="mode === 'upload'"
      :is-tips="true"
      :show-cover="true"
      :is-private="true"
      :maxLimit="1"
      @change="onFilesChange"
      @uploadComplete="handleUploadComplete">
      <div v-if="hasModel"
        style="margin-top:10px;"
        slot="tips">
        <div>温馨提示：</div>
        <div>
          <span>请上传EXCEL格式文件，最大支持3M </span>
          <el-button type="text"
            @click="downMobel">下载模版</el-button>
        </div>
      </div>
    </uploader>
  </section>
</template>

<script lang="ts">
import { Component, Emit, Model, Prop, Vue } from 'vue-property-decorator'
import Uploader from '@/components/Uploader/index.vue'

@Component({
  components: {
    Uploader
  }
})
export default class extends Vue {
  @Prop() private title!: string
  @Prop({ default: 'download' }) private mode!: 'download' | 'upload' // 下载、上传模式
  @Prop({ default: false }) private hasModel!: boolean

  @Model('change')
  private files!: string

  @Emit('change')
  private onFilesChange(file: string[]) {
    return file.join(',')
  }

  private uploaderDlgVisible = false

  get fileList() {
    return this.files.split(',').filter((item) => item !== '')
  }

  get disabled() {
    return this.mode === 'download'
  }

  private onClick() {
    this.uploaderDlgVisible = true
  }

  // 下载模板
  private downMobel() {
    let time = new Date().getTime()
    let { protocol } = window.location
    window.open(protocol + '//gtfstatic.mdguanjia.com/批量定价导入模板.xlsx?time=' + time)
  }

  private handleUploadComplete(file: string[]) {
    // 
  }
}
</script>


<style scoped lang="scss">
.uploader-wrapper {
  margin-top: 12px;
  width: 260px;
}
.uploader {
  background: #fffcfc;
}
.title {
  margin-bottom: 8px;
}

.file-list {
  display: flex;
  flex-direction: column;
}
.uploader-btn {
  color: #b43c3c;
  margin-left: 12px;
  font-size: 12px;
  cursor: pointer;
}
</style>