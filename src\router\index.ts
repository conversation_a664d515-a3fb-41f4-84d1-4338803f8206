import Vue from 'vue'
import VueRouter, { RouteConfig } from 'vue-router'
import Main from '@/router/main'
import Project from '@/router/project'
import Finance from '@/router/finance'
import Assets from '@/router/assets'
import Prewarning from '@/router/prewarning'
import Detail from '@/router/detail'
import ProjectInvestment from '@/router/projectInvestment'
Vue.use(VueRouter)

// 不带 layouts 布局的路由
const routes: Array<RouteConfig> = [
  {
    path: '/login',
    name: 'Login',
    meta: { title: '登录' },
    component: () => import('@/views/login/index.vue')
  }
]

// 带有 layouts 布局的路由
const routesLayout: Array<RouteConfig> = [...Main, ...Prewarning, ...Project, ...Finance, ...Assets, ...ProjectInvestment, ...Detail]

// 解决路由跳转重复的报错
const originalPush: any = VueRouter.prototype.push
VueRouter.prototype.push = function push(location: any) {
  return originalPush.call(this, location).catch((err: any) => err)
}

// 路由初始化
const router = new VueRouter({
  mode: 'hash',
  base: process.env.BASE_URL,
  routes: routes.concat(routesLayout),
  // 记录页面滚动条位置
  scrollBehavior(to, from, savePosition) {
    if (savePosition) {
      return savePosition
    } else {
      return { x: 0, y: 0 }
    }
  }
})

export default router
