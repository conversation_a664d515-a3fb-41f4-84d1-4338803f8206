<template>
  <section class="panel-header-wrap">
    <div class="click-area"
      @click="toCockpit"></div>
    <!-- 左侧 -->
    <div class="panel panel-left">
      <el-tooltip effect="dark"
        content="架构图"
        placement="top-start">
        <i class="el-icon-set-up icons m-r-20"
          @click="visibleArchitecture = true" />
      </el-tooltip>

      <!-- <el-tooltip effect="dark"
        content="PPT"
        placement="top-start">
        <i class="icons el-icon-document-copy m-r-30"
          @click="viewPPT('https://fh-ka.oss-cn-hangzhou.aliyuncs.com/金华市国资委监管系统20220802.pptx')" />
      </el-tooltip> -->

      <!-- 左侧头部切换列表 -->
      <!-- <div v-if="!getHideTabMode"
        class="panel-header__switch switch-left">
        <div v-for="item in leftSwitchList"
          :key="item.name"
          @click="companyTabs(item)"
          :class="['panel-header__switch-item', item.name === value && 'panel-header__switch-item--checked' ]">
          <div class="panel-header__highlight"></div>
          <span>{{ item.label }}</span>
        </div>
      </div> -->
      
      <div v-if="!getHideTabMode"
        class="panel-header__switch switch-left">
        <div v-for="item in switchList"
          :key="item.name"
          @click="companyTabs(item)"
          :class="['panel-header__switch-item', item.name === value && 'panel-header__switch-item--checked' ]">
          <div class="panel-header__highlight"></div>
          <span>{{ item.label }}</span>
        </div>
      </div>

      <template v-else>
        <el-tooltip v-if="!isWindowFull"
          effect="dark"
          content="资产首页"
          placement="top-start">
          <router-link to="/assets/overviews"
            tag="i"
            class="assets-home el-icon-s-flag" />
        </el-tooltip>
      </template>
    </div>

    <!-- 大标题 -->
    <div class="panel-middel">
      <span class="gd" />
      <CockpitTitle title="金华市国资委数智监管系统"
        :height="174"
        :fontSize="70"
        color="#fff"
        class="m-f-t-12" />
    </div>

    <!-- 右侧 -->
    <div class="panel panel-right">
      <!-- 右侧头部切换列表 -->
      <!-- <div v-if="!getHideTabMode"
        class="panel-header__switch switch-right">
        <div v-for="item in rightSwitchList"
          :key="item.name"
          @click="companyTabs(item)"
          :class="['panel-header__switch-item', item.name === value && 'panel-header__switch-item--checked' ]">
          <div class="panel-header__highlight"></div>
          <span>{{ item.label }}</span>
        </div>
      </div> -->

      <img v-if="!isWindowFull"
        src="@/views/main/cockpitcren/images/full.png"
        class="full"
        @click="inWindowFullScreen" />

      <el-button v-else
        type="primary"
        icon="el-icon-s-unfold"
        class="full full-out"
        title="退出全屏"
        @click="outWindowExitFullScreen()" />
    </div>

    <!-- 查看架构图 -->
    <Architecture v-if="visibleArchitecture"
      :visible.sync="visibleArchitecture" />
  </section>
</template>

<script lang="ts">
import { Component, Emit, Model, Prop, Vue } from 'vue-property-decorator'
import { viewOffice, windowExitFullScreen, windowFullScreen } from '@/utils'
import Weather from '@/components/Weather/index.vue'
import Architecture from '@/views/main/cockpitcren/components/Public/Architecture.vue'
import CockpitTitle from '@/views/main/cockpitcren/components/Public/CockpitTitle.vue'

export interface SwitchItem<T> {
  label: string
  name: T
}

@Component({
  components: {
    Weather,
    Architecture,
    CockpitTitle
  }
})
export default class extends Vue {
  @Prop() private switchList!: SwitchItem<any>[]

  @Model('change')
  private value!: any

  @Emit('change')
  private onSwitchChange(value: any) {
    return value
  }

  private momentHms = ''
  private nowTime = ''
  private isWindowFull = false
  private visibleArchitecture = false
  private optionTimes: any[] = []
  private leftSwitchList: SwitchItem<any>[] = []
  private rightSwitchList: SwitchItem<any>[] = []

  // 是否隐藏tab切换
  get getHideTabMode() {
    let { meta } = this.$route
    return meta && meta.hideTab ? meta.hideTab : false
  }

  // 数据初始化
  private created() {
    if (this.switchList.length) {
      this.leftSwitchList = this.filterList(0)
      this.rightSwitchList = this.filterList(1)
    }

    this.keyDown()
  }

  // 监听键盘 enter 事件（在资产监管模块，驾驶舱需要这样做）
  private keyDown() {
    if (this.getHideTabMode) {
      document.onkeydown = (event) => {
        let { path, query } = this.$route

        if (path.indexOf('/assets/moduledetail') > -1 && query.module === 'RealEstate') {
          let e: any = event || window.event
          let keyCode = e.keyCode || e.which
          if (keyCode === 13) {
            this.$router.push({
              path: '/assets/overviews'
            })
          }
        }
      }
    }
  }

  // 点击顶部各个模块 tabs 按钮
  private companyTabs(item: SwitchItem<any>) {
    this.onSwitchChange(item.name)
  }

  // 筛选模块列表 index 0 左侧 1 右侧
  private filterList(index: number) {
    let middleIndex = Math.floor(this.switchList.length / 2)
    let list = [...this.switchList]
    if (index) {
      return list.splice(middleIndex, middleIndex + 1)
    } else {
      return list.splice(index, middleIndex)
    }
  }

  // 组件初始化
  private mounted() {
    let that = this as any
    this.nowTime = that.$moment(new Date()).add('year', 0).format('YYYY-MM-DD')

    this.windowFullScreen()
    this.setInterHms()
  }

  // 监听全屏触发
  private windowFullScreen() {
    this.$bus.$on('BusWindowFullScreen', (data: boolean) => {
      this.isWindowFull = data
    })
  }

  // 获取当前星期
  private getMomentWeek() {
    let that = this as any
    return that.$moment().format('dddd')
  }

  // 设置倒计时
  private setInterHms() {
    setInterval(() => {
      let that = this as any
      this.momentHms = that.$moment().format('HH:mm:ss')
    }, 1000)
  }

  // 查看ppt文件
  private viewPPT(src: string) {
    viewOffice(src)
  }

  // 进入全屏
  private inWindowFullScreen() {
    this.isWindowFull = true
    windowFullScreen('')
  }

  // 退出全屏
  private outWindowExitFullScreen() {
    this.isWindowFull = false
    windowExitFullScreen()
  }

  // 点击退出二级
  private toCockpit() {
    if (!this.getHideTabMode) {
      this.$router.push({
        path: '/cockpit'
      })
    }
  }
}
</script>

<style scoped lang="scss">
$textColor: #63f1ff;

@keyframes keyGdMove {
  0% {
    left: -300px;
    opacity: 0.8;
  }
  25% {
    left: 0;
    opacity: 1;
  }
  50% {
    left: 200px;
    opacity: 0.8;
  }
  75% {
    left: 0;
    opacity: 1;
  }
  100% {
    left: -300px;
    opacity: 0.8;
  }
}

.panel-header-wrap {
  height: 135px;
  margin-bottom: 35px;
  .click-area {
    position: absolute;
    z-index: 1000;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -75%);
    width: 840px;
    height: 105px;
    cursor: pointer;
  }

  position: relative;
  display: flex;
  justify-content: space-between;
  color: $textColor;
  padding: 0 30px;
  background: url('../../cockpitcren/images/panel_title_bg.png') no-repeat center center;
  background-size: 100%;

  .panel-header {
    &__switch {
      display: flex;
      align-items: center;
      width: 100%;
      &.switch-left {
        margin-left: 150px;
      }
      &.switch-right {
        margin-right: 150px;
      }
    }
    &__switch-item {
      position: relative;
      font-size: 38px;
      font-weight: bold;
      flex: 1;
      cursor: pointer;
      span {
        position: relative;
        z-index: 1;
      }
      &--checked {
        color: #fff;
        .panel-header__highlight {
          height: 80px;
          opacity: 1;
          top: 0;
        }
      }
    }

    &__highlight {
      position: absolute;
      top: 80px;
      left: 50%;
      transform: translateX(-50%);
      background-image: url('../images/switch_highlight.png');
      background-size: 100% 100%;
      width: 200%;
      height: 0;
      opacity: 0;
      transform-origin: center bottom;
      will-change: height, opacity, top;
      transition: all 0.3s linear;
    }
  }

  .pointer {
    cursor: pointer;
  }

  .panel {
    position: relative;
    display: flex;
    align-items: center;
    width: 1020px;
    margin-top: -20px;
  }

  .panel-left {
    .icons {
      font-size: 46px;
      cursor: pointer;
    }
    .time {
      font-size: 56px;
      font-weight: normal;
      font-family: 'FZZZHONGHJW';
    }
  }

  .panel-middel {
    position: relative;
    flex: 1;
    height: 174px;
    background: url('../../cockpitcren/images/panel_title.png') no-repeat center top;
    background-size: 100% 100%;
    .gd {
      position: absolute;
      left: -300px;
      bottom: -38px;
      width: 1000px;
      height: 158px;
      background: url('../../cockpitcren/images/head_gd.png') no-repeat center top;
      background-size: 100% 100%;
      animation: keyGdMove 30s ease infinite;
    }
  }

  .panel-right {
    justify-content: end;
    .full {
      width: 140px;
      font-size: 40px;
      border: none;
      cursor: pointer;
      color: $textColor;
    }
    .full-out {
      width: 100px;
      background: none !important;
      cursor: pointer;
      color: $textColor;
      border: 1px solid #3aa7d2;
      background: none !important;
    }
  }
}

.assets-home {
  font-size: 46px;
  cursor: pointer;
}
</style>