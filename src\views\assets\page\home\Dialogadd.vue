<template>
  <Dialog title="选择快捷方式" width="400px" :visible="visible" @close="handleClose">
    <div slot="body" v-loading="loading">
      <el-tree
        :data="treeData"
        show-checkbox
        node-key="id"
        :default-checked-keys="menuTreeObj"
        :props="defaultProps"
        :accordion="true"
        ref="tree"
      ></el-tree>
    </div>
    <div slot="footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="edit()">确定</el-button>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import { getLocalStorage, setLocalStorage } from '@/utils/cache'
import { entryTreekeys, updateMenuGrant } from '@/api/assetsv2'
@Component({
  components: {
    Dialog
  }
})
export default class extends Vue {
  @Prop() private visible!: boolean
  private treeData: any = []
  private menuTreeObj: any = []
  private loading = false
  private defaultProps: any = {
    children: 'children',
    label: 'name'
  } // tree筛选label
  // 获取本地保存的侧边栏数据
  get getAsiderMenu() {
    let asider = []
    let asiderMenu = JSON.parse(getLocalStorage('saber-asiderMenu') || '')
    if (Array.isArray(asiderMenu) && asiderMenu.length) {
      let menuList = asiderMenu[0]
      if (Array.isArray(menuList.children) && menuList.children.length) {
        asider = menuList.children
      }
    }

    return asider
  }

  mounted() {
    // 获取菜单权限
    this.treeData = this.getAsiderMenu
    let list = JSON.parse(getLocalStorage('checked-url') || ' []')
    let idList = list.map((res: any) => {
      return res.id
    })
    this.getEntrymenuList()
  }
  // 已选择数据
  private async getEntrymenuList() {
    try {
      this.loading = true
      let res = await entryTreekeys({})
      if (res.success) {

        this.menuTreeObj = res.data.menu
        this.loading = false
      }
    } catch (e) {
      console.error(e)
    }
  }
  //   点击更新
  private edit() {
    let treeRef: any = this.$refs.tree
    let ischeckidList = treeRef.getCheckedNodes().map((res: any) => {
      return res.id
    })
    this.updateMenuGrant(ischeckidList)
    // setLocalStorage('checked-url', JSON.stringify(treeRef.getCheckedNodes()))
  }
  // 更新远程数据
  private async updateMenuGrant(menuIds: any[]) {
    try {
      let res = await updateMenuGrant({
        menuIds: menuIds
      })
      if (res.success) {
        this.$emit('initData')
        this.handleClose()
      }
    } catch (e) {
      console.error(e)
    }
  }
  //   关闭
  private handleClose() {
    this.$emit('update:visible', false)
  }
}
</script>

<style lang="scss" scoped></style>
