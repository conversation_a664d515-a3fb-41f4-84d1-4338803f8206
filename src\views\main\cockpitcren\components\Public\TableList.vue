/* 自动滚动表格 */

<template>
  <div :style="{ fontSize: `${fontSize || 22}px` }"
    class="project-implementation-overview">
    <el-row class="project-implementation-overview__header"
      :span="24">
      <el-col :class="[item.align === 'right' && 'text-right']"
        v-for="(item, index) in cols"
        :key="index"
        :span="item.span">{{ item.label }}</el-col>
    </el-row>

    <div class="project-implementation-overview__list"
      ref="list">
      <el-row v-for="(data, index) in source"
        :key="`data${index}`"
        :span="24"
        :class="['project-implementation-overview__item', currentIndex === index && 'project-implementation-overview__item--checked']"
        @click.native="onItemClick(index, data, $event)">
        <el-col v-for="(item, i) in cols"
          :key="`item${i}`"
          :class="[item.align === 'right' && 'text-right']"
          :span="item.span">
          {{ data[item.prop] }}
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'

export interface ColItem {
  label: string
  span: number
  prop: string
  type?: string
  align?: 'right' | 'center' | 'left'
}

@Component({
  components: {}
})
export default class extends Vue {
  @Prop({ default: () => [] }) cols!: ColItem[]
  @Prop({ default: () => [] }) source!: Record<string, any>[]
  @Prop({ default: 5 }) private middleIndex!: number
  @Prop() private fontSize!: number

  private currentIndex = 0
  private timer: any

  // 组件初始化
  private mounted() {
    this.setTimer()
  }

  // 点击列表
  private onItemClick(index: number, data: object) {
    this.currentIndex = index
  }

  // 开启滚动定时器
  private setTimer() {
    if (this.timer) {
      clearTimeout(this.timer)
    }
    let listDom = this.$refs['list'] as HTMLDivElement
    this.timer = setInterval(() => {
      this.currentIndex++
      if (this.currentIndex > this.middleIndex && this.currentIndex < this.source.length) {
        listDom.scrollTo(0, (this.currentIndex - this.middleIndex) * 48)
      } else if (this.currentIndex === this.source.length) {
        this.currentIndex = 0
        listDom.scrollTo(0, 0)
      }
    }, 4 * 1000)
  }

  // 组件销毁
  private destroyed() {
    clearInterval(this.timer)
  }
}
</script>

<style scoped lang="scss">
.project-implementation-overview {
  position: relative;
  width: 100%;
  height: 100%;

  .rank {
    position: absolute;
    right: 10px;
    top: -64px;
    z-index: 10;
    color: #53eef5;
    font-size: 52px;
    cursor: pointer;
  }

  .el-col {
    text-align: center;
    font-weight: bold;
    padding: 0 10px;
    font-size: 28px;
    height: 56px;
    line-height: 56px;
    white-space: nowrap;
  }
  .text-right {
    text-align: right !important;
  }
  &__header {
    .el-col {
      color: rgba(74, 151, 248, 1);
    }
  }
  &__list {
    height: calc(100% - 48px);
    overflow-x: hidden;
    overflow-y: auto;
    scroll-behavior: smooth;
    &::-webkit-scrollbar {
      display: none;
    }
  }

  &__item {
    position: relative;
    z-index: 10;
    transition: all 0.5s;
    will-change: background;
    opacity: 0.7;
    cursor: pointer;
    .el-col {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      font-size: 30px;
    }

    &:nth-child(odd) {
      background: #1e5eff34;
      border-radius: 4px;
    }
    &--checked,
    &:hover {
      .el-col {
        height: 64px;
        line-height: 64px;
      }

      background: #1e5eff88 !important;
      opacity: 1;
    }
  }
}
</style>