// 财务监管弹窗
<template>
  <div>
    <Dialog
      :title="'企业发债审批-'+getmode"
      width="1100px"
      :visible="visible"
      :close-on-click-modal="true"
      :destroy-on-close="true"
      :append-to-body="true"
      @close="handleClose"
      @open="openDiaHandle"
      :loading="loading"
    >
      <div slot="body" class="">
        <el-row :gutter="40" type="flex" justify="start">
          <el-form ref="elForm" label-position="left" :disabled="mode=='see'?true:false" :model="formData" :rules="rules" size="medium" label-width="100px">
            <el-col :span="8">
              <el-form-item label="单据编号" prop="documentNo" disable>
                <el-input disabled v-model="formData.documentNo" placeholder="自动带入单据编号" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="决策机构" prop="decisionMakingBody">
                <el-input v-model="formData.decisionMakingBody" placeholder="请输入决策机构" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="发行方" prop="publisher">
                <el-input v-model="formData.publisher" placeholder="请输入发行方" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="文号" prop="referenceNo">
                <el-input v-model="formData.referenceNo" placeholder="请输入文号" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="签发人" prop="issuer">
                <el-input v-model="formData.issuer" placeholder="请输入签发人" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="发债原因" prop="issuanceBondsReason">
                <el-input v-model="formData.issuanceBondsReason" placeholder="请输入发债原因" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-row :gutter="40">
                <el-col :span="8">
                  <el-form-item label="发行目的" prop="issuancePurpose">
                    <el-input v-model="formData.issuancePurpose" placeholder="请输入发行目的" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="发行币种" prop="issuanceCurrency">
                    <el-select  :style="{ width: '100%' }" v-model="formData.issuanceCurrency" placeholder="请选择发行币种">

                      <el-option v-for="(item,index) in getDictData('issuance_currency')" :value="item.value" :label="item.label" :key="index"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="其他币种" v-show="formData.issuanceCurrency == '99'">
                    <el-input v-model="formData.currency" placeholder="请输入币种类型" clearable></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
            <el-col :span="8">
              <el-form-item label="填报人" prop="filler">
                <el-input v-model="formData.filler" placeholder="请输入填报人" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="联系电话" prop="contactPhone">
                <el-input v-model="formData.contactPhone" placeholder="请输入联系电话" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="增信措施" prop="creditEnhancementMeasures">
                <el-input v-model="formData.creditEnhancementMeasures" placeholder="请输入增信措施" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="归还措施" prop="restitutionMeasures">
                <el-input v-model="formData.restitutionMeasures" placeholder="请输入归还措施" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="年度" prop="year">
                <el-date-picker
                  type="year"
                  v-model="formData.year"
                  format="yyyy"
                  value-format="yyyy"
                  placeholder="请选择年度"
                  clearable
                   :style="{ width: '100%' }"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="其他补充" prop="otherSupplements">
                <el-input v-model="formData.otherSupplements" placeholder="请输入其他补充" clearable></el-input>
              </el-form-item>
            </el-col>
            <!-- 表单二 -->
            <el-col :span="8">
              <el-form-item label="发券类型" prop="bondsType">
                <el-input v-model="formData.bondsType" placeholder="请输入发券类型" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="发券金额" prop="bondAmount">
                <inputNumber type="decimal" v-model="formData.bondAmount" placeholder="请输入发券金额" clearable>
                <template slot="append">万</template>
                </inputNumber>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="主要用途" prop="mainUses">
                <el-input v-model="formData.mainUses" placeholder="请输入主要用途" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="有效期" prop="validTime">
                <inputNumber v-model="formData.validTime" >
                <template slot="append">年</template>
                </inputNumber>
                <!-- <el-date-picker
                  v-model="formData.validTime"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  placeholder="请选择有效期"
                  clearable
                ></el-date-picker> -->
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="有效截止日" prop="expiryDate">
                <el-date-picker
                 :style="{ width: '100%' }"
                  v-model="formData.expiryDate"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  placeholder="请选择有效截止日"
                  clearable
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="发行利率" prop="issuanceRate">
                <inputNumber max="100"  type="decimal" v-model="formData.issuanceRate" placeholder="请输入发行利率" clearable>
                  <template slot="append">%</template>
                </inputNumber>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="发行次数" prop="issuanceTimeNumber">
                <el-select v-model="formData.issuanceTimeNumber"  :style="{ width: '100%' }" placeholder="请选择次数" clearable>
                  <el-option
                   :style="{ width: '100%' }"
                    v-for="(item, index) in getDictData('issuance_times_num')"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                    :disabled="item.disabled"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="发行形式" prop="issuanceForm">
                <el-select  :style="{ width: '100%' }" v-model="formData.issuanceForm" placeholder="请选择形式" clearable>
                  <el-option
                    v-for="(item, index) in getDictData('issuance_form')"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                    :disabled="item.disabled"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="发行对象" prop="issuanceTarget">
                <el-select  :style="{ width: '100%' }" v-model="formData.issuanceTarget" placeholder="请选择发行对象" clearable>
                  <el-option
                    v-for="(item, index) in getDictData('issuance_target')"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                    :disabled="item.disabled"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="其他对象" v-if="formData.issuanceTarget == '99'">
                <el-input v-model="formData.otherIssuanceTarget" placeholder="请输入其他发行对象"></el-input>
              </el-form-item>
            </el-col>
          
          </el-form>
        </el-row>
    <AccessoryList v-model="formData.attachmentFileDTOList" dict="financial_bondIssuance_attach" title="附件列表" :mode="mode=='see'?'see':'upload'" class="m-20" />
      </div>
      <div slot="footer">
        <!-- <el-button @click="resetForm">重置</el-button> -->
        <el-button v-if="mode=='see'" @click="closeDialog">关闭</el-button>
        <el-button v-if="mode!='see'" @click="handleClose">取消</el-button>
        <el-button v-if="mode!='see'" @click="submitForm" type="primary">{{ Diaformdata.id == '' ? '提交' : '确定修改' }}</el-button>
      </div>
    </Dialog>

  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import { submitDevFormData, DetailExternalGuarantee, serialNo } from '@/api/finance'
import AccessoryList, { Accessory } from './AccessoryFileList.vue'
import { Confirm } from '@/decorators/index'
import inputNumber from "@/components/FormComment/inputNumber.vue"
  import { BusinessModule } from '@/store/modules/businessDict'

// export interface Accessory {
//   fileName: string
//   fileList: any[]
//   isRequired: boolean
//   mobelUrl?: string
//   prop?: string // 对应接口字段名
// }
@Component({
  components: {
    Dialog,
    AccessoryList,
    inputNumber
  }
})
export default class Container extends Vue {
  @Prop() private visible!: boolean
  @Prop() private Diaformdata!: any
  @Prop({default:'add'}) private mode!: "see"|'edit'|'add'
  private loading = false

  private rules: object = {
    documentNo: [
      {
        required: false,
        message: '自动带入单据编号',
        trigger: 'blur'
      }
    ],
    decisionMakingBody: [
      {
        required: true,
        message: '请输入决策机构',
        trigger: 'blur'
      }
    ],
    publisher: [
      {
        required: true,
        message: '请输入发行方',
        trigger: 'blur'
      }
    ],
    referenceNo: [
      {
        required: true,
        message: '请输入文号',
        trigger: 'blur'
      }
    ],
    issuer: [
      {
        required: true,
        message: '请输入签发人',
        trigger: 'blur'
      }
    ],
    issuanceBondsReason: [
      {
        required: true,
        message: '请输入发债原因',
        trigger: 'blur'
      }
    ],
    issuancePurpose: [
      {
        required: true,
        message: '请输入',
        trigger: 'blur'
      }
    ],
    issuanceCurrency: [
      {
        required: true,
        message: '请选择',
        trigger: 'blur'
      }
    ],
    filler: [
      {
        required: true,
        message: '请输入填报人',
        trigger: 'blur'
      }
    ],
    contactPhone: [
      {
        required: true,
        message: '请输入联系电话',
        trigger: 'blur'
      },
      { pattern: /^1[3456789]\d{9}$/, message: '手机号格式不对', trigger: 'blur' }
    ],
    creditEnhancementMeasures: [
      {
        required: true,
        message: '请输入增信措施',
        trigger: 'blur'
      }
    ],
    restitutionMeasures: [
      {
        required: true,
        message: '请输入归还措施',
        trigger: 'blur'
      }
    ],
    otherSupplements: [
      {
        required: false,
        message: '请输入其他补充',
        trigger: 'blur'
      }
    ],
    year: [
      {
        required: true,
        message: '请选择年度',
        trigger: 'blur'
      }
    ],
    bondsType: [
      {
        required: true,
        message: '请输入发券类型',
        trigger: 'blur'
      }
    ],
    bondAmount: [
      {
        required: true,
        message: '请输入发券金额',
        trigger: 'blur'
      }
    ],
    mainUses: [
      {
        required: true,
        message: '请输入主要用途',
        trigger: 'blur'
      }
    ],
    validTime: [
      {
        required: true,
        message: '请输入有效期',
        trigger: 'blur'
      }
    ],
    expiryDate: [
      {
        required: true,
        message: '请选择截止日期',
        trigger: 'change'
      }
    ],
    issuanceRate: [
      {
        required: true,
        message: '请输入发行利率',
        trigger: 'blur'
      },
      {
        pattern: /(^([-]?)[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^([-]?)(0){1}$)|(^([-]?)[0-9]\.[0-9]([0-9])?$)/,
        message: '请输入正确的利率！',
        trigger: 'blur'
      }
    ],
    issuanceTimeNumber: [
      {
        required: true,
        message: '请选择发行次数',
        trigger: 'change'
      }
    ],
    issuanceForm: [
      {
        required: true,
        message: '请选择发行形式',
        trigger: 'change'
      }
    ],
    issuanceTarget: [
      {
        required: true,
        message: '请选择发行对象',
        trigger: 'change'
      }
    ],
    otherIssuanceTarget: [
      {
        required: true,
        message: '请输入其他发行对象',
        trigger: 'change'
      }
    ],
    attachmentFile1: [
      {
        required: true,
        message: '请上传债券发行方案',
        trigger: 'change'
      }
    ],
    attachmentFile2: [
      {
        required: true,
        message: '请上传董事会决议文件',
        trigger: 'change'
      }
    ],
    attachmentFile3: [
      {
        required: true,
        message: '请上传发债请示文件',
        trigger: 'change'
      }
    ]
  }
  // 文件上传
  private uploaderDlgVisible = false
  private uploaderDlgTitle = '文件上传'
  private uploaderDlgMode = 'upload' //download  upload  see
  private uploadInitList: Array<string> = []
  private currentRow: any = {}
  private uploadtype!: string
  // upload end
  // private formData = this.Diaformdata
  private accessoryList: Accessory[] = [
    {
      fileName: '债券发行方案',
      isRequired: true,
      prop: 'attachmentFile1',
      fileList: []
    },
    {
      fileName: '董事会决议文件',
      isRequired: true,
      prop: 'attachmentFile2',
      fileList: []
    },
    {
      fileName: '发债请示文件',
      isRequired: true,
      prop: 'attachmentFile3',
      fileList: []
    }
  ]
  private formData: any = {
    documentNo: '',
    decisionMakingBody: '',
    publisher: '',
    referenceNo: '',
    issuer: '',
    issuanceBondsReason: '',
    issuancePurpose: '',
    issuanceCurrency: '',
    filler: '',
    contactPhone: '',
    creditEnhancementMeasures: '',
    restitutionMeasures: '',
    otherSupplements: '',
    bondsType: '',
    bondAmount: '',
    mainUses: '',
    validTime: '',
    expiryDate: '',

    issuanceRate: '',
    issuanceTimeNumber: '',
    issuanceForm: '',
    issuanceTarget: '',
    otherIssuanceTarget: '',
    year: '',
    attachmentFileDTOList:[],

  }
  private rules1: {} = {}
  private issuanceTimeNumberOptions: object[] = [
    {
      label: '一次',
      value: 1
    },
    {
      label: '多次',
      value: 2
    }
  ]
  private issuanceFormOptions: object[] = [
    {
      label: '境内',
      value: 1
    },
    {
      label: '境外',
      value: 2
    }
  ]
  private issuanceTargetOptions: object[] = [
    {
      label: '机构',
      value: 1
    },
    {
      label: '自然人',
      value: 2
    },
    {
      label: '其他',
      value: 3
    }
  ]
  // 打开弹窗
  // 打开弹窗
  created() {
    if(this.mode=="see"||this.mode=='edit'){
        this.getdetail()
    }
    this.openDiaHandle()
    let investProjectPlanFiling = this.formData
  }
    get getmode(){
    switch(this.mode){
      case "see":
        return "查看"
      case "edit":
        return "编辑"
      default:
        return "新增"
    }
  }
get getDictData() {
  return (key: string) => {
    let dictionary: any = BusinessModule.dictLaodData || {}
    return dictionary[key] ? dictionary[key] : []
  }
}
//  打开获取编号
  private openDiaHandle() {
    try {
      if (this.Diaformdata.id == '') {
        this.getSerialNo(1)
        ;(this.$refs['elForm'] as any).resetFields()
        return ''
      } else {
        this.getdetail()
      }
    } catch (error) {
      return ''
    }
  }
  // 获取编辑详情
  private async getdetail() {
    this.loading=true
     try {
          let res = await DetailExternalGuarantee({
            id: this.Diaformdata.id,
          })
          if (res.success) {
            this.formData= Object.assign(this.formData,res.data)
           this.loading=false
          }
        } catch (e) {
          console.error(e)
           this.loading=false
        }
  }
  private UrlList(List: any) {
    return List.map((i: any) => {
      return i.id
    })
  }
  private async getSerialNo(id: number) {
    try {
      let res: any = await serialNo({
        index: id
      })
      this.formData.documentNo = res.data
    } catch (e) {
      this.$message.info('获取编号失败')
    }
  }
  get uploadable() {
    return this.uploaderDlgMode === 'upload'
  }
  get fileList() {
    return this.currentRow.fileList
  }

  private uploadfile(value: string) {
    this.$set(this.currentRow, 'fileList', [])

    this.uploadtype = value
    this.uploaderDlgVisible = true
  }

  private onFilesChange(fileList: string[]) {
    this.currentRow.fileList = [...fileList]
  }
  private lookfile(value: Array<any>) {
    let List: any = value.map
    this.currentRow = {
      fileName: '附件',
      fileList: value.map((i: any) => {
        return {}
      }),
      isRequired: true
    }
  }
  private handleUploadComplete(fileList: Array<any>) {
    // this.formData[this.uploadtype] = fileList[0].url
    // Object.assign(this.formData,{})

    this.$set(
      this.formData,
      this.uploadtype + '',
      fileList.map((i: any) => {
        return i.id
      })
    )
    //
  }
  //新增表单数据
  // 拉取接口远程数据
  @Confirm({
    title: '提示',
    content: `是否提交？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'success'
  })
  private async submitDevForm() {
    try {
      let params = JSON.parse(JSON.stringify(this.formData))

      // this.loading = true
      let res = await submitDevFormData(params)
      if (res.success) {
        // this.option = res.data

        // this.loading = false
        this.$emit('changshowDialogAdd', false)
        this.$message.success('提交成功')
      }
    } catch (e) {
      // this.loading = false
    }
  }

  private submitForm() {
    if (!this.validateFiles()) return
    ;(this.$refs['elForm'] as any).validate((valid: any) => {
      if (!valid) return
      // TODO 提交表单
        if(this.formData.attachmentFileDTOList.length==0){
        this.$message.warning("请上传附件")
        return
      }
      this.submitDevForm()
    })
  }
  private validateFiles(): boolean {
    // let investProjectPlanFiling = this.formData

    // for (let index in this.accessoryList) {
    //   let accessory = this.accessoryList[index]
    //   let fileUrl = accessory.fileList.map((item) => item.id)
    //   this.formData[accessory.prop as keyof typeof investProjectPlanFiling] = fileUrl
    //   if (fileUrl.length == 0 && accessory.isRequired) {
    //     this.$message.warning(`请上传${accessory.fileName}！`)
    //     return false
    //   }
    // }
    return true
  }
  private resetForm() {
    // this.formData=this.$options.data().formData
    ;(this.$refs['elForm'] as any).resetFields()
  }
  @Confirm({
    title: '提示',
    content: `是否关闭？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private handleClose() {
    this.resetForm()
    this.$emit('changshowDialogAdd', false)
  }
  private closeDialog(){
     this.$emit('changshowDialogAdd', false)
  }
}
</script>

<style lang="scss" scoped>
::v-deep.el-form-item__label {
  float: none; // 取消label左浮动
  word-break: break-word; // 支持单词截断换行
}
::v-deep.el-input--medium .el-input__inner {
  height: auto !important;
  line-height: auto;
}
/* 过于长的label分两行展示样式 */
::v-deep.fold_label .el-form-item__label {
  white-space: pre-line;
  text-align-last: justify;
  text-align: justify;
  margin-top: -4px;
  line-height: 25px;
  text-justify: distribute-all-lines;
}
/* 其他label根据宽度自动撑开 */
::v-deep.el-form-item__label {
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
}
.file-box {
  text-align: center;
  p:nth-child(2) {
    text-align: center;
  }
  ::v-deep.el-form-item__error {
    text-align: center;
  }
}
::v-deep .el-input.is-disabled .el-input__inner{
  color: #2e2e2e;
  background-color: #fff;
  width: 100% !important;
}

</style>

