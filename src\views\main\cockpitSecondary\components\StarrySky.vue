/** 星空 */
<template>
  <section class="starry-sky">
    <canvas id="canvas"
      width="3240"
      height="1823"></canvas>
  </section>
</template>

<script lang="ts">
/* eslint-disable */
import { Component, Vue } from 'vue-property-decorator'

@Component
export default class extends Vue {
  mounted() {
    this.createStarrySky()
  }

  private createStarrySky() {
    let canvas = document.getElementById('canvas') as HTMLCanvasElement
    let ctx = canvas.getContext('2d')

    let w = (canvas.width = window.innerWidth) //1536
    let h = (canvas.height = window.innerHeight) //316
    let hue = 217
    let stars: any[] = []
    let count = 0
    let maxStars = 1000 //星星数量

    let canvas2 = document.createElement('canvas') as HTMLCanvasElement
    let ctx2 = canvas2.getContext('2d') as CanvasRenderingContext2D
    canvas2.width = 50
    canvas2.height = 70
    let half = canvas2.width / 10
    let gradient2 = ctx2.createRadialGradient(half, half, 0, half, half, half)

    // gradient2.addColorStop(0.025, '#fff')
    // gradient2.addColorStop(0.1, 'hsl(' + hue + ', 61%, 33%)')
    // gradient2.addColorStop(0.25, 'hsl(' + hue + ', 64%, 6%)')
    // gradient2.addColorStop(1, 'transparent')

    gradient2.addColorStop(0, '#5db0ea')
    gradient2.addColorStop(1, '#5db0ea')

    ctx2.fillStyle = gradient2
    ctx2.beginPath()
    ctx2.arc(half, half, half, 0, Math.PI * 2)
    ctx2.fill()

    // End cache

    function random(min: number, max: number) {
      if (arguments.length < 2) {
        max = min
        min = 0
      }

      if (min > max) {
        var hold = max
        max = min
        min = hold
      }

      return Math.floor(Math.random() * (max - min + 1)) + min
    }

    //轨道
    function maxOrbit(x: number, y: number) {
      var max = Math.max(x, y), //1536,316
        diameter = Math.round(Math.sqrt(max * max + max * max)) //2172,Math.round:四舍五入; Math.sqrt:开根号
      return diameter / 1 //1086
      //星星移动范围，值越大范围越小，
    }

    var Star = function (this: any) {
      // @ts-ignore
      this.orbitRadius = random(maxOrbit(w, h)) //orbit:轨道,0~1086
      // @ts-ignore
      let radius = random(60, this.orbitRadius) / 8
      this.radius = +radius > 30 ? 30 : +radius
      //星星大小
      // @ts-ignore
      this.orbitX = w / 2 //768
      // @ts-ignore
      this.orbitY = h / 2 //158
      // @ts-ignore
      this.timePassed = random(0, maxStars) //0~1300
      // @ts-ignore
      this.speed = random(this.orbitRadius) / 200000
      //星星移动速度
      // @ts-ignore
      this.alpha = random(2, 10) / 80

      count++
      // @ts-ignore
      stars[count] = this
    }

    Star.prototype.draw = function () {
      var x = Math.sin(this.timePassed) * this.orbitRadius + this.orbitX,
        y = Math.cos(this.timePassed) * this.orbitRadius + this.orbitY,
        // @ts-ignore
        twinkle = random(10)

      if (twinkle === 1 && this.alpha > 0) {
        this.alpha -= 0.06
      } else if (twinkle === 2 && this.alpha < 1) {
        this.alpha += 0.06
      }
      // @ts-ignore
      ctx.globalAlpha = this.alpha
      // @ts-ignore
      ctx.drawImage(canvas2, x - this.radius / 2, y - this.radius / 2, this.radius, this.radius)
      this.timePassed += this.speed
    }

    for (var i = 0; i < maxStars; i++) {
      // @ts-ignore
      new Star()
    }

    function animation() {
      // @ts-ignore
      ctx.clearRect(0, 0, canvas.width, canvas.height) // 清除上一次绘制图形
      //ctx.globalCompositeOperation = 'source-over' //后绘制的图形位于先绘制的图形上方

      // @ts-ignore
      ctx.globalAlpha = 0 // 背景透明度
      // @ts-ignore
      ctx.fillStyle = 'hsla(' + hue + ', 64%, 6%, 2)' //H:色相,S:饱和度,L:明度;
      // @ts-ignore
      //ctx.fillRect(0, 0, w, h)
      // @ts-ignore
      //ctx.globalCompositeOperation = 'lighter' //后绘制的图形与先绘制的图形重叠部分的值相加,使该部分变亮。
      for (var i = 1, l = stars.length; i < l; i++) {
        stars[i].draw() //star原型上的方法
      }

      window.requestAnimationFrame(animation)
    }

    animation()
  }
}
</script>


<style scoped lang="scss">
.starry-sky {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  canvas {
    width: 100%;
    height: 100%;
  }
}
</style>