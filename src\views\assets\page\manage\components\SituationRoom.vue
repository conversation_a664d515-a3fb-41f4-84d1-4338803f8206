<template>
  <section class="situation-roomLand-wrap">
    <el-form :inline="true"
      :model="formData"
      ref="ruleForm"
      label-width="84px"
      class="form-box">
      <el-form-item label="关键词"
        prop="keyword">
        <el-input v-model.trim="formData.keyword"
          clearable
          placeholder="资产编号/资产名称"
          class="mode-input" />
      </el-form-item>
      <el-form-item label="使用状态"
        prop="useStatus">
        <el-select v-model="formData.useStatus"
          clearable
          placeholder="请选择"
          class="mode-input">
          <el-option v-for="item of getDictData('asset_use_status')"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="资产用途"
        prop="purpose">
        <el-select v-model="formData.purpose"
          clearable
          placeholder="请选择"
          class="mode-input">
          <el-option v-for="item of getDictData('asset_purpose')"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="朝向"
        prop="roomFace">
        <el-select v-model="formData.roomFace"
          clearable
          placeholder="请选择"
          class="mode-input">
          <el-option v-for="item of getDictData('asset_room_face')"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="面积筛选"
        prop="minCoveredArea">
        <InputNumber v-model.trim="formData.minCoveredArea"
          type="decimal"
          placeholder="最小面积"
          class="mode-input">
          <template slot="append">㎡</template>
        </InputNumber>
      </el-form-item>
      <el-form-item label=" "
        prop="maxCoveredArea">
        <InputNumber v-model.trim="formData.maxCoveredArea"
          type="decimal"
          placeholder="最大面积"
          class="mode-input">
          <template slot="append">㎡</template>
        </InputNumber>
      </el-form-item>
      <el-form-item label=" ">
        <el-button type="primary"
          @click="searchForm">查 询</el-button>
        <el-button @click="resetForm">重 置</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="tableData"
      border
      v-loading="loadingTable"
      height="300px"
      style="width:100%;">
      <!-- <el-table-column prop="assetNo"
        label="资产编号"
        width="160"
        :show-overflow-tooltip="true" /> -->
<!-- 
      <el-table-column prop="itemName"
        label="资产明细名称"
        width="120"
        :show-overflow-tooltip="true" /> -->
      <el-table-column prop="itemNo"
        label="资产明细编号"
        width="120"
        :show-overflow-tooltip="true" />
      <el-table-column prop="location"
        label="资产坐落"
        width="200"
        :show-overflow-tooltip="true" />
      <el-table-column prop="coveredArea"
        label="总面积(㎡)"
        width="120"
        :show-overflow-tooltip="true" />
      <!-- <el-table-column prop="landArea"
        label="土地面积(㎡)"
        width="120"
        :show-overflow-tooltip="true" /> -->
      <el-table-column label="层/总层"
        :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{scope.row.currentFloor}}</span>
          /
          <span>{{scope.row.totalFloor}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="orgName"
        label="所属单位名称"
        width="120"
        :show-overflow-tooltip="true" />
      <el-table-column prop="purpose"
        label="资产用途"
        :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{getDictLabelData('asset_purpose', scope.row.purpose)}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="roomFace"
        label="朝向"
        :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{getDictLabelData('asset_room_face', scope.row.roomFace)}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="useStatus"
        label="使用状态"
        :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{getDictLabelData('asset_use_status', scope.row.useStatus)}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="safetyLevel"
        label="安全等级"
        :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{getDictLabelData('asset_quality_leve', scope.row.safetyLevel)}}</span>
        </template>
      </el-table-column>

      <el-table-column width="70"
        fixed="right"
        label="操作">
        <template slot-scope="scope">
          <el-button @click="handleClick('see', scope.row)"
            type="text">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination :current-page="page.current"
      :page-size="page.size"
      :total="page.total"
      background
      layout="total, prev, pager, next, jumper"
      class="pagination-box"
      @current-change="handleCurrentChange" />

    <!-- 详情弹窗 -->
    <DetailRoomLand v-if="visibleDetail"
      :visible.sync="visibleDetail"
      :id="detailRow.id" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { assetsListPage } from '@/api/assetsv2'
import { ElForm } from 'element-ui/types/form'
import { Loading } from '@/decorators/index'
import { BusinessModule } from '@/store/modules/businessDict'
import InputNumber from '@/components/FormComment/inputNumber.vue'
import DetailRoomLand from './DetailRoomLand.vue'

@Component({
  components: {
    InputNumber,
    DetailRoomLand
  }
})
export default class extends Vue {
  @Prop() private assetType!: number // 资产类型
  @Prop() private assetNo!: string // 资产编号

  private loadingTable = false
  private visibleDetail = false
  private detailRow = {}
  private tableData = []
  private page = {
    current: 1,
    size: 20,
    total: 0
  }
  private formData = {
    keyword: '',
    useStatus: '',
    purpose: '',
    roomFace: '',
    minCoveredArea: '',
    maxCoveredArea: ''
  }

  // 获取字典数据
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }

  // 获取字典项label
  get getDictLabelData() {
    return (type: string, value: string) => {
      if (this.getDictData(type).length) {
        let obj = this.getDictData(type).find((item: any) => {
          return +item.value === +value
        })

        return obj ? obj.label : ''
      } else {
        return ''
      }
    }
  }

  // 组件初始化
  private mounted() {
    this.tableLond()
  }

  // 当前页改变
  private handleCurrentChange(val: number) {
    this.page.current = val
    this.tableLond()
  }

  // 获取表格数据
  @Loading('loadingTable')
  private async tableLond() {
    let objData = Object.assign(
      {
        assetNo: this.assetNo,
        size: this.page.size,
        current: this.page.current
      },
      this.formData
    )

    let { data } = await assetsListPage(objData)

    this.tableData = data.records || []
    this.page.current = data.current || 1
    this.page.total = data.total
  }

  // 搜索
  private searchForm() {
    this.tableLond()
  }

  // 操作
  private handleClick(type: string, row: object) {
    switch (type) {
      case 'see': // 查看
        this.detailRow = row
        this.visibleDetail = true
        break
    }
  }

  // 重置
  private resetForm() {
    ;(this.$refs.ruleForm as ElForm).resetFields()
    this.tableLond()
  }
}
</script>

<style scoped lang="scss">
.situation-roomLand-wrap {
  position: relative;
  .form-box {
    margin-bottom: 14px;
    padding: 10px 0 0;
    border-radius: 4px;
    background: rgb(172 196 221 / 10%);
    border: 1px solid rgb(172 196 221 / 20%);
    .el-form-item {
      margin-bottom: 10px;
    }
    .mode-input {
      width: 168px;
    }
  }
  .pagination-box {
    margin-top: 10px;
    text-align: right;
  }
}
</style>