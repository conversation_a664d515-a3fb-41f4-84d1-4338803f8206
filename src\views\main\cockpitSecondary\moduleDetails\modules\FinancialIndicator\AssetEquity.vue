/* 历年资产+权益趋势 */

<template>
  <section class="profit-trend-wrap">
    <CommonTitle :title="title"
      :isRotuerThrow="true"
      :routerObj="typeBizObj" />

    <div class="content-box"
      ref="refEcharts" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { echartConfigure } from '@/views/main/cockpitcren/baseData'
import { deepClone } from '@/utils'
import { bigNumberFormat } from '@/utils'
import * as echarts from 'echarts'
import CommonTitle from '@/views/main/cockpitSecondary/components/CommonTitle.vue'

type EChartsOption = echarts.EChartsOption

@Component({
  components: {
    CommonTitle
  }
})
export default class extends Vue {
  @Prop() private title!: string // 标题
  @Prop() private echartsData!: any // 渲染数据
  @Prop({
    default: () => {
      return {}
    }
  })
  private typeBizObj?: object // 标题跳转

  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private seriesData: any[] = []
  private option: EChartsOption = {}
  private legendData: string[] = []
  private xAxisData: string[] = []

  // 数据变化，渲染视图
  @Watch('echartsData', { deep: true })
  private changeEchartsData() {
    this.initData()
  }

  // 万元数据转化成亿元
  get getBigNumberFormat() {
    return (num: string | number) => {
      return bigNumberFormat(+num).value || 0
    }
  }

  // 组件初始化
  private mounted() {
    this.chartDom = this.$refs.refEcharts as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)
  }

  // 请求数据接口
  private async initData() {
    let data = deepClone(this.echartsData)

    // 组装echarts数据
    if (!Array.isArray(data) || !data.length) return

    let legendList: string[] = []
    let xAxisList: string[] = []
    let seriesList: any[] = []
    data.forEach((item, index) => {
      xAxisList.push(item.year)
      item.list.forEach((itemChild: any) => {
        !index && legendList.push(itemChild.label)
      })
    })

    legendList.forEach((item, index) => {
      let arr: number[] = []
      data.forEach((itemData: any, indexData: number) => {
        if (item === itemData.list[index].label) {
          arr.push(+this.getBigNumberFormat(itemData.list[index].value))
        }
      })

      seriesList.push({
        name: item,
        data: arr,
        type: 'line',
        symbol: 'circle',
        smooth: false,
        lineStyle: {
          width: 4
        }
      })
    })

    this.legendData = legendList
    this.xAxisData = xAxisList
    this.seriesData = seriesList
    this.initEcharts()
  }

  // 初始化数据
  private initEcharts() {
    let textSize = echartConfigure.textSize
    let legendSize = echartConfigure.legendSize
    let legendData = this.legendData
    let xAxisData = this.xAxisData
    let seriesData = this.seriesData

    this.option = {
      color: [
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(252, 180, 94, 1)'
            },
            {
              offset: 1,
              color: 'rgba(252, 180, 94, 1)'
            }
          ],
          global: false // 缺省为 false
        },
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(62, 238, 255, 1)'
            },
            {
              offset: 1,
              color: 'rgba(62, 238, 255, 1)'
            }
          ],
          global: false // 缺省为 false
        },
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(228, 0, 127, 1)'
            },
            {
              offset: 1,
              color: 'rgba(228, 0, 127, 1)'
            }
          ],
          global: false
        }
      ],
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#00000000',
        shadowColor: '#00000000',
        borderWidth: 0,
        formatter: (params: any) => {
          let dom = ''
          let year = params[0].axisValue || ''

          // 根据数据中的同比正负值，编辑不同的样式
          params.forEach((item: any, index: number) => {
            let style = ''
            let rote = ''

            let filterList = this.echartsData.filter((itemfil: any) => {
              return item.name === itemfil.year
            })

            if (filterList.length) {
              let roteList = filterList[0].list.filter((itemFilter: any) => {
                return itemFilter.label === item.seriesName
              })

              if (roteList.length) rote = roteList[0].rote
            }

            if (+rote > 0) {
              style = `
                <span style="color: #fb3f3f; margin-left: 10px; font-size: 30px;">
                  <img style="width:22px;" src="${require('../../../images/thows.png')}" />${rote}%
                </span>
              `
            } else if (+rote < 0) {
              style = `
                <span style="color: #00a92b; margin-left: 10px; font-size: 30px;">
                  <img style="width:22px;" src="${require('../../../images/thowx.png')}" />${rote}%
                </span>
              `
            }

            dom += `
              <div style="display: flex; align-items: center; justify-content: space-between;">
                <span style="padding-right: 40px;">${item.seriesName}</span>
                <span>${item.value}亿元</span>
                ${style}
              </div>
              `
          })

          return `
            <div style="font-size:34px; line-height: 70px; color: #fff; border-radius:10px;border:2px solid #0C3EB6; padding: 20px; display: flex;flex-direction: column; background: linear-gradient(0deg, rgba(0, 103, 177, 1), rgba(0, 103, 177, 1));">
              <div style="font-weight:bold;">${year}年</div>
              ${dom}
            </div>
          `
        }
      },
      legend: {
        show: false,
        top: 40,
        right: 10,
        padding: 0,
        width: 500,
        itemHeight: 24,
        itemWidth: 24,
        itemStyle: {
          borderCap: 'butt'
        },
        lineStyle: {
          type: [0, 10],
          dashOffset: 5
        },
        textStyle: {
          color: '#fff',
          fontSize: 24,
          fontWeight: 'bold'
        },
        data: legendData
      },
      grid: {
        top: '20%',
        left: '1%',
        right: '1%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisPointer: {
          type: 'shadow'
        },
        axisLabel: {
          fontSize: textSize,
          fontWeight: 'bold'
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#5db0ea'
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '亿元',
        nameTextStyle: {
          color: '#5db0ea',
          align: 'left',
          fontWeight: 'bold',
          fontSize: textSize / 1.2
        },
        axisLabel: {
          fontSize: textSize,
          fontWeight: 'bold'
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#5db0ea'
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0, 103, 177, 0.6)'
          }
        }
      },
      series: seriesData,
      animationDelay: function (idx) {
        return idx * 1000
      }
    }

    this.myChart && this.myChart.setOption(this.option, true)
  }
}
</script>

<style scoped lang="scss">
.profit-trend-wrap {
  position: relative;
  height: 100%;
  .content-box {
    width: 100%;
    height: 100%;
    transform: translateY(-80px);
  }
}
</style>


