<template>
  <section class="jump-cockpit-wrap">
    <div v-for="(item,index) of outPathList"
      :key="index"
      class="mode"
      @click="routeOutPath(item.path)">
      <img src="@/assets/images/public/cockpit-icon.png" />
      <p>{{item.name}}</p>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

@Component({})
export default class extends Vue {
  @Prop() private path!: string

  private outPathList = [
    {
      path: ''
    }
  ]

  private created() {
    this.outPathList[0].path = this.path
  }

  // 点击各模块打开新页面
  private routeOutPath(path: string) {
    this.$router.push(path)
  }
}
</script>

<style scoped lang="scss">
.jump-cockpit-wrap {
  position: fixed;
  right: 0;
  bottom: 40px;
  z-index: 10000;
  overflow-x: hidden;

  .mode {
    display: flex;
    align-items: center;
    padding: 4px;
    font-size: 13px;
    border-radius: 4px 0 0 4px;
    cursor: pointer;
    opacity: 0.5;
    background: #ffff;
    border: 2px solid #aeb5cd;
    border-right: 0;
    box-shadow: 0 0 8px 0 rgba(180, 60, 60, 0.21);
    &:hover {
      opacity: 1;
    }
    img {
      width: 24px;
      height: 24px !important;
      height: auto;
    }
    p {
      margin: 0;
      font-size: 13px;
      line-height: 14px;
    }
  }
}
</style>