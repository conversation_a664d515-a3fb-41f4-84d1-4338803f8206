// 导入

<template>
  <section class="import-wrap">
    <el-button icon="el-icon-download"
      @click="visible = true">{{btnName}}</el-button>

    <Dialog :title="importTitle"
      width="480px"
      :visible="visible"
      :close-on-click-modal="true"
      :destroy-on-close="true"
      :append-to-body="true"
      @close="closeHandle">
      <!-- 内容区域 -->
      <div slot="body">
        <el-form ref="form"
          :rules="formRules"
          :model="formModel"
          label-width="100px">
          <el-form-item label="上传附件"
            required>
            <el-upload class="upload-demo"
              ref="upload"
              :accept="accept"
              :limit="limit"
              :data="uploadParams"
              :multiple="false"
              :auto-upload="false"
              :headers="aosHeader"
              :action="aosActionUrl"
              :on-change="handleChange"
              :file-list="fileList"
              :on-success="handleAOSSuccess"
              :on-error="handleAOSError">
              <el-button slot="trigger"
                size="small"
                type="primary">选取文件</el-button>

              <div slot="tip"
                class="el-upload__tip">
                {{tip}}
                <el-button v-if="isDownMobel"
                  type="text"
                  :loading="loadingMoble"
                  @click="downMobel">下载模版</el-button>
              </div>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>

      <!-- 底部按钮 -->
      <div slot="footer">
        <el-button @click="closeHandle">取消</el-button>
        <el-button type="primary"
          class="primary-buttom"
          :loading="loading"
          @click="confirmHandle">确认</el-button>
      </div>
    </Dialog>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { replaceOssStr } from '@/utils'
import { ElUpload } from 'element-ui/types/upload'
import { getToken } from '@/components/Uploader/config'
import { BusinessModule } from '@/store/modules/businessDict'
import { getReadOnlySignUrl } from '@/api/public'
import Dialog from '@/components/Dialog/index.vue'

@Component({
  components: {
    Dialog
  }
})
export default class Import extends Vue {
  @Prop({ default: '导入' }) private btnName?: string // 按钮名称
  @Prop({ default: '导入' }) private importTitle?: string // 导入弹窗标题
  @Prop({ default: '.xls,.xlsx' }) private accept?: string // 导入文件格式
  @Prop({ default: '请上传文件，且单个不超过20M' }) private tip?: string // 导入文件提示语
  @Prop({ default: false }) private isDownMobel?: boolean // 是否需要下载模版
  @Prop() private downMobelUrl?: string // 如果 isDownMobel 为true，需要提供模版地址
  @Prop() private downMobelName?: string // 如果 isDownMobel 为true，可以提供下载文件名称，如果没有提供就采用链接后缀做为文件名称
  @Prop({ default: 1 }) private limit?: number // 文件上传个数
  @Prop({
    default: () => {
      return {}
    }
  })
  private params?: object // 文件导入提供的额外参数

  @Prop() private submitUrl!: string // 提交接口地址

  private loading = false
  private loadingMoble = false
  private visible = false

  // 提交表单数据
  private aosActionUrl = ''
  private aosHeader = {}
  private formModel = {}
  private formRules = {}
  private uploadParams = {}
  private fileList: File[] = []

  // 获取字典数据
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }

  // 数据初始化
  created() {
    // 设置文件上传必要数据
    this.aosHeader = {
      Authorization: `Basic c2FiZXI6c2FiZXJfc2VjcmV0`,
      'Fht-Auth': `bearer ${getToken()}`
    }

    // 设置提交的地址
    let url = ''
    if(window.location.origin.includes('szjg.gzw.jinhua.gov.cn')) {
      url = process.env.VUE_APP_BASE_URL
    } else {
      url = process.env.VUE_APP_DPM1_URL
    }

    this.aosActionUrl = `${url}${this.submitUrl}`
    this.setUploadParams()
  }

  // 设置提交时的额外参数
  private setUploadParams() {
    this.uploadParams = Object.assign({}, this.params, this.formModel)
  }

  // 下载模板
  @Loading('loadingMoble')
  private async downMobel() {
    let downMobelName = this.downMobelName as string
    let downMobelUrl = this.downMobelUrl as string

    const res:any = await getReadOnlySignUrl({
      fileName: downMobelUrl, 
      zipFlag: false 
    });

    if (res.code !== 200) return;

    // 配合后端，做一个域名替换
    downMobelUrl = JSON.parse(JSON.stringify(res.data));
    downMobelUrl = replaceOssStr(downMobelUrl);

    console.log('替换地址：' + downMobelUrl)

    if (downMobelName) {
      this.getBlob(downMobelUrl).then((blob: any) => {
        let a = document.createElement('a')
        let url = window.URL.createObjectURL(blob)
        a.setAttribute('href', url)
        a.setAttribute('target', '_blank')
        a.setAttribute('download', downMobelName)
        document.body.appendChild(a)
        a.click()
        a.remove()
        window.URL.revokeObjectURL(url)
      })
    } else {
      window.open(downMobelUrl)
    }
  }

  // 转换下载的文件名称
  private getBlob(url: string) {
    return new Promise((resolve) => {
      const xhr = new XMLHttpRequest()
      xhr.open('GET', url, true)
      xhr.responseType = 'blob'
      xhr.onload = () => {
        if (xhr.status === 200) {
          resolve(xhr.response)
        }
      }
      xhr.send()
    })
  }

  // 文件上传
  handleChange(file: File, fileList: File[]) {
    this.fileList = [...fileList]
  }

  // 文件上传导入成功
  handleAOSSuccess(res: any) {
    this.$message.success(res.msg || '导入成功！')
    this.updataHandle()
    this.closeHandle()
  }

  // 文件上传失败
  private handleAOSError(error: object, file: File, fileList: Array<File>) {
    this.$message.warning('上传失败，请重新上传')
  }

  // 确认
  @Loading('loading')
  confirmHandle() {
    if (this.fileList.length > 0) {
      ;(this.$refs.upload as ElUpload).submit()
    } else {
      this.$message.warning('请选择文件')
    }
  }

  // 关闭弹窗
  private closeHandle() {
    this.visible = false
  }

  // 触发父组件更新
  private updataHandle() {
    this.$emit('updataHandle')
  }
}
</script>

<style scoped lang="scss">
.import-wrap {
  display: inline-block;
}
</style>