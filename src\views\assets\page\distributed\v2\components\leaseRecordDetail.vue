<template>
  <div>
    <el-card class="box-card m-t-5" v-for="(item, index) in itemData" :key="index">
      <el-descriptions :title="item.subjectName" :column="2">
        <el-descriptions-item label="承租人" :span="2" label-class-name="my-label" content-class-name="my-content">
          {{ item.renterName }}
        </el-descriptions-item>
        <el-descriptions-item label="房间">8间</el-descriptions-item>
        <el-descriptions-item label="面积">{{ item.totalRentArea }}㎡</el-descriptions-item>
        <el-descriptions-item label="合同金额" :span="2">{{ item.totalFee }}万元</el-descriptions-item>
        <el-descriptions-item label="合同时间">{{ item.startTime + '--' + item.endTime }}</el-descriptions-item>
      </el-descriptions>
    </el-card>
    <div v-if="itemData.length == 0" class="text-center">暂无数据</div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

@Component({
  components: {}
})
export default class extends Vue {
  @Prop() private itemData!: any
}
</script>

<style lang="scss" scoped>
::v-deep .el-descriptions-item__container .el-descriptions-item__content {
  font-size: 14px;
  color: #303133;
}
::v-deep .el-descriptions-item__label {
  font-size: 14px;
  color: #909399;
}
</style>
