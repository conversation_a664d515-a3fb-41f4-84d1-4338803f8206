<template>
  <el-header class="header-wrap">
    <div class="left-box">
      <img src="@/assets/images/logo.png" />
      <h4>
        <label>金华市国资委财务监管系统</label>
      </h4>
    </div>
  </el-header>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { BusinessModule } from '@/store/modules/businessDict'

@Component({
  name: 'Header',
  components: {}
})
export default class Header extends Vue {
  // 组件初始化
  private mounted() {
    this.getBusinessModule()
  }

  // 获取各个业务系统字典项
  private async getBusinessModule() {
    await BusinessModule.getDictLoad('finance')
  }
}
</script>

<style scoped lang="scss">
.header-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px !important;
  background: #b43c3c;

  .left-box {
    display: flex;
    align-items: center;
    img {
      width: 36px;
    }
    h4 {
      margin: 0;
      font-size: 24px;
      color: #fff;
      margin-left: 10px;
      display: flex;
      align-items: center;
    }
  }
}
</style>
