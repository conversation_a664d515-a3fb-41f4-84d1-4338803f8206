/* 今日概况 */

<template>
  <section class="today-overview-wrap">
    <CommonTitle :title="title"
      :isRotuerThrow="true"
      :routerObj="{
        bizAppCode: 'alert',
        detailsPath: '/prewarning/events'
      }" />

    <div class="content-box">
      <div class="mode-left">
        <div v-for="(item, index) of earlyWarningData.warningType"
          :key="index"
          class="mode">
          <p>{{item.label}}</p>
          <CountTo :decimals="0"
            :startVal='0'
            :endVal='+item.value'
            :duration='2000'
            class="count"
            :class="[{'h':!index+1}, {'r':+index+1===2}, {'w':+index+1===3}, {'l':+index+1===4}]" />
        </div>
      </div>

      <div class="mode-right">
        <img class="img"
          src="@/views/main/cockpitSecondary/images/today_overview_bg.png" />

        <div v-for="(item,index) of earlyWarningData.warningInfo"
          :key="index"
          class="mode"
          :class="`mode${index+1}`">
          <i v-if="+item.rote > 0"
            class="s">+{{item.rote}}%</i>
          <i v-if="+item.rote < 0"
            class="x">{{item.rote}}%</i>
          <CountTo :decimals="0"
            :startVal='0'
            :endVal='+item.value'
            :duration='2000'
            class="count" />
          <span>{{item.label}}</span>
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { deepClone } from '@/utils'
import CountTo from 'vue-count-to'
import CommonTitle from '@/views/main/cockpitSecondary/components/CommonTitle.vue'

@Component({
  components: {
    CountTo,
    CommonTitle
  }
})
export default class extends Vue {
  @Prop() private title!: string // 标题
  @Prop() private echartsData!: any // 渲染数据

  private earlyWarningData = {}

  // 数据变化，渲染视图
  @Watch('echartsData', { deep: true })
  private changeEchartsData() {
    this.initData()
  }

  // 组件初始化
  private mounted() {
    this.initData()
  }

  // 请求数据接口
  private async initData() {
    this.earlyWarningData = deepClone(this.echartsData)
  }
}
</script>

<style scoped lang="scss">
.today-overview-wrap {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  h6,
  p {
    margin: 0;
  }

  @keyframes keySwirlMove {
    0% {
      transform: translate(0, 0) rotateZ(0deg) scale(1);
    }
    50% {
      transform: translate(0, 0) rotateZ(180deg) scale(0.6);
    }
    100% {
      transform: translate(0, 0) rotateZ(360deg) scale(1);
    }
  }

  .content-box {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .mode-left {
      width: 190px;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-right: 30px;
      .mode {
        padding: 0 4px;
        text-align: center;
        box-sizing: border-box;
        background: linear-gradient(0deg, rgba(9, 57, 160, 0.8), rgba(7, 44, 127, 0.8));
        border: 2px solid #0c3eb6;
        border-radius: 7px;
        p {
          font-size: 32px;
          line-height: 60px;
        }
        .count {
          font-size: 56px;
          font-weight: normal;
          font-family: 'PangMenZhengDao';
        }
      }
    }
    .mode-right {
      flex: 1;
      height: 549px;
      position: relative;
      .img {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;
        z-index: 1;
        width: 90%;
        height: 90%;
        opacity: 0.7;
        animation: keySwirlMove 20s linear infinite;
      }
      .mode {
        $w: 170px;
        position: absolute;
        z-index: 2;
        width: $w;
        height: $w;
        margin: auto;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        background: linear-gradient(0deg, #0939a0, rgba(13, 36, 89, 1));
        border: 5px solid rgba($color: #00fcfd, $alpha: 0.8);
        border-radius: 50%;
        i {
          margin-top: 8px;
          font-size: 28px;
          font-family: 'digital-7';
        }
        .count {
          color: #40eeff;
          font-size: 50px;
          font-weight: normal;
          font-family: 'PangMenZhengDao';
          line-height: 50px;
        }
      }

      .mode1 {
        left: 0;
        right: 0;
      }
      .mode2 {
        right: 0;
        top: 0;
        bottom: 0;
      }
      .mode3 {
        left: 0;
        right: 0;
        bottom: 0;
      }
      .mode4 {
        top: 0;
        bottom: 0;
      }
    }
  }

  .s {
    color: rgba($color: #fb3f3f, $alpha: 0.8);
  }
  .x {
    color: rgba($color: #00a92b, $alpha: 0.8);
  }
  .h {
    color: #fb3f3f;
  }
  .r {
    color: #ff7500;
  }
  .w {
    color: #f5b331;
  }
  .l {
    color: #249df7;
  }
}
</style>


