<template>
  <Dialog width="1200px"
    :visible="visible"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :append-to-body="true"
    @close="handleClose">
    <!-- 自定义标题 -->
    <div slot="header"
      class="header-box">
      <div class="title">
        <h4>{{ detailData.location }}</h4>
        <span v-if="+detailData.assetType === 1">房</span>
        <span v-if="+detailData.assetType === 2">土</span>
      </div>
    </div>

    <!-- 内容区域 -->
    <div v-loading="loading"
      slot="body"
      class="body-box">
      <div class="conter-info">
        <!-- 左侧内容 -->
        <div class="left mode">
          <p>子资产编号：{{ detailData.itemNo || '--' }}</p>
          <p>所属单位：{{ detailData.orgName || '--' }}</p>
          <p>{{ +detailData.assetType === 1 ? '建筑总面积' : '土地面积' }}：{{ +detailData.assetType === 1 ? detailData.coveredArea : detailData.landArea || '--' }} ㎡</p>
          <p>租金总额：{{ detailData.totalBillFee || '--' }}元</p>
          <p>应收租金：{{ detailData.billFee || '--' }}元</p>
          <p>实收租金：{{ detailData.totalPaidFee || '--' }}元</p>
          <p>
            <span>附件文件：</span>
            <span v-for="(item, index) of detailData.fileUrlList"
              :key="index"
              class="file"
              @click="openFileName(item.url)">{{ item.originalFileName }}</span>
          </p>
        </div>

        <!-- 中间内容：先隐藏，对应数据去掉了该值 -->
        <div v-show="false"
          class="middle mode">
          <el-carousel height="180px"
            v-if="detailData.imgList">
            <el-carousel-item v-for="item in 4"
              :key="item">
              <el-image style="height:100%;
              width:100%"
                fit="cover"
                src="https://img2.baidu.com/it/u=1419977712,1630679449&fm=253&fmt=auto&app=120&f=JPEG?w=902&h=500"></el-image>
            </el-carousel-item>
          </el-carousel>

          <el-empty v-else
            :image-size="130"
            style="height:150px;"
            description="暂无图片" />
          <!-- <div id="chartDom" /> -->
        </div>

        <!-- 右侧内容 -->
        <div class="right mode"
          style="width: 350px; flex: none;">
          <el-carousel v-if="Array.isArray(detailData.picUrlList) && detailData.picUrlList.length"
            :interval="8000"
            height="180px"
            class="carousel">
            <el-carousel-item v-for="(item, index) in detailData.picUrlList"
              :key="index">
              <img :src="getImgSrc(item)" />
            </el-carousel-item>
          </el-carousel>

          <el-empty v-else
            :image-size="130"
            style="width: 350px; height:150px;"
            description="暂无图片" />
        </div>
      </div>

      <el-tabs v-model="activeName">
        <el-tab-pane label="租赁情况"
          name="lease">
          <SituationLease :id="id"
            :itemNo="itemNo"
            :orgCode="orgCode"
            :isAssets="isAssets" />
        </el-tab-pane>
        <el-tab-pane label="资产详情"
          name="assets">
          <SituationAssets :detailData="detailData" />
        </el-tab-pane>
        <!-- <el-tab-pane v-if="+detailData.assetType === 1"
          label="房间列表"
          name="roomLand">
          <SituationRoom :assetType="+detailData.assetType"
            :assetNo="detailData.assetNo" />
        </el-tab-pane> -->
        <!-- <el-tab-pane v-if="+detailData.assetType === 2"
          label="土地列表"
          name="roomLand">
          <SituationRoom :assetType="+detailData.assetType"
            :assetNo="detailData.assetNo" />
        </el-tab-pane> -->
      </el-tabs>
    </div>

    <!-- 底部按钮 -->
    <div slot="footer"
      class="footer-box">
      <el-button @click="handleClose">关 闭</el-button>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { assetsDetail, assetsDetailByCode } from '@/api/assetsv2'
import { Loading } from '@/decorators'
import Dialog from '@/components/Dialog/index.vue'
import SituationLease from '@/views/assets/page/manage/components/SituationLease.vue'
import SituationAssets from '@/views/assets/page/manage/components/SituationAssets.vue'
import SituationRoom from '@/views/assets/page/manage/components/SituationRoom.vue'
import SituationLand from '@/views/assets/page/manage/components/SituationLand.vue'
import DetailAssetInfo from '@/views/assets/v2page/Subjectmanagement/DetailAssetinfo.vue'
import { replaceOssStr } from '@/utils'

let chartDom = null
let myChart: any = null

@Component({
  name: 'DetailCom',
  components: {
    Dialog,
    SituationLease,
    SituationAssets,
    SituationRoom,
    SituationLand,
    DetailAssetInfo
  }
})
export default class extends Vue {
  @Prop() private visible!: boolean // 是否显示
  @Prop({ default: true }) private isAssets?: boolean // 判断是资产还是预警

  // 从【资产】模块过来的，传这两个参数
  @Prop() private id?: string
  @Prop({ default: '' }) private itemId?: string

  // 从【预警】模块过来的，传这两个参数
  @Prop({ default: '' }) private itemNo?: string
  @Prop({ default: '' }) private orgCode?: string

  private loading = false
  private detailData: any = {}
  private activeName: 'lease' | 'assets' | 'roomLand' = 'lease'

  // 组件初始化
  private mounted() {
    this.$nextTick(() => {
      // chartDom = document.getElementById('chartDom') as HTMLElement
      // myChart = echarts.init(chartDom as HTMLElement)
      this.initDetailData()
    })
  }
  private Diadetaillist: any = {}
  private visvileDetail = false
  private visvileDetailif = false

  // 详情图片转换
  get getImgSrc() {
    return (item: string) => {
      let src = replaceOssStr(item)
      return src
    }
  }

  //  查看合同详情
  private loaddetail(row: any) {
    this.Diadetaillist = row
    this.visvileDetailif = true
    this.$nextTick(() => {
      this.visvileDetail = true
    })
  }

  // 查看附件文件
  private openFileName(url: string) {
    window.open(url)
  }

  // 资产详情
  @Loading('loading')
  private async initDetailData() {
    let data = {}

    // 判断是从资产还是预警过来的（详情接口不同）
    if (this.isAssets) {
      await assetsDetail({
        id: this.id,
        itemId: this.itemId
      })
        .then((res) => {
          data = res.data || {}
        })
        .catch(() => {
          this.handleClose()
        })
    } else {
      await assetsDetailByCode({
        itemNo: this.itemNo,
        orgCode: this.orgCode
      })
        .then((res) => {
          data = res.data || {}
        })
        .catch(() => {
          this.handleClose()
        })
    }

    this.detailData = data || {}
    this.initEcharts()
  }

  // 初始化“出租面积、闲置面积”echarts组件
  private initEcharts() {
    let seriesData = this.detailData.assetLeasingPie

    let option = {
      grid: {
        top: '0%'
      },
      legend: {
        top: '0%',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        valueFormatter: (value: string) => `${value}㎡`
      },
      series: [
        {
          type: 'pie',
          radius: '50%',
          data: seriesData,
          label: {
            show: true,
            fontSize: 12
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        },
        {
          type: 'pie',
          radius: '50%',
          data: seriesData,
          label: {
            show: true,
            position: 'inside',
            formatter: `{d}%`,
            color: '#fff',
            fontSize: 12
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }

    myChart && myChart.setOption && myChart.setOption(option)
  }

  // 关闭弹窗
  private handleClose() {
    this.$emit('update:visible', false)
  }
}
</script>

<style scoped lang="scss">
.header-box {
  h4,
  p {
    margin: 0;
  }

  .title {
    display: flex;
    align-items: center;
    font-weight: normal;
    h4 {
      font-size: 18px;
      font-weight: normal;
    }
    span {
      color: #17beb7;
      font-size: 13px;
      margin-left: 10px;
      width: 16px;
      height: 16px;
      padding: 1px;
      line-height: 16px;
      border-radius: 50%;
      text-align: center;
      border: 1px solid #17beb7;
      background: rgba($color: #30cdc7, $alpha: 0.2);
    }
  }
}

.body-box {
  h4 {
    font-weight: normal;
  }
  h4,
  p {
    margin: 0;
  }
  .conter-info {
    position: relative;
    height: 180px;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 20px;
    .mode {
      flex: 1;
      height: 100%;
      margin-right: 20px;
      &:nth-last-child(1) {
        margin-right: 0;
      }
      .file {
        color: #ce4c4c;
        margin-right: 10px;
        cursor: pointer;
      }
    }
    .left {
      display: grid;
      grid-template-columns: 1fr 1fr; /* 两列等宽 */
      p {
        margin-bottom: 22px;
        &:nth-last-child(1) {
          margin-bottom: 0;
        }
      }
    }
    .middle {
      .el-image__inner {
        height: 180px;
      }
    }
    .right {
      img {
        width: 100%;
        height: 100%;
      }
      ::v-deep .carousel {
        .el-carousel__indicator {
          padding: 0;
        }
        .is-active {
          .el-carousel__button {
            background: rgba($color: #000000, $alpha: 0.6);
          }
        }
        .el-carousel__button {
          width: 20px;
          height: 2px;
          margin: 0 4px 10px;
          background: rgba($color: #000000, $alpha: 0.3);
        }
      }
    }
  }
}
</style>
