<template>
  <CommonWrapper :hasTabs="true"
    :switchList="getSwitchList"
    :currentModule="moduleName"
    @moduleChange="onModuleChange">
    <!-- 内容区域 -->
    <div class="content"
      slot="content">
      <component :is="moduleName" />
    </div>
  </CommonWrapper>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import CommonWrapper from '../components/commonWrapper.vue'

@Component({
  components: {
    CommonWrapper,
    CompositeIndex: () => import('./modules/CompositeIndex/index.vue'),
    FinancialIndicator: () => import('./modules/FinancialIndicator/index.vue'),
    AssetComposition: () => import('./modules/AssetComposition/index.vue'),
    DebtWarning: () => import('./modules/DebtWarning/index.vue'),
    WarningRanking: () => import('./modules/WarningRanking.vue'),
    ProjectProgress: () => import('./modules/ProjectProgress/index.vue'),
    SanZhongYiDa: () => import('./modules/SanZhongYiDa/index.vue'),
    RealEstate: () => import('./modules/RealEstate/index.vue'),
    Liabilities: () => import('./modules/Liabilities/index.vue'),
    CompanyDetail: () => import('./modules/CompanyDetail/index.vue')
  }
})
export default class extends Vue {
  @Watch('$route.query.module', { immediate: true })
  private moduleNameChange() {
    if (this.$route.query && this.$route.query.module) {
      this.moduleName = (this.$route.query as any).module
      if (!this.switchList.map((item) => item.name).includes(this.moduleName)) this.moduleName = 'FinancialIndicator'
    }
  }

  // 默认展示的二级模块
  private moduleName = 'FinancialIndicator'

  // 所有二级模块数据
  private switchList: Array<{ label: string, name: string }> = [
    // {
    //   label: '资产监管',
    //   name: 'AssetComposition'
    // },
    {
      label: '不动产管理',
      name: 'RealEstate'
    },
    {
      label: '投资管理',
      name: 'ProjectProgress'
    },
    {
      label: '财务监管',
      name: 'FinancialIndicator'
    },
    {
      label: '债务监管',
      name: 'Liabilities'
    },

    // {
    //   label: '三重一大',
    //   name: 'SanZhongYiDa'
    // },
    {
      label: '企业画像',
      name: 'CompanyDetail'
    },
    {
      label: '智慧预警',
      name: 'DebtWarning'
    },
    {
      label: '发展指数',
      name: 'CompositeIndex'
    }
  ]

  // 根据路由 module 去筛选出要展示的二级模块
  get getSwitchList() {
    let switchList = []
    let list: Array<{ label: string, name: string }> = [
      {
        label: '不动产管理',
        name: 'RealEstate'
      },
      {
        label: '投资管理',
        name: 'ProjectProgress'
      },
      {
        label: '财务监管',
        name: 'FinancialIndicator'
      },
      {
        label: '债务监管',
        name: 'Liabilities'
      },
      // {
      //   label: '三重一大',
      //   name: 'SanZhongYiDa'
      // },
      {
        label: '企业画像',
        name: 'CompanyDetail'
      },
      {
        label: '智慧预警',
        name: 'DebtWarning'
      },
      {
        label: '发展指数',
        name: 'CompositeIndex'
      }
    ]

    let {module} = this.$route.query

    if(!module) {
      switchList = list
    } else {
      return switchList = list.filter((item) => {
        if(
          module === 'ProjectProgress' || 
          module === 'FinancialIndicator' || 
          module === 'Liabilities' 
        ) {
          return item.name === 'ProjectProgress' || item.name === 'FinancialIndicator' || item.name === 'Liabilities'
        } else if(
          module === 'CompositeIndex'
        ) {
          return item.name === 'CompositeIndex' || item.name === 'SanZhongYiDa'
        } else if(
          module === 'SanZhongYiDa' || 
          module === 'DebtWarning'
        ) {
          return item.name === 'DebtWarning' || item.name === 'SanZhongYiDa'
        } else {
          return item.name === module
        }
      })
    }

    return switchList
  }

  // 路由切换
  private onModuleChange(moduleName: string) {
    this.$router.push({ path: '', query: { module: moduleName } })
  }
}
</script>


<style lang="scss">
.content {
  width: 100%;
  overflow: auto;
  &::-webkit-scrollbar {
    display: none;
  }
}
</style>