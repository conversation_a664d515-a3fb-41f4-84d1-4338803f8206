/** 仪表盘 */
<template>
  <div :id="chartId"
    :style="{width: `${width}px`,height: `${height}px`}" />
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import * as echarts from 'echarts'

type EChartsOption = echarts.EChartsOption

@Component
export default class extends Vue {
  @Prop() private seriesData!: any[]
  @Prop() private chartId!: string
  @Prop({ default: 100 }) private width!: number
  @Prop({ default: 100 }) private height!: number
  @Prop({ default: '已执行' }) private text!: string
  @Prop() private title!: string

  // 监听 seriesData 数据变化，更新视图
  @Watch('seriesData', { deep: true })
  changeSeriesData() {
    this.initEcharts()
  }

  private loading = false
  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}
  private echartsDatas: any[] = []
  private infoData = {
    all: 548,
    building: 324,
    budget: '105.7',
    executed: '42.43'
  }

  get rate() {
    return ((+this.infoData.executed || 0) / (+this.infoData.budget || 0)) * 100
  }

  created() {
    let building = Math.floor(Math.random() * 70)
    let executed = (Math.random() * 100).toFixed(2)
    this.infoData = {
      all: building + 123,
      building: building,
      budget: (+executed + 64.5).toFixed(2),
      executed: executed
    }
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById(this.chartId) as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    // this.initData()
    this.initEcharts()
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 初始化数据
  private initEcharts() {
    let textSize = 22
    let series = this.seriesData

    this.option = {
      title: {
        show: !!this.title,
        text: this.title,
        textStyle: {
          color: '#fff',
          fontSize: 30,
          fontWeight: 'bold'
        },
        left: 'center',
        bottom: 0
      },
      grid: {
        left: '0%',
        right: '0%',
        top: '0%',
        bottom: '0%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 103, 177, 1)',
        borderColor: 'rgba(0, 103, 177, 1)',
        textStyle: {
          color: '#fff',
          fontSize: textSize
        }
      },
      series: [
        {
          type: 'gauge',
          startAngle: 180,
          name: '项目预算',
          endAngle: 0,
          min: 0,
          radius: '90%',
          max: 100,
          center: ['50%', '50%'],
          splitNumber: 1,
          itemStyle: {
            color: '#58D9F9',
            shadowColor: 'rgba(0,138,255,0.45)',
            shadowBlur: 10,
            shadowOffsetX: 2,
            shadowOffsetY: 2
          },
          progress: {
            show: true,
            roundCap: true,
            width: 20,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: this.rate < 30 ? '#7CB7FF44' : this.rate < 60 ? '#7CB7FF44' : '#7CB7FF44' // 0% 处的颜色
                  },
                  {
                    offset: 0.7,
                    color: this.rate < 30 ? '#4680FF' : this.rate < 60 ? '#4680FF' : '#4680FF' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          },
          pointer: {
            show: false,
            icon: 'path://M2090.36389,615.30999 L2090.36389,615.30999 C2091.48372,615.30999 2092.40383,616.194028 2092.44859,617.312956 L2096.90698,728.755929 C2097.05155,732.369577 2094.2393,735.416212 2090.62566,735.56078 C2090.53845,735.564269 2090.45117,735.566014 2090.36389,735.566014 L2090.36389,735.566014 C2086.74736,735.566014 2083.81557,732.63423 2083.81557,729.017692 C2083.81557,728.930412 2083.81732,728.84314 2083.82081,728.755929 L2088.2792,617.312956 C2088.32396,616.194028 2089.24407,615.30999 2090.36389,615.30999 Z',
            length: '50%',
            width: 16,
            offsetCenter: [0, '0%']
          },
          axisLine: {
            roundCap: true,
            lineStyle: {
              width: 20,
              color: [[1, '#0E233A']]
            }
          },
          axisTick: {
            splitNumber: 2,

            lineStyle: {
              width: 2,
              color: '#0E233A'
            }
          },
          splitLine: {
            show: false
          },
          axisLabel: {
            distance: -50,
            color: '#5db0ea',
            formatter: '{value} %',
            fontSize: 14
          },
          title: {
            show: false
          },
          detail: {
            width: '100%',
            lineHeight: 40,
            height: 24,
            fontSize: 38,
            color: '#E6B607',
            borderRadius: 8,
            offsetCenter: [0, '25%'],
            valueAnimation: true,
            formatter: () => {
              return `{a|${this.infoData.budget}}{b|亿元}`
            },
            rich: {
              a: {
                fontSize: 58,
                color: '#E6B607',
                fontWeight: 'bold',
                opacity: 0.8,
                fontFamily: 'digital-7'
              },
              b: {
                fontSize: '38px',
                fontWeight: 'bold',
                color: '#E6B607'
              }
            }
          },
          // tooltip: {
          //   position: ['50%', '50%'],
          //   formatter: ({ data }: { data: any }) => {
          //     // ${
          //     //   +(data.value / this.sum).toFixed(1) * 100
          //     // }%
          //     return `{a|${data.value}亿元} {b|${data.name}}`
          //   },
          // },
          data: [
            {
              value: this.rate,
              name: this.text,
              title: {
                offsetCenter: ['0%', '-8%'],
                show: true,
                color: '#5db0ea',
                fontSize: 28,
                fontWeight: 'bold'
              }
            }
          ]
        }
      ]
    }

    this.myChart && this.myChart.setOption && this.myChart.setOption(this.option)
  }
}
</script>

<style scoped lang="scss">
#ProjectImplementation {
  width: 100%;
  height: 100%;
}
.project-implementation {
  height: 100%;
  width: 100%;
}
.info {
  width: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
  span:first-child {
    font-size: 18px;
    font-family: Source Han Sans SC;
    font-weight: 500;
    color: #ffffff;
    line-height: 120px;
    opacity: 0.5;
    margin-right: 28px;
  }
  span:last-child {
    font-size: 48px;
    font-family: DIN Alternate;
    font-weight: bold;
    color: #ffffff;
    line-height: 120px;
  }
}
</style>