<template>
  <div class="markinfo-content">
    <div class="markinfo-header">
      <div class="markinfo-header-img">
        <!-- <div   style="width: 100px; height:100px;backgroup:pink;">
                
        </div>-->
        <img style="width: 100%; height:100%"
          src="https://img2.baidu.com/it/u=4266492467,905963900&fm=253&fmt=auto&app=120&f=JPEG?w=660&h=396" />
      </div>
      <div class="markinfo-header-text">
        <p class="markinfo-header-maintext markinfo-header-tag"
          style="margin:5px 0;line-height:15px;">
          <span>五山林场新大楼北</span>
          <span><el-tag type="">土地</el-tag></span>
        </p>
        <div>五山林场新大楼北(金华婺城区) <span class="markinfo-header-maintext"></span></div>
        <div>所属单位 <span class="markinfo-header-maintext">城投集团</span></div>
        <div>产权单位 <span class="markinfo-header-maintext">金华市国有资产投资控股集团有限公司</span></div>

        <!-- <el-descriptions title="" :colon="false"  :column="1" >
        <el-descriptions-item label="">五山林场新大楼北(金华婺城区)</el-descriptions-item>
        <el-descriptions-item label="所属单位">城投集团</el-descriptions-item>
        <el-descriptions-item label="产权单位" :span="2">金华市国有资产投资控股集团有限公司</el-descriptions-item>
        </el-descriptions>-->
      </div>
    </div>
    <div class="markinfo-center">
      <el-descriptions :contentStyle='{"color":"#2f2f2f"}'
        title
        direction="vertical"
        :colon="false"
        :column="3">
        <el-descriptions-item label="出租率">100%</el-descriptions-item>
        <el-descriptions-item label="房间数量">10间</el-descriptions-item>
        <el-descriptions-item label="账面价值（已出租）"
          :span="2">20303元</el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="markinfo-bottom">
      <!-- <el-divider></el-divider> -->
      <!-- <el-button type="text">资产详情</el-button> -->
      <el-link type="primary"
        @click="getdetail()">资产详情</el-link>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'

@Component({
  components: {}
})
export default class extends Vue {
  private getdetail() {
    console.log('xq')
  }
}
</script>

<style lang="scss" scoped>
.markinfo-content {
  display: flex;
  flex-direction: column;
  height: 100%;

  .markinfo-bottom {
    width: 100%;
    text-align: center;
    font-weight: 500;
    font-size: 14px;
    border-top: 1px solid #303133;
    padding: 10px 0 0 0;
  }
  .markinfo-header {
    .markinfo-header-img {
      height: 150px;
      width: 150px;
    }
    .markinfo-header-tag {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .markinfo-header-text {
      color: #909399;
      margin: 0 0 10px 20px;
      height: 150px;
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      .markinfo-header-maintext {
        color: #303133;
        font-weight: 600;
      }
    }
    height: 150px;
    margin-bottom: 15px;
    display: flex;
    h5 {
      padding: 0;
    }
    //   justify-content: space-between;
    div {
      //   margin: 0 10px;
    }
  }
}
.el-link.el-link--success {
  font-size: 20px;
  font-weight: 500;
}
::v-deep .el-descriptions-item__cell {
  color: #909399;
  font-size: 15px;
}
::v-deep .el-descriptions-item__content {
  color: #303133;
}
</style>