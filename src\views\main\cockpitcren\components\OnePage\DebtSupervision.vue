/* 债务监管 */

<template>
  <section class="debt-supervision-wrap">
    <TitleCom title="债务监管"
      module="Liabilities" />

    <div v-loading="loading"
      id="liability" />

    <img ref="img2"
      src="@/views/main/cockpitcren/images/bar_bg1.png"
      srcset=""
      class="img_bar">
    <img ref="img"
      src="@/views/main/cockpitcren/images/bar_bg2.png"
      srcset=""
      class="img_bar">

    <!-- 详情弹窗 -->
    <CockiptGridDialog v-if="visibleDetail"
      :visible.sync="visibleDetail"
      :year="year"
      :closeModal="false"
      :code="tabActive.code"
      :columns="columns"
      :remoteUrl="remoteUrl"
      :searchParams="searchParams"
      title="债务监管" />
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { debtProfile } from '@/api/cockpit'
import { Loading, Throttle } from '@/decorators'
import { echartConfigure } from '@/views/main/cockpitcren/baseData'
import { isWindowFull, bigNumberFormat } from '@/utils'
import * as echarts from 'echarts'
import TitleCom from '@/views/main/cockpitcren/components/Public/TitleCom.vue'
import CockiptGridDialog from '@/views/main/cockpitcren/components/Public/CockiptDialog.vue'

type EChartsOption = echarts.EChartsOption
type typeTabItem = {
  id: string
  code: string
  name: string
}

@Component({
  components: {
    TitleCom,
    CockiptGridDialog
  }
})
export default class extends Vue {
  private loading = false
  private year = ''
  private timer: any = null
  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private imgDom: HTMLElement | null = null
  private imgDom2: HTMLElement | null = null
  private barData: number[] = []
  private bfData: number[] = []
  private option: EChartsOption = {}
  private echartsDatas: any[] = []
  private tabActive: typeTabItem = {
    id: '',
    code: '',
    name: ''
  }

  // 详情表格数据
  private visibleDetail = false
  private remoteUrl = '/fht-monitor/ds/screen/data/asset'
  private searchParams = {}
  private columns: any[] = [
    {
      prop: '',
      label: '企业名称'
    },
    {
      prop: '',
      label: '所属集团'
    },
    {
      prop: '',
      label: '资产负债率'
    },
    {
      prop: '',
      label: '负债率同比'
    },
    {
      prop: '',
      label: '资产负债总额'
    },
    {
      prop: '',
      label: '负债总额同比'
    },
    {
      prop: '',
      label: '资产总额'
    },
    {
      prop: '',
      label: '资产总额同比'
    }
  ]

  // 判断窗口是否处于全屏模式
  get getIsWindowFull() {
    return isWindowFull
  }

  // 万元数据转化成亿元
  get getBigNumberFormat() {
    return (num: string | number) => {
      return bigNumberFormat(+num).value || 0
    }
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('liability') as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)
    this.imgDom = this.$refs['img'] as HTMLImageElement
    this.imgDom2 = this.$refs['img2'] as HTMLImageElement
    this.year = String((this as any).$moment(new Date()).add('year', 0).format('YYYY') - 1)

    // 中间各tabs切换触发数据更新
    this.$bus.$on('BusTabsChange', (data: typeTabItem) => {
      this.tabActive = data
      this.initData()
    })

    // 时间改变时触发数据更新
    this.$bus.$on('BusYearTimeChange', (data: string) => {
      this.yearHandel(data)
    })
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await debtProfile({
      year: this.year,
      companyCode: this.tabActive.code
    })

    if (!Array.isArray(data) || !data.length) return
    this.echartsDatas = data
    this.filterEchartsData()
  }

  // 返回数组数据按年份大小排序
  private companyListSort(key: string) {
    return function (a: any, b: any) {
      return a[key] - b[key]
    }
  }

  // 筛选相关数据
  private filterEchartsData() {
    // 获取负债金额数据
    let filterSeries = this.echartsDatas.filter((item) => {
      return +item.companyCode === +this.tabActive.code && item.itemCode === 'debt_amount'
    })

    filterSeries.sort(this.companyListSort('year'))

    let barData: number[] = []
    filterSeries.forEach((item) => {
      barData.push(+this.getBigNumberFormat(item.itemValue))
    })
    // end

    // 获取同比%
    let filterBfSeries = this.echartsDatas.filter((item) => {
      return +item.companyCode === +this.tabActive.code && item.itemCode === 'debt_growth_rate'
    })
    let bfData: number[] = []
    filterBfSeries.forEach((item) => {
      bfData.push(+this.getBigNumberFormat(item.itemValue))
    })
    // end

    this.barData = barData
    this.bfData = bfData
    this.initEcharts()
  }

  // 初始化数据
  private initEcharts() {
    let legendSize = echartConfigure.legendSize
    let textSize = echartConfigure.textSize
    let yeas = ~~this.year
    let barData = this.barData
    let bfData = this.bfData
    let showAxis = true
    let imgDom: any = this.imgDom
    let imgDom2: any = this.imgDom2
    let tooltipData: any = Object.assign(
      {
        trigger: 'axis'
      },
      echartConfigure.tooltip
    )

    this.option = {
      color: [
        {
          image: imgDom2,
          repeat: 'repeat'
        }
      ],
      legend: {
        top: 0,
        padding: 0,
        itemHeight: 20,
        itemWidth: 20,
        itemStyle: {
          borderCap: 'butt'
        },
        lineStyle: {
          type: [0, 10],
          dashOffset: 5
        },
        textStyle: {
          color: '#fff',
          fontSize: legendSize,
          fontWeight: 'bold'
        },
        data: ['年度', '同比上涨/下降']
      },
      grid: {
        left: '0%',
        right: '0%',
        bottom: '0%',
        top: '24%',
        containLabel: true
      },
      tooltip: tooltipData,
      xAxis: [
        {
          type: 'category',
          data: [yeas - 4, yeas - 3, yeas - 2, yeas - 1, yeas],
          axisPointer: {
            type: 'shadow'
          },
          axisLabel: {
            fontSize: textSize * 1.1,
            fontWeight: 'bold',
            fontFamily: echartConfigure.fontFamilyNumber
          },
          axisLine: {
            lineStyle: {
              color: '#5db0ea'
            }
          }
        }
      ],
      yAxis: [
        {
          show: showAxis,
          type: 'value',
          name: '亿元',
          min: 0,
          max: function (value) {
            return Math.ceil(value.max) + 60
          },
          nameTextStyle: {
            color: '#5db0ea',
            align: 'left',
            fontWeight: 'bold',
            fontSize: textSize / 1.2
          },
          axisLabel: {
            show: true,
            fontSize: textSize,
            fontWeight: 'bold',
            formatter: '{value}',
            fontFamily: echartConfigure.fontFamilyNumber
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#5db0ea'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0, 103, 177, 0.4)'
            }
          }
        },
        {
          show: showAxis,
          type: 'value',
          name: '同比(%)',
          min: 0,
          max: function (value) {
            return Math.ceil(value.max) + 20
          },
          minInterval: 10,
          maxInterval: 50,
          nameTextStyle: {
            color: '#5db0ea',
            align: 'right',
            fontWeight: 'bold',
            fontSize: textSize / 1.2
          },
          axisLabel: {
            show: true,
            fontSize: textSize,
            fontWeight: 'normal',
            formatter: '{value}',
            fontFamily: echartConfigure.fontFamilyNumber
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#5db0ea'
            }
          },
          splitLine: {
            show: false,
            lineStyle: {
              color: 'rgba(0, 103, 177, 0.4)'
            }
          }
        }
      ],
      animationDuration: echartConfigure.animationDuration,
      animationDurationUpdate: echartConfigure.animationDuration,
      series: [
        {
          name: '年度',
          type: 'bar',
          barWidth: textSize * 1.4,
          label: {
            show: true,
            position: 'top',
            color: '#fff',
            fontSize: textSize,
            fontWeight: 'bold',
            formatter: '{c}',
            fontFamily: 'digital-7'
          },
          emphasis: {
            itemStyle: {
              color: {
                image: imgDom,
                repeat: 'repeat'
              }
            },
            label: {
              fontSize: textSize,
              color: '#EE9D00'
            }
          },
          select: {
            itemStyle: {
              color: {
                image: imgDom,
                repeat: 'repeat'
              }
            },
            label: {
              fontSize: textSize,
              color: '#EE9D00'
            }
          },
          selectedMode: true,
          tooltip: {
            valueFormatter: function (value) {
              return value + ' 亿元'
            }
          },
          data: barData
        },
        {
          name: '同比上涨/下降',
          type: 'line',
          symbol: 'circle',
          yAxisIndex: 1,
          label: {
            show: true,
            position: 'top',
            color: '#fff',
            fontSize: textSize,
            fontWeight: 'bold',
            formatter: '{c}%',
            fontFamily: 'digital-7'
          },
          tooltip: {
            valueFormatter: function (value) {
              return value + '%'
            }
          },
          data: bfData
        }
      ]
    }

    this.myChart && this.myChart.setOption && this.myChart && this.myChart.setOption(this.option)
    this.echartSelect()
    this.clickEchartsItem()
  }

  // 点击 eacharts 图表
  @Throttle
  private clickEchartsItem() {
    this.myChart.on('click', (event: any) => {
      this.year = event && event.name
      this.visibleDetail = true
    })
  }

  // 定时器，随机选中数据表中的数据
  private echartSelect() {
    if (this.timer) clearInterval(this.timer)

    let index = 0
    this.timer = setInterval(() => {
      if (index > 4) index = 0
      this.myChart &&
        this.myChart.dispatchAction({
          type: 'select',
          seriesIndex: 0,
          dataIndex: index
        })
      index++
    }, 5000)
  }

  // 年份切换
  private yearHandel(year: string) {
    this.year = year
    this.initData()
  }

  // tabs切换
  private tabsHandel(item: typeTabItem) {
    this.tabActive = item

    if (Array.isArray(this.echartsDatas) && this.echartsDatas.length) {
      this.filterEchartsData()
    }
  }

  // 销毁相关数据
  private destroyed() {
    clearInterval(this.timer)
    this.chartDom = null
    this.myChart = null
    this.timer = null
  }
}
</script>

<style scoped lang="scss">
.debt-supervision-wrap {
  position: relative;
  width: 100%;
  height: 610px;
  background: url('../../images/panel_bg2.png') no-repeat left top;
  background-size: 100% 100%;

  #liability {
    height: 420px;
    transform: translateY(-26px);
  }

  .img_bar {
    opacity: 0;
  }
}
</style>