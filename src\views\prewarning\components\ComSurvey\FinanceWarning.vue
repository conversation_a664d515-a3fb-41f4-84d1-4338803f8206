<template>
  <section class="echarts-dom-wrap">
    <div v-loading="loading"
      ref="chartDom"
      class="chartDom"
      :class="{'hide': !interfaceData.length}" />

    <el-empty description="暂无数据"
      class="empty-none-data"
      :class="{'none': interfaceData.length}" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { getFinDistribute } from '@/api/prewarning'
import { Loading } from '@/decorators'
import * as echarts from 'echarts'

let chartDom = null
let myChart: any = null

@Component({})
export default class extends Vue {
  @Prop() private year!: string // 年份
  @Prop() private dealStatus = ''

  private loading = false
  private yAxisData: string[] = []
  private seriesData: any[] = []
  private interfaceData: any[] = []
  private colorList: string[] = []

  // 监听搜索条件改变
  @Watch('year', { deep: true })
  private watchFormData() {
    this.initData()
  }

  // 监听搜索条件改变
  @Watch('dealStatus', { deep: true })
  private watchDealStatus() {
    this.initData()
  }

  // 组件初始化
  private mounted() {
    this.$nextTick(() => {
      chartDom = this.$refs.chartDom
      myChart = echarts.init(chartDom as HTMLElement)

      this.initData()
    })
  }

  // 获取数据
  @Loading('loading')
  private async initData() {
    let { data } = await getFinDistribute({
      year: this.year,
      dealStatus: this.dealStatus
    })
    // console.log('🚀 打印222:', data)

    this.interfaceData = data || []

    // 组装数据
    let xAxisData: string[] = []
    let seriesData: number[] = []
    let colorList: string[] = []

    // 预警颜色映射
    const colorMap: Record<string, string> = {
      红色预警: '#D54941',
      橙色预警: '#E99C22',
      黄色预警: '#E6C829',
      蓝色预警: '#3A86FF'
    }

    Array.isArray(data) &&
      data.forEach((item) => {
        xAxisData.push(item.name)
        seriesData.push(+item.value)
        // 根据预警名称设置对应颜色
        colorList.push(colorMap[item.name as keyof typeof colorMap] || '#5470C6')
      })

    this.yAxisData = xAxisData
    this.seriesData = seriesData
    this.colorList = colorList
    this.initEcharts()
  }

  // 初始化echarts组件
  private initEcharts() {
    let xAxisData = this.yAxisData
    let seriesData = this.seriesData
    let colorList = this.colorList

    let option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        valueFormatter: function (value: string) {
          return value + ' 个'
        }
      },
      grid: {
        top: '14%',
        left: '1%',
        right: '4%',
        bottom: '0%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: xAxisData
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          type: 'bar',
          barWidth: 40,
          label: {
            show: true,
            position: 'inside'
          },
          data: seriesData.map((value, index) => {
            return {
              value: value,
              itemStyle: {
                color: colorList[index]
              },
              label: {
                show: value > 0,
                position: 'inside'
              }
            }
          })
        }
      ]
    }

    myChart && myChart.setOption && myChart.setOption(option)
  }
}
</script>

<style scoped lang="scss">
.chartDom {
  width: 100%;
  height: 100%;
}
</style>