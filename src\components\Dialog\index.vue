<template>
  <el-dialog top="60px"
    class="common-dailog"
    :class="{ 'bg-primary': $attrs['bg-primary'] }"
    :close-on-click-modal="$attrs['close-on-click-modal'] ? $attrs['close-on-click-modal'] : false"
    :close-on-press-escape="$attrs['close-on-press-escape'] ? $attrs['close-on-press-escape'] : false"
    v-bind="$attrs"
    v-on="$listeners">
    <template slot="title">
      <slot name="header" />
    </template>
    <div class="dialog-body">
      <slot name="body" />
    </div>
    <template slot="footer">
      <slot name="footer" />
    </template>
    <div v-if="$attrs['loading']"
      v-loading="$attrs['loading']"
      class="dialog-loading"></div>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'

@Component
export default class Dialog extends Vue {}
</script>

<style lang="scss">
.common-dailog {
  $padding: 20px;
  text-align: center;
  display: grid;
  padding: 80px;
  &.bg-primary {
    .el-dialog__body {
      background: #f6fafd;
    }
    .el-dialog__footer {
      background: #f6fafd;
    }
  }
  & > .el-dialog {
    position: relative;
    display: flex;
    flex-direction: column;
    margin: auto !important;
    width: auto;
    max-width: 1600px;
    min-width: 340px;
    overflow-x: hidden;
    overflow-y: auto;
    .el-dialog__header {
      position: relative;
      background: #fff;
      .el-dialog__title {
        font-size: 16px;
      }
      & > *:nth-child(1) {
        display: block;
        text-align: left;
        padding-right: 32px;
      }
      position: relative;
      padding: 14px $padding;
      .el-dialog__headerbtn {
        right: 16px;
        top: 0;
        bottom: 0;
        margin: auto 0;
      }
      &:after {
        content: '';
        position: absolute;
        height: 1px;
        left: 0;
        right: 1px;
        bottom: -1px;
        background: #e9e9e9;
      }
    }
    & > .el-dialog__body {
      padding: 20px 0;
      flex: 1;
      overflow: auto;
      & > .dialog-body {
        text-align: left;
        padding: 0 $padding;
        overflow: hidden;
      }
    }
    .el-dialog__footer {
      padding: 0 $padding $padding;
      text-align: right;
      .el-button {
        min-width: 88px;
      }
    }
  }
  .el-dialog__header {
    font-weight: bold;
  }
  .dialog-loading {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: transparent;
    z-index: 100;
    cursor: not-allowed;
  }
}
</style>
