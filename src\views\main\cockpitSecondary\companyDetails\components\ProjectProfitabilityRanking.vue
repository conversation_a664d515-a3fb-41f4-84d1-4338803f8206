/** 项目盈利排名 */
<template>
  <div id="ProjectProfitabilityRanking" />
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import * as echarts from 'echarts'

type EChartsOption = echarts.EChartsOption

@Component
export default class extends Vue {
  private timer: any
  private loading = false
  private maxValue = 100
  private year = ''
  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}
  private realData: any = {
    '2019': [
      {
        name: '城投集团',
        value: 7249
      },
      {
        name: '交投集团',
        value: 7249
      },
      {
        name: '金投集团',
        value: 7249
      },
      {
        name: '轨道集团',
        value: 7249
      },
      {
        name: '水务集团',
        value: 7249
      },
      {
        name: '社发集团',
        value: 7249
      }
    ],
    '2020': [
      {
        name: '城投集团',
        value: 43354
      },
      {
        name: '交投集团',
        value: 43354
      },
      {
        name: '金投集团',
        value: 43354
      },
      {
        name: '轨道集团',
        value: 43354
      },
      {
        name: '水务集团',
        value: 43354
      },
      {
        name: '社发集团',
        value: 43354
      }
    ],
    '2021': [
      {
        name: '城投集团',
        value: 8132
      },
      {
        name: '交投集团',
        value: -3008
      },
      {
        name: '金投集团',
        value: 13739
      },
      {
        name: '轨道集团',
        value: 1540
      },
      {
        name: '水务集团',
        value: 3492
      },
      {
        name: '社发集团',
        value: -12505
      }
    ],
    '2022': [
      {
        name: '城投集团',
        value: 855
      },
      {
        name: '交投集团',
        value: 1878
      },
      {
        name: '金投集团',
        value: 1284
      },
      {
        name: '轨道集团',
        value: 237
      },
      {
        name: '水务集团',
        value: 320
      },
      {
        name: '社发集团',
        value: 357
      }
    ]
  }
  private seriesData: any[] = [
    {
      name: '城投集团',
      value: 0
    },
    {
      name: '交投集团',
      value: 0
    },
    {
      name: '金投集团',
      value: 0
    },
    {
      name: '轨道集团',
      value: 0
    },
    {
      name: '水务集团',
      value: 0
    },
    {
      name: '社发集团',
      value: 0
    }
  ]

  private companyList = [
    {
      code: '1',
      name: '城投集团项目'
    },
    {
      code: '4',
      name: '交投集团项目'
    },
    {
      code: '2',
      name: '金投集团项目'
    },
    {
      code: '5',
      name: '轨道集团项目'
    },
    {
      code: '3',
      name: '水务集团项目'
    },
    {
      code: '6',
      name: '社发集团项目'
    }
  ]

  // 获取集团列表
  get getCompanyList() {
    let list: string[] = []
    this.companyList.forEach((item) => {
      list.push(item.name)
    })
    return list
  }

  get currentTabCode(): any {
    return this.$route.query ? this.$route.query.id || '' : ''
  }

  // 筛选数据
  private filterData() {
    let seriesData = this.realData['2022'] || [
      {
        name: '城投集团',
        value: 0
      },
      {
        name: '交投集团',
        value: 0
      },
      {
        name: '金投集团',
        value: 0
      },
      {
        name: '轨道集团',
        value: 0
      },
      {
        name: '水务集团',
        value: 0
      },
      {
        name: '社发集团',
        value: 0
      }
    ]
    this.seriesData = seriesData.sort((a: any, b: any) => a.value - b.value)
    this.initEcharts()
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('ProjectProfitabilityRanking') as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    this.filterData()
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 初始化数据
  private initEcharts() {
    let textSize = 22
    let series = this.seriesData.map((item) => {
      // 设置圆角方向
      if (item.value < 0) {
        item.itemStyle = {
          borderRadius: [20, 0, 0, 20]
        }
        item.backgroundStyle = {
          borderRadius: [20, 0, 0, 20]
        }
      }
      return item
    })
    let yeas = ~~this.getMomentTime()
    let companyList = this.getCompanyList
    this.option = {
      color: [
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: '#835002' // 0% 处的颜色
            },
            {
              offset: 1,
              color: '#E6B607' // 100% 处的颜色
            }
          ]
        },
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: '#7E358B' // 0% 处的颜色
            },
            {
              offset: 1,
              color: '#EC342F' // 100% 处的颜色
            }
          ]
        }
      ],
      axisPointer: {
        show: false
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 103, 177, 1)',
        borderColor: 'rgba(0, 103, 177, 1)',
        textStyle: {
          color: '#fff',
          fontSize: textSize
        }
      },
      legend: {
        show: false
      },
      grid: {
        top: '0%',
        left: '6%',
        right: '6%',
        bottom: '0%',
        containLabel: true
      },
      title: {
        text: '万元',
        bottom: 0,
        right: 0,
        textStyle: {
          color: '#5db0ea',
          fontSize: 22
        }
      },
      xAxis: {
        show: false,
        type: 'value',
        name: '万元',

        nameTextStyle: {
          color: '#5db0ea'
        },
        axisLabel: {
          color: '#5db0ea',
          fontSize: textSize * 1.2
        }
      },
      yAxis: {
        type: 'category',
        axisLabel: {
          color: '#5db0ea',
          fontSize: textSize,
          shadowBlur: 10,
          shadowColor: '#0B388F',
          backgroundColor: '#0B388F66',
          borderRadius: 6,
          formatter: (value: any, index: number) => {
            return `{a|0${series.length - index}} {b|${value}}`
          },
          rich: {
            a: {
              fontStyle: 'italic',
              fontWeight: 'bold',
              color: '#4A97F8',
              fontSize: textSize * 1.6,
              lineHeight: textSize * 2.4,
              padding: 10
            },
            b: {
              fontWeight: 'bold',
              fontSize: textSize * 1.2,
              lineHeight: textSize * 2.4,
              padding: 10
            }
          }
        },
        data: companyList.reverse()
      },
      series: [
        {
          name: '资产利用情况',
          type: 'bar',
          stack: 'total',
          selectedMode: 'single',
          barWidth: textSize * 2.2,
          label: {
            show: true,
            fontSize: textSize * 1.6,
            fontWeight: 'bold',
            color: '#FFF'
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'RGBA(233, 60, 167, 0)'
                  },
                  {
                    offset: 0.4,
                    color: 'RGBA(233, 60, 167, 0.3)'
                  },
                  {
                    offset: 1,
                    color: 'RGBA(233, 60, 167, 1)'
                  }
                ]
              },
              shadowColor: 'RGBA(233, 60, 167, 0.6)',
              shadowBlur: 30
            }
          },
          select: {
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'RGBA(233, 60, 167, 0)'
                  },
                  {
                    offset: 0.4,
                    color: 'RGBA(233, 60, 167, 0.3)'
                  },
                  {
                    offset: 1,
                    color: 'RGBA(233, 60, 167, 1)'
                  }
                ]
              },
              shadowColor: 'RGBA(233, 60, 167, 0.6)',
              shadowBlur: 30
            }
          },
          itemStyle: {
            borderRadius: [0, 20, 20, 0]
          },
          showBackground: true,
          backgroundStyle: {
            color: 'rgba(0, 103, 177, 0.2)',
            borderRadius: [0, 20, 20, 0]
          },
          data: series
        }
      ]
    }

    this.myChart && this.myChart.setOption && this.myChart && this.myChart.setOption(this.option)
    this.change()
  }
  // 年份切换
  private yearHandler(year: string) {
    this.year = year
    // this.initData()
  }

  private change() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    this.timer = setInterval(() => {
      let index = Math.max(Math.floor(Math.random() * 4), 0)

      this.myChart &&
        this.myChart.dispatchAction({
          type: 'select',
          seriesIndex: 0,
          dataIndex: index
        })
    }, 5000)
  }

  destroyed() {
    clearInterval(this.timer)
  }
}
</script>

<style scoped lang="scss">
#ProjectProfitabilityRanking {
  width: 100%;
  height: 100%;
}
</style>