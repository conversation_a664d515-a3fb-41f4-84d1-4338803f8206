/* 预警分类统计 */

<template>
  <section class="early-warning-wrap">
    <CommonTitle :title="title"
      :isRotuerThrow="true"
      :routerObj="{
        bizAppCode: 'alert',
        detailsPath: '/prewarning/events'
      }"
      class="title-box m-b-30" />

    <div class="content-box">
      <div ref="refEcharts"
        id="chartDom" />
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { echartConfigure } from '@/views/main/cockpitcren/baseData'
import { deepClone } from '@/utils'
import * as echarts from 'echarts'
import CountTo from 'vue-count-to'
import CommonTitle from '@/views/main/cockpitSecondary/components/CommonTitle.vue'

type EChartsOption = echarts.EChartsOption

@Component({
  components: {
    CountTo,
    CommonTitle
  }
})
export default class extends Vue {
  @Prop() private title!: string // 标题
  @Prop() private echartsData!: any // 渲染数据

  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private seriesData: any[] = []
  private option: EChartsOption = {}
  private legendData: string[] = []
  private axisLabelData: string[] = []

  // 数据变化，渲染视图
  @Watch('echartsData', { deep: true })
  private changeEchartsData() {
    this.initData()
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('chartDom') as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)
  }

  // 请求数据接口
  private async initData() {
    let data: any = deepClone(this.echartsData)

    // 根据接口数据，设置echarts高度
    ;(this.chartDom as HTMLElement).style.height = `${data.length * 130}px`
    this.myChart.resize()

    // 设置相关数据
    let legendList: string[] = []
    let axisLabelData: string[] = []
    let seriesList: any[] = []

    Array.isArray(data) &&
      data.forEach((item: any, index: number) => {
        let names = item.name
        let nums = ''
        item.list.forEach((itemChild: any) => {
          nums += '-' + itemChild.value
          !index && legendList.push(itemChild.label)
        })
        axisLabelData.push(names + nums)
      })

    legendList.forEach((item, index) => {
      let arr: number[] = []
      Array.isArray(data) &&
        data.forEach((itemData: any) => {
          if (item === itemData.list[index].label) {
            arr.push(itemData.list[index].value)
          }
        })

      seriesList.push({
        name: item,
        type: 'bar',
        stack: 'total',
        label: {
          show: false
        },
        barWidth: 20,
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(9, 48, 145, 1)',
          borderRadius: [0, 20, 20, 0]
        },
        data: arr.map((item) => {
          return {
            value: item,
            itemStyle: {
              borderRadius: [0, 10, 10, 0]
            }
          }
        })
      })
    })

    this.legendData = legendList
    this.axisLabelData = axisLabelData
    this.seriesData = seriesList

    this.initEcharts()
  }

  // 初始化数据
  private initEcharts() {
    let legendSize = echartConfigure.legendSize
    let legendData = this.legendData
    let axisLabelData = this.axisLabelData
    let seriesData = this.seriesData
    let colorList = ['#fb3f3f', '#ff7500', '#F5B331', '#249DF7']

    this.option = {
      color: [
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(240, 73, 148, 0.1)'
            },
            {
              offset: 0.3,
              color: 'rgba(240, 73, 148, 0.7)'
            },
            {
              offset: 1,
              color: 'rgba(240, 73, 148, 1)'
            }
          ]
        },
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(255, 117, 0, 0.1)'
            },
            {
              offset: 0.3,
              color: 'rgba(255, 117, 0, 0.7)'
            },
            {
              offset: 1,
              color: 'rgba(255, 117, 0, 1)'
            }
          ]
        },
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(246, 181, 48, 0.1)'
            },
            {
              offset: 0.3,
              color: 'rgba(246, 181, 48, 0.7)'
            },
            {
              offset: 1,
              color: 'rgba(246, 181, 48, 1)'
            }
          ]
        },
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(36, 157, 247, 0.1)'
            },
            {
              offset: 0.3,
              color: 'rgba(36, 157, 247, 0.7)'
            },
            {
              offset: 1,
              color: 'rgba(36, 157, 247, 1)'
            }
          ]
        }
      ],
      tooltip: {
        show: false,
        trigger: 'axis',
        backgroundColor: '#00000000',
        shadowColor: '#00000000',
        borderWidth: 0,
        formatter: (params: any) => {
          let dom = ''
          let totalValue = 0
          let axisValue = params[0].axisValue || ''
          let title = axisValue.split('-')[0]

          params.forEach((item: any, index: number) => {
            totalValue += +item.value

            dom += `
              <div style="display: flex; align-items: center; justify-content: space-between;">
                <span style="padding-right: 40px;">${item.seriesName}</span>
                <span style="color:${colorList[index]};">${item.value}</span>个
              </div>
              `
          })

          return `
            <div style="font-size:34px; line-height: 70px; color: #fff; border-radius:10px;border:2px solid #0C3EB6; padding: 20px; display: flex;flex-direction: column; background: linear-gradient(0deg, rgba(0, 103, 177, 1), rgba(0, 103, 177, 1));">
              <div style="font-weight:bold; display: flex; align-items: center; justify-content: space-between;">
                <strong>${title}</strong>
                <strong style="color:#00F6FF;">${totalValue}</strong>
              </div>
              ${dom}
            </div>
          `
        }
      },
      legend: {
        show: true,
        top: '0%',
        left: 'center',
        itemHeight: 24,
        itemWidth: 24,
        itemStyle: {
          borderCap: 'butt'
        },
        lineStyle: {
          type: [0, 10],
          dashOffset: 5
        },
        textStyle: {
          color: 'rgba(0, 166, 245, 0.7)',
          fontSize: legendSize / 1.5,
          fontWeight: 'bold'
        },
        data: legendData
      },
      grid: {
        top: '15%',
        left: '0%',
        right: '0%',
        bottom: '0%',
        containLabel: true
      },
      xAxis: {
        show: false
      },
      yAxis: {
        type: 'category',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          margin: 0,
          inside: true,
          color: '#fff',
          padding: [0, 0, 20, 0],
          verticalAlign: 'bottom',
          formatter: function (value: string) {
            let arr = value.split('-')
            return `{a|${arr[0]}} {b|${arr[1]}} {c|${arr[2]}} {d|${arr[3]}} {e|${arr[4]}}`
          },
          rich: {
            a: {
              width: 350,
              fontSize: 32,
              color: '#fff',
              fontWeight: 'bold'
            },
            b: {
              width: 100,
              align: 'right',
              color: '#fb3f3f',
              fontSize: 34,
              fontWeight: 'normal',
              fontFamily: 'PangMenZhengDao'
            },
            c: {
              width: 60,
              align: 'right',
              color: '#ff7500',
              fontSize: 34,
              fontWeight: 'normal',
              fontFamily: 'PangMenZhengDao'
            },
            d: {
              width: 100,
              align: 'right',
              color: '#F5B331',
              fontSize: 34,
              fontWeight: 'normal',
              fontFamily: 'PangMenZhengDao'
            },
            e: {
              width: 100,
              align: 'right',
              color: '#249DF7',
              fontSize: 34,
              fontWeight: 'normal',
              fontFamily: 'PangMenZhengDao'
            }
          }
        },
        data: axisLabelData
      },
      series: seriesData,
      animationDelay: function (idx) {
        return idx * 300
      }
    }

    this.myChart && this.myChart.setOption(this.option, true)
  }
}
</script>

<style scoped lang="scss">
.early-warning-wrap {
  position: relative;
  height: 100%;
  h6,
  p {
    margin: 0;
  }

  .title-box {
    width: 100%;
  }
  .content-box {
    position: absolute;
    z-index: 2;
    width: 100%;
    height: 88%;
    overflow-y: auto;
    &::-webkit-scrollbar {
      display: none;
    }
  }
}
</style>


