/** 成员总体情况 */

<template>
  <div class="member-info">
    <div v-for="(item, index) in membersInfo"
      :key="index"
      class="item">
      <div class="item__top">
        <CountTo :startVal='0'
          :endVal='+item.value || 0'
          :duration='2000' />
        <span class="unit">人</span>
      </div>
      <div class="item__bottom">
        {{ item.label }}
      </div>
    </div>
  </div>

</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import CountTo from 'vue-count-to'

@Component({
  components: {
    CountTo
  }
})
export default class extends Vue {
  private membersInfo = [
    {
      label: '在编人员',
      value: 256
    },
    {
      label: '提名推荐',
      value: 6
    },
    {
      label: '提拔人数',
      value: 23
    },
    {
      label: '调整岗位',
      value: 56
    },
    {
      label: '本年离岗',
      value: 13
    },
    {
      label: '免职人员',
      value: 6
    },
    {
      label: '辞职人数',
      value: 2
    },
    {
      label: '辞退人员',
      value: 1
    }
  ]
}
</script>

<style scoped lang="scss">
.member-info {
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  border-radius: var(--normal-border-radius);
  padding: 36px 8px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .item {
    width: 20%;
    height: 150px;
    margin: 12px 2.5%;
    background: url('../../images/iconbg.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    padding: 10px 0;
    color: #fff;
    cursor: pointer;
    &:hover {
      background: url('../../images/iconbg2.png') no-repeat;
      background-size: 100% 100%;
      .item__bottom {
        color: #fff;
      }
    }
    .item {
      &__top,
      &__bottom {
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
      }

      &__top {
        height: 64%;
        font-size: 58px;
        // padding-top: 14px;
        .unit {
          font-size: 30px;
          margin-left: 6px;
        }
      }
      &__bottom {
        height: 40%;
        font-size: 30px;
      }
    }
  }
}
</style>