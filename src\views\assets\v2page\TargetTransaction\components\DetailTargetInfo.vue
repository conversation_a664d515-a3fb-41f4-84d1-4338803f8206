// 标的信息管理
<template>
  <div style="width:100%">
    <el-descriptions
      title="基本信息"
      :column="3"
      border
      :labelStyle="{
        width: '130px'
      }"
    >
      <!-- <el-descriptions-item label="标的名称" :span="3">{{ detail.subjectName }}</el-descriptions-item> -->
      <!-- <el-descriptions-item label="标的编号" :span="3">{{ detail.subjectNo }}</el-descriptions-item> -->
      <el-descriptions-item label="是否国有资产">{{ detail.stateAssetDesc }}</el-descriptions-item>
      <el-descriptions-item label="国家出租企业或主管部门名称">{{ detail.competentDeptName }}</el-descriptions-item>
      <el-descriptions-item label="内部决策情况">{{ detail.internalDecisionDesc }}</el-descriptions-item>
      <el-descriptions-item label="批准单位名称">{{ detail.approvalOrganName }}</el-descriptions-item>
      <!-- <el-descriptions-item label="批准单位名称">{{ detail.name }}</el-descriptions-item> -->
      <el-descriptions-item label="批准文件名称">{{ detail.approvalFileName }}</el-descriptions-item>
    </el-descriptions>
    <el-descriptions
      title="交易条件"
      :column="3"
      border
      :labelStyle="{
        width: '130px'
      }"
    >
      <el-descriptions-item label="出租面积(㎡)">{{ detail.rentArea || '-' }}</el-descriptions-item>
      <el-descriptions-item label="租金挂牌价(元/年)">{{ detail.rentPrice || '-' }}</el-descriptions-item>
      <el-descriptions-item label="竞拍保证金(元)">{{ detail.deposit || '-' }}</el-descriptions-item>
      <el-descriptions-item label="加价幅度(元/次)">{{ detail.leaseCond.markupRange || '-' }}</el-descriptions-item>
      <el-descriptions-item label="租期">{{ detail.leaseTermDesc }}</el-descriptions-item>
      <el-descriptions-item label="免租期">{{ detail.hasFreeTermDesc }}</el-descriptions-item>
      <!-- <el-descriptions-item label="是否包含租期内">{{ detail.name }} 无</el-descriptions-item> -->
      <el-descriptions-item label="标的状态">{{ detail.subjectStatusDesc }}</el-descriptions-item>
      <!-- <el-descriptions-item label="原合同到期日">{{ detail.name }}无</el-descriptions-item> -->
      <el-descriptions-item label="原承租人名称">{{ detail.originalLessee }}</el-descriptions-item>
      <el-descriptions-item label="其他权利情况">{{ detail.otherRightsDesc }}</el-descriptions-item>
      <el-descriptions-item label="是否有优先承租权">{{ detail.priorityLeaseDesc }}</el-descriptions-item>
      <el-descriptions-item label="房产使用用途">{{ detail.assetPurpose }}</el-descriptions-item>
      <el-descriptions-item label="租金支付要求">{{ detail.paymentRequire }}</el-descriptions-item>
    </el-descriptions>

    <Title title="资产明细" />
    <Grid
      ref="grid"
      :columns="cols"
      :show-selection="false"
      :show-pagination="false"
      :overflow-tooltip="true"
      :show-index="true"
      show-index-fixed="left"
      :data="detail.assetList"
      class="m-t-15 grid"
    >
      <template #RealEstatePhoto="{row}">
        <AccessoryList title mode="see" btntype="text" v-model="row.fileList" />
      </template>
    </Grid>
    <AccessoryList v-model="detail.fileList" mode="see" />
    <AccessoryList title="挂牌图片：" v-model="detail.picList" mode="see" />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import Title from '@/views/assets/components/Title/index.vue'
import Grid from '@/components/Grid/index.vue'
import AccessoryList from '@/views/assets/components/astFileList/index.vue'
@Component({
  components: {
    Title,
    Grid,
    AccessoryList
  }
})
export default class extends Vue {
  @Prop({
    default: () => ({
      name: 'name',
      auditRecord: {}
    })
  })
  private detail: any
  // private detail: any = {
  //   name: 'name'
  // }
  private cols = [
    { prop: 'location', label: '房屋坐落' },
    { prop: 'assetTypeDesc', label: '资产类别' },
    { prop: 'province', label: '省' },
    { prop: 'city', label: '市' },
    { prop: 'district', label: '区（县）' },
    { prop: 'realEstateCertificateNo', label: '不动产证' },
    { prop: 'titleDeedNo', label: '房产证' },
    { prop: 'landCertificateNo', label: '土地号证' },
    { prop: 'constructionArea', label: '建筑面积' },
    { prop: 'constructionAreaUnit', label: '建筑面积单位', minWidth: 100 },
    { prop: 'houseStatus', label: '房屋当前状态', minWidth: 100 },
    { prop: 'assetPurpose', label: '房屋设计用途', width: 100 },
    { fixed: 'right', label: '房产照片', minWidth: 140, slotName: 'RealEstatePhoto' }
  ]
}
</script>

<style lang="scss" scoped>
::v-deep.el-descriptions {
  margin-bottom: 15px;
}
.grid {
  ::v-deep.el-table td .cell {
    max-height: 50px;
  }
}
</style>
