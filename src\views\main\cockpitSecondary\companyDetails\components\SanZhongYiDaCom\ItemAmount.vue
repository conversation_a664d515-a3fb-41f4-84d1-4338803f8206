/** 重大项目金额 */ 

<template>
  <div id="ItemAmount" />
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { ItemAmountData } from '@/views/main/cockpitSecondary/baseData'
import { capitalComponent, capitalProfile } from '@/api/cockpit'
import * as echarts from 'echarts'
import { deepClone } from '@/utils'
import CommonModuleWrapper from '@/views/main/cockpitSecondary/components/CommonModuleWrapper.vue'

type EChartsOption = echarts.EChartsOption

type dataType = {
  value: number
  name: string
}

@Component({
  components: {
    CommonModuleWrapper
  }
})
export default class extends Vue {
  private timer: any
  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}
  private realData: any = {
    ALL: [103467, 23378, 34099, 11034],
    CT: [103467, 23378, 34099, 11034],
    KT: [88045, 20988, 17093, 8703],
    SW: [54098, 9986, 22091, 4051],
    JT: [48091, 13047, 8508, 3586],
    GD: [56092, 14098, 16095, 1304],
    SF: [8094, 5664, 2098, 981]
  }
  private seriesData: any[] = [0, 0, 0, 0]
  private echartsDatas: any[] = []

  get currentTabCode(): any {
    return this.$route.query ? this.$route.query.id || '' : ''
  }

  // 筛选数据
  private filterData() {
    this.seriesData = this.realData[this.currentTabCode] || [0, 0, 0, 0]
    this.initEcharts()
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('ItemAmount') as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    this.filterData()
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await capitalProfile()
    if (!Array.isArray(data) || !data.length) return
    this.echartsDatas = data
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 初始化数据
  private initEcharts() {
    let textSize = 16
    let series = this.seriesData
    this.option = {
      color: [
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: '#0DB89600'
            },
            {
              offset: 0.2,
              color: '#0DB896aa'
            },
            {
              offset: 1,
              color: '#0DB896'
            }
          ]
        }
      ],
      title: {
        text: '大额资金运作事项',
        top: 0,
        textStyle: {
          color: '#fff',
          fontSize: 26
        }
      },
      legend: {
        show: true
      },
      grid: {
        left: '0%',
        right: '0%',
        top: '24%',
        bottom: '0%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 103, 177, 1)',
        borderColor: 'rgba(0, 103, 177, 1)',
        textStyle: {
          color: '#fff',
          fontSize: textSize
        }
      },
      xAxis: {
        type: 'category',
        axisLabel: {
          fontSize: textSize * 1,
          fontWeight: 'bold',
          hideOverlap: false,
          interval: 0,
          color: '#5db0ea'
        },
        axisLine: {
          lineStyle: {
            color: '#5db0ea'
          }
        },
        data: ['年度投资', '大宗货物', '重大工程', '服务项目']
      },
      yAxis: {
        show: true,
        type: 'value',
        axisLabel: {
          fontWeight: 'bold',
          color: '#5db0ea'
        },
        axisLine: {
          lineStyle: {
            color: 'RGBA(147, 148, 149, 1)'
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0, 103, 177, 0.4)'
          }
        }
      },
      axisPointer: {
        show: false
      },
      series: [
        {
          selectedMode: 'single',
          label: {
            show: true,
            position: 'top',
            color: '#5db0ea',
            fontSize: textSize * 1.4
          },
          itemStyle: {
            borderRadius: [textSize * 2.4, textSize * 2.4, 0, 0],
            shadowColor: '#0DB89666',
            shadowBlur: 30
          },
          emphasis: {
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: '#20DCF900'
                  },
                  {
                    offset: 0.2,
                    color: '#20DCF9aa'
                  },
                  {
                    offset: 1,
                    color: '#20DCF9'
                  }
                ]
              },
              shadowColor: '#20DCF966',
              shadowBlur: 30
            },
            label: {
              fontSize: textSize * 1.6,
              color: '#20DCF9'
            }
          },
          select: {
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: '#20DCF900'
                  },
                  {
                    offset: 0.2,
                    color: '#20DCF9aa'
                  },
                  {
                    offset: 1,
                    color: '#20DCF9'
                  }
                ]
              },
              shadowColor: '#20DCF988',
              shadowBlur: 30
            },
            label: {
              fontSize: textSize * 1.6,
              color: '#20DCF9'
            }
          },
          showBackground: false,
          backgroundStyle: {
            color: '#081C5866',
            borderRadius: [textSize * 2.4, textSize * 2.4, 0, 0]
          },
          barWidth: textSize * 2.4,
          data: series,
          type: 'bar'
        }
      ]
    }

    this.myChart && this.myChart.setOption && this.myChart && this.myChart.setOption(this.option)
    this.change()
  }

  private change() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    this.timer = setInterval(() => {
      let index = Math.max(Math.floor(Math.random() * 4), 0)

      this.myChart &&
        this.myChart.dispatchAction({
          type: 'select',
          seriesIndex: 0,
          dataIndex: index
        })
    }, 5000)
  }

  destroyed() {
    clearInterval(this.timer)
  }
}
</script>

<style scoped lang="scss">
#ItemAmount {
  width: 320px;
  height: 240px;
}
</style>