import { getToken } from './auth';

export default {
    // 附件预览 this.$file.open(url, name)
    open: function( url:string, name:string ) {
        // 配合后端，做一个域名替换
        let urlReplace = JSON.parse(JSON.stringify(url));


        let src = process.env.VUE_APP_File_Url + encodeURIComponent(btoa(urlReplace))

        if (name != null || name != undefined) {
          src += '&name=' + window.btoa(encodeURIComponent(name))
        }

        src = src + `&token=${getToken()}`

        console.log('附件预览地址：' + src)

        // 获取屏幕宽度和高度
        const screenWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
        const screenHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;

        const w = 1100; // 弹窗宽度

        // 计算弹窗在屏幕中心的位置
        const left = screenWidth / 2 - w / 2;
        const top = 0; // 设置顶部位置为0
        
        window.open(
          src,
          src,
          'width=' + w + ',height=' + screenHeight + ',left=' + left + ',top=' + top,
        ) ;
        return;
    },

}