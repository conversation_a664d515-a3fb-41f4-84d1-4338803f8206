<template>
  <section v-loading="loading"
    class="alert-detail-wrap">
    <el-descriptions border
      :column="3">
      <el-descriptions-item label="预警编号">{{ detailData.eventNo }}</el-descriptions-item>
      <el-descriptions-item label="预警名称">{{ detailData.ruleName }}</el-descriptions-item>
      <el-descriptions-item label="预警级别">{{ detailData.levelDesc }}</el-descriptions-item>
      <el-descriptions-item label="预警分类">{{ detailData.metricsDesc }}</el-descriptions-item>
      <el-descriptions-item label="预警时间">{{ detailData.createTime }}</el-descriptions-item>
      <el-descriptions-item label="所属集团">{{ detailData.orgName }}</el-descriptions-item>
      <el-descriptions-item label="规则状态"
        :span="3">{{ detailData.ruleStatusDesc }}</el-descriptions-item>
      <el-descriptions-item label="预警说明"
        :span="3">{{ detailData.ruleDesc }}</el-descriptions-item>
      <el-descriptions-item label="政策说明"
        :span="3">{{ detailData.policyDesc }}</el-descriptions-item>
      <el-descriptions-item label="事件内容"
        :span="3">{{ detailData.results || params.results }}</el-descriptions-item>
      <el-descriptions-item label="反馈人">{{ detailData.operateName }}</el-descriptions-item>
      <el-descriptions-item label="联系方式">{{ detailData.operatePhone }}</el-descriptions-item>
      <el-descriptions-item label="附件">
        <el-button v-if="Array.isArray(detailData.feedBackFiles)"
          type="text"
          size="medium"
          style="padding: 0;"
          @click="showUploader = true">查看附件({{+detailData.feedBackFiles.length}})</el-button>
      </el-descriptions-item>
      <el-descriptions-item v-if="detailData.dealContent || params.dealContent"
        label="处置内容/结果">{{ detailData.dealContent || params.dealContent }}</el-descriptions-item>
      <el-descriptions-item label="采取的措施" :span="3">
          {{ detailData.measures || params.measures }}
        </el-descriptions-item>
    </el-descriptions>

    <!-- 查看附件 -->
    <Uploader v-if="Array.isArray(detailData.feedBackFiles)"
      v-model="detailData.feedBackFiles"
      title="查看附件"
      :visible.sync="showUploader"
      :uploadable="false"
      :is-private="false"
      :show-cover="false" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { debtWarningDetail } from '@/api/prewarning'
import { Loading } from '@/decorators'
import Uploader from '@/components/Uploader/index.vue'

@Component({
  components: {
    Uploader
  }
})
export default class extends Vue {
  @Prop() private params!: any // 详情数据

  private loading = false
  private showUploader = false
  private detailData = {}

  // 数据初始化
  private created() {
    this.initData()
  }

  // 获取详情数据
  @Loading('loading')
  private async initData() {
    let { data } = await debtWarningDetail({
      id: this.params.id
    })

    this.detailData = data || {}
  }
}
</script>

<style scoped lang="scss">
.alert-detail-wrap {
  $hot: #fb3f3f;
  $yellow: #e6a23c;
  $blur: #409eff;

  position: relative;
  p {
    margin: 0;
  }
  .hot {
    color: $hot;
  }
  .yellow {
    color: $yellow;
  }
  .blue {
    color: $blur;
  }
  .company-name {
    width: 90%;
  }

  ::v-deep .el-descriptions {
    .el-descriptions__table {
      font-size: 14px;
      padding-bottom: 20px;
    }
    .el-descriptions-item__label {
      width: 120px;
    }
  }
}
</style>
