// 分配
<template>
  <Dialog title="分配" width="600px" :visible="visible" @close="handleClose" center>
    <div slot="body" class="center" v-loading="loading">
      <el-form :model="FromTransaction" ref="ruleForm" label-width="80px" class="demo-ruleForm">
        <el-form-item label="分配人员" :rules="[{ required: true, message: '请选择分配人', trigger: 'blur' }]">
          <el-select filterable v-model="FromTransaction.assignUserId" placeholder="请选择分配人" @change="selectUser">
            <el-option v-for="(item, index) in peopleList" :value="item.id" :label="item.name" :key="index"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="DetermineAllocation">确定</el-button>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import { transactionAssign, getUserList } from '@/api/assets'
import { api } from 'v-viewer'
import { Throttle } from '../../../../../decorators'
export interface Transaction {
  leaseId: number | string
  assignUserId: number | string
  assignUserName: string
}
@Component({
  components: {
    Dialog
  }
})
export default class extends Vue {
  @Prop() private visible!: boolean
  @Prop() private assetId!: string
  private peopleList = [] //人员列表
  private loading = false
  private FromTransaction: Transaction = {
    leaseId: this.assetId,
    assignUserId: '',
    assignUserName: ''
  }
  private mounted() {
    this.getUserList()
  }
  // 选择人员时加载数据
  private selectUser(id: any) {
    let list: any = this.peopleList.find((res: any) => {
      return res.id == id
    })
    this.FromTransaction.assignUserId = list.id
    this.FromTransaction.assignUserName = list.name
    this.FromTransaction.leaseId = this.assetId
  }
  //  加载人员树
  private async getUserList() {
    try {
      this.loading = true
      let res = await getUserList({})
      if (res.success) {
        this.peopleList = res.data
        this.loading = false
      }
    } catch (e) {
      this.loading = false
    }
  }
  private handleClose() {
    this.$emit('update:visible', false)
  }
  //   验证表单
  @Throttle
  private async DetermineAllocation() {
    if (this.FromTransaction.assignUserId != '' && this.FromTransaction.assignUserName != '') {
      this.sendApi()
    } else {
      this.$message.info('请选择分配人')
      return false
    }
  }
  //   发送请求
  @Throttle
  private async sendApi() {
    let res: any = {}
    try {
      this.loading = true
      res = await transactionAssign({
        ...this.FromTransaction
      })
      if (res.success) {
        this.$message.success('分配成功')
        this.loading = true
        this.handleClose()
      }
    } catch (e) {
      this.loading = true
      this.$message.success(res.msg)
    }
  }
}
</script>

<style lang="scss" scoped>
.center {
  text-align: center;
}
</style>
