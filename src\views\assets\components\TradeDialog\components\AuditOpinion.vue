// 审核意见
<template>
  <section class="rental-info">
    <!-- 基本情况 -->
    <!-- <el-descriptions
      class="margin-top"
      title
      :column="24"
      :labelStyle="{
        width: '120px'
      }"
      border
    >
      <el-descriptions-item label="出租标的和出租方" :span="24" contentClassName="accessory-list">
        <el-row :span="24">
          <el-col :span="24">附件资料（以下附件如为复印件应加盖公章）</el-col>
        </el-row>

        <el-row :span="24">
          <el-col :span="24">
            <el-checkbox
              disabled
              v-model="checkedMap.lessor.attachmentFile1"
              label="营业执照或其他主体资格证明材料"
            ></el-checkbox>
            <el-checkbox disabled v-model="checkedMap.lessor.attachmentFile3" label="自然人身份证明"></el-checkbox>
            <el-checkbox disabled v-model="checkedMap.lessor.attachmentFile2" label="法定代表人身份证明"></el-checkbox>
          </el-col>
        
        </el-row>

        <el-row :span="24">
          <el-col :span="24">
            <el-checkbox disabled v-model="checkedMap.basic.attachmentFile1" label="授权委托人的身份证明"></el-checkbox>
          </el-col>
      
        </el-row>
        <el-row :span="24">
          <el-col :span="24">
            <el-checkbox disabled v-model="checkedMap.basic.attachmentFile2" label="授权委托书"></el-checkbox>
          </el-col>
     
        </el-row>
        <el-row :span="24">
          <el-col :span="24">
            <el-checkbox disabled v-model="checkedMap.basic.attachmentFile3" label="内部决策文件"></el-checkbox>
          </el-col>
   
        </el-row>
        <el-row :span="24">
          <el-col :span="24">
            <el-checkbox disabled v-model="checkedMap.basic.attachmentFile4" label="审批文件"></el-checkbox>
          </el-col>
       
        </el-row>
        <el-row :span="24">
          <el-col :span="24">
            <el-checkbox disabled v-model="checkedMap.basic.attachmentFile5" label="资产权属证明"></el-checkbox>
          </el-col>
        
        </el-row>
        <el-row :span="24">
          <el-col :span="24">
            <el-checkbox
              disabled
              v-model="checkedMap.basic.attachmentFile7"
              label="其他资产相关权利人的意思表示（如涉及）"
            ></el-checkbox>
          </el-col>
        
        </el-row>
        <el-row :span="24">
          <el-col :span="24">
            <el-checkbox
              disabled
              v-model="checkedMap.basic.attachmentFile8"
              label="其他具有法律效力的权属证明文件（如涉及）"
            ></el-checkbox>
          </el-col>
         
        </el-row>
        <el-row :span="24">
          <el-col :span="24">
            <el-checkbox disabled v-model="checkedMap.lessee.attachmentFile1" label="委托出租服务协议"></el-checkbox>
          </el-col>
         
        </el-row>
        <el-row :span="24">
          <el-col :span="24">
            <el-checkbox disabled v-model="checkedMap.lessee.attachmentFile2" label="租赁合同样稿"></el-checkbox>
          </el-col>
       
        </el-row>
        <el-row :span="24">
          <el-col :span="24">
            <el-checkbox disabled v-model="checkedMap.lessee.attachmentFile3" label="浙交汇意向方报名时所有文本"></el-checkbox>
          </el-col>
        
        </el-row>
        <el-row :span="24">
          <el-col :span="24">
            <el-checkbox disabled v-model="checkedMap.lessee.attachmentFile4" label="其他文件"></el-checkbox>
          </el-col>
       
        </el-row>
      </el-descriptions-item>

   
    </el-descriptions> -->
    <el-descriptions title="受托机构核实意见" class="m-t-20" border>
      <template slot="title">
        <div style="display:flex;">
          受托机构核实意见 
          <div v-if="result!=3" class="m-l-200 buttonlist" style="margin-left:300px">
            <!-- <el-button size="mediu" v-if="true" :type="result===0?'primary':''" @click="ChangeResult(0)">驳回</el-button> -->
            <!-- <el-button size="mediu"  v-if="true" :type="result===1?'primary':''" @click="ChangeResult(1)">通过</el-button> -->
          </div>
        </div>
        <!-- {{result}} -->
        <div v-if="result!=3">
          <el-radio v-model="result" @change="changeresult" :label="1">通过</el-radio>
          <el-radio v-model="result" @change="changeresult" :label="0">驳回</el-radio>
        </div>
        <div v-else class="m-t-20">
        <el-tag v-if="detail.verify.result==0" type="success'"> 核实驳回 </el-tag>
        <el-tag v-else-if="detail.verify.result==1" type="success'"> 核实通过 </el-tag>
        <el-tag v-else type="success'"> 未核实 </el-tag>
        </div>
      </template>
      <el-descriptions-item label="意见" :span="24" :labelStyle="{
        width: '90px'
      }">
        <el-input
          type="textarea"
          :autosize="{ minRows: 3, maxRows: 6}"
          maxlength="300"
          show-word-limit
          v-if="result===0||result===1"
          v-model="verifyOpinions"
          placeholder="请输入审核意见"
        ></el-input>
        <p v-if="result==3" style="min-height:30px">{{ detail.verify.verifyOpinions }}</p>
      </el-descriptions-item>
    </el-descriptions>

    <!-- 附件列表 -->
    <!-- {{result}} -->
    <div v-show="result==1">
      <AccessoryList
        v-model="detail.verify.attachmentFileDTOList"
        dict="asset_verify_pass_attach"
        mode="upload"
      />
    </div>

    <div v-show="result==0">
      <AccessoryList
        v-model="detail.verify.attachmentFileDTOList"
        dict="asset_verify_fail_attach"
        mode="upload"
      />
    </div>
    <div v-show="result!=0&&result!=1">
      <AccessoryList v-model="detail.verify.attachmentFileDTOList" mode="see" />
    </div>
  </section>
</template>

<script lang="ts">
import { deepClone } from '@/utils'
import { decisionTypeList } from '@/views/assets/filterOptions'
import { ElForm } from 'element-ui/types/form'
import { Component, Model, Prop, Vue } from 'vue-property-decorator'
import { TradeDetail, Verify } from '../TradeDetailDialog.vue'
import AccessoryList, { Accessory } from './AccessoryFileList.vue'

type FileKey = 'attachmentFile1' | 'attachmentFile2'

@Component({
  components: {
    AccessoryList
  }
})
export default class extends Vue {
  @Prop({ default: () => ({}) }) private initData!: TradeDetail
  @Prop() private mode!: string
  @Prop({ default: () => 0 }) private resultnum!: number
  private verifyOpinions = ''
  private result = 1
  private detail: any = {
    assetList: [],
    lessee: {
      attachmentFile1: [],
      attachmentFile2: [],
      attachmentFile3: []
    },
    lessor: {
      attachmentFile1: [],
      attachmentFile2: [],
      attachmentFile3: [],
      attachmentFile4: []
    },
    basic: {
      attachmentFile1: [],
      attachmentFile2: [],
      attachmentFile3: [],
      attachmentFile4: [],
      attachmentFile5: [],
      attachmentFile6: [],
      attachmentFile7: [],
      attachmentFile8: []
    },
    currentAsset: {},
    verify: {
      attachmentFileDTOList: []
    }
  }
  private decisionTypeList = decisionTypeList

  private checkedMap: any = {
    lessee: {
      attachmentFile1: false,
      attachmentFile2: false,
      attachmentFile3: false
    },
    lessor: {
      attachmentFile1: false,
      attachmentFile2: false,
      attachmentFile3: false,
      attachmentFile4: false
    },
    basic: {
      attachmentFile1: false,
      attachmentFile2: false,
      attachmentFile3: false,
      attachmentFile4: false,
      attachmentFile5: false,
      attachmentFile6: false,
      attachmentFile7: false,
      attachmentFile8: false
    }
  }

  get isChecked() {
    return (len: number) => {
      return !!len
    }
  }
  // 判断文件是否上传成功
  private isupload() {
    return true
  }
  private changeresult(){
    this.detail.verify.attachmentFileDTOList=[]
  }
  private ChangeResult(state: number) {
    this.result = state
  }
  created() {
    this.detail = deepClone(this.initData)

    if (this.mode == 'see') {
      this.result = 3
    }

    // 判断是否上传文件
    // for (let prop in this.checkedMap) {
    //   for (let fileName in this.checkedMap[prop]) {
    //     //
    //     if (!this.detail[prop]) {
    //       this.checkedMap[prop][fileName] = false
    //     } else if (!this.detail[prop][fileName] || !Array.isArray(this.detail[prop][fileName])) {
    //       this.checkedMap[prop][fileName] = false
    //     } else {
    //       this.checkedMap[prop][fileName] = !!this.detail[prop][fileName].length
    //     }
    //   }
    // }

    this.checkedMap = Object.assign({}, this.checkedMap)

    // 附件列表赋值
    // this.accessoryList0.forEach((item: any) => {
    //   item.fileList = (this.detail.verify as Verify)[item.prop as FileKey] || []
    // })
    // this.accessoryList.forEach((item: any) => {
    //   item.fileList = (this.detail.verify as Verify)[item.prop as FileKey] || []
    // })
  }
}
</script>


<style scoped lang="scss">
::v-deep .el-radio__label {
  line-height: 36px;
}
::v-deep .accessory-list {
  padding: 0 !important;
}

::v-deep .el-col {
  line-height: 36px;
  padding: 6px;
  border-top: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
}

::v-deep .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  background-color: #b43c3c;
  border-color: #b43c3c;
}
</style>
<style scoped>
.el-message--warning {
  z-index: 9999 !important;
}
</style>