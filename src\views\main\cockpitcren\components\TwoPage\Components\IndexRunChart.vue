/* 国企发展指数：指标趋势图 */

<template>
  <section v-loading="loading"
    element-loading-background="rgba(0, 0, 0, 0)"
    class="cockpit-enter-option-wrap">
    <!-- echarts 视图 -->
    <div class="refEcharts"
      ref="refEcharts" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { performanceFiveYearTrends } from '@/api/cockpit'
import { Loading } from '@/decorators'
import * as echarts from 'echarts'

type EChartsOption = echarts.EChartsOption

@Component({
  components: {}
})
export default class extends Vue {
  @Prop() private code?: string // 集团code
  @Prop() private year?: string // 年份

  // echarts 视图数据
  private loading = false
  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private seriesData: any[] = []
  private legendData: string[] = []
  private xAxisData: string[] = []
  private option: EChartsOption = {}

  // 初始化
  private mounted() {
    this.chartDom = this.$refs.refEcharts as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    this.filterData()
  }

  // 渲染数据
  @Loading('loading')
  private async filterData() {
    let { data } = await performanceFiveYearTrends({
      year: this.year,
      orgCode: this.code
    })

    let legendData: string[] = []
    let xAxisData: string[] = []
    let seriesData: any[] = []

    let len = data.length
    Array.isArray(data) &&
      data.forEach((item, index) => {
        let dataList: number[] = []
        legendData.push(item.name)

        if (Array.isArray(item.data) && item.data.length) {
          xAxisData = []
          dataList = []
          item.data.forEach((itemList: { year: string; value: number }) => {
            xAxisData.push(itemList.year)
            dataList.push(+itemList.value)
          })
        }

        if (index < len - 1) {
          seriesData.push({
            name: item.name,
            type: 'bar',
            barWidth: 20,
            label: {
              show: true,
              color: '#fff',
              position: 'top',
              fontSize: 12
            },
            data: dataList
          })
        }

        if (index === len - 1) {
          seriesData.push({
            name: item.name,
            type: 'line',
            symbol: 'circle',
            data: dataList
          })
        }
      })

    // 组装数据
    this.legendData = legendData
    this.xAxisData = xAxisData
    this.seriesData = seriesData
    this.initEcharts()
  }

  // 渲染 echarts 视图
  private initEcharts() {
    let legendData = this.legendData
    let xAxisData = this.xAxisData
    let seriesData = this.seriesData

    this.option = {
      color: ['#E6B607', '#EF702E', '#35AAF6', '#00AEA5', '#F56C6C', '#3eeeff'],
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        textStyle: {
          color: '#fff'
        },
        data: legendData
      },
      grid: {
        top: '10%',
        left: '0%',
        right: '0%',
        bottom: '0%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        axisLabel: {
          fontSize: 16,
          margin: 14,
          align: 'center'
        },
        axisLine: {
          lineStyle: {
            color: '#5db0ea'
          }
        },
        data: xAxisData
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          fontSize: 16
        },
        axisLine: {
          lineStyle: {
            color: '#5db0ea'
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0, 103, 177, 0.4)'
          }
        }
      },
      series: seriesData
    }

    this.myChart && this.myChart.setOption(this.option, true)
  }
}
</script>

<style scoped lang="scss">
.cockpit-enter-option-wrap {
  position: relative;
  width: 100%;
  height: 500px;
  padding-bottom: 20px;
  .refEcharts {
    width: 100%;
    height: 100%;
  }
}
</style>