/* 全资和控股企业情况 */

<template>
  <section class="enterprise-graph-wrap">
    <TitleCom title="企业画像"
      module="CompanyDetail" />

    <div v-if="isImg"
      class="enterprise-graph-box">
      <img :src="imgSrc"
        class="img" />

      <!-- 国资总况 -->
      <template v-if="+tabActive.code === 0">
        <a href="javascript:void(0)"
          @click="openDetail(GYYY.href)"
          class="gzzk-gyyy">国有资本运营公司</a>
        <a href="javascript:void(0)"
          @click="openDetail(ABGS.href)"
          class="gzzk-abgs">安邦护卫</a>
        <a href="javascript:void(0)"
          @click="openDetail(CTJT.href)"
          class="gzzk-ctjt">城投集团</a>
        <a href="javascript:void(0)"
          @click="openDetail(JTTJT.href)"
          class="gzzk-jttjt">交投集团</a>
        <a href="javascript:void(0)"
          @click="openDetail(GDJT.href)"
          class="gzzk-gdjt">轨道集团</a>
        <a href="javascript:void(0)"
          @click="openDetail(JTJT.href)"
          class="gzzk-jtjt">金投集团</a>
      </template>

      <!-- 国资运营 -->
      <template v-if="+tabActive.code === 7">
        <a href="javascript:void(0)"
          @click="openDetail(GYYY.href)"
          class="gyyy-gyyy">国有资本运营公司</a>
        <a href="javascript:void(0)"
          @click="openDetail(ABGS.href)"
          class="gyyy-abgs">安邦护卫</a>
        <a href="javascript:void(0)"
          @click="openDetail(CTJT.href)"
          class="gyyy-ctjt">城投集团</a>
        <a href="javascript:void(0)"
          @click="openDetail(JTTJT.href)"
          class="gyyy-jttjt">交投集团</a>
        <a href="javascript:void(0)"
          @click="openDetail(GDJT.href)"
          class="gyyy-gdjt">轨道集团</a>
      </template>

      <!-- 城投集团 -->
      <template v-if="+tabActive.code === 1">
        <a href="javascript:void(0)"
          @click="openDetail(CTJT.href)"
          class="ctjt-ctjt">城投集团</a>
      </template>

      <!-- 交投集团 -->
      <template v-if="+tabActive.code === 4">
        <a href="javascript:void(0)"
          @click="openDetail(JTTJT.href)"
          class="jtjt-jttjt">交投集团</a>
      </template>

      <!-- 金投集团 -->
      <template v-if="+tabActive.code === 2">
        <a href="javascript:void(0)"
          @click="openDetail(JTJT.href)"
          class="jtjt-jtjt">金投集团</a>
      </template>

      <!-- 轨道集团 -->
      <template v-if="+tabActive.code === 5">
        <a href="javascript:void(0)"
          @click="openDetail(GDJT.href)"
          class="gdjt-gdjt">轨道集团</a>
      </template>
    </div>

    <!-- 各模块详情弹窗-->
    <ViewIframe v-if="visibleViewIframe"
      :visible.sync="visibleViewIframe"
      :src="iframeSrc"
      :title="iframeTitle"
      :isHeader="isHeader"
      width="1170px"
      height="680px" />
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import TitleCom from '@/views/main/cockpitcren/components/Public/TitleCom.vue'
import ViewIframe from '@/components/ViewIframe/index.vue'

type typeTabItem = {
  id: string
  code: string
  name: string
}

@Component({
  components: {
    TitleCom,
    ViewIframe
  }
})
export default class extends Vue {
  private isImg = false
  private isHeader = false
  private visibleViewIframe = false
  private imgSrc = ''
  private iframeSrc = ''
  private iframeTitle = ''
  private imgList = [
    {
      code: '0',
      src: require('@/views/main/cockpitcren/images/enterprise_graph_0.png')
    },
    {
      code: '7',
      src: require('@/views/main/cockpitcren/images/enterprise_graph_7.png')
    },
    {
      code: '1',
      src: require('@/views/main/cockpitcren/images/enterprise_graph_1.png')
    },
    {
      code: '4',
      src: require('@/views/main/cockpitcren/images/enterprise_graph_4.png')
    },
    {
      code: '2',
      src: require('@/views/main/cockpitcren/images/enterprise_graph_2.png')
    },
    {
      code: '5',
      src: require('@/views/main/cockpitcren/images/enterprise_graph_5.png')
    }
  ]
  private tabActive: typeTabItem = {
    id: '',
    code: '',
    name: ''
  }

  // 各集团调整链接
  private GYYY = {
    name: '国有资本运营公司',
    href: 'https://www.tianyancha.com/company/79913673?tree_full=true'
  }
  private CTJT = {
    name: '城投集团',
    href: 'https://www.tianyancha.com/company/3223473454?tree_full=true'
  }
  private JTTJT = {
    name: '交投集团',
    href: 'https://www.tianyancha.com/company/3223470210?tree_full=true'
  }
  private JTJT = {
    name: '金投集团',
    href: 'https://www.tianyancha.com/company/3377334842?tree_full=true'
  }
  private GDJT = {
    name: '轨道集团',
    href: 'https://www.tianyancha.com/company/3269921889?tree_full=true'
  }
  private ABGS = {
    name: '安邦护卫',
    href: 'https://www.tianyancha.com/company/*********?tree_full=true'
  }

  // 组件初始化
  private mounted() {
    // 中间各tabs切换触发数据更新
    this.$bus.$on('BusTabsChange', (data: typeTabItem) => {
      this.tabActive = data
      this.filterImgSrc()
    })
  }

  // 筛选图片地址
  private filterImgSrc() {
    this.isImg = false
    let filterImgTab = this.imgList.filter((item) => {
      return +this.tabActive.code === +item.code
    })

    if (Array.isArray(filterImgTab) && filterImgTab.length) {
      this.imgSrc = filterImgTab[0].src
    }

    setTimeout(() => {
      this.isImg = true
    }, 100)
  }

  // 详情弹窗
  private openDetail(src: string) {
    this.iframeSrc = src
    this.visibleViewIframe = true
  }
}
</script>

<style scoped lang="scss">
.enterprise-graph-wrap {
  position: relative;
  width: 100%;
  height: 610px;
  background: url('../../images/panel_bg2.png') no-repeat left top;
  background-size: 100% 100%;

  @keyframes keyImgMove {
    0% {
      transform: scale(0);
    }
    100% {
      transform: scale(1);
    }
  }

  .enterprise-graph-box {
    position: relative;
    height: 444px;
    margin-top: -41px;
    box-sizing: border-box;
    animation: keyImgMove 3s ease;
    a {
      position: absolute;
      color: #fff;
      font-size: 0;
      border-radius: 50%;
      &:hover {
        box-shadow: 0 0 100px #00e6fb;
      }
    }
    .img {
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      right: 0;
      margin: auto;
      width: 96%;
      height: auto;
    }
  }

  // 国资总况
  .gzzk-gyyy {
    left: 201px;
    top: 153px;
    width: 120px;
    height: 120px;
  }
  .gzzk-abgs {
    left: 145px;
    top: 30px;
    width: 90px;
    height: 90px;
  }
  .gzzk-ctjt {
    left: 25px;
    top: 147px;
    width: 90px;
    height: 90px;
  }
  .gzzk-jttjt {
    left: 54px;
    top: 295px;
    width: 90px;
    height: 90px;
  }
  .gzzk-gdjt {
    left: 198px;
    top: 324px;
    width: 90px;
    height: 90px;
  }
  .gzzk-jtjt {
    right: 25px;
    top: 226px;
    width: 90px;
    height: 90px;
  }

  // 国资运营
  .gyyy-gyyy {
    left: 289px;
    top: 44px;
    width: 160px;
    height: 160px;
  }
  .gyyy-ctjt {
    left: 55px;
    top: 85px;
    width: 90px;
    height: 90px;
  }
  .gyyy-jttjt {
    left: 132px;
    top: 252px;
    width: 90px;
    height: 90px;
  }
  .gyyy-gdjt {
    left: 324px;
    top: 315px;
    width: 90px;
    height: 90px;
  }
  .gyyy-abgs {
    right: 133px;
    top: 256px;
    width: 90px;
    height: 90px;
  }

  // 城投集团
  .ctjt-ctjt {
    left: 318px;
    top: 162px;
    width: 110px;
    height: 110px;
  }

  // 交投集团
  .jtjt-jttjt {
    left: 318px;
    top: 162px;
    width: 110px;
    height: 110px;
  }

  // 金投集团
  .jtjt-jtjt {
    left: 289px;
    top: 44px;
    width: 160px;
    height: 160px;
  }

  // 轨道集团
  .gdjt-gdjt {
    left: 285px;
    bottom: 47px;
    width: 170px;
    height: 170px;
  }
}
</style>