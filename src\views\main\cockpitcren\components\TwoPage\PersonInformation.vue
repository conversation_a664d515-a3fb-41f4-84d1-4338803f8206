/* 企业数量 */

<template>
  <section v-loading="loading"
    class="cockpit-person-information-wrap"
    :class="{'cockpit-wrapperFull-wrap': isWrapperFull}">
    <TitleCom title="企业数量"
      module="CompanyDetail"
      :isThrow="false"
      :isFullScreen="true"
      @fullScreenHandle="fullScreenHandle" />

    <div class="info-box">
      <div class="total">
        <div class="mode"
          @click="detailHandle('jg')">
          <span>监管企业</span>
          <CountTo :decimals="0"
            :startVal="0"
            :endVal="+dataObj.total || 0"
            :duration="1500"
            class="num" />
          <span>家</span>
        </div>
      </div>

      <div class="divider">
        <div class="dr dr1" />
        <div class="dr dr2" />
      </div>
    </div>

    <div class="outside-box">
      <div class="mode mode_bb"
        @click="detailHandle('bb')">
        <span>并表企业</span>
        <CountTo :decimals="0"
          :startVal="0"
          :endVal="+dataObj.spTotal || 0"
          :duration="1500"
          class="num" />
        <span>家</span>
      </div>

      <div class="mode mode_cg"
        @click="detailHandle('cg')">
        <span>参股企业</span>
        <CountTo :decimals="0"
          :startVal="0"
          :endVal="+dataObj.ownerCount || 0"
          :duration="1500"
          class="num" />
        <span>家</span>
      </div>
    </div>

    <div class="content-box">
      <PiePersons :dataList="dataObj.levelList"
        textColor="#3eeeff"
        :colorList="['#00BAFF', '#FF7A27', '#FFDE02']"
        class="mode" />
      <PiePersons :dataList="dataObj.typeList"
        textColor="#3eeeff"
        :colorList="['#E04290', '#03E791', '#8F44F4']"
        class="mode" />
    </div>

    <!-- 数据改变提示层 -->
    <img v-if="visibleFlicker"
      class="cockipt-bg-flicker"
      src="@/views/main/cockpitcren/images/panel_bg3_cg.png" />

    <!-- 详情弹窗 -->
    <CockiptGridDialog v-if="visibleDetail"
      :visible.sync="visibleDetail"
      :closeModal="false"
      :unit="false"
      :isYear="false"
      :year="params.year"
      :code="orgActive.orgCode"
      :columns="columns"
      :remoteUrl="remoteUrl"
      :searchParams="searchParams"
      :title="titleDetail" />
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { entHomeSummary } from '@/api/cockpit'
import CountTo from 'vue-count-to'
import TitleCom from '@/views/main/cockpitcren/components/Public/TitleCom.vue'
import GaugeRing from '@/views/main/cockpitcren/components/TwoPage/GaugeRing.vue'
import PiePersons from '@/views/main/cockpitcren/components/TwoPage/PiePersons.vue'
import CockiptGridDialog from '@/views/main/cockpitcren/components/Public/CockiptDialog.vue'

@Component({
  components: {
    TitleCom,
    CountTo,
    GaugeRing,
    PiePersons,
    CockiptGridDialog
  }
})
export default class extends Vue {
  private loading = false
  private visibleFlicker = false
  private dataObj = {
    total: 0,
    levelList: [],
    typeList: []
  }

  // 所属集团+时间切换 选中数据
  private orgActive: {
    orgCode: string
  } = {
    orgCode: '0'
  }
  private params = {
    year: String(new Date().getFullYear()),
    month: ''
  }

  // 详情弹窗数据
  private visibleDetail = false
  private titleDetail = ''
  private remoteUrl = '/fht-monitor/ent/suprList'
  private searchParams = {}
  private columns: any[] = [
    {
      label: '企业名称',
      prop: 'entName'
    },
    {
      label: '功能分类',
      prop: 'fnCategoryDesc'
    },
    {
      label: '企业类别',
      prop: 'entCategoryDesc'
    },
    {
      label: '监管级次',
      prop: 'suprLevel'
    },
    {
      label: '股权比例(%)',
      prop: 'parentRatio'
    }
  ]

  // 是否放大展示
  private isWrapperFull = false

  // 设置自定义颜色
  get getCircleColor() {
    return (index: number) => {
      let color = '#3AD4FE'

      switch (index) {
        case 0:
          color = '#3AD4FE'
          break
        case 1:
          color = '#FF7A27'
          break
        case 2:
          color = '#FEDE02'
          break
        case 3:
          color = '#03E791'
          break
        case 4:
          color = '#E04290'
          break
      }

      return color
    }
  }

  // 转换 . 后面为0的数据
  get getNumZero() {
    return (num: string | number) => {
      let data = String(num).split('.')

      if (data.length > 1 && !data[1]) {
        return +data[0]
      } else {
        return +num
      }
    }
  }

  // 初始化
  private mounted() {
    this.listenerDate()
  }

  // 监听全局事件触发
  private listenerDate() {
    // 集团 切换
    this.$bus.$on('BusTabsChange', (orgActive: any) => {
      this.orgActive = orgActive
      this.initData()
    })

    // 年份 切换
    this.$bus.$on('BusYearTimeChange', (year: string) => {
      this.params.year = year
      this.initData()
    })

    // // 月份 切换
    // this.$bus.$on('BusMoonTimeChange', (month: string) => {
    //   this.params.month = month
    //   this.initData()
    // })
  }

  // 获取渲染视图数据
  @Loading('loading')
  private async initData() {
    this.loading = true
    this.flickerHandle()
    await entHomeSummary({
      orgCode: this.orgActive.orgCode,
      year: this.params.year
    })
      .then((res) => {
        this.dataObj = res.data || {}
      })
      .catch(() => {
        this.dataObj = {
          total: 0,
          levelList: [],
          typeList: []
        }
      })
  }

  // 查看详情
  private detailHandle(fnCategory: string) {
    if (fnCategory === 'jg') {
      this.searchParams = {}
      this.titleDetail = '监管企业'
      this.remoteUrl = '/fht-monitor/ent/suprList'
    } else if (fnCategory === 'bb') {
      this.searchParams = { fnCategory: 4, regularCategory: 1 }
      this.titleDetail = '并表企业'
      this.remoteUrl = '/fht-monitor/ent/suprList'
    } else if (fnCategory === 'cg') {
      this.searchParams = {}
      this.titleDetail = '参股企业'
      this.remoteUrl = '/fht-monitor/ent/ownerList'
    }

    this.visibleDetail = true
  }

  // 放大/缩小展示
  private fullScreenHandle(isFull: boolean) {
    this.isWrapperFull = isFull
  }

  // 触发背景闪烁效果
  private flickerHandle() {
    this.visibleFlicker = true
    setTimeout(() => {
      this.visibleFlicker = false
    }, 3000)
  }
}
</script>

<style scoped lang="scss">
.cockpit-person-information-wrap {
  position: relative;
  transition: 1s;
  width: 100%;
  height: 530px;
  background: url('../../images/panel_bg3.png') no-repeat left top;
  background-size: 100% 100%;

  .info-box {
    position: absolute;
    left: 409px;
    width: 887px;
    z-index: 2;
    transform: translateY(-108px);
    .total {
      display: flex;
      align-items: center;
      justify-content: center;
      .mode {
        display: flex;
        align-items: center;
        justify-content: right;
        cursor: pointer;
        span {
          font-size: 38px;
        }
        .num {
          color: #ff7a27;
          padding: 0 20px;
          font-size: 58px;
          font-weight: normal;
          font-family: 'PangMenZhengDao';
        }
      }
    }

    .divider {
      width: 110%;
      padding: 10px 0 10px;
      .dr {
        height: 3px;
      }
      .dr1 {
        background: linear-gradient(
          to left,
          rgba(0, 0, 0, 0) 0%,
          rgba(0, 0, 0, 0.3) 20%,
          rgba(0, 0, 0, 0.8) 50%,
          rgba(0, 0, 0, 0.3) 80%,
          rgba(0, 0, 0, 0.1) 90%,
          rgba(0, 0, 0, 0) 100%
        );
      }
      .dr2 {
        background: linear-gradient(
          to left,
          rgba(37, 137, 254, 0) 0%,
          rgba(37, 137, 254, 0.3) 20%,
          rgba(37, 137, 254, 0.8) 50%,
          rgba(37, 137, 254, 0.3) 80%,
          rgba(37, 137, 254, 0.1) 90%,
          rgba(37, 137, 254, 0) 100%
        );
      }
    }
  }

  .outside-box {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 142px;
    left: 409px;
    width: 887px;
    z-index: 3;
    .mode {
      padding: 0 40px;
      color: rgba($color: #fff, $alpha: 0.8);
      cursor: pointer;
      .num {
        font-size: 40px;
        font-weight: normal;
        font-family: 'PangMenZhengDao';
        color: rgba($color: #ff7a27, $alpha: 0.8);
      }

      &:hover {
        transform: scale(1.1);
        color: rgba($color: #fff, $alpha: 1);
        .num {
          color: rgba($color: #ff7a27, $alpha: 1);
        }
      }
    }
  }

  .content-box {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    transform: translateY(51px);
    .mode {
      flex: 1;
      box-sizing: border-box;
    }
  }
}

.cockpit-wrapperFull-wrap {
  z-index: 1000;
  transform-origin: center bottom;
  transform: scale(1.7);
  border-radius: 20px;
  background: #072979;
  box-shadow: 0 0 40px #000;
}
</style>