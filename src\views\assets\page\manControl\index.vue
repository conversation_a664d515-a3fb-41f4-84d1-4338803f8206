<template>
  <section v-loading="loading"
    class="assets-manContorl-wrap">
    <el-tabs v-model="activeName"
      class="tabs-box">
      <el-tab-pane name="house">
        <span slot="label">
          <i class="el-icon-office-building" />
          房产
        </span>
        <HouseLand v-if="activeName === 'house'"
          assetType="1" />
      </el-tab-pane>

      <el-tab-pane name="land">
        <span slot="label">
          <i class="el-icon-receiving" />
          土地
        </span>
        <HouseLand v-if="activeName === 'land'"
          assetType="2" />
      </el-tab-pane>
    </el-tabs>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import HouseLand from './components/HouseLand.vue'

@Component({
  components: {
    HouseLand
  }
})
export default class extends Vue {
  private loading = false
  private activeName: 'house' | 'land' = 'house'
}
</script>

<style scoped lang="scss">
.assets-manContorl-wrap {
  background: #fff;
  padding: 10px 14px;

  ::v-deep .tabs-box {
    .el-tabs__item {
      font-size: 16px;
    }
  }
}
</style>
