<template>
  <section v-loading="loading"
    class="echarts-overviews-wrap">
    <div class="header-box">
      <el-form ref="form"
        :inline="true"
        :model="formData">
        <el-form-item style="text-align: left;">
          <el-radio-group v-model="formData.orgCode">
            <el-radio-button v-for="(item, index) of compTree"
              :key="index"
              :label="item.deptCode"
              :value="item.deptName">{{item.deptName}}</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item style="margin-right: 0;">
          <el-date-picker v-model="formData.year"
            :clearable="false"
            type="year"
            placeholder="年份"
            value-format="yyyy"
            style="width: 150px;">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </div>

    <div class="summaryWrap">
      <Summary propName="name"
        propUnit="unit"
        propTotal="value"
        propTb="tb"
        :isTb="true"
        :data="dataList" />
    </div>

    <!-- 对应驾驶舱跳转 -->
    <JumpCockpit path="/moduledetail?module=DebtWarning" />

    <div v-if="isShow"
      class="conter-box">

      <!-- 资产经营预警分布 -->
      <div class="modules">
        <div class="mode">
          <div class="til">
            <i class="el-icon-s-marketing" />
            <span>预警分布</span>

            <el-radio-group v-model="dealAssetWarning"
              size="mini"
              class="type-box">
              <el-radio-button label="">全部</el-radio-button>
              <el-radio-button label="0">未处置</el-radio-button>
              <el-radio-button label="1">已处置</el-radio-button>
            </el-radio-group>
          </div>

          <div class="cter">
            <AssetWarning :year="formData.year"
              :dealStatus="dealAssetWarning"
              :orgCode="formData.orgCode" />
          </div>
        </div>
      </div>

      <!-- 融资信息预警分布 -->
      <!-- <div class="modules">
        <div class="mode">
          <div class="til">
            <i class="el-icon-s-marketing" />
            <span>融资信息预警分布</span>

            <el-radio-group v-model="dealFinanceWarning"
              size="mini"
              class="type-box">
              <el-radio-button label="">全部</el-radio-button>
              <el-radio-button label="0">未处置</el-radio-button>
              <el-radio-button label="1">已处置</el-radio-button>
            </el-radio-group>
          </div>

          <div class="cter">
            <FinanceWarning :year="formData.year"
              :dealStatus="dealFinanceWarning" />
          </div>
        </div>
      </div> -->

      <!-- 年度预警数量 -->
      <div class="modules">
        <div class="mode">
          <div class="til">
            <i class="el-icon-s-marketing" />
            <span>年度预警数量</span>

            <el-radio-group v-model="dealStatusAnnualQuantity"
              size="mini"
              class="type-box">
              <el-radio-button label="">全部</el-radio-button>
              <el-radio-button label="0">未处置</el-radio-button>
              <el-radio-button label="1">已处置</el-radio-button>
            </el-radio-group>
          </div>

          <div class="cter">
            <EchartsAlertNums :isAssets="getAssetsRoute"
              :year="formData.year"
              :dealStatus="dealStatusAnnualQuantity"
              :orgCode="formData.orgCode" />
          </div>
        </div>
      </div>

      <!-- 集团年度预警数量 -->
      <div class="modules">
        <div class="mode">
          <div class="til">
            <i class="el-icon-s-marketing" />
            <span>集团年度预警数量</span>

            <el-radio-group v-model="dealStatusGroundAnnualQuantity"
              size="mini"
              class="type-box">
              <el-radio-button label="">全部</el-radio-button>
              <el-radio-button label="0">未处置</el-radio-button>
              <el-radio-button label="1">已处置</el-radio-button>
            </el-radio-group>
          </div>

          <div class="cter">
            <EchartsRankingQuantity :isAssets="getAssetsRoute"
              :year="formData.year"
              :dealStatus="dealStatusGroundAnnualQuantity"
              :orgCode="formData.orgCode" />
          </div>
        </div>
      </div>

      <!-- 各类业务预警数量(Top 10)、业务预警占比(Top 6) -->
      <div v-if="false"
        class="modules">
        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>各类业务预警数量(Top 10)</span>
          </h4>
          <div class="cter">
            <EchartsBusinessQuantity :isAssets="getAssetsRoute"
              :year="formData.year" />
          </div>
        </div>

        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>业务预警占比(Top 6)</span>
          </h4>
          <div class="cter">
            <EchartsProportionBusiness :isAssets="getAssetsRoute"
              :year="formData.year" />
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import JumpCockpit from '@/components/JumpCockpit/index.vue'
import EchartsAlertNums from './components/ComSurvey/EchartsAlertNums.vue'
import EchartsRankingQuantity from './components/ComSurvey/EchartsRankingQuantity.vue'
import AssetWarning from './components/ComSurvey/AssetWarning.vue'
import FinanceWarning from './components/ComSurvey/FinanceWarning.vue'
import EchartsBusinessQuantity from './components/ComSurvey/EchartsBusinessQuantity.vue'
import EchartsProportionBusiness from './components/ComSurvey/EchartsProportionBusiness.vue'
import Summary from '@/views/prewarning/components/ComHome/Summary.vue'
import { getTopData } from '@/api/prewarning'
import { getGroupList, getOrgCodeList } from '@/api/public'

@Component({
  components: {
    JumpCockpit,
    EchartsAlertNums,
    EchartsRankingQuantity,
    EchartsBusinessQuantity,
    EchartsProportionBusiness,
    Summary,
    AssetWarning,
    FinanceWarning
  }
})
export default class extends Vue {
  private isShow = true
  private loading = false
  private dealStatusAnnualQuantity = ''
  private dealStatusGroundAnnualQuantity = ''
  private dealAssetWarning = ''
  private dealFinanceWarning = ''
  private timer: any = null
  private compTree = []
  private formData: {
    year: string | null
    orgCode: string | null
  } = {
    year: '',
    orgCode: ''
  }
  private dataList = []

  // 添加 watch 监听 formData.year 变化
  @Watch('formData.year')
  private onYearChange() {
    this.initTopData()
  }

  // 添加 watch 监听 formData.orgCode 变化
  @Watch('formData.orgCode')
  private onOrgCodeChange() {
    this.initTopData()
  }

  // 获取集团列表
  private async getCompTree() {
    let { data } = await getOrgCodeList()

    data.unshift({
      deptCode: '0',
      deptName: '全部'
    })

    // 设置第一个选中数据
    if (Array.isArray(data) && data.length) this.formData.orgCode = data[0].deptCode

    this.compTree = data || []
  }

  private async initTopData() {
    let { data } = await getTopData({
      year: this.formData.year,
      orgCode: this.formData.orgCode
    })
    this.dataList = data
  }

  // 资产中的“预警汇总”模块和智慧预警的概况页面一样，做一个区分（true：是；false：否）
  get getAssetsRoute() {
    let { path } = this.$route

    if (path.indexOf('/assets/prewarning') > -1) {
      return true
    } else {
      return false
    }
  }

  // 数据初始化
  private created() {
    this.formData.year = (this as any).$moment(new Date()).add('year', 0).format('YYYY')
    this.getCompTree()
  }

  // 组件初始化
  private mounted() {
    window.addEventListener('resize', () => {
      clearTimeout(this.timer)
      this.isShow = false
      this.loading = true
      this.timer = setTimeout(() => {
        this.isShow = true
        this.loading = false
      }, 500)
    })
  }

  // 组件销毁
  private destroyed() {
    clearTimeout(this.timer)
    this.timer = null
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-radio-group {
  .el-radio-button__inner {
    font-size: 12px !important;
  }
}

.type-box {
  margin-left: 20px;
}

.summaryWrap {
  background-color: #fff;
  margin-bottom: 8px;
  box-sizing: border-box;
  padding: 8px;

  ::v-deep .info {
    width: 100%;
    justify-content: center;
  }
}
</style>
