<template>
  <section class="echarts-dom-wrap">
    <div v-loading="loading"
      ref="chartDom"
      class="chartDom"
      :class="{'hide': !interfaceData.length}" />

    <el-empty description="暂无数据"
      class="empty-none-data"
      :class="{'none': interfaceData.length}" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { monthHistory } from '@/api/prewarning'
import { Loading } from '@/decorators'
import * as echarts from 'echarts'

let chartDom = null
let myChart: any = null

@Component({})
export default class extends Vue {
  @Prop() private orgCode!: string // 集团code
  @Prop() private year!: string // 年份
  @Prop() private dealStatus?: string // 类型
  @Prop() private isAssets?: false // 判断是否是资产中的“预警汇总”模块

  private loading = false
  private legendData: string[] = []
  private xAxisData: string[] = []
  private seriesData: any[] = []
  private interfaceData: any[] = []

  // 监听搜索条件改变
  @Watch('orgCode', { deep: true })
  @Watch('year', { deep: true })
  @Watch('dealStatus', { deep: true })
  private watchYear() {
    this.initData()
  }

  // 组件初始化
  private mounted() {
    this.$nextTick(() => {
      chartDom = this.$refs.chartDom
      myChart = echarts.init(chartDom as HTMLElement)

      this.initData()
    })
  }

  // 获取数据
  @Loading('loading')
  private async initData() {
    let { data } = await monthHistory({
      orgCode: this.orgCode,
      year: this.year,
      dealStatus: this.dealStatus
    })

    // let data = [
    //   {
    //     name: '红色预警',
    //     list: [
    //       {
    //         name: '1',
    //         value: 10
    //       },
    //     ]
    //   },
    // ]

    this.interfaceData = data || []

    // 组装数据
    let legendData: string[] = []
    let seriesData: any[] = []
    let xAxisData: string[] = []

    Array.isArray(data) &&
      data.forEach((item: { name: string; list: any[] }) => {
        legendData.push(item.name)

        if (Array.isArray(item.list) && item.list.length) {
          xAxisData = []
          item.list.forEach((itemList: { name: string }) => {
            xAxisData.push(itemList.name + '月')
          })
        }

        seriesData.push({
          name: item.name,
          type: 'line',
          symbol: 'circle',
          label: {
            show: true,
            position: 'top'
          },
          data: item.list.map((item) => {
            return item.value
          })
        })
      })

    this.legendData = legendData
    this.xAxisData = xAxisData
    this.seriesData = seriesData
    this.initEcharts()
  }

  // 初始化echarts组件
  private initEcharts() {
    let legendData = this.legendData
    let xAxisData = this.xAxisData
    let seriesData = this.seriesData

    let option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        valueFormatter: function (value: string) {
          return value + ' 个'
        }
      },
      legend: {
        data: legendData
      },
      color: ['#D54941', '#f59806', '#feca02', '#00ACFF'],
      grid: {
        top: '14%',
        left: '1%',
        right: '4%',
        bottom: '0%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: xAxisData
      },
      yAxis: {
        type: 'value',
        name: '数量',
        nameTextStyle: {
          color: '#999',
          fontSize: 12,
          align: 'left'
        }
      },
      series: seriesData
    }

    myChart && myChart.setOption && myChart.setOption(option)
  }
}
</script>

<style scoped lang="scss">
.chartDom {
  width: 100%;
  height: 100%;
}
</style>
