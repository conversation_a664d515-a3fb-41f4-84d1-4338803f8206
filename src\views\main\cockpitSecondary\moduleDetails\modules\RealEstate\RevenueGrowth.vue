/* 年度租金收入 */

<template>
  <section class="echarts-content-wrap">
    <div class="content-box"
      ref="refEcharts" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { echartConfigure } from '@/views/main/cockpitcren/baseData'
import { Loading } from '@/decorators'
import { deepClone } from '@/utils'
import { bigNumberFormat } from '@/utils'
import * as echarts from 'echarts'

type EChartsOption = echarts.EChartsOption

@Component({
  components: {}
})
export default class extends Vue {
  @Prop() private seriesData!: any[] // 渲染数据

  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private option: EChartsOption = {}
  private legendData: string[] = []
  private xAxisData: string[] = []
  private series: any[] = []

  // 监听数据变化，更新视图
  @Watch('seriesData', { deep: true, immediate: true })
  private changeSeriesData() {
    this.initData()
  }

  // 万元数据转化成亿元
  get getBigNumberFormat() {
    return (num: string | number) => {
      return bigNumberFormat(+num).value || 0
    }
  }

  // 组件初始化
  private mounted() {
    this.chartDom = this.$refs.refEcharts as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    this.initEcharts()
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let data = deepClone(this.seriesData)

    // 组装echarts数据
    if (!Array.isArray(data) || !data.length) return

    let legendData: string[] = []
    let xAxisData: string[] = []
    let seriesData: any[] = []
    let xAxisDataList: string[] = []

    data.forEach((item, index) => {
      legendData.push(item.name)
      xAxisDataList = []
      if (Array.isArray(item.list) && item.list.length) {
        item.list.forEach((itemList: { year: string }) => {
          xAxisDataList.push(itemList.year)
        })
      }

      if (!index) {
        let arr: number[] = []
        Array.isArray(item.list) &&
          item.list.forEach((itemList: { revenueAmount: number }) => {
            let num = itemList.revenueAmount ? +itemList.revenueAmount.toFixed(2) : 0
            arr.push(num)
          })

        seriesData.push({
          name: item.name,
          type: 'bar',
          barWidth: 60,
          tooltip: {
            valueFormatter: function (value: number) {
              return value + ' 万元'
            }
          },
          itemStyle: {
            borderRadius: [70, 70, 0, 0]
          },
          data: arr
        })
      } else {
        let arr: number[] = []
        Array.isArray(item.list) &&
          item.list.forEach((itemList: { averagePrice: number }) => {
            let num = itemList.averagePrice ? +itemList.averagePrice.toFixed(2) : 0
            arr.push(num)
          })

        seriesData.push({
          name: item.name,
          type: 'line',
          symbol: 'circle',
          yAxisIndex: 1,
          lineStyle: {
            width: 4
          },
          itemStyle: {
            borderRadius: [70, 70, 0, 0]
          },
          tooltip: {
            valueFormatter: function (value: number) {
              return value + ' 元'
            }
          },
          data: arr
        })
      }
    })

    xAxisData = xAxisDataList

    this.legendData = legendData
    this.xAxisData = xAxisData
    this.series = seriesData
    this.initEcharts()
  }

  // 初始化数据
  private initEcharts() {
    let legendData = this.legendData
    let xAxisData = this.xAxisData
    let seriesData = this.series
    let tooltipData: any = Object.assign(
      {
        trigger: 'axis'
      },
      echartConfigure.tooltip
    )

    this.option = {
      tooltip: tooltipData,
      color: [
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(62, 238, 255, 0.5)'
            },
            {
              offset: 0.3,
              color: 'rgba(62, 238, 255, 0.7)'
            },
            {
              offset: 1,
              color: 'rgba(62, 238, 255, 1)'
            }
          ],
          global: false // 缺省为 false
        },
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(252, 180, 94, 0.5)'
            },
            {
              offset: 0.3,
              color: 'rgba(252, 180, 94, 0.7)'
            },
            {
              offset: 1,
              color: 'rgba(252, 180, 94, 1)'
            }
          ],
          global: false
        }
      ],
      legend: {
        top: 0,
        right: 'center',
        padding: 0,
        itemHeight: 24,
        itemWidth: 24,
        itemStyle: {
          borderCap: 'butt'
        },
        lineStyle: {
          type: [0, 10],
          dashOffset: 5
        },
        textStyle: {
          color: '#fff',
          fontSize: 30,
          fontWeight: 'bold'
        },
        data: legendData
      },
      grid: {
        top: '26%',
        left: '2%',
        right: '2%',
        bottom: '4%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          data: xAxisData,
          axisLabel: {
            fontSize: 30,
            fontWeight: 'normal'
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#5db0ea'
            }
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          min: 0,
          name: `${legendData[0] ? legendData[0] : ''}(万元)`,
          max: function (value) {
            return Math.ceil(value.max) + 10
          },
          nameTextStyle: {
            color: '#5db0ea',
            align: 'left',
            fontWeight: 'bold',
            fontSize: 28
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0, 103, 177, 0.4)'
            }
          },
          axisLabel: {
            fontSize: 26,
            fontWeight: 'bold'
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#5db0ea'
            }
          }
        },
        {
          type: 'value',
          name: `${legendData[1] ? legendData[1] : ''}(元)`,
          nameTextStyle: {
            color: '#5db0ea',
            align: 'right',
            fontWeight: 'bold',
            fontSize: 28
          },
          axisLabel: {
            fontSize: 26,
            fontWeight: 'bold'
          },
          axisLine: {
            lineStyle: {
              color: '#5db0ea'
            }
          },
          splitLine: {
            show: false,
            lineStyle: {
              color: 'rgba(0, 103, 177, 0)'
            }
          }
        }
      ],
      series: seriesData,
      animationDelay: function (idx) {
        return idx * 300
      }
    }

    this.myChart && this.myChart.setOption(this.option, true)
  }
}
</script>

<style scoped lang="scss">
.echarts-content-wrap {
  position: relative;
  height: 100%;
  .content-box {
    width: 100%;
    height: 100%;
  }
}
</style>


