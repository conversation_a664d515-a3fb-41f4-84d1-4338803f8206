<template>
  <el-container class="prewarning-points-wrap"
    direction="vertical">
    <!-- 搜索头 -->
    <SearchBar :items="searchItems"
      @onSearch="handleSearch">
      <el-button v-if="getPermission('polgovernrisk_add')"
        type="primary"
        @click="addOperateHandel">新增</el-button>
    </SearchBar>

    <!-- 表格 -->
    <Grid ref="grid"
      :columns="cols"
      :border="true"
      :show-index="true"
      :show-selection="false"
      :show-pagination="true"
      :overflow-tooltip="true"
      :remote-url="remoteUrl"
      :search-params="searchParams"
      @row-click="rowClick">
      <template slot="slotStatus"
        slot-scope="scope">
        <div @click.stop>
          <el-switch v-model="scope.row.ruleStatus"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="关闭"
            @change="changeStatus(scope.row)" />
        </div>
      </template>
      <template slot="slotLevel"
        slot-scope="scope">
        <span
          :class="[{'hot':+scope.row.level === 1},{'orange':+scope.row.level === 2},{'yellow':+scope.row.level === 3},{'blue':+scope.row.level === 4}]">{{getAlertLabel(scope.row.level)}}</span>
      </template>
      <template slot="operationSlot"
        slot-scope="scope">
        <Operation :list="operationItems"
          :row="scope.row" />
      </template>
    </Grid>

    <!-- 预警新增、查看、编辑 -->
    <AlertAction v-if="visibleAlertAction"
      :visible.sync="visibleAlertAction"
      :type="typeAlertAction"
      :params="paramsData"
      @updataHandle="refresh(true)" />
  </el-container>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { deepClone } from '@/utils'
import { BusinessModule } from '@/store/modules/businessDict'
import { PermissionModule } from '@/store/modules/permissionDict'
import { alertTmetricDict, prewarningUpdateStatus } from '@/api/prewarning'
import Grid from '@/components/Grid/index.vue'
import SearchBar from '@/components/SearchBar/index.vue'
import AlertAction from './components/ComPoints/AlertAction.vue'

@Component({
  components: {
    Grid,
    SearchBar,
    AlertAction
  }
})
export default class extends Vue {
  private remoteUrl = Object.freeze('/fht-monitor/ewm/rule/page')
  private paramsData = {}
  private searchParams = {}
  private typeAlertAction = ''
  private visibleAlertAction = false
  private searchItems: any[] = [
    {
      type: 'select',
      key: 'level',
      placeholder: '预警级别',
      options: this.getDictData('ewmLevel')
    },
    {
      type: 'select',
      key: 'ruleStatus',
      placeholder: '预警状态',
      options: [
        {
          label: '启用',
          value: 1
        },
        {
          label: '关闭',
          value: 0
        }
      ]
    },
    {
      type: 'select',
      key: 'metricsId',
      placeholder: '预警分类',
      options: []
    },
    {
      type: 'text',
      key: 'keyword',
      width: '200px',
      placeholder: '预警名称'
    }
  ]
  private operationItems = [
    {
      label: '查看',
      params: { type: 'see' },
      click: this.operateHandel,
      visible: (row: any) => {
        return this.getPermission('prewarning_view')
      }
    },
    {
      label: '编辑',
      params: { type: 'edit' },
      click: this.operateHandel,
      visible: (row: any) => {
        return this.getPermission('prewarning_edit')
      }
    }
  ]
  private cols = [
    {
      label: '预警名称',
      prop: 'ruleName',
      minWidth: 100
    },
    {
      label: '预警等级',
      prop: 'level',
      slotName: 'slotLevel'
    },
    {
      label: '预警类型',
      prop: 'metricsDesc'
    },
    {
      label: '预警状态',
      prop: 'ruleStatus',
      slotName: 'slotStatus'
    },
    {
      label: '预警描述',
      prop: 'ruleDesc',
      minWidth: 160
    },
    {
      label: '限期反馈时间',
      prop: 'expireDay'
    },
    {
      label: '操作',
      width: '100',
      fixed: 'right',
      slotName: 'operationSlot'
    }
  ]

  private mounted() {
    this.getPointTypeDictBizData()
  }

  // 权限方法
  get getPermission() {
    return (code: string) => {
      return PermissionModule.dictLaodData.includes(code) || false
    }
  }

  // 获取字典项
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }

  // 获取预警等级文案
  get getAlertLabel() {
    return (level: number) => {
      let levelList = this.getDictData('ewmLevel').filter((item: any) => {
        return +level === +item.value
      })

      if (levelList.length) {
        return levelList[0].label
      } else {
        return ''
      }
    }
  }

  // 获取“预警分类”接口数据
  private async getPointTypeDictBizData() {
    let { data } = await alertTmetricDict()

    let list: any[] = []
    if (Array.isArray(data)) {
      data.forEach((item) => {
        list.push({
          label: item.name,
          value: item.id
        })
      })
    }

    let index = this.searchItems.findIndex((item) => {
      return item.key === 'metricsId'
    })

    this.$set(this.searchItems, index, {
      type: 'select',
      key: 'metricsId',
      placeholder: '预警分类',
      options: list || []
    })
  }

  // 列表加载
  private refresh(isCurrent = false) {
    ;(this.$refs.grid as Grid).refresh(isCurrent)
  }

  // 列表数据搜索
  private handleSearch(condition: any) {
    this.searchParams = Object.assign({}, deepClone(condition))
    this.refresh()
  }

  // 新增
  private addOperateHandel() {
    this.typeAlertAction = 'add'
    this.paramsData = {}
    this.visibleAlertAction = true
  }
  // 点击行
  private rowClick(row: any) {
    this.typeAlertAction = 'see'
    this.paramsData = row
    this.visibleAlertAction = true
  }
  // 列表操作
  private operateHandel(row: any = {}, index: number, params: any) {
    this.typeAlertAction = params.type
    this.paramsData = row
    this.visibleAlertAction = true
  }

  // 列表切换时
  private async changeStatus(row: { id: string; ruleStatus: number }) {
    try {
      let res = await prewarningUpdateStatus({
        id: row.id,
        ruleStatus: row.ruleStatus
      })
      this.$message.success(res.msg || '操作成功')
      this.refresh(true)
    } catch (error) {
      this.refresh(true)
    }
  }
}
</script>

<style scoped lang="scss">
.prewarning-points-wrap {
  height: 100%;

  .hot {
    color: #fb3f3f;
  }
  .orange {
    color: #ff6700;
  }
  .yellow {
    color: #e6a23c;
  }
  .blue {
    color: #409eff;
  }

  ::v-deep .el-switch {
    .el-switch__label {
      span {
        font-size: 12px !important;
      }
    }
  }
}
</style>
