// 出租方信息
<template>
  <section class="rental-info">
    <el-form ref="LessorInfoForm"
      :rules="lessorInfoFormRules"
      :model="lessorInfoForm"
      label-width="145px">
      <!-- 法人及其他组织机构 -->
      <el-descriptions class="margin-top"
        title
        :column="24"
        :labelStyle="{
          width: '130px'
        }"
        border>
        <el-descriptions-item label="公司名称"
          :span="24">
          <span slot="label">
            公司名称
            <span style="color: #fb3f3f">*</span>
          </span>
          <el-form-item label
            label-width="0px"
            prop="companyName">
            <el-input v-model="lessorInfoForm.companyName"
              placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="注册地(住所)"
          :span="24">
          <span slot="label">
            注册地(住所)
            <span style="color: #fb3f3f">*</span>
          </span>
          <el-form-item label
            label-width="0px"
            prop="registrationPlace">
            <el-input v-model="lessorInfoForm.registrationPlace"
              placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="注册资本"
          :span="12">
          <span slot="label">
            注册资本
            <span style="color: #fb3f3f">*</span>
          </span>

          <el-form-item label
            label-width="0px"
            prop="registeredCapital">
            <InputNumber v-model="lessorInfoForm.registeredCapital"
              clearable
              type="decimal"
              placeholder="请输入金额">
              <template slot="append">万元</template>
            </InputNumber>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="经济性质"
          :span="12">
          <span slot="label">
            经济性质
            <span style="color: #fb3f3f">*</span>
          </span>
          <el-form-item label
            label-width="0px"
            prop="economicNature">
            <el-select v-model="lessorInfoForm.economicNature"
              placeholder="请选择">
              <el-option v-for="(item, index) in getDictData('jjxz')"
                :key="index"
                :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item label="企业类型"
          :span="12">
          <span slot="label">
            企业类型
            <span style="color: #fb3f3f">*</span>
          </span>
          <el-form-item label
            label-width="0px"
            prop="enterpriseType">
            <!-- <el-input v-model="lessorInfoForm.enterpriseType" placeholder="请输入企业类型"></el-input> -->
            <el-select v-model="lessorInfoForm.enterpriseType"
              placeholder="请选择企业类型">
              <el-option v-for="(item, index) in economicNatureList"
                :key="index"
                :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item label="法定代表人"
          :span="12">
          <span slot="label">
            法定代表人
            <span style="color: #fb3f3f">*</span>
          </span>
          <el-form-item label
            label-width="0px"
            prop="legalPerson">
            <el-input v-model="lessorInfoForm.legalPerson"
              placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item label="所属行业类型"
          :span="12">
          <span slot="label">
            所属行业类型
            <span style="color: #fb3f3f">*</span>
          </span>
          <el-form-item label
            label-width="0px"
            prop="industryType">
            <el-select v-model="lessorInfoForm.industryType"
              placeholder="请选择"
              @change="changeIndustryType()">
              <el-option v-for="(item, index) in industryTypeList"
                :key="index"
                :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item label="所属行业"
          :span="12">
          <span slot="label">
            所属行业
            <span style="color: #fb3f3f">*</span>
          </span>
          <el-form-item label
            label-width="0px"
            prop="industry">
            <el-select v-model="lessorInfoForm.industry"
              placeholder="请选择">
              <el-option v-for="(item, index) in industryList"
                :key="index"
                :label="item.label"
                :value="item.value"></el-option>
            </el-select>
            <!-- <el-input v-model="lessorInfoForm.industry"
            placeholder="请输入" />-->
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item label="统一社会信用代码"
          :span="12">
          <span slot="label">
            统一社会信用代码
            <span style="color: #fb3f3f">*</span>
          </span>
          <el-form-item label
            label-width="0px"
            prop="lessorSocialCreditCode">
            <el-input v-model="lessorInfoForm.lessorSocialCreditCode"
              placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 自然人 -->
      <el-descriptions class="margin-top m-t-12"
        title="自然人"
        :column="3"
        :labelStyle="{
          width: '120px'
        }"
        border>
        <el-descriptions-item label="姓名"
          :span="3">
          <el-form-item label
            label-width="0px"
            prop="naturalPerson">
            <el-input v-model="lessorInfoForm.naturalPerson"
              placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="证件类型"
          :span="1">
          <el-form-item label
            label-width="0px"
            prop="naturalPersonCardType">
            <el-select v-model="lessorInfoForm.naturalPersonCardType"
              placeholder="请选择">
              <el-option v-for="(item, index) in naturalPersonCardTypeList"
                :key="index"
                :label="item.label"
                :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="证件号码"
          :span="2">
          <el-form-item label
            label-width="0px"
            prop="naturalPersonCertificateNo">
            <el-input v-model="lessorInfoForm.naturalPersonCertificateNo"
              placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 联系方式 -->
      <el-descriptions class="margin-top m-t-12"
        title="联系方式"
        :column="3"
        :labelStyle="{
          width: '120px'
        }"
        border>
        <el-descriptions-item label="联系人*"
          :span="2">
          <span slot="label">
            联系人
            <span style="color: #fb3f3f">*</span>
          </span>
          <el-form-item label
            label-width="0px"
            prop="contacts">
            <el-input v-model="lessorInfoForm.contacts"
              placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="联系人电话*"
          :span="1">
          <span slot="label">
            联系人电话
            <span style="color: #fb3f3f">*</span>
          </span>
          <el-form-item label
            label-width="0px"
            prop="contactPhone">
            <el-input v-model="lessorInfoForm.contactPhone"
              placeholder="请输入"
              type="tel" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="邮箱"
          :span="3">
          <el-form-item label
            label-width="0px"
            prop="email">
            <el-input v-model="lessorInfoForm.email"
              placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item label="联系地址*"
          :span="3">
          <span slot="label">
            联系地址
            <span style="color: #fb3f3f">*</span>
          </span>
          <el-form-item label
            label-width="0px"
            prop="contactAddress">
            <el-input v-model="lessorInfoForm.contactAddress"
              placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
    </el-form>

    <!-- 附件列表 -->
    <AccessoryList v-model="lessorInfoForm.attachmentFileDTOList"
      dict="asset_lessor_attach"
      mode="upload" />
  </section>
</template>

<script lang="ts">
import { decisionTypeList, economicNatureList } from '@/views/assets/filterOptions'
import { ElForm } from 'element-ui/types/form'
import { Component, Model, Vue, Watch } from 'vue-property-decorator'
import AccessoryList, { Accessory } from './AccessoryFileList.vue'
import InputNumber from '@/components/FormComment/inputNumber.vue'
import { validateEmail, validateLessorSocialCreditCode, validatePhone } from '@/utils/validate'
import { BusinessModule } from '@/store/modules/businessDict'
import { priceUnitList, tenantUnsolicitedTypeList } from '@/views/assets/filterOptions'

export interface LessorInfo {
  attachmentFile1: string[] // 附件-营业执照或其他主体资格证明材料
  attachmentFile2: string[] // 附件-法定代表人身份证明
  attachmentFile3: string[] // 	附件-自然人身份证明（填写自然人信息的需要上传）
  companyName: string // 公司名称
  contactAddress: string // 联系地址
  contactPhone: string // 联系人电话
  contacts: string // 	联系人(取登录人)
  economicNature: number | string // 	经济性质,(1:国资监管机构/政府部门,2:国有独资公司（企业）/国有全资企业,3:国有控股企业,4:国有事业单位，国有社团等,5:国有实际控制企业,6:国有参股企业,7:集体,8:私营,9:外资企业,99:其他)
  email: string // 邮箱
  enterpriseType: string // 企业类型
  industry: string // 所属行业
  industryType: string // 所属行业类型
  legalPerson: string // 法定代表人
  lessorSocialCreditCode: string // 统一社会信用代码
  naturalPerson: string // 自然人
  naturalPersonCardType: number | '' // 证件类型
  naturalPersonCertificateNo: string // 证件号码
  registeredCapital: string //	注册资本( 万元)
  registrationPlace: string //	注册地( 住所)
  depositBank: string // 出租方指定账户开户行
  accountName: string // 出租方指定账户名
  bankAccount: string // 出租方指定账户
  invoiceType: number | '' // 发票类型，1：普票，2：专票
  receivePhone: string // 电子发票接受手机号
  receiveEmail: string // 电子发票接受邮箱
  invoicingRemark: string // 开票备注
  attachmentFileDTOList: []
}

type FileKey = 'attachmentFile1' | 'attachmentFile2' | 'attachmentFile3'

@Component({
  components: {
    AccessoryList,
    InputNumber
  }
})
export default class extends Vue {
  @Model('change') private initData!: LessorInfo
  private tenantUnsolicitedTypeList = tenantUnsolicitedTypeList
  @Watch('initData')
  private changeInitdate() {
    this.lessorInfoForm = Object.assign(this.lessorInfoForm, this.initData)

    // 附件列表赋值
    // this.accessoryList.forEach(item => {
    //   item.fileList = this.lessorInfoForm[item.prop as FileKey] || []
    // })
    //   let list = this.getDictData('sshy').find((res: any) => {
    //   if (res.value == this.lessorInfoForm.industryType) {
    //     return res.children
    //   }
    // })
    // this.industryList = list.children||[]
  }
  private lessorInfoForm: LessorInfo = {
    attachmentFile1: [],
    attachmentFile2: [],
    attachmentFile3: [],
    attachmentFileDTOList: [],
    companyName: '',
    contactAddress: '',
    contactPhone: '',
    contacts: '',
    economicNature: '',
    email: '',
    enterpriseType: '',
    industry: '',
    industryType: '',
    legalPerson: '',
    lessorSocialCreditCode: '',
    naturalPerson: '',
    naturalPersonCardType: '',
    naturalPersonCertificateNo: '',
    registeredCapital: '',
    registrationPlace: '',
    depositBank: '',
    accountName: '',
    bankAccount: '',
    invoiceType: '',
    receiveEmail: '',
    receivePhone: '',
    invoicingRemark: ''
  }

  private lessorInfoFormRules = {
    companyName: [{ required: true, trigger: 'blur', message: '请输入公司名称' }],
    contactAddress: [{ required: true, trigger: 'blur', message: '请输入联系地址' }],
    contactPhone: [{ required: true, trigger: 'blur', validator: validatePhone }],
    contacts: [{ required: true, trigger: 'blur', message: '请输入联系人' }],
    economicNature: [{ required: true, trigger: 'blur', message: '请选择经济性质' }],
    email: [{ required: false, trigger: 'blur', validator: validateEmail }],
    enterpriseType: [{ required: true, trigger: 'blur', message: '请选择企业类型' }],
    industry: [{ required: true, trigger: 'blur', message: '请选择所属行业' }],
    industryType: [{ required: true, trigger: 'blur', message: '请选择所属行业类型' }],
    legalPerson: [{ required: true, trigger: 'blur', message: '请输入法定代表人' }],
    lessorSocialCreditCode: [
      { required: true, trigger: 'blur', validator: validateLessorSocialCreditCode },
      { required: true, trigger: 'blur', message: '请选择统一社会信用代码' }
    ],
    naturalPerson: [{ required: false, trigger: 'blur', message: '请输入自然人姓名' }],
    naturalPersonCardType: [{ required: false, trigger: 'blur', message: '请选择证件类型' }],
    naturalPersonCertificateNo: [{ required: false, trigger: 'blur', message: '请输入证件号码' }],
    registeredCapital: [{ required: true, trigger: 'change', message: '请输入注册资本' }],
    registrationPlace: [{ required: true, trigger: 'blur', message: '请输入注册地(住所)' }]
  }

  private decisionTypeList: any = decisionTypeList

  private accessoryList: Accessory[] = [
    {
      fileName: '营业执照或其他主体资格证明材料',
      isRequired: true,
      prop: 'attachmentFile1',
      fileList: []
    },
    {
      fileName: '法定代表人身份证明',
      isRequired: true,
      prop: 'attachmentFile2',
      fileList: []
    },
    {
      fileName: '自然人身份证明（填写自然人信息的需要上传）',
      isRequired: false,
      prop: 'attachmentFile3',
      fileList: []
    }
  ]

  private economicNatureList = this.getDictData('enterprise_type')
  private industryTypeList = this.getDictData('sshy')
  private naturalPersonCardTypeList = this.getDictData('zjlx')
  private industryList = []
  // private economicNatureList = []
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  created() {
    this.lessorInfoForm = Object.assign(this.lessorInfoForm, this.initData)

    // 附件列表赋值
    this.accessoryList.forEach((item) => {
      item.fileList = this.lessorInfoForm[item.prop as FileKey] || []
    })
  }
  private setData() {
    this.lessorInfoForm = Object.assign(this.lessorInfoForm, this.initData)
    // 附件列表赋值
    // this.accessoryList.forEach((item) => {
    //   item.fileList = this.rentalInfoForm[item.prop as FileKey] || []
    // })
  }
  // 更改行业
  private changeIndustryType() {
    let list = this.getDictData('sshy').find((res: any) => {
      if (res.value == this.lessorInfoForm.industryType) {
        return res.children
      }
    })
    this.lessorInfoForm.industry = ''
    this.industryList = list.children || []
  }
  // 获取数据,先执行 validate 再执行 getData
  public getData() {
    // 附件赋值
    for (let index in this.accessoryList) {
      let accessory = this.accessoryList[index]
      this.lessorInfoForm[accessory.prop as FileKey] = accessory.fileList
    }
    return this.lessorInfoForm
  }

  public validate(): Promise<boolean> {
    let form = this.$refs.LessorInfoForm as ElForm
    return form.validate().then(
      () => {
        if (this.lessorInfoForm.attachmentFileDTOList.length == 0) {
          this.$message.warning(`请上传附件 !`)
          return Promise.reject(false)
        }

        return Promise.resolve(true)
      },
      () => {
        this.$message.warning('请完善出租方信息！')
        return Promise.reject(false)
      }
    )
  }
}
</script>


<style scoped lang="scss">
::v-deep .el-form-item {
  margin-bottom: 0px !important;
}
::v-deep .department-name-input {
  .el-form-item {
    margin-bottom: 0px !important;
  }
}
</style>