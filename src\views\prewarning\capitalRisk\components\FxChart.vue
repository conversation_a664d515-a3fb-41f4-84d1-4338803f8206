<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts'

export default {
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
  },

  data () {
    return {
      chart: null,
      option: {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          formatter: (params) => {
            // console.log('请帮我', params)

            // 获取x轴类目名称
            const categoryName = params[0].name
            // 构建tooltip内容
            let result = `${categoryName}<br/>`

            // 遍历每个系列的数据
            params.forEach(param => {
              const color = param.color
              const seriesName = param.seriesName
              let value = param.value

              if (param.seriesName == '平均值') {
                value += 40
              } else if (param.seriesName == '良好值') {
                value += 60
              } else if (param.seriesName == '优秀值') {
                value += 80
              }

              // 对于scatter类型(实际值)，需要取数组第二个值
              if (param.seriesType === 'scatter') {
                value = param.value[1]
              }

              // 使用div实现对齐效果
              result += `
        <div style="display: flex; justify-content: space-between; align-items: center; min-width: 160px;">
          <span>
            <span style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background-color: ${color}; margin-right: 8px;"></span>
            <span style="font-size: 12px;">${seriesName}</span>
          </span>
          <span style="font-size: 14px;font-weight:bolder;">${value}</span>
        </div>`
            })

            return result
          }
        },
        legend: {
          data: ['较差值', '较低值', '平均值', '良好值', '优秀值', '实际值']
        },
        grid: {
          left: '0%',
          right: '0%',
          bottom: '0%',
          top: "18%",
          containLabel: true
        },
        xAxis: {
          type: 'category',
          axisTick: { show: false },
          axisLabel: {
            interval: 0,
            width: 100,        // 设置标签容器宽度
            overflow: 'truncate', // 超出显示省略号
            ellipsis: '...'   // 自定义省略号样式
          },
          data: ['现金流动负债比率', '资产负债率', '带息负债比率', '已获利息倍数', '速动比率', '净资产收益率']
        },
        yAxis: {
          type: 'value',
          max: 100,
          axisLabel: {
            formatter: value => value.toFixed(0)
          }
        },
        series: [
          {
            name: '较差值',
            type: 'bar',
            stack: 'total',
            data: [0, 0, 0, 0, 0, 0],
            itemStyle: { color: '#113C7D' }
          },
          {
            name: '较低值',
            type: 'bar',
            stack: 'total',
            data: [40, 40, 40, 40, 40, 40],
            itemStyle: { color: '#0F63EE' }
          },
          {
            name: '平均值',
            type: 'bar',
            stack: 'total',
            data: [20, 20, 20, 20, 20, 20],
            itemStyle: { color: '#259BF9' }
          },
          {
            name: '良好值',
            type: 'bar',
            stack: 'total',
            data: [20, 20, 20, 20, 20, 20],
            itemStyle: { color: '#46BDFA' }
          },
          {
            name: '优秀值',
            type: 'bar',
            stack: 'total',
            data: [20, 20, 20, 20, 20, 20],
            itemStyle: { color: '#7BD6FC' }
          },
          {
            name: '实际值',
            type: 'scatter',
            symbolSize: 12,
            itemStyle: { color: '#FF0000' },
            data: [
              [0, null], // 现金流动负债比率
              [1, 0.27], // 资产负债率
              [2, 0.35], // 带息负债比率
              [3, 0.18], // 已获利息倍数
              [4, 0.25], // 速动比率
              [5, 0.18]  // 净资产收益率
            ],
            label: {
              show: true,
              position: 'top',
              formatter: params => params.data[1].toFixed(2)
            }
          }
        ]
      }
    }
  },

  beforeDestroy () {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },

  methods: {

    preInitChart () {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
      this.$nextTick(() => {
        this.initChart()
      })
    },

    initChart () {
      this.chart = echarts.init(this.$el, 'macarons')

      this.chart.setOption(this.option)
    }
  }
}
</script>
