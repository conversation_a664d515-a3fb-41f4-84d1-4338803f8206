<template>
  <Dialog width="900px"
    title="新增联系人"
    :visible="visible"
    :modal-append-to-body="true"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :append-to-body="true"
    class="add-contact-wrap"
    @close="handleClose">

    <!-- 内容区域 -->
    <div slot="body">
      <SearchBar :items="searchItems"
        @onSearch="handleSearch"
        class="search-bar-box" />

      <Grid ref="grid"
        method="GET"
        :columns="cols"
        :show-index="true"
        :show-selection="true"
        :show-pagination="true"
        :overflow-tooltip="true"
        :remote-url="remoteUrl"
        :search-params="searchParams"
        style="height: 340px;"
        @selection-change="handleSelectionChange"
        @select-all="handleSelectionChange" />
    </div>

    <!-- 底部按钮 -->
    <div slot="footer">
      <el-button type="primary"
        @click="handleConfirm">确 认</el-button>
      <el-button @click="handleClose">关 闭</el-button>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { deepClone } from '@/utils'
import Grid from '@/components/Grid/index.vue'
import Dialog from '@/components/Dialog/index.vue'
import SearchBar from '@/components/SearchBar/index.vue'

@Component({
  components: {
    Grid,
    Dialog,
    SearchBar
  }
})
export default class extends Vue {
  @Prop({ default: false }) private visible?: boolean // 弹窗显隐

  private remoteUrl = Object.freeze('fht-admin/search/user')
  private searchParams = {}
  private contactList: any[] = []
  private cols = [
    {
      label: '单位/集团名称',
      prop: 'deptName'
    },
    {
      label: '姓名',
      prop: 'name'
    },
    {
      label: '职位',
      prop: 'postName'
    },
    {
      label: '联系方式',
      prop: 'phone'
    }
  ]
  private searchItems: any[] = [
    {
      type: 'text',
      key: 'deptName',
      width: '200px',
      placeholder: '单位/集团名称'
    },
    {
      type: 'text',
      key: 'name',
      width: '140px',
      placeholder: '姓名'
    },
    {
      type: 'text',
      key: 'postName',
      width: '240px',
      placeholder: '职位'
    }
  ]

  // 列表加载
  private refresh(isCurrent = false) {
    ;(this.$refs.grid as Grid).refresh(isCurrent)
  }

  // 列表数据搜索
  private handleSearch(condition: any) {
    this.searchParams = Object.assign({}, deepClone(condition))
    this.refresh()
  }

  // 表格勾选时触发
  private handleSelectionChange(data: any[]) {
    if (Array.isArray(data) && data.length) {
      this.contactList = data
    } else {
      this.contactList = []
    }
  }

  // 点击确认
  private handleConfirm() {
    let isPass = true
    for (let i = 0; i < this.contactList.length; i++) {
      if (!this.contactList[i].phone) {
        isPass = false
        break
      }
    }

    // 处理数据
    let contactList: any[] = []
    this.contactList.forEach((item) => {
      contactList.push({
        id: '',
        name: item.name,
        phone: item.phone,
        receiveUser: item.id
      })
    })

    if (isPass) {
      this.$emit('selectContactHandle', contactList)
      this.handleClose()
    } else {
      this.$message.warning('请勾选设置了联系方式的项')
    }
  }

  // 关闭弹窗
  private handleClose() {
    this.$emit('update:visible', false)
  }
}
</script>

<style scoped lang='scss'>
.add-contact-wrap {
  .search-bar-box {
    padding: 10px;
    background: #f5f7fa;
  }
}
</style>