<template>
  <Dialog title="资产标的" width="1000px" :visible="visible" @close="handleClose" append-to-body>
    <div slot="body">
      <!-- 资产列表 -->
      <Title title="资产列表"></Title>
      <el-autocomplete
        v-model="searchdata"
        :fetch-suggestions="querySearchAsync"
        placeholder="请输入资产名称搜索"
        @select="handleSelect"
        clearable
        prefix-icon="el-icon-zoom-in"
        style="width:30%;padding-left:0;"
        :debounce="0"
      >
        <template slot-scope="{ item }">
          <div class="name">{{ item.value }}</div>
          <span class="addr">{{ item.address }}</span>
        </template>
      </el-autocomplete>
      <Grid
        ref="grid"
        :columns="cols"
        :show-selection="true"
        :show-pagination="false"
        :overflow-tooltip="true"
        :show-index="true"
        border
        show-index-fixed="left"
        :data="houseForm.assetList"
        class="m-t-15 grid"
        style="margin-bottom:20px;min-height:150px;"
      >
        <template #deteleSlot="{row,index,$index}">
          <el-button type="text" @click="deteleAsset(row, index, $index)">删除</el-button>
        </template>
        <template #RealEstatePhoto="{row}">
          <AccessoryList btntype="text" title="" mode="see" v-model="row.fileList" />
        </template>
      </Grid>
      <!-- 资产信息 -->
      <el-form
        class="target-form"
        ref="HouseForm"
        :rules="houseFormRules"
        :model="houseForm"
        label-width="160px"
        :disabled="disabled"
        size="small"
      >
        <el-descriptions
          class="margin-top"
          title="交易条件"
          :column="2"
          :labelStyle="{
            width: '0px'
          }"
          border
        >
          <el-descriptions-item :span="2">
            <el-form-item label="标的名称" prop="subjectName">
              <el-input v-model="houseForm.subjectName" placeholder="请输入"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item :span="2">
            <el-form-item label="标的联系地址" prop="contactAddress">
              <el-input v-model="houseForm.contactAddress" placeholder="请输入"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <el-form-item label="租金挂牌价" prop="rentPrice">
              <inputNumber v-model.number="houseForm.rentPrice" placeholder="请输入"></inputNumber>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <el-form-item label="租金挂牌价单位" prop="priceUnit">
              <el-select v-model="houseForm.priceUnit" placeholder="请选择">
                <el-option
                  v-for="(item, index) in getDictData('subjectPriceUnit')"
                  :value="item.value"
                  :label="item.label"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <el-form-item label="竞拍保证金" prop="deposit">
              <InputNumber v-model="houseForm.deposit" placeholder="请输入">
                <template slot="append">万元</template>
              </InputNumber>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item></el-descriptions-item>

          <el-descriptions-item label="标的状态" :span="2">
            <el-form-item label="标的状态" prop="subjectStatus" placeholder>
              <el-radio-group v-model="houseForm.subjectStatus">
                <el-radio label="1">空置</el-radio>
                <el-radio label="2">
                  使用
                  <span v-if="houseForm.subjectStatus == '2'">
                    （原合同已到期/原合同于
                    <el-date-picker
                      v-model="houseForm.contractEndDate"
                      style="width:50%"
                      type="date"
                      value-format="yyyy-MM-dd"
                      placeholder="选择日期"
                    ></el-date-picker>
                    截止）
                  </span>
                </el-radio>
                <el-radio label="99">
                  其他
                  <el-input
                    v-if="houseForm.subjectStatus == '99'"
                    v-model="houseForm.subjectStatusDesc"
                    placeholder="请输入其他标的状态"
                  ></el-input>
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-descriptions-item>

          <!-- <el-descriptions-item :span="2">
              <el-form-item label="原合同已到期/原合同与" prop="rentPrice1">
                <el-date-picker v-model="houseForm.rentPrice1" type="date" placeholder="选择日期"></el-date-picker>
              </el-form-item>
            </el-descriptions-item>-->
          <el-descriptions-item>
            <el-form-item label="其他权利情况" prop="otherRights">
              <el-select v-model="houseForm.otherRights" placeholder="请选择">
                <el-option
                  v-for="(item, index) in getDictData('subjectOtherRights')"
                  :value="item.value"
                  :label="item.label"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <el-form-item label="分别计价" prop="isUnbundling">
              <el-select v-model="houseForm.isUnbundling" placeholder="请选择">
                <el-option v-for="(item, index) in isNo" :value="item.value" :label="item.label" :key="index"></el-option>
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item :span="2">
            <el-form-item label="租期" prop="leaseTerm">
              <el-select v-model="houseForm.leaseTerm" placeholder="请选择">
                <el-option
                  v-for="(item, index) in getDictData('subjectLeaseTerm')"
                  :value="item.value"
                  :label="item.label"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item v-if="houseForm.leaseTerm == '99'">
            <el-form-item label="开始时间" prop="leaseStartDate">
              <el-date-picker v-model="houseForm.leaseStartDate" type="date" placeholder="选择日期"></el-date-picker>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item v-if="houseForm.leaseTerm == '99'">
            <el-form-item label="结束时间" prop="leaseEndDate">
              <el-date-picker v-model="houseForm.leaseEndDate" type="date" placeholder="选择日期"></el-date-picker>
            </el-form-item>
          </el-descriptions-item>

          <!-- <el-descriptions-item></el-descriptions-item> -->
          <el-descriptions-item :span="2">
            <el-form-item label="免租期" prop="hasFreeTerm">
              <el-select v-model="houseForm.hasFreeTerm" placeholder="请选择">
                <el-option v-for="(item, index) in isNo" :value="item.value" :label="item.label" :key="index"></el-option>
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item v-if="houseForm.hasFreeTerm == 1">
            <el-form-item label="免租期时间" prop="freeTerm">
              <!-- <el-select v-model="houseForm.freeTerm" placeholder>
                  <el-option
                    v-for="(item,index) in getDictData('free_term')"
                    :value="item.value"
                    :label="item.label"
                    :key="index"
                  ></el-option>
                </el-select>-->
              <el-input v-model="houseForm.freeTerm" placeholder="请输入"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item v-if="houseForm.hasFreeTerm == 1">
            <el-form-item label="免租期时间单位" prop="freeTermUnit">
              <el-select v-model="houseForm.freeTermUnit" placeholder="请选择单位">
                <el-option
                  v-for="(item, index) in getDictData('subjectFreeTermUnit')"
                  :value="item.value"
                  :label="item.label"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item v-if="houseForm.hasFreeTerm == 1">
            <el-form-item label="是否包含在租期内" prop="inLeaseTerm">
              <el-select v-model="houseForm.inLeaseTerm" placeholder="请选择">
                <el-option v-for="(item, index) in isNo" :value="item.value" :label="item.label" :key="index"></el-option>
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <el-form-item label="原承租人姓名" prop="originalLessee">
              <el-input placeholder="请输入承租人" v-model="houseForm.originalLessee"></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <el-form-item label="是否有优先承租权" prop="priorityLease">
              <el-select v-model="houseForm.priorityLease" placeholder="请选择">
                <el-option v-for="(item, index) in isNo" :value="item.value" :label="item.label" :key="index"></el-option>
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <el-form-item label="出租使用用途" prop="assetPurposeList">
              <el-select v-model="houseForm.assetPurposeList" multiple placeholder="请选择">
                <el-option
                  v-for="(item, index) in getDictData('subjectAssetPurpose')"
                  :value="item.value"
                  :label="item.label"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <el-form-item label="出租面积(可租赁面积)" prop="rentArea">
              <inputNumber v-model="houseForm.rentArea" placeholder="请输入">
                <template slot="append">平方米</template>
              </inputNumber>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item :span="2">
            <el-form-item label="租金支付要求" prop="paymentRequire">
              <el-input
                type="textarea"
                placeholder="请输入"
                v-model="houseForm.paymentRequire"
                :autosize="{ minRows: 4, maxRows: 6 }"
                maxlength="300"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item :span="2">
            <el-form-item label="标的挂牌展示图片" prop="paymentRequire">
              <el-button type="primary" @click="uploadIMG" icon="el-icon-upload">
                {{ mode == 'see' ? '查看' : '上传' }}附件({{ houseForm.fileList.length || 0 }})
              </el-button>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
      <div style="margin-left:80px;"></div>

      <uploader
        title="房屋图片"
        :visible.sync="uploaderDlgVisible"
        v-model="houseForm.fileList"
        url-key="url"
        :show-cover="true"
        :is-private="false"
        :is-attachment="true"
        :is-img="true"
        maxLimit="10"
        :uploadable="mode == 'see' ? false : true"
        @change="onFilesChange"
        @uploadComplete="handleUploadComplete"
        btntype="text"
      ></uploader>
    </div>
    <div slot="footer">
      <el-button type="primary" @click="handleClose">取消</el-button>
      <el-button type="primary" @click="addForm" v-show="mode == 'add'">确定</el-button>
      <el-button type="primary" @click="addForm" v-show="mode == 'edit'">更新</el-button>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import Grid from '@/components/Grid/index.vue'
import AccessoryList, { Accessory } from '@/views/assets/components/astFileList/index.vue'
import Dialog from '@/components/Dialog/index.vue'
import { BusinessModule } from '@/store/modules/businessDict'
import InputNumber from '@/components/FormComment/inputNumber.vue'
import Title from '@/views/assets/components/Title/index.vue'
import { SearchAstList } from '@/api/assetsv2'
import { deepClone } from '../../../../../utils'
import uploader from '@/views/assets/components/Uploader/index.vue'
import { Throttle } from '@/decorators'
import { Confirm } from '@/decorators/index'

@Component({
  components: {
    Grid,
    AccessoryList,
    Dialog,
    InputNumber,
    Title,
    uploader
  }
})
export default class extends Vue {
  @Prop({ default: 'add' }) private mode?: string
  @Prop() private visible?: boolean
  @Prop() private formData?: boolean
  private searchList: any = []
  private searchdata = ''
  private loadingsearch = false
  private houseFormRules = {
    contactAddress: [{ required: true, message: '请输入', trigger: 'blur' }],
    subjectName: [{ required: true, message: '请输入', trigger: 'blur' }],
    rentPrice: [{ required: true, message: '请输入', trigger: 'blur' }],
    deposit: [{ required: true, message: '请输入', trigger: 'blur' }],
    priceUnit: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
    subjectStatus: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
    leaseStartDate: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
    leaseEndDate: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
    otherRights: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
    isUnbundling: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
    leaseTerm: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
    hasFreeTerm: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
    freeTerm: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
    inLeaseTerm: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
    originalLessee: [{ required: false, message: '请选择', trigger: ['blur', 'change'] }],
    priorityLease: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
    assetPurposeList: [{ required: true, message: '请选择', trigger: 'blur' }],
    freeTermUnit: [{ required: true, message: '请选择', trigger: 'blur' }],
    rentArea: [{ required: true, message: '请输入', trigger: 'blur' }],
    paymentRequire: [{ required: true, message: '请上传资产图片', trigger: ['blur', 'change'] }]
  }

  private isNo: any = [
    {
      label: '否',
      value: '0'
    },
    {
      label: '是',
      value: '1'
    }
  ]
  private houseForm: any = {
    assetList: [],
    fileList: [],
    subjectName: '',
    contactAddress: '',
    rentPrice: '',
    priceUnit: '',
    deposit: '',
    subjectStatus: '',
    contractEndDate: '',
    otherRights: '',
    isUnbundling: '',
    leaseTerm: '',
    leaseStartDate: '',
    leaseEndDate: '',
    hasFreeTerm: '',
    freeTerm: '',
    inLeaseTerm: '',
    originalLessee: '',
    priorityLease: '',
    assetPurposeList: '',
    rentArea: '',
    paymentRequire: '',
    subjectStatusDesc: '',
    freeTermUnit: ''
  }
  private uploaderDlgVisible = false //房屋附件
  private disabled = false
  private assetList: any[] = [] // 资产列表
  private cols = [
    { prop: 'location', label: '房屋坐落' },
    { prop: 'assetTypeDesc', label: '资产类别' },
    { prop: 'province', label: '省' },
    { prop: 'city', label: '市' },
    { prop: 'district', label: '区（县）' },
    { prop: 'realEstateCertNo', label: '不动产证' },
    { prop: 'houseCertNo', label: '房产证' },
    { prop: 'landCertNo', label: '土地号证' },
    { prop: 'constructionArea', label: '建筑面积' },
    { prop: 'constructionAreaUnit', label: '建筑面积单位', minWidth: 100 },
    { prop: 'houseStatus', label: '房屋当前状态', minWidth: 100 },
    { prop: 'assetPurpose', label: '房屋设计用途', width: 100 },
    { fixed: 'right', label: '房产照片', minWidth: 120, slotName: 'RealEstatePhoto' },
    { fixed: 'right', label: '操作', width: 100, slotName: 'deteleSlot' }
  ]
  mounted() {
    // 根据模式判断
    if (this.mode == 'see') {
      Object.assign(this.houseForm, this.formData)
      this.disabled = true
    } else if (this.mode == 'edit') {
      // this.houseForm = deepClone(this.formData)
      Object.assign(this.houseForm, this.formData)
      this.disabled = false
    }
  }
  private handleClose() {
    this.$emit('update:visible', false)
  }
  // 删除资产
  @Confirm({
    title: '提示',
    content: `是否确认删除？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private deteleAsset(row: any, index: any, $index: any) {
    this.houseForm.assetList.splice($index, 1)
  }
  // 字典
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  //
  private addForm() {
    let FormRef = this.$refs.HouseForm as any
    FormRef.validate()
      .then(() => {
        if (this.houseForm.fileList.length == 0 || this.houseForm.assetList.length == 0) {
          if (this.houseForm.fileList.length == 0) {
            this.$message.warning('请上传标的挂牌展示图片')
            return ''
          }
          if (this.houseForm.assetList.length == 0) {
            this.$message.warning('请选择标的资产')
            return ''
          }
        } else {
          if (this.mode == 'add') {
            this.$emit('addForm', this.houseForm)
          } else if (this.mode == 'edit') {
            this.$emit('updatedForm', this.houseForm)
          }
        }
      })
      .catch(() => {
        //
      })
  }
  // 搜索相关项目
  private handleSelect(query: any) {
    this.houseForm.assetList.push(query)
    this.$set(this.houseForm, 'contactAddress', query.location)
    this.$set(this.houseForm, 'subjectName', query.location)
    this.searchdata = ''
  }
  get getitemNoList() {
    return () => {
      let list: any = []
      ;(this.$attrs['targrtList'] as any).forEach((res: any) => {
        res.assetList.forEach((r: any) => {
          if (r != []) {
            list.push(r)
          }
        })
      })
      if (this.houseForm.assetList.length > 0) {
        list.push(this.houseForm.assetList)
      }

      let itemNoList: any = list.map((res: any) => {
        if (res.itemNo != 'null' && res.itemNo) {
          return res.itemNo
        }
      })
      return itemNoList
    }
  }
  // 远程搜索
  @Throttle
  private async querySearchAsync(queryString: string, cb: any) {
    let res: any = {}
    let itemNoList: any = this.getitemNoList()
    try {
      res = await SearchAstList({
        itemNoList: itemNoList,
        assetNo: (this.$attrs['assetData'] as any).assetNo, //	资产编号		false
        keyword: queryString, //	关键字：		false
        orgCode: (this.$attrs['assetData'] as any).orgCode //	机构编号：各个企业侧的编号		false
      })
      if (res.success) {
        // this.data= res.data.records
        let list: any = await res.data.map((item: any) => {
          return Object.assign(
            {
              label: item.location,
              value: item.location
            },
            item
          )
        })
        if (list.length) {
          cb(list)
        } else {
          this.$message.info('可用资产列表为空')
          cb([])
        }
      }
    } catch (e) {
      // console.error(e)
    }
  }
  private uploadIMG() {
    this.uploaderDlgVisible = true
  }
  //  文件上传
  private onFilesChange() {
    //
  }
  private handleUploadComplete() {
    //
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-descriptions-item__label.is-bordered-label {
  display: none;
}
.item-flex {
  width: 100%;
  display: flex;
  justify-content: space-evenly;
}
::v-deep .el-dialog__body {
  .dialog-body {
    padding-bottom: 16px !important;
  }
}

::v-deep .el-form-item {
  margin-bottom: 0px !important;
}

::v-deep .el-select {
  width: 100%;
}
.w50 {
  width: 50%;
}
::v-deep .el-descriptions-item__label.is-bordered-label {
  display: none;
}
::v-deep .grid .el-table td .cell{
  max-height: 42px;
}
:v-deep.el-radio__input.is-disabled .el-radio__inner,
.el-radio__input.is-disabled.is-checked .el-radio__inner {
  background-color: #b43c3c;
}
.content {
  padding: 10px 24px;
}
.footer {
  display: flex;
  justify-content: flex-end;
}
::v-deep .el-descriptions-item__label.is-bordered-label {
  display: none;
}
::v-deep.el-descriptions .is-bordered .el-descriptions-item__cell {
  border: 0;
}
::v-deep.el-form-item--small .el-form-item__label {
  // line-height: 25px;
}
::v-deep.el-select,
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
}
.otherTime {
  display: flex;
  justify-content: flex-start;
}
::v-deep.el-input.is-disabled .el-input__inner,
.el-select .el-input.is-disabled .el-input__inner,
.el-input.is-disabled .el-input__inner {
  background-color: #ffffff !important;
  color: #2f2f2f;
}
::v-deep.el-input.is-disabled .el-input__inner {
  background-color: #ffffff !important;
}
.grid {
  ::v-deep.el-table td .cell {
    max-height: 50px;
  }
}
::v-deep.target-form {
  .el-input.is-disabled .el-input__inner,
  .el-textarea__inner {
    background-color: #ffffff !important;
    color: #2f2f2f;
  }
}
</style>
