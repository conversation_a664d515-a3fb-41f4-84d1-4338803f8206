/* 不动产管理——预警情况 */

<template>
  <div class="operation-situation-wrap">
    <div class="content-box">
      <div v-for="(item, index) of seriesData"
        :key="index"
        class="modes">
        <img src="@/assets/images/public/icons_warning.png"
          class="text" />
        <span :title="item.label"
          class="text text-ellipsis-1">{{item.label}}</span>
        <CountTo :startVal='0'
          :endVal='+item.value'
          :duration='1500'
          class="num text" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import CountTo from 'vue-count-to'

@Component({
  components: {
    CountTo
  }
})
export default class extends Vue {
  @Prop({ default: () => [] }) seriesData!: any[]
  @Prop({ default: () => [] }) source!: Record<string, any>[]

  // 监听 seriesData 数据变化，更新视图
  @Watch('seriesData', { immediate: true })
  changeSeriesData() {
    this.initData()
  }

  private listData: any[] = []

  // 数据初始化
  private initData() {
    this.listData = this.seriesData
  }
}
</script>

<style scoped lang="scss">
.operation-situation-wrap {
  height: 100%;
  overflow-y: auto;
  padding-top: 20px;

  .content-box {
    padding: 0 23px;
    .modes {
      display: flex;
      align-items: center;
      padding: 10px;
      font-size: 36px;
      font-weight: normal;
      border: 1px solid #1968a4;
      margin-bottom: 10px;
      background: rgba($color: #1968a4, $alpha: 0.2);
      &:hover {
        background: rgba($color: #1968a4, $alpha: 0.6);
      }
      img {
        flex: none !important;
        width: 40px;
        height: auto;
        margin-right: 20px;
        padding: 0 10px;
      }
      span {
        flex: 1;
        text-align: left;
      }
      .num {
        flex: none;
        text-align: right;
        padding: 0 20px;
        font-weight: normal;
        font-family: 'PangMenZhengDao';
      }
    }
  }
}
</style>