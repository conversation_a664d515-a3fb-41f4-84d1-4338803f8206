/** 成员年龄情况 */

<template>
  <div id="AgeOfMembers" />
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { AgeOfMembersData, companyList } from '@/views/main/cockpitSecondary/baseData'
import { capitalProfile } from '@/api/cockpit'
import * as echarts from 'echarts'
import { deepClone } from '@/utils'

type EChartsOption = echarts.EChartsOption

type dataType = {
  value: number
  name: string
}

@Component
export default class extends Vue {
  private loading = false
  private maxValue = 100
  private year = ''
  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}
  private tabList = []
  private echartsDatas: any[] = []
  private seriesData: any[] = deepClone(AgeOfMembersData)
  private tabActive: {
    code: string
    name: string
  } = {
    code: '',
    name: ''
  }

  // 获取集团列表
  get getCompanyList() {
    let list: string[] = []
    companyList.forEach((item) => {
      list.push(item.name)
    })
    return list
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('AgeOfMembers') as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    // this.initData()
    this.initEcharts()
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await capitalProfile()
    if (!Array.isArray(data) || !data.length) return
    this.echartsDatas = data
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 初始化数据
  private initEcharts() {
    let textSize = 22
    let series = this.seriesData
    let yeas = ~~this.getMomentTime()
    let companyList = this.getCompanyList
    let allNum = series.reduce((sum: any, current: any) => (sum += current.value), 0)
    // rgba(0, 103, 177, 0.2)
    this.option = {
      series: [
        {
          type: 'gauge',
          startAngle: 0,
          endAngle: -270,
          pointer: {
            show: false
          },
          progress: {
            show: true,
            overlap: false,
            roundCap: true,
            clip: false,
            itemStyle: {
              borderWidth: 2,
              borderColor: '#464646'
            }
          },
          axisLine: {
            lineStyle: {
              width: 130,
              color: [[1, '#0E233A']]
            }
          },
          splitLine: {
            show: false,
            distance: 0,
            length: 10
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false,
            distance: 50
          },
          data: series,
          title: {
            fontSize: textSize * 0.8,
            fontWeight: 'bold',
            fontStyle: 'italic',
            color: '#ffffff88'
          },
          itemStyle: {
            color: '#58D9F9',
            shadowColor: 'rgba(0,138,255,0.45)',
            shadowBlur: 10,
            shadowOffsetX: 2,
            shadowOffsetY: 2
          },
          detail: {
            fontSize: textSize * 1.4,
            color: 'auto',
            formatter: (value: any) => {
              return `${((value / allNum) * 100).toFixed(1)}% ${value}人`
            }
          }
        }
      ]
    }

    this.myChart && this.myChart.setOption && this.myChart && this.myChart.setOption(this.option)
  }
  // 年份切换
  private yearHandler(year: string) {
    this.year = year
    // this.initData()
  }
}
</script>

<style scoped lang="scss">
#AgeOfMembers {
  width: 100%;
  height: 100%;
}
</style>