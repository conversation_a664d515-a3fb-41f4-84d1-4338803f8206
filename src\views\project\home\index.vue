<!-- 项目总看板 -->
<template>
  <el-container class="container"
    direction="vertical">
    <div class="header">
      <div class="header-item">
        <div class="item-title">项目总数</div>
        <div class="item-num"
          :style="'background:'+colorList[0]">0</div>
        <div class="item-tip">项目总数(个)</div>
        <div class="item-amount"
          :style="'color:'+colorList[0]">0</div>
      </div>

      <div class="header-item">
        <div class="item-title">在建项目</div>
        <div class="item-num"
          :style="'background:'+colorList[1]">0</div>
        <div class="item-tip">在建项目(个)</div>
        <div class="item-amount"
          :style="'color:'+colorList[1]">0</div>
      </div>

      <div class="header-item">
        <div class="item-title">本月新增数</div>
        <div class="item-num"
          :style="'background:'+colorList[2]">0</div>
        <div class="item-tip">本月新增数(个)</div>
        <div class="item-amount"
          :style="'color:'+colorList[2]">0</div>
      </div>

      <div class="header-item">
        <div class="item-title">本月合同量</div>
        <div class="item-num"
          :style="'background:'+colorList[3]">0</div>
        <div class="item-tip">本月合同量(份)</div>
        <div class="item-amount"
          :style="'color:'+colorList[3]">0</div>
      </div>

      <div class="header-item">
        <div class="item-title">本月招标数</div>
        <div class="item-num"
          :style="'background:'+colorList[4]">0</div>
        <div class="item-tip">本月招标数(个)</div>
        <div class="item-amount"
          :style="'color:'+colorList[4]">0</div>
      </div>

      <div class="header-item">
        <div class="item-title">本年新增数</div>
        <div class="item-num"
          :style="'background:'+colorList[5]">0</div>
        <div class="item-tip">本年新增数(个)</div>
        <div class="item-amount"
          :style="'color:'+colorList[5]">0</div>
      </div>

      <div class="header-item">
        <div class="item-title">本年招标数</div>
        <div class="item-num"
          :style="'background:'+colorList[6]">0</div>
        <div class="item-tip">本年招标数(个)</div>
        <div class="item-amount"
          :style="'color:'+colorList[6]">0</div>
      </div>

      <div class="header-item">
        <div class="item-title">本年竣工数</div>
        <div class="item-num"
          :style="'background:'+colorList[7]">0</div>
        <div class="item-tip">本年竣工数(个)</div>
        <div class="item-amount"
          :style="'color:'+colorList[7]">0</div>
      </div>

      <div class="header-item">
        <div class="item-title">竣工总数</div>
        <div class="item-num"
          :style="'background:'+colorList[8]">0</div>
        <div class="item-tip">竣工总数(个)</div>
        <div class="item-amount"
          :style="'color:'+colorList[8]">0</div>
      </div>
    </div>
    <el-table class="project-table"
      style="width: 100%"
      border
      :data="tableData">
      <el-table-column label="项目状态"
        prop="statusStr">
      </el-table-column>
      <el-table-column label="项目名称"
        prop="name">
      </el-table-column>
      <el-table-column label="估算额"
        prop="imputedAmount">
      </el-table-column>
      <el-table-column label="概算额"
        prop="probablyAmount">
      </el-table-column>
      <el-table-column label="累计合同额"
        width="100"
        prop="totalAmount">
      </el-table-column>
      <el-table-column label="结算额"
        prop="calAmount"></el-table-column>
      <el-table-column label="决算额"
        prop="calAmount">
      </el-table-column>
      <el-table-column label="工期"
        prop="time">
      </el-table-column>
      <el-table-column label="合同数量"
        prop="contractNum">
      </el-table-column>
      <el-table-column label="项目负责人"
        width="100"
        prop="charge">
      </el-table-column>
      <el-table-column label="项目地点"
        prop="address">
      </el-table-column>
      <el-table-column label="立项日期"
        prop="date">
      </el-table-column>
      <el-table-column label="立项部门"
        prop="depart">
      </el-table-column>
    </el-table>
    <el-pagination></el-pagination>
  </el-container>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'

@Component
export default class Project extends Vue {
  private colorList = ['#DD855F', '#7489D0', '#56999E', '#7489D0', '#DD855F', '#56789E', '#DD855F', '#57C5C6', '#7AA241']
  private tableData = [
    {
      name: '项目1',
      statusStr: '进行中',
      imputedAmount: '200万',
      probablyAmount: '200万',
      totalAmount: '200万',
      calAmount: '200万',
      time: '3个月',
      contractNum: '10',
      charge: '王刚',
      address: '金华市',
      depart: '交通部',
      date: '2022-02-15'
    },
    {
      name: '项目2',
      statusStr: '进行中',
      imputedAmount: '300万',
      probablyAmount: '300万',
      totalAmount: '300万',
      calAmount: '300万',
      time: '4个月',
      contractNum: '20',
      charge: '李芳',
      address: '金华市',
      depart: '国资委',
      date: '2022-02-15'
    },
    {
      name: '项目3',
      statusStr: '进行中',
      imputedAmount: '200万',
      probablyAmount: '200万',
      totalAmount: '200万',
      calAmount: '200万',
      time: '3个月',
      contractNum: '10',
      charge: '王刚',
      address: '金华市',
      depart: '交通部',
      date: '2022-02-15'
    },
    {
      name: '项目4',
      statusStr: '进行中',
      imputedAmount: '200万',
      probablyAmount: '200万',
      totalAmount: '200万',
      calAmount: '200万',
      time: '3个月',
      contractNum: '10',
      charge: '王刚',
      address: '金华市',
      depart: '交通部',
      date: '2022-02-15'
    },
    {
      name: '项目5',
      statusStr: '进行中',
      imputedAmount: '200万',
      probablyAmount: '200万',
      totalAmount: '200万',
      calAmount: '200万',
      time: '3个月',
      contractNum: '10',
      charge: '王刚',
      address: '金华市',
      depart: '交通部',
      date: '2022-02-15'
    },
    {
      name: '项目6',
      statusStr: '进行中',
      imputedAmount: '200万',
      probablyAmount: '200万',
      totalAmount: '200万',
      calAmount: '200万',
      time: '3个月',
      contractNum: '10',
      charge: '王刚',
      address: '金华市',
      depart: '交通部',
      date: '2022-02-15'
    },
    {
      name: '项目7',
      statusStr: '进行中',
      imputedAmount: '200万',
      probablyAmount: '200万',
      totalAmount: '200万',
      calAmount: '200万',
      time: '3个月',
      contractNum: '10',
      charge: '王刚',
      address: '金华市',
      depart: '交通部',
      date: '2022-02-15'
    }
  ]
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  .header {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    .header-item {
      text-align: center;
      overflow: hidden;
      box-shadow: 0 0 5px 1px #ddd;
      border-radius: 5px;
      background-color: #fff;
      padding: 20px 15px;
      margin: 0 10px;
      .item-titile {
        font-size: 16px;
        text-align: center;
        color: #a1a5b0;
        margin-bottom: 20px;
        height: 34px;
      }
      .item-num {
        display: inline-block;
        width: 56px;
        height: 56px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        color: #fff;
        margin: 20px auto;
      }
      .item-tip {
        color: #a1a5b0;
        font-size: 15px;
        text-align: center;
        margin: 10px 0px;
      }
      .item-amount {
        font-size: 18px;
      }
    }
  }

  .project-table {
    flex: 1;
  }
}
</style>