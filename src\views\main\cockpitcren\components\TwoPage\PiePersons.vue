/* 人事信息子集 */

<template>
  <section class="cockpit-pie-persons-wrap">
    <!-- echarts 视图 -->
    <div v-loading="loading"
      class="refEcharts"
      ref="refEcharts" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { echartConfigure } from '@/views/main/cockpitcren/baseData'
import * as echarts from 'echarts'

type EChartsOption = echarts.EChartsOption

@Component({
  components: {}
})
export default class extends Vue {
  @Prop({ default: '#3eeeff' }) private textColor?: string
  @Prop({
    default: () => {
      return []
    }
  })
  private dataList?: any[]

  @Prop({
    default: () => {
      return []
    }
  })
  private colorList?: any[]

  // 渲染数据
  private loading = false

  // echarts 视图数据
  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private seriesData: any[] = []
  private option: EChartsOption = {}

  @Watch('dataList')
  changeDataList() {
    this.filterData()
  }

  // 初始化
  private mounted() {
    this.chartDom = this.$refs.refEcharts as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)
  }

  // 渲染数据
  private filterData() {
    let seriesData: any[] = []

    // 组装数据
    Array.isArray(this.dataList) &&
      this.dataList.forEach((item) => {
        seriesData.push({
          name: `${item.label} ${item.value} 家`,
          value: +item.value
        })
      })

    this.seriesData = seriesData
    this.initEcharts()
  }

  // 渲染 echarts 视图
  private initEcharts() {
    let textColor = this.textColor
    let color = this.colorList
    let seriesData = this.seriesData
    let tooltipData: any = Object.assign(
      {
        trigger: 'item',
        valueFormatter: (value: number) => ''
      },
      echartConfigure.tooltipBody
    )

    this.option = {
      tooltip: tooltipData,
      color: color,
      legend: {
        right: 'right',
        top: 'center',
        align: 'left',
        left: '45%',
        orient: 'vertical',
        itemGap: 30,
        itemWidth: 20,
        itemHeight: 20,
        textStyle: {
          color: '#fff',
          rich: {
            a: {
              fontSize: 28,
              fontWeight: 'bold'
            },
            b: {
              color: textColor,
              fontSize: 44,
              fontWeight: 'normal',
              fontFamily: 'PangMenZhengDao'
            },
            c: {
              fontSize: 28
            }
          }
        },
        formatter: function (data) {
          let legendStr = data.split(' ')
          if (Array.isArray(legendStr) && legendStr.length) {
            return `{a|${legendStr[0]}} {b|${legendStr[1]}} {c|${legendStr[2]}}`
          } else {
            return ''
          }
        }
      },
      grid: {
        left: '0%',
        right: '0%',
        bottom: '0%',
        top: '0%',
        containLabel: true
      },
      series: [
        {
          type: 'pie',
          right: `50%`,
          radius: ['50%', '80%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          labelLine: {
            show: false
          },
          data: seriesData
        }
      ]
    }

    this.myChart && this.myChart.setOption(this.option, true)
  }
}
</script>

<style scoped lang="scss">
.cockpit-pie-persons-wrap {
  position: relative;
  width: 100%;
  height: 240px;
  .refEcharts {
    width: 100%;
    height: 100%;
  }
}
</style>