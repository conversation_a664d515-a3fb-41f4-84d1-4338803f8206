/* 利润概况 */

<template>
  <section class="profit-overview-wrap">
    <CommonTitle :title="title"
      :isRotuerThrow="true"
      :routerObj="typeBizObj" />

    <div class="content-box"
      ref="refEcharts" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { echartConfigure } from '@/views/main/cockpitcren/baseData'
import { deepClone } from '@/utils'
import { bigNumberFormat } from '@/utils'
import * as echarts from 'echarts'
import CommonTitle from '@/views/main/cockpitSecondary/components/CommonTitle.vue'

type EChartsOption = echarts.EChartsOption

@Component({
  components: {
    CommonTitle
  }
})
export default class extends Vue {
  @Prop() private title!: string // 标题
  @Prop() private year?: string
  @Prop() private echartsData!: any // 渲染数据
  @Prop({
    default: () => {
      return {}
    }
  })
  private typeBizObj?: object // 标题跳转

  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private seriesData: any[] = []
  private option: EChartsOption = {}
  private xAxisData: string[] = []

  // 数据变化，渲染视图
  @Watch('echartsData', { deep: true })
  private changeEchartsData() {
    this.initData()
  }

  // 万元数据转化成亿元
  get getBigNumberFormat() {
    return (num: string | number) => {
      return bigNumberFormat(+num).value || 0
    }
  }

  // 组件初始化
  private mounted() {
    this.chartDom = this.$refs.refEcharts as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)
  }

  // 请求数据接口
  private async initData() {
    let data = deepClone(this.echartsData)

    // 组装echarts数据
    if (!Array.isArray(data) || !data.length) return
    let xAxisData: string[] = []
    let seriesData: number[] = []

    data.forEach((item) => {
      xAxisData.push(item.name)
      seriesData.push(+this.getBigNumberFormat(item.value))
    })

    this.xAxisData = xAxisData
    this.seriesData = seriesData
    this.initEcharts()
  }

  // 初始化数据
  private initEcharts() {
    let textSize = echartConfigure.textSize
    let xAxisData = this.xAxisData
    let seriesData = this.seriesData

    this.option = {
      color: [
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(252, 180, 94, 0.1)'
            },
            {
              offset: 0.3,
              color: 'rgba(252, 180, 94, 0.7)'
            },
            {
              offset: 1,
              color: 'rgba(252, 180, 94, 1)'
            }
          ],
          global: false // 缺省为 false
        }
      ],
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#00000000',
        shadowColor: '#00000000',
        borderWidth: 0,
        formatter: (params: any) => {
          let dom = ''
          let style = ''
          let query = params ? params[0] : {}
          let index = query.dataIndex
          let rote = this.echartsData[index].rote

          if (+rote > 0) {
            style = `
              <div style="display: flex; align-items: center; justify-content: space-between;">
                <span style="padding-right: 40px;">同比</span>
                <span style="color: #fb3f3f; margin-left: 10px; font-size: 30px;">
                  <img style="width:22px;" src="${require('../../../images/thows.png')}" /> ${rote}%
                </span>
              </div>
            `
          } else if (+rote <= 0) {
            style = `
              <div style="display: flex; align-items: center; justify-content: space-between;">
                <span style="padding-right: 40px;">同比</span>
                <span style="color: #00a92b; margin-left: 10px; font-size: 30px;">
                  <img style="width:22px;" src="${require('../../../images/thowx.png')}" /> ${rote}%
                </span>
              </div>
            `
          }

          dom += `
            <div style="display: flex; align-items: center; justify-content: space-between;">
              <span style="padding-right: 40px;">金额</span>
              <span>${query.value}亿元</span>
            </div>
            ${style}
          `

          return `
            <div style="font-size:34px; line-height: 70px; color: #fff; border-radius:10px;border:2px solid #0C3EB6; padding: 20px; display: flex;flex-direction: column; background: linear-gradient(0deg, rgba(0, 103, 177, 1), rgba(0, 103, 177, 1));">
              <div style="font-weight:bold;">${query.axisValueLabel}</div>
              ${dom}
            </div>
          `
        }
      },
      grid: {
        top: '30%',
        left: '1%',
        right: '1%',
        bottom: '0%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisPointer: {
          type: 'shadow'
        },
        axisLabel: {
          fontSize: textSize,
          fontWeight: 'bold'
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#5db0ea'
          }
        }
      },
      yAxis: {
        name: '亿元',
        type: 'value',
        nameTextStyle: {
          color: '#5db0ea',
          align: 'left',
          fontWeight: 'bold',
          fontSize: 30
        },
        axisLabel: {
          fontSize: textSize,
          fontWeight: 'bold'
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#5db0ea'
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0, 103, 177, 0.6)'
          }
        }
      },
      series: [
        {
          data: seriesData,
          type: 'bar',
          barWidth: 100,
          label: {
            show: true,
            position: 'top',
            fontSize: textSize,
            color: '#fff'
          },
          itemStyle: {
            borderRadius: [70, 70, 0, 0]
          }
        }
      ]
    }

    this.myChart && this.myChart.setOption(this.option, true)
  }
}
</script>

<style scoped lang="scss">
.profit-overview-wrap {
  position: relative;
  height: 100%;
  .title-box {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
  }
  .content-box {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }
}
</style>


