// 用户信息
import store from '@/store/index'
import { VuexModule, Module, Action, Mutation, getModule } from 'vuex-module-decorators'

export interface UserState {
  token: string;
  userName: string;
  userId: string;
}

@Module({ dynamic: true, store, name: 'user' })
class User extends VuexModule implements UserState {
  public token = ''
  public userId = ''
  public userName = ''
}

export const UserModule = getModule(User)
