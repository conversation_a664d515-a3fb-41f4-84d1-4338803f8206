/** 不动产二级 */

<template>
  <section :loading="loading">
    <!-- 内容区域 -->
    <div class="content"
      slot="content">
      <el-row :span="24"
        :gutter="30">
        <el-col :span="6">
          <div class="content__left">
            <!-- 资产使用状态、出租率情况 -->
            <div class="m-f-t-20 pis-relative cockipt-approach-left-top">
              <!-- 数据改变提示层 -->
              <img v-if="visibleFlicker"
                class="cockipt-bg-flicker"
                src="@/views/main/cockpitcren/images/panel_bg1_cg.png" />

              <CommonModuleWrapper :loading="loadingUseData"
                :activeTabs="activeTabs"
                :borderGbMode="1"
                :componentsName="assetUsageStatusAndOccupancyRate" />
            </div>

            <!-- 资产分类统计 -->
            <div class="pis-relative cockipt-approach-left-bottom">
              <!-- 数据改变提示层 -->
              <img v-if="visibleFlicker"
                class="cockipt-bg-flicker"
                src="@/views/main/cockpitcren/images/panel_bg2_cg.png" />

              <CommonModuleWrapper title="资产分类统计"
                :height="640"
                :loading="loadingClassification"
                :middleIndex="3"
                :borderGbMode="2"
                :activeTabs="activeTabs"
                :cols="assetClassificationCols"
                :source="assetClassificationData"
                bizAppCode="asset"
                detailsPath="/assets/manage"
                componentsName="TableList" />
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="content__middle">
            <!-- 各集团tabs  -->
            <CommonTabs :orgCode="activeTabs"
              module="RealEstate"
              class="cockipt-approach-middel-top"
              @commonTabsHandle="commonTabsHandle" />

            <!-- 综合指标  -->
            <CommonModuleWrapper :hasTitle="false"
              :indexList="comIndicatorData"
              :height="200"
              :fontSize="52"
              :hasBorder="false"
              :activeTabs="activeTabs"
              class="m-f-t-10 cockipt-approach-middel-top"
              wrapper-class="asset-map-module"
              componentsName="TabsList1"
              @itemClick="list1ItemClick" />

            <div class="m-b-20 cockipt-approach-middel-top">
              <AssetsMap :height="720"
                :companyCode="activeTabs" />
            </div>

            <!-- 年度租金收入 -->
            <div class="pis-relative m-f-t-14 cockipt-approach-middel-bottom">
              <!-- 数据改变提示层 -->
              <img v-if="visibleFlicker"
                class="cockipt-bg-flicker"
                src="@/views/main/cockpitcren/images/panel_bg3_cg.png" />

              <CommonModuleWrapper title="年度租金收入"
                :height="360"
                :borderGbMode="3"
                :activeTabs="activeTabs"
                :seriesData="annualRevenueGrowthData"
                :loading="loadingAnnualRevenueGrowth"
                bizAppCode="asset"
                componentsName="RevenueGrowth"
                detailsPath="/assets/contractManagement"
                wrapperClass="annual-revenue-growth-common-wrapper" />
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="content__right">
            <!-- 预警情况 -->
            <div class="pis-relative cockipt-approach-right-top">
              <!-- 数据改变提示层 -->
              <img v-if="visibleFlicker"
                class="cockipt-bg-flicker"
                src="@/views/main/cockpitcren/images/panel_bg2_cg.png" />

              <CommonModuleWrapper title="预警情况"
                :height="300"
                :borderGbMode="2"
                :activeTabs="activeTabs"
                :loading="loadingOperation"
                :seriesData="alertNoticeData"
                bizAppCode="asset"
                detailsPath="/assets/prewarningEvents"
                componentsName="OperationSituation"
                wrapperClass="real-estate-common-wrapper" />
            </div>

            <!-- 逾期情况、合同情况 -->
            <div class="pis-relative cockipt-approach-right-top">
              <!-- 数据改变提示层 -->
              <img v-if="visibleFlicker"
                class="cockipt-bg-flicker"
                src="@/views/main/cockpitcren/images/panel_bg2_cg.png" />

              <CommonModuleWrapper :loading="loadingOverdueContract"
                :isSwitch="false"
                :borderGbMode="2"
                :height="380"
                :activeTabs="activeTabs"
                :componentsName="overdueContractSituation"
                bizAppCode="asset"
                detailsPath="/assets/contractManagement"
                wrapperClass="real-estate-common-wrapper" />
            </div>

            <!-- 浙交汇 -->
            <CommonModuleWrapper title="浙交汇"
              :height="290"
              :borderGbMode="2"
              :middleIndex="1"
              :activeTabs="activeTabs"
              :cols="earlyWarningCols"
              :source="earlyWarningData"
              type="ZJH"
              bizAppCode="asset"
              detailsPath="https://szjg.gzw.jinhua.gov.cn:6443/monitor/#/myiframe/urlPath?name=资产交易&url=https%3A%2F%2Fwww.jhcqpt.com%2FJHPT%2F"
              componentsName="TableList"
              wrapperClass="real-estate-common-wrapper"
              class="cockipt-approach-right-bottom" />
          </div>
        </el-col>
      </el-row>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import CommonModuleWrapper, { ComponentItem } from '@/views/main/cockpitSecondary/components/CommonModuleWrapper.vue'
import { ColItem } from '@/views/main/cockpitSecondary/components/TableList.vue'
import { companyList } from '@/views/main/cockpitSecondary/baseData'
import { Loading } from '@/decorators'
import { bigNumberFormat } from '@/utils'
import {
  immovablesOverview,
  assetStatusRatio,
  assetOccupancyRate,
  assetClassify,
  annualRevenueGrowth,
  contractBill,
  overdueStatus,
  immovablesCollectionRate
} from '@/api/cockpit'
import { getEwmSummaryAst } from '@/api/prewarning'
import CommonTabs from '@/views/main/cockpitSecondary/components/CommonActiveTabs.vue'
import AssetsMap from './AssetsMap.vue'

@Component({
  components: {
    CommonTabs,
    AssetsMap,
    CommonModuleWrapper
  }
})
export default class extends Vue {
  private loading = false
  private loadingZjh = false
  private loadingUseData = false
  private loadingOperation = false
  private loadingClassification = false
  private loadingComIndicators = false
  private loadingOverdueContract = false
  private loadingAnnualRevenueGrowth = false

  private currentYear = new Date().getFullYear() - 1
  private currentMoon = ''
  private currentCompanyIndex = '0'
  private tabsIndex = 0

  private visibleFlicker = true

  // tabs 各个集团数据
  private activeTabs = ''
  private comIndicatorData: any[] = []

  // 资产使用状态数据
  private useData: any[] = []

  // 出租率情况数据
  private occupancyData: any[] = []

  // 资产分类统计数据
  private assetClassificationData: any[] = []
  private assetClassificationCols: ColItem[] = [
    {
      label: '类别名称',
      span: 10,
      prop: 'name'
    },
    {
      label: '数量',
      span: 6,
      prop: 'sum',
      align: 'center'
    },
    {
      label: '建筑面积(m²)',
      span: 8,
      prop: 'coveredArea',
      align: 'right'
    }

    // {
    //   label: '土地面积(m²)',
    //   span: 7,
    //   prop: 'landArea',
    //   align: 'right'
    // }
  ]

  // 年度租金收入数据
  private annualRevenueGrowthData: any[] = []

  // 预警情况数据
  private alertNoticeData: any[] = []

  // 合同情况数据
  private contractData = {}

  // 逾期情况数据
  private situationData: any[] = []

  // 浙交汇数据
  private earlyWarningCols: ColItem[] = [
    {
      label: '项目名称',
      span: 5,
      prop: 'name'
    },
    {
      label: '交易品类',
      span: 5,
      prop: 'category'
    },
    {
      label: '挂牌价格',
      span: 5,
      prop: 'price'
    },
    {
      label: '挂牌时间',
      span: 4,
      prop: 'date'
    },
    {
      label: '点击数量',
      span: 5,
      align: 'right',
      prop: 'clickNum'
    }
  ]
  private earlyWarningData = [
  {
    "name": "金华市自来水有限公司废旧水表一批",
    "category": "资产转让",
    "price": "49.6283万元",
    "date": "2025.4.14",
    "clickNum": "43",
    "link": "https://www.zjpse.com/prjs/real_right/detail/180976.html"
  },
  {
    "name": "金华市资产管理有限公司罚没物资一批",
    "category": "资产转让",
    "price": "5.1865万元",
    "date": "2025.4.9",
    "clickNum": "339",
    "link": "https://www.zjpse.com/prjs/real_right/detail/179674.html"
  },
  {
    "name": "金华市丹溪路1113号申华大厦1幢1-03室、1-04室、4-01室房产",
    "category": "资产转让",
    "price": "3638.00万元",
    "date": "2025.4.3",
    "clickNum": "90",
    "link": "https://www.zjpse.com/prjs/real_right/detail/178767.html"
  },
  {
    "name": "金华市婺城区胜利街570号2幢1楼北侧五年租赁权",
    "category": "资产租赁",
    "price": "15000.00 元/首年",
    "date": "2025.4.10",
    "clickNum": "96",
    "link": "https://www.zjpse.com/prjs/lease/detail/180284.html#"
  },
  {
    "name": "金华市环城西路1300号五间营业房两年租赁权",
    "category": "资产租赁",
    "price": "60000.00 元/首年",
    "date": "2025.4.10",
    "clickNum": "81",
    "link": "https://www.zjpse.com/prjs/lease/detail/180298.html#"
  },
  {
    "name": "金华市轨道交通集团实训中心两年租赁权",
    "category": "资产租赁",
    "price": "105,000.00 元/年",
    "date": "2025.4.9",
    "clickNum": "60",
    "link": "https://www.zjpse.com/prjs/lease/detail/179774.html#"
  },
  {
    "name": "金华市秋滨街道金地路469号腾马产业园A栋第四层东面办公室二两年租赁权",
    "category": "资产租赁",
    "price": "16825.90 元/首年",
    "date": "2025.4.7",
    "clickNum": "28",
    "link": "https://www.zjpse.com/prjs/lease/detail/179487.html#"
  },
  {
    "name": "金华市秋滨街道金地路469号腾马产业园A栋第四层走廊北侧东二、东三、东四办公室(共三间)两年租赁权",
    "category": "资产租赁",
    "price": "11965.20 元/首年",
    "date": "2025.4.7",
    "clickNum": "14",
    "link": "https://www.zjpse.com/prjs/lease/detail/179488.html"
  },
  {
    "name": "金华市婺城区凤山街352号三年租赁权",
    "category": "资产租赁",
    "price": "33500.00 元/年",
    "date": "2025.4.3",
    "clickNum": "43",
    "link": "https://www.zjpse.com/prjs/lease/detail/179046.html#"
  },
  {
    "name": "金华市银桂苑1幢5个单元46套房屋五年租赁权",
    "category": "资产租赁",
    "price": "841200.00 元/首年 ",
    "date": "2025.4.1",
    "clickNum": "51",
    "link": "https://www.zjpse.com/prjs/lease/detail/178549.html#"
  },
  {
    "name": "金华市银桂苑3幢1单元11套房屋五年租赁权",
    "category": "资产租赁",
    "price": "212600.00 元/首年",
    "date": "2025.4.1",
    "clickNum": "19",
    "link": "https://www.zjpse.com/prjs/lease/detail/178550.html#"
  },
  {
    "name": "金华市丹桂苑2幢3单元20套房屋五年租赁权",
    "category": "资产租赁",
    "price": "358500.00 元（首年）",
    "date": "2025.4.1",
    "clickNum": "22",
    "link": "https://www.zjpse.com/prjs/lease/detail/178551.html"
  },
  {
    "name": "金华市婺城区红旗小区2号楼1-201、202室三年租赁权",
    "category": "资产租赁",
    "price": "38800.00 元/年",
    "date": "2025.3.20",
    "clickNum": "39",
    "link": "https://www.zjpse.com/prjs/lease/detail/175122.html#"
  },
  {
    "name": "金华市婺城区马路里78号中501室三年租赁权",
    "category": "资产租赁",
    "price": "7800.00 元/年",
    "date": "2025.3.20",
    "clickNum": "29",
    "link": "https://www.zjpse.com/prjs/lease/detail/175238.html#"
  },
  {
    "name": "金华市婺城区青年路88号（现78号）1层103三年租赁权",
    "category": "资产租赁",
    "price": "2400.00 元/年",
    "date": "2025.3.20",
    "clickNum": "33",
    "link": "https://www.zjpse.com/prjs/lease/detail/175243.html#"
  },
  {
    "name": "金华市婺城区春晖路300号（工商公寓）3幢1-102室三年租赁权",
    "category": "资产租赁",
    "price": "14800.00 元/年",
    "date": "2025.3.20",
    "clickNum": "25",
    "link": "https://www.zjpse.com/prjs/lease/detail/175258.html#"
  },
  {
    "name": "金华市石门农场二队内农田103.3亩一年租赁权",
    "category": "资产租赁",
    "price": "110640.00 元/年",
    "date": "2025.4.11",
    "clickNum": "18",
    "link": "https://www.zjpse.com/prjs/lease/detail/180879.html"
  },
  {
    "name": "金华市石门农场八队内农田及鱼塘五年租赁权",
    "category": "资产租赁",
    "price": "73400.00 元/年",
    "date": "2025.4.11",
    "clickNum": "12",
    "link": "https://www.zjpse.com/prjs/lease/detail/180881.html"
  },
  {
    "name": "金华市石门农场果木队内果园、农田及房屋五年租赁权",
    "category": "资产租赁",
    "price": "48254.00 元/年",
    "date": "2025.4.11",
    "clickNum": "19",
    "link": "https://www.zjpse.com/prjs/lease/detail/180882.html"
  },
  {
    "name": "金华市石门农场果木队内旱地27亩一年租赁权",
    "category": "资产租赁",
    "price": "27540.00 元/年",
    "date": "2025.4.11",
    "clickNum": "14",
    "link": "https://www.zjpse.com/prjs/lease/detail/180883.html"
  }
]

  // tabs：获取各个集团数据
  get getCompanyList() {
    return companyList
  }

  // 万元数据转化成亿元
  get getBigNumberFormat() {
    return (num: string | number) => {
      return bigNumberFormat(+num).value || 0
    }
  }

  // 资产使用状态：
  get useDataSum() {
    let that = this as any
    let index = this.activeTabs
    let useData: any = that.useData
    return useData.reduce((sum: any, currentItem: any) => (sum += +currentItem.value), 0)
  }

  // echarts 年份数据
  get yearList() {
    let year = +this.getMomentTime()
    return [year - 4, year - 3, year - 2, year - 1, year]
  }

  // 资产使用状态和出租状态模块
  get assetUsageStatusAndOccupancyRate() {
    let componentNameList: ComponentItem[] = [
      {
        name: 'ProportionOfRing',
        height: 610,
        title: '使用状态：经营性',
        margin: '0 0 20px 0',
        bizAppCode: 'asset',
        detailsPath: '/assets/manage',
        attrs: {
          seriesData: this.useData,
          text: this.useDataSum,
          ringWidth: 330,
          chartId: 'assetUsageStatus',
          subtext: '总数'
        }
      },
      // {
      //   name: 'CommonLineChart',
      //   height: 350,
      //   title: '出租率情况',
      //   bizAppCode: 'asset',
      //   detailsPath: '/assets/manage',
      //   attrs: {
      //     xData: this.yearList,
      //     seriesData: this.occupancyData,
      //     chartId: 'OccupancyRate'
      //   }
      // }
    ]

    return componentNameList
  }

  // 合同情况和逾期情况模块
  get overdueContractSituation() {
    let componentNameList: ComponentItem[] = [
      {
        name: 'ContractSituation',
        height: 440,
        title: '合同情况',
        bizAppCode: 'asset',
        detailsPath: '/assets/contractManagement',
        attrs: {
          seriesData: this.contractData
        }
      }
      // {
      //   name: 'OverdueSituation',
      //   height: 400,
      //   title: '逾期情况',
      //   bizAppCode: 'asset',
      //   detailsPath: '/assets/manage',
      //   attrs: {
      //     seriesData: this.situationData
      //   }
      // }
    ]

    return componentNameList
  }

  // 数据初始化
  private created() {
    // 如果链接上带有集团code，需要让tabs选中该集团
    let { query} = this.$route
    if(query && query.orgCode) {
      this.activeTabs = (query.orgCode as string) || '0'
    }
    // end
  }

  // 组件初始化
  private mounted() {
    // 时间改变时触发数据更新
    this.$bus.$on('BusYearTimeChange', (data: string) => {
      this.currentYear = +data
      this.modulesUpDate()
    })

    this.$bus.$on('BusMoonTimeChange', (data: string) => {
      this.currentMoon = data
      this.modulesUpDate()
    })
  }

  // 各个集团 tabs 切换触发
  private commonTabsHandle(activeTab: string) {
    this.activeTabs = activeTab
    // 触发该页面其他模块更新数据
    this.modulesUpDate()
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  /*-----在该页面统一请求数据接口，分发给各个模块组件------*/
  // 各模块执行总线程
  private modulesUpDate() {
    this.comIndicators()
    this.assetUsageStatus()
    this.occupancyRate()
    this.assetClassification()
    this.annualRevenueGrowth()
    this.operationStatus()
    this.contractSituation()
    // this.overdueSituation()
    this.flickerHandle()
  }

  // 资产综合指标
  @Loading('loadingComIndicators')
  private async comIndicators() {
    let { data } = await immovablesOverview({
      year: this.currentYear,
      month: this.currentMoon,
      orgCode: this.activeTabs
    })
    const { data: collectRate } = await immovablesCollectionRate({
      year: this.currentYear,
      month: this.currentMoon,
      orgCode: this.activeTabs
    })

    if (!data) return

    this.comIndicatorData = [
      {
        label: '经营性资产总数',
        value: data.manageItemNum,
        percent: data.countRate,
        unit: '处',
        decimals: 0,
        isPercent: true,
        noFormat: true
      },
      {
        label: '建筑面积',
        value: data.bizBuildingArea,
        percent: data.buildingAreaRate,
        unit: '万m²',
        isPercent: true,
        noFormat: true
      },
      {
        label: '土地面积',
        value: data.bizLandArea,
        percent: data.landAreaRate,
        unit: '万m²',
        isPercent: true,
        noFormat: true
      },
      {
        label: '出租率',
        value: data.occupancyRate,
        percent: data.occupancyChainRatio,
        unit: '%',
        isPercent: true,
        noFormat: true
      },
      {
        label: '空置率',
        value: data.vacancyRate,
        percent: data.vacancyChainRatio,
        unit: '%',
        isPercent: true,
        noFormat: true
      },
      {
        label: '租金收缴率',
        value: collectRate.slice(0,-1),
        percent: data.collectionChainRatio,
        unit: '%',
        isPercent: true,
        noFormat: true
      }
    ]
  }

  // 资产使用状态
  @Loading('loadingUseData')
  private async assetUsageStatus() {
    this.flickerHandle()
    let { data } = await assetStatusRatio({
      year: this.currentYear,
      month: this.currentMoon,
      orgCode: this.activeTabs
    })
    if (Array.isArray(data) && data.length) {
      let list: any[] = []
      data.forEach((item) => {
        list.push({
          value: item.count,
          name: item.useStatus
        })
      })
      this.useData = list
    }
  }

  // 出租率情况
  @Loading('loadingUseData')
  private async occupancyRate() {
    let { data } = await assetOccupancyRate({
      orgCode: this.activeTabs
    })

    if (Array.isArray(data) && data.length) {
      let list: any[] = []
      data.forEach((item) => {
        list.push(Number(item.rate.toFixed(2)))
      })

      this.occupancyData = [
        {
          data: list
        }
      ]
    }
  }

  // 资产分类统计
  @Loading('loadingClassification')
  private async assetClassification() {
    let { data } = await assetClassify({
      year: this.currentYear,
      month: this.currentMoon,
      orgCode: this.activeTabs
    })

    if (Array.isArray(data) && data.length) {
      let list: any[] = []
      data.forEach((item) => {
        list.push({
          name: item.typeName,
          sum: item.num,
          coveredArea: item.buildNum,
          landArea: item.landNum
        })
      })

      this.assetClassificationData = list
    }
  }

  // 年度租金收入
  @Loading('loadingAnnualRevenueGrowth')
  private async annualRevenueGrowth() {
    let { data } = await annualRevenueGrowth({
      orgCode: this.activeTabs
    })

    if (Array.isArray(data) && data.length) {
      this.annualRevenueGrowthData = data
    }
  }

  // 预警情况
  @Loading('loadingOperation')
  private async operationStatus() {
    let { data } = await getEwmSummaryAst({
      year: this.currentYear,
      month: this.currentMoon,
      orgCode: this.activeTabs
    })

    this.alertNoticeData = data || []
  }

  // 合同情况
  @Loading('loadingOverdueContract')
  private async contractSituation() {
    let { data } = await contractBill({
      year: this.currentYear,
      month: this.currentMoon,
      orgCode: this.activeTabs
    })

    this.contractData = data || {}
  }

  // 逾期情况
  @Loading('loadingOverdueContract')
  private async overdueSituation() {
    let { data } = await overdueStatus({
      year: this.currentYear,
      month: this.currentMoon,
      // orgCode: this.activeTabs
      companyCode: '0'
    })

    this.situationData = data || []
  }

  // 点击综合指标模块跳转到【资产监管-资产列表】
  private list1ItemClick(index: number, item: any) {
    let path = ''

    switch (item.label) {
      case '空置率':
        if (!this.activeTabs || this.activeTabs == '0') {
          path = '/assets/limitToMoreThanThreeMonths'
        } else {
          path = `/assets/limitToMoreThanThreeMonths?orgCode=${this.activeTabs}`
        }
        break
      case '租金收缴率':
        if (!this.activeTabs || this.activeTabs == '0') {
          path = '/assets/rentArrearsOnemonth'
        } else {
          path = `/assets/rentArrearsOnemonth?orgCode=${this.activeTabs}`
        }
        break
      default:
        if (!this.activeTabs || this.activeTabs == '0') {
          path = '/assets/manage'
        } else {
          path = `/assets/manage?orgCode=${this.activeTabs}`
        }
        break
    }

    this.$bus.$emit('BustoBusiness', {
      code: 'asset',
      path: path
    })
  }

  // 触发背景闪烁效果
  private flickerHandle() {
    this.visibleFlicker = true
    setTimeout(() => {
      this.visibleFlicker = false
    }, 3000)
  }
}
</script>

<style lang="scss">
.content {
  width: 100%;
  overflow: hidden;
}
</style>

<style scoped lang="scss">
.real-estate-common-wrapper {
  padding-bottom: 45px !important;
}

.asset-map-wrapper {
  padding: 0 !important;
  // margin-bottom: 50px !important;
}
.company-list__tabs {
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 60px;
  margin: 14px 0px;
  overflow: hidden;
  padding: 4px;
  border-radius: 4px;
}
.company-list__tabs-item {
  font-size: 32px;
  cursor: pointer;
  width: 200px;
  line-height: 60px;
  text-align: center;
  border: 3px solid rgba(40, 124, 246, 1);
  box-shadow: 0 0 30px 0px inset rgba(40, 124, 246, 0.6);
  border-radius: 6px;
}
.content__left,
.content__right {
  margin-top: -18px;
}
</style>
