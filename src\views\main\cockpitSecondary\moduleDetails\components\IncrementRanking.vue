/** 增长排名 */

<template>
  <div class="increment-ranking">
    <div class="increment-ranking__list"
      ref="list">
      <div v-for="(data, index) in source"
        :key="`data${index}`"
        :class="['increment-ranking__item', currentIndex === index && 'increment-ranking__item--checked']"
        @click="onItemClick(index, data)">
        <div class="shadow"></div>
        <span class="item__no">0{{ index + 1 }}</span>
        <span class="company">{{ data.name }}</span> 本年度{{ data.rate > 0 ? '增加' : '减少' }}
        <span :class="data.rate > 0 ? 'up' : 'down'">
          <img class="icon"
            v-if="+data.rate  > 0"
            src="../../images/thows.png" />
          <img class="icon"
            v-if="+data.rate  < 0"
            src="../../images/thowx.png" />
          <i>{{ data.rate || '--' }}%</i>
        </span>，
        <!-- <span>共计</span>
        <span class="value">{{ data.sum || '-' }} </span>
        <span>处</span>; -->
        <span>{{ indexLabel }}为</span>
        <span class="value">{{ bigNumberFormat(data.value).value}}</span>
        <span> {{ bigNumberFormat(data.value).unit }}</span> 。
      </div>
    </div>

  </div>

</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { capitalProfile } from '@/api/cockpit'
import { bigNumberFormat } from '@/utils'

export interface ColItem {
  label: string
  span: number
  prop: string
  align?: 'right' | 'center' | 'left'
}

@Component
export default class extends Vue {
  @Prop({ default: () => [] }) source!: Record<string, any>[]
  @Prop() private fontSize!: number
  @Prop() private indexLabel!: string
  @Prop() private unitStr!: string

  private currentIndex = 0
  private loading = false
  private timer: any

  // 组件初始化
  private mounted() {
    this.setTimer()
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await capitalProfile()
  }

  private onItemClick(index: number) {
    this.currentIndex = index
  }

  private setTimer() {
    if (this.timer) {
      clearTimeout(this.timer)
    }
    let listDom = this.$refs['list'] as HTMLDivElement
    this.timer = setInterval(() => {
      this.currentIndex++
      if (this.currentIndex === this.source.length) {
        this.currentIndex = 0
      }
    }, 1 * 1000)
  }

  get bigNumberFormat() {
    return (value: any) => {
      return this.unitStr === '%'
        ? {
            value,
            unit: this.unitStr
          }
        : bigNumberFormat(value)
    }
  }

  destroyed() {
    clearInterval(this.timer)
  }
}
</script>

<style scoped lang="scss">
.increment-ranking {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 0 0 0 20px;

  &__item {
    vertical-align: middle;
    position: relative;
    height: 100px;
    background: rgba(5, 16, 29, 0.55);
    border: 1px solid #264071;
    border-left: 8px solid rgba(30, 79, 170, 1);
    font-size: 36px;
    line-height: 100px;
    margin-bottom: 7px;
    opacity: 0.8;
    border-radius: 4px;
    cursor: pointer;
    color: rgba(65, 133, 218, 1);
    display: flex;
    align-items: center;
    white-space: nowrap;

    .shadow {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
    }

    &--checked,
    &:hover {
      background: rgba(5, 16, 29, 0.55);
      .increment-ranking__item::after,
      .increment-ranking__item::before {
        box-shadow: 0px 4px 10px 0 linear-gradient(to right, #16baff, #16baff11);
        background: linear-gradient(to right, #16baff, #16baff11);
      }
    }
    .company {
      color: #fff;
      font-size: 38px;
      line-height: 60px;
      margin: 4px;
    }
    .value {
      color: #fff;
      font-size: 46px;
      line-height: 60px;
      margin: 4px;
      font-weight: normal;
      font-family: 'PangMenZhengDao';
    }
    .up {
      font-size: 42px;
      font-weight: normal;
      color: rgba(255, 72, 30, 1);
      margin: 4px;
      font-family: 'PangMenZhengDao';
      img {
        width: 26px;
      }
    }
    .down {
      font-size: 48px;
      font-weight: bold;
      color: rgba(72, 185, 28, 1);
      margin: 4px;
      font-family: 'PangMenZhengDao';
      img {
        width: 24px;
      }
    }
    &::after {
      content: '';
      position: absolute;
      height: 4px;
      width: 100%;
      left: 0;
      top: 0;
      box-shadow: 0px 4px 10px 0 linear-gradient(to right, #264071, #26407111);
      background: linear-gradient(to right, #264071, #26407111);
    }

    &::before {
      content: '';
      position: absolute;
      height: 4px;
      width: 100%;
      left: 0;
      bottom: 0;
      background: linear-gradient(to right, #264071, #26407111);
      box-shadow: 0px -4px 10px 0 linear-gradient(to right, #264071, #26407111);
    }
    .item__no {
      font-size: 46px;
      font-family: Source Han Sans SC;
      font-weight: 800;
      font-style: italic;
      color: #4c8bff;
      line-height: 48px;
      margin: 0 30px;
    }
  }
}
</style>