<template>
  <section class="echarts-dom-wrap">
    <div v-loading="loading"
      ref="chartDom"
      class="chartDom" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { assetsReceivablesOverYear } from '@/api/assetsv2'
import { Loading } from '@/decorators'
import * as echarts from 'echarts'

let chartDom = null
let myChart: any = null

@Component({})
export default class extends Vue {
  @Prop() private orgCode!: string // 集团code
  @Prop() private year!: string // 年份

  private loading = false
  private legendData: string[] = []
  private xAxisData: string[] = []
  private seriesData: any[] = []
  private interfaceData = []

  // 监听搜索条件改变
  @Watch('orgCode', { deep: true })
  @Watch('year', { deep: true })
  private watchFormData() {
    this.initData()
  }

  // 组件初始化
  private mounted() {
    this.$nextTick(() => {
      chartDom = this.$refs.chartDom
      myChart = echarts.init(chartDom as HTMLElement)

      this.initData()
    })
  }

  // 获取数据
  @Loading('loading')
  private async initData() {
    this.initEcharts()
  }

  // 初始化echarts组件
  private initEcharts() {
    let xlist: any = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    let list = [
      {
        barWidth: '9%',
        name: '城投集团',
        type: 'bar',
        data: xlist.map((res: any) => {
          return Math.ceil(Math.random() * 100)
        })
      },
      {
        barWidth: '9%',

        name: '国资运营',
        type: 'bar',
        data: xlist.map((res: any) => {
          return Math.ceil(Math.random() * 100)
        })
      },
      {
        barWidth: '9%',

        name: '交投集团',
        type: 'bar',
        data: xlist.map((res: any) => {
          return Math.ceil(Math.random() * 100)
        })
      },
      {
        barWidth: '9%',

        name: '金投集团',
        type: 'bar',
        data: xlist.map((res: any) => {
          return Math.ceil(Math.random() * 100)
        })
      },
      {
        barWidth: '9%',

        name: '轨道集团',
        type: 'bar',
        data: xlist.map((res: any) => {
          return Math.ceil(Math.random() * 100)
        })
      }
    ]
    let legend = list.map((res) => {
      return res.name
    })
    legend.push('挂牌总额')
    let option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          // 坐标轴指示器，坐标轴触发有效
          type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
        }
      },
      legend: {
        legend
      },
      barCategoryGap: '50%',
      grid: {
        left: '3%',
        right: '3%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          data: xlist
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: '房产租赁挂牌数（笔）',
          nameTextStyle: {
            align: 'left',
            lineHeight: 0,
            nameGap: 10
          }
        },
        {
          name: '挂牌总额(万)',
          type: 'value',
          min: 0,
          max: 25,
          nameRotate: 0,
          nameGap: 10
        }
      ],
      series: [
        ...list,
        {
          color: '#45b787',
          name: '挂牌总额',
          type: 'line',
          symbol: 'circle',
          data: xlist.map((res: any) => {
            return Math.ceil(Math.random() * 100)
          }),
          label: {
            show: true,
            position: 'top'
          }
        }
      ]
    }

    myChart && myChart.setOption && myChart.setOption(option)
  }
}
</script>

<style scoped lang="scss">
.chartDom {
  width: 100%;
  height: 100%;
}
</style>
