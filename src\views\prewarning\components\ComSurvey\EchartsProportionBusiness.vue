<template>
  <section class="echarts-dom-wrap">
    <div v-loading="loading"
      ref="chartDom"
      class="chartDom"
      :class="{'hide': !interfaceData.length}" />

    <el-empty description="暂无数据"
      class="empty-none-data"
      :class="{'none': interfaceData.length}" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { getEventStatistics } from '@/api/prewarning'
import { Loading } from '@/decorators'
import * as echarts from 'echarts'

let chartDom = null
let myChart: any = null

@Component({})
export default class extends Vue {
  @Prop() private year!: string // 年份
  @Prop() private isAssets?: false // 判断是否是资产中的“预警汇总”模块

  private loading = false
  private yAxisData: string[] = []
  private seriesData: any[] = []
  private interfaceData: any[] = []

  // 监听搜索条件改变
  @Watch('year', { deep: true })
  private watchFormData() {
    this.initData()
  }

  // 组件初始化
  private mounted() {
    this.$nextTick(() => {
      chartDom = this.$refs.chartDom
      myChart = echarts.init(chartDom as HTMLElement)

      this.initData()
    })
  }

  // 获取数据
  @Loading('loading')
  private async initData() {
    // let { data } = await getEventStatistics({
    //   year: this.year
    // })

    let data: any[] = [
      { value: 10, name: '资产' },
      { value: 30, name: '负债' },
      { value: 20, name: '欠租' },
      { value: 15, name: '合同' },
      { value: 25, name: '收款' }
    ]

    this.interfaceData = data || []

    // 组装数据
    this.seriesData = data
    this.initEcharts()
  }

  // 初始化echarts组件
  private initEcharts() {
    let seriesData = this.seriesData

    let option = {
      legend: {
        show: false
      },
      grid: {
        top: '0%',
        left: '1%',
        right: '0%',
        bottom: '0%',
        containLabel: true
      },
      series: [
        {
          type: 'pie',
          radius: '50%',
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          data: seriesData
        },
        {
          type: 'pie',
          radius: '50%',
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: true,
            position: 'inside',
            formatter: `{d}%`,
            color: '#333',
            fontSize: 12
          },
          data: seriesData
        }
      ]
    }

    myChart && myChart.setOption && myChart.setOption(option)
  }
}
</script>

<style scoped lang="scss">
.chartDom {
  width: 100%;
  height: 140%;
  margin-top: -50px;
}
</style>
