/** 项目执行概况 */
<template>

  <div class="project-implementation-overview">
    <el-row class="project-implementation-overview__header"
      :span="24">
      <el-col :span="2">项目编码</el-col>
      <el-col :span="4">项目名称</el-col>
      <el-col :span="2">项目分类</el-col>
      <el-col :span="2">所属单位</el-col>
      <el-col :span="2">建设性质</el-col>
      <el-col :span="2">负责人</el-col>
      <el-col :span="2">立项时间</el-col>
      <el-col :span="2">计划金额/万元</el-col>
      <el-col :span="2">实际金额/万元</el-col>
      <el-col :span="2">计划执行率</el-col>
      <el-col :span="2">预算超支</el-col>
    </el-row>
    <div class="project-implementation-overview__list"
      ref="list">
      <el-row v-for="(item, index) in dataList"
        :key="index"
        :span="24"
        :class="['project-implementation-overview__item', currentIndex === index && 'project-implementation-overview__item--checked']"
        @click="onItemClick(index)">
        <el-col :span="2">{{ item.code }}</el-col>
        <el-col :span="4">{{ item.name }}</el-col>
        <el-col :span="2">{{ item.typeStr }}</el-col>
        <el-col :span="2">{{ item.unitName }}</el-col>
        <el-col :span="2">{{ item.constructionTypeStr }}</el-col>
        <el-col :span="2">{{ item.principal }}</el-col>
        <el-col :span="2">{{ item.createDate }}</el-col>
        <el-col :span="2"
          class="text-right">{{ item.predictSum }}</el-col>
        <el-col :span="2"
          class="text-right">{{ item.realitySum }}</el-col>
        <el-col :span="2">{{ item.rate || '-' }}%</el-col>
        <el-col :span="2">{{ item.excessBudget }}</el-col>
      </el-row>
    </div>

  </div>

</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { ProjectImplementationOverviewData } from '@/views/main/cockpitSecondary/baseData'
import { capitalProfile } from '@/api/cockpit'
import { deepClone } from '@/utils'

@Component
export default class extends Vue {
  private currentIndex = 0
  private loading = false
  private timer: any
  private year = ''

  private dataList = deepClone(ProjectImplementationOverviewData)

  // 组件初始化
  private mounted() {
    this.setTimer()
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await capitalProfile()
  }

  private onItemClick(index: number) {
    this.currentIndex = index
  }

  private setTimer() {
    if (this.timer) {
      clearTimeout(this.timer)
    }
    let listDom = this.$refs['list'] as HTMLDivElement
    this.timer = setInterval(() => {
      this.currentIndex++
      if (this.currentIndex > 5 && this.currentIndex < this.dataList.length) {
        listDom.scrollTo(0, (this.currentIndex - 5) * 48)
      } else if (this.currentIndex === this.dataList.length) {
        this.currentIndex = 0
        listDom.scrollTo(0, 0)
      }
    }, 1 * 1000)
  }

  // 年份切换
  private yearHandler(year: string) {
    this.year = year
    // this.initData()
  }

  destroyed() {
    clearInterval(this.timer)
  }
}
</script>

<style scoped lang="scss">
.project-implementation-overview {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .el-col {
    font-size: 28px;
    text-align: center;
    font-weight: bold;
    height: 60px;
    line-height: 60px;
  }
  &__header {
    .el-col {
      color: rgba(74, 151, 248, 1);
      font-size: 18px;
    }
  }
  &__list {
    height: calc(100% - 48px);
    overflow-x: hidden;
    overflow-y: auto;
    scroll-behavior: smooth;
    &::-webkit-scrollbar {
      display: none;
    }
  }

  &__item {
    transition: all 0.5s;
    will-change: background;
    opacity: 0.7;
    cursor: pointer;
    .el-col {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .text-right {
      text-align: right !important;
    }
    &:nth-child(odd) {
      background: #1e5eff34;
      border-radius: 4px;
    }
    &--checked,
    &:hover {
      .el-col {
        height: 72px;
        line-height: 72px;
      }
      background: #1e5eff88 !important;
      opacity: 1;
    }
  }
}
</style>