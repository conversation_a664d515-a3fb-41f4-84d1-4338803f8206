function getnum(len:number){
    let chars = '0123456789';
    let maxPos = chars.length;
    let str = '';
    for (let i = 0; i < len; i++) {
      str += chars.charAt(Math.floor(Math.random() * maxPos));
    }
    return str;

}

function randomString(len:number){
    //默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1
    let chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678';
    let tempLen = chars.length, tempStr='';
    for(let i=0; i<len; ++i){
        tempStr += chars.charAt(Math.floor(Math.random() * tempLen ));
    }
    return tempStr;
}

export interface DebissuanceType{
    id:number,
    state:number,
    documentNo:string,//单据编号
    decisionMakingBody:string//决策机构
    publisher:string//发行方
    referenceNo:string//文号
    issuer:string//签发人
    year:number//年度
    issuanceBondsReason:string//发债原因
    issuancePurpose:string//发行目的
    issuanceCurrency:number//发行币种"发行币种,1:人民币，2：美元，3：其他")
    filler:string //填报人
    contactPhone:string //联系电话
    creditEnhancementMeasures:string //增信措施
    restitutionMeasures:string //归还措施
    otherSupplements:string //其他补充
    bondsType:string //发券类型
    BigDecimal:string //发券金额
    mainUses:string //主要用途
    validityStartDate:string //有效开始日期
    expiryDate:string//截止日期
    issuanceRate:string //发行利率
    issuanceTimeNumber:number //发行次数
    issuanceForm:number //发行形式
    issuanceTarget:number //发行对象
    otherIssuanceTarget: string//发行对象内容
    remark:string//备注
    attachment1FileIds:string//附件-债券发行方案
    attachment2FileIds:string//附件-董事会决议文件
    attachment3FileIds:string//附件-发债请示文件
}
// 企业发债审批
export const DataDebissuance:DebissuanceType[]=[
    {
        id:1,
        state:3,
        documentNo:"QYFZSP20220500003",//单据编号
        decisionMakingBody:"金华市风升集团",//决策机构
        publisher:"金华市风升集团",//发行方
        referenceNo:"FZ20205000001",//文号
        issuer:"王益",//签发人
        year:1,//年度
        issuanceBondsReason:"筹建新项目",//发债原因
        issuancePurpose:"融资",//发行目的
        issuanceCurrency:1,//发行币种"发行币种,1:人民币，2：美元，3：其他")
        filler:"张三", //填报人
        contactPhone:"15188888888", //联系电话
        creditEnhancementMeasures:"财务监管", //增信措施
        restitutionMeasures:"财务监管", //归还措施
        otherSupplements:"财务监管", //其他补充
        bondsType:"财务监管", //发券类型
        BigDecimal:"财务监管", //发券金额
        mainUses:"财务监管", //主要用途
        validityStartDate:"2022-06-28", //有效开始日期
        expiryDate:"2022-06-28", //有效开始日期
        issuanceRate:"1.38%", //发行利率
        issuanceTimeNumber:1, //发行次数
        issuanceForm:1, //发行形式
        issuanceTarget:1, //发行对象
        otherIssuanceTarget: "财务监管",//发行对象内容
        remark:"财务监管",//备注
        attachment1FileIds:"财务监管",//附件-债券发行方案
        attachment2FileIds:"财务监管",//附件-董事会决议文件
        attachment3FileIds:"财务监管",//附件-发债请示文件
    },
    {
        id:1,
        state:2,
        documentNo:"QYFZSP20220500003",//单据编号
        decisionMakingBody:"金华市风升集团",//决策机构
        publisher:"金华市风升集团",//发行方
        referenceNo:"FZ20205000001",//文号
        issuer:"王益",//签发人
        year:1,//年度
        issuanceBondsReason:"筹建新项目",//发债原因
        issuancePurpose:"融资",//发行目的
        issuanceCurrency:2,//发行币种"发行币种,1:人民币，2：美元，3：其他")
        filler:"张三", //填报人
        contactPhone:"15188888888", //联系电话
        creditEnhancementMeasures:"财务监管", //增信措施
        restitutionMeasures:"财务监管", //归还措施
        otherSupplements:"财务监管", //其他补充
        bondsType:"财务监管", //发券类型
        BigDecimal:"财务监管", //发券金额
        mainUses:"财务监管", //主要用途
        validityStartDate:"财务监管", //有效开始日期
        expiryDate:"2022-06-28", //有效开始日期
        issuanceRate:"1.38%", //发行利率
        issuanceTimeNumber:1, //发行次数
        issuanceForm:1, //发行形式
        issuanceTarget:1, //发行对象
        otherIssuanceTarget: "财务监管",//发行对象内容
        remark:"财务监管",//备注
        attachment1FileIds:"财务监管",//附件-债券发行方案
        attachment2FileIds:"财务监管",//附件-董事会决议文件
        attachment3FileIds:"财务监管",//附件-发债请示文件
    },
    {
        id:1,
        state:3,
        documentNo:"QYFZSP20220500003",//单据编号
        decisionMakingBody:"金华市风升集团",//决策机构
        publisher:"金华市风升集团",//发行方
        referenceNo:"FZ20205000001",//文号
        issuer:"王益",//签发人
        year:1,//年度
        issuanceBondsReason:"筹建新项目",//发债原因
        issuancePurpose:"融资",//发行目的
        issuanceCurrency:1,//发行币种"发行币种,1:人民币，2：美元，3：其他")
        filler:"张三", //填报人
        contactPhone:"15188888888", //联系电话
        creditEnhancementMeasures:"财务监管", //增信措施
        restitutionMeasures:"财务监管", //归还措施
        otherSupplements:"财务监管", //其他补充
        bondsType:"财务监管", //发券类型
        BigDecimal:"财务监管", //发券金额
        mainUses:"财务监管", //主要用途
        validityStartDate:"财务监管", //有效开始日期
        expiryDate:"2022-06-28", //有效开始日期
        issuanceRate:"1.38%", //发行利率
        issuanceTimeNumber:1, //发行次数
        issuanceForm:1, //发行形式
        issuanceTarget:1, //发行对象
        otherIssuanceTarget: "财务监管",//发行对象内容
        remark:"财务监管",//备注
        attachment1FileIds:"财务监管",//附件-债券发行方案
        attachment2FileIds:"财务监管",//附件-董事会决议文件
        attachment3FileIds:"财务监管",//附件-发债请示文件
    },
    {
        id:1,
        state:1,
        documentNo:"QYFZSP20220500001",//单据编号
        decisionMakingBody:"金华市风升集团",//决策机构
        publisher:"金华市风升集团",//发行方
        referenceNo:"FZ20205000001",//文号
        issuer:"王益",//签发人
        year:1,//年度
        issuanceBondsReason:"筹建新项目",//发债原因
        issuancePurpose:"融资",//发行目的
        issuanceCurrency:2,//发行币种"发行币种,1:人民币，2：美元，3：其他")
        filler:"张三", //填报人
        contactPhone:"15188888888", //联系电话
        creditEnhancementMeasures:"财务监管", //增信措施
        restitutionMeasures:"财务监管", //归还措施
        otherSupplements:"财务监管", //其他补充
        bondsType:"财务监管", //发券类型
        BigDecimal:"财务监管", //发券金额
        mainUses:"财务监管", //主要用途
        validityStartDate:"财务监管", //有效开始日期
        expiryDate:"2022-06-28", //有效开始日期
        issuanceRate:"1.38%", //发行利率
        issuanceTimeNumber:1, //发行次数
        issuanceForm:1, //发行形式
        issuanceTarget:1, //发行对象
        otherIssuanceTarget: "财务监管",//发行对象内容
        remark:"财务监管",//备注
        attachment1FileIds:"财务监管",//附件-债券发行方案
        attachment2FileIds:"财务监管",//附件-董事会决议文件
        attachment3FileIds:"财务监管",//附件-发债请示文件
    },

]

//   资产减值
interface AssetsWriteoffSaveType{
    documentNo:string //单据编号
    recordNo:string//备案编号
    fillName:string//填报名称
    enterpriseNature:string//资产核销企业性质
    decisionMakingBody:string//经济行为决策机构
    amount:number//核销不良资产金额
    LocalDate:string//批准文件日期"yyyy-MM-dd"
    year:string//年度
    assetCurrency:number//发行币种 1:人民币，2：美元，3：其他]
    currency:string//发行币种
    currencyUnit:string//货币单位 1:元，2:万元，3:亿]
    content:string//核销不良资产内容
    remark?:string//备注
    // file文件
    attachment1FileIds:string //董事会决议文件
    attachment2FileIds:string //申请备案的报告
    attachment3FileIds:string //相关证据意见书等
}
export const AssetList=[
    {
    documentNo:"ZCJZZBCWHXBA20220"+getnum(3), //单据编号
    recordNo:"ZCJZZBCWHXBA20220"+getnum(3),//备案编号
    fillName:"",//填报单位
    enterpriseNature:"",//资产核销企业性质
    decisionMakingBody:"",//经济行为决策机构
    amount:getnum(5),//核销不良资产金额
    LocalDate:"",//批准文件日期"yyyy-MM-dd"
    year:202+getnum(1),//年度
    assetCurrency:"人民币",//发行币种 1:人民币，2：美元，3：其他]
    currency:"",//发行币种
    currencyUnit:"",//货币单位 1:元，2:万元，3:亿]
    content:"",//核销不良资产内容
    remark:"",//备注
    // file文件
    attachment1FileIds:"", //董事会决议文件
    attachment2FileIds:"", //申请备案的报告
    attachment3FileIds:"", //相关证据意见书等
} ,
    {
    documentNo:"ZCJZZBCWHXBA20220"+getnum(3), //单据编号
    recordNo:"ZCJZZBCWHXBA20220"+getnum(3),//备案编号
    fillName:"",//填报名称
    enterpriseNature:"",//资产核销企业性质
    decisionMakingBody:"",//经济行为决策机构
    amount:getnum(5),//核销不良资产金额
    LocalDate:"",//批准文件日期"yyyy-MM-dd"
    year:202+getnum(1),//年度
    assetCurrency:"人民币",//发行币种 1:人民币，2：美元，3：其他]
    currency:"",//发行币种
    currencyUnit:"",//货币单位 1:元，2:万元，3:亿]
    content:"",//核销不良资产内容
    remark:"",//备注
    // file文件
    attachment1FileIds:"", //董事会决议文件
    attachment2FileIds:"", //申请备案的报告
    attachment3FileIds:"", //相关证据意见书等
} ,
    {
    documentNo:"ZCJZZBCWHXBA20220"+getnum(3), //单据编号
    recordNo:"ZCJZZBCWHXBA20220"+getnum(3),//备案编号
    fillName:"",//填报名称
    enterpriseNature:"",//资产核销企业性质
    decisionMakingBody:"",//经济行为决策机构
    amount:getnum(5),//核销不良资产金额
    LocalDate:"",//批准文件日期"yyyy-MM-dd"
    year:202+getnum(1),//年度
    assetCurrency:"人民币",//发行币种 1:人民币，2：美元，3：其他]
    currency:"",//发行币种
    currencyUnit:"",//货币单位 1:元，2:万元，3:亿]
    content:"",//核销不良资产内容
    remark:"",//备注
    // file文件
    attachment1FileIds:"", //董事会决议文件
    attachment2FileIds:"", //申请备案的报告
    attachment3FileIds:"", //相关证据意见书等
} ,
    {
    documentNo:"ZCJZZBCWHXBA20220"+getnum(3), //单据编号
    recordNo:"ZCJZZBCWHXBA20220"+getnum(3),//备案编号
    fillName:"",//填报名称
    enterpriseNature:"",//资产核销企业性质
    decisionMakingBody:"",//经济行为决策机构
    amount:getnum(5),//核销不良资产金额
    LocalDate:"2022-06-28",//批准文件日期"yyyy-MM-dd"
    year:202+getnum(1),//年度
    assetCurrency:"人民币",//发行币种 1:人民币，2：美元，3：其他]
    currency:"",//发行币种
    currencyUnit:"",//货币单位 1:元，2:万元，3:亿]
    content:"",//核销不良资产内容
    remark:"",//备注
    // file文件
    attachment1FileIds:"", //董事会决议文件
    attachment2FileIds:"", //申请备案的报告
    attachment3FileIds:"", //相关证据意见书等
} ,
]

// // 企业对外担保交易
// export interface ExternalGuaranteeType{

// }