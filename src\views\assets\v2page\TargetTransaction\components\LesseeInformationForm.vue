// 承租方资格条件
<template>
  <section class="lessee-qualifications">
    <el-form ref="LesseeQualificationsForm"
      :rules="lesseeQualificationsFormRules"
      :model="lesseeQualificationsForm"
      label-width="100px"
      class="lessee_form">
      <!-- 资格条件信息 -->
      <el-descriptions class="margin-top"
        title
        :column="24"
        :labelStyle="{}"
        border>
        <el-descriptions-item label="遴选方式"
          :span="8">
          <el-form-item label="遴选方式"
            prop="selectType">
            <el-select v-model="lesseeQualificationsForm.selectType"
              placeholder>
              <el-option v-for="(item, index) in getDictData('subjectSelectType')"
                :value="item.value"
                :label="item.label"
                :key="index"></el-option>
            </el-select>
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item label="*"
          :span="8">
          <el-form-item label="征集到承租方后"
            prop="intentionType">
            <el-select v-model="lesseeQualificationsForm.intentionType"
              placeholder>
              <el-option v-for="(item, index) in getDictData('subjectIntentionType')"
                :value="item.value"
                :label="item.label"
                :key="index"></el-option>
            </el-select>
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item label
          :span="24">
          <el-form-item label="是否会同出租方审查"
            prop="isReviewedWithLessor">
            <el-select v-model="lesseeQualificationsForm.isReviewedWithLessor"
              placeholder>
              <el-option v-for="(item, index) in isNo"
                :value="item.value"
                :label="item.label"
                :key="index"></el-option>
            </el-select>
          </el-form-item>
        </el-descriptions-item>
        <!-- <el-descriptions-item label :span="8">
          <el-form-item label="遴选方式" prop="tenantUnsolicited">
            <el-select v-model="lesseeQualificationsForm.tenantUnsolicited" placeholder>
              <el-option
                v-for="(item,index) in getDictData('tenant_un_solicited')"
                :value="item.value"
                :label="item.label"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-descriptions-item>-->

        <el-descriptions-item v-if="lesseeQualificationsForm.selectType == 5"
          label
          :span="8">
          <el-form-item label="价格显示单位"
            prop="tenantUnsolicited">
            <el-select v-model="lesseeQualificationsForm.tenantUnsolicited"
              placeholder>
              <el-option v-for="(item, index) in getDictData('subjectDynamicPriceUnit')"
                :value="item.value"
                :label="item.label"
                :key="index"></el-option>
            </el-select>
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item v-if="lesseeQualificationsForm.selectType == 5"
          label
          :span="8">
          <el-form-item label="起拍价"
            prop="startingPrice">
            <InputNumber v-model="lesseeQualificationsForm.startingPrice"
              clearabletype="decimal"
              placeholder="请输入">
              <template slot="append">万元</template>
            </InputNumber>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item v-if="lesseeQualificationsForm.selectType == 5"
          label
          :span="8">
          <el-form-item label="保留价"
            prop="reversePrice">
            <InputNumber v-model="lesseeQualificationsForm.reversePrice"
              clearabletype="decimal"
              placeholder="请输入">
              <template slot="append">万元</template>
            </InputNumber>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item v-if="lesseeQualificationsForm.selectType == 5"
          label
          :span="8">
          <el-form-item label="自由报价开始时间"
            prop="freeQuoteStartTime">
            <el-time-picker v-model="lesseeQualificationsForm.freeQuoteStartTime"
              placeholder="选择时间日期"
              value-format="HH:mm"
              format="HH:mm"></el-time-picker>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item v-if="lesseeQualificationsForm.selectType == 5"
          label
          :span="8">
          <el-form-item label="自由报价期（工作日）"
            prop="freeQuoteDay">
            <InputNumber v-model="lesseeQualificationsForm.freeQuoteDay"
              clearabletype="decimal"
              placeholder="请输入"></InputNumber>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item v-if="lesseeQualificationsForm.selectType == 5"
          label
          :span="8">
          <el-form-item label="限时报价开始时间"
            prop="limitQuoteStartTime">
            <el-time-picker v-model="lesseeQualificationsForm.limitQuoteStartTime"
              type="time"
              placeholder="选择时间日期"
              value-format="HH:mm"
              format="HH:mm"></el-time-picker>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item v-if="lesseeQualificationsForm.selectType == 5"
          label
          :span="8">
          <el-form-item label="限时报价周期（秒）"
            prop="limitQuoteSecond">
            <InputNumber v-model="lesseeQualificationsForm.limitQuoteSecond"
              clearabletype="decimal"
              placeholder="请输入"></InputNumber>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item v-if="lesseeQualificationsForm.selectType == 5"
          label
          :span="8">
          <el-form-item label="最终报价考虑期（秒）"
            prop="considerQuoteSecond">
            <InputNumber v-model="lesseeQualificationsForm.considerQuoteSecond"
              clearabletype="decimal"
              placeholder="请输入"></InputNumber>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item v-if="lesseeQualificationsForm.selectType == 5"
          label
          :span="8">
          <el-form-item label="行权期考虑时长（秒）"
            prop="vestingSecond">
            <InputNumber v-model="lesseeQualificationsForm.vestingSecond"
              clearabletype="decimal"
              placeholder="请输入"></InputNumber>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item v-if="lesseeQualificationsForm.selectType == 5"
          label
          :span="8">
          <el-form-item label="加价幅度（元）"
            prop="markupRange">
            <InputNumber v-model="lesseeQualificationsForm.markupRange"
              clearabletype="decimal"
              placeholder="请输入"></InputNumber>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item v-if="lesseeQualificationsForm.selectType == 5"
          label
          :span="8">
          <el-form-item label="是否允许连续报价"
            prop="contQuote">
            <el-select v-model="lesseeQualificationsForm.contQuote"
              placeholder>
              <el-option v-for="(item, index) in isNo"
                :value="item.value"
                :label="item.label"
                :key="index"></el-option>
            </el-select>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item v-if="lesseeQualificationsForm.selectType == 5"
          label
          :span="8">
          <el-form-item label="是否允许起始价报价"
            prop="initialQuote">
            <el-select v-model="lesseeQualificationsForm.initialQuote"
              placeholder>
              <el-option v-for="(item, index) in isNo"
                :value="item.value"
                :label="item.label"
                :key="index"></el-option>
            </el-select>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item v-if="lesseeQualificationsForm.selectType == 5"
          label
          :span="8">
          <el-form-item label="报价联系人"
            prop="dynamicContactName">
            <el-input v-model="lesseeQualificationsForm.dynamicContactName"
              placeholder="请输入"></el-input>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item v-if="lesseeQualificationsForm.selectType == 5"
          label
          :span="8">
          <el-form-item label="报价联系人电话"
            prop="dynamicContactPhone">
            <input-number v-model="lesseeQualificationsForm.dynamicContactPhone"
              clearabletype="decimal"
              placeholder="请输入"></input-number>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item v-if="lesseeQualificationsForm.selectType == 5"
          label
          :span="8">
          <el-form-item label="竞价场次名称"
            prop="competeSessionName">
            <el-input v-model="lesseeQualificationsForm.competeSessionName"
              clearabletype="decimal"
              placeholder="请输入"></el-input>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item v-if="lesseeQualificationsForm.selectType == 5"
          label
          :span="24">
          <AccessoryList v-model="lesseeQualificationsForm.fileList"
            dict="asset_lessee_attach"
            mode="upload"
            title="竞价图片"></AccessoryList>
        </el-descriptions-item>

        <el-descriptions-item label
          :span="8">
          <el-form-item label="信息公告期(工作日)"
            prop="announcementDay">
            <InputNumber v-model="lesseeQualificationsForm.announcementDay"
              clearable
              type="decimalZero"
              placeholder="请输入">
              <template slot="append">个工作日</template>
            </InputNumber>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="约定工作日"
          :span="8">
          <el-form-item label="约定工作日"
            prop="agreedWorkingDay">
            <InputNumber v-model="lesseeQualificationsForm.agreedWorkingDay"
              clearable
              type="decimal"
              placeholder="请输入">
              <template slot="append">个工作日</template>
            </InputNumber>
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item label="延期周期"
          :span="8">
          <el-form-item label="延期周期"
            prop="delayPeriod">
            <InputNumber v-model="lesseeQualificationsForm.delayPeriod"
              clearable
              type="decimal"
              placeholder="请输入">
              <template slot="append">个工作日</template>
            </InputNumber>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="未征集到承租方后"
          :span="8">
          <el-form-item label="未征集到承租方后"
            prop="unIntentionType">
            <el-select v-model="lesseeQualificationsForm.unIntentionType"
              placeholder="">
              <el-option v-for="(item, index) in getDictData('subjectUnIntentionType')"
                :value="item.value"
                :label="item.label"
                :key="index"></el-option>
            </el-select>
            <!-- <el-radio-group v-model="lesseeQualificationsForm.unIntentionType">
              <el-radio :key="index" :label="item.value" v-for="(item, index) in getDictData('subjectUnIntentionType')">
                {{ item.label }}
              </el-radio>
            </el-radio-group> -->
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="未征集到承租方后"
          :span="8"></el-descriptions-item>
        <el-descriptions-item label="未征集到承租方后"
          :span="8"></el-descriptions-item>
        <el-descriptions-item label="保证金处理条款"
          :span="24">
          <el-form-item label="保证金处理条款"
            prop="depositClause">
            <el-input v-model="lesseeQualificationsForm.depositClause"
              type="textarea"
              :rows="10"
              maxlength="2000"
              show-word-limit
              placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="交易条件"
          :span="24">
          <el-form-item label="交易条件"
            prop="tradeCond">
            <el-input v-model="lesseeQualificationsForm.tradeCond"
              type="textarea"
              :rows="10"
              maxlength="2000"
              show-word-limit
              placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="承租方资格条件"
          :span="24">
          <el-form-item label="承租方资格条件"
            prop="leaseCond">
            <el-input v-model="lesseeQualificationsForm.leaseCond"
              type="textarea"
              :rows="10"
              maxlength="2000"
              show-word-limit
              placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="公告期限及延牌规则"
          :span="24">
          <el-form-item label="公告期限及延牌规则"
            prop="rules">
            <el-input v-model="lesseeQualificationsForm.rules"
              type="textarea"
              :rows="10"
              maxlength="2000"
              show-word-limit
              placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item label="其他披露事项"
          :span="24">
          <el-form-item label="其他披露事项"
            prop="otherDisclosure">
            <el-input v-model="lesseeQualificationsForm.otherDisclosure"
              type="textarea"
              :rows="10"
              placeholder="请输入"
              maxlength="2000"
              show-word-limit />
          </el-form-item>
        </el-descriptions-item>
        <!--
        <el-descriptions-item label="出租方指定账号" contentClassName="form-group" :span="24">
          <el-form-item label="户名" label-width="160px" prop="accountName">
            <el-input v-model="lesseeQualificationsForm.accountName" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="开户行" label-width="160px" prop="depositBank">
            <el-input v-model="lesseeQualificationsForm.depositBank" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="账户" label-width="160px" prop="bankAccount">
            <el-input v-model="lesseeQualificationsForm.bankAccount" placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item label="发票信息" contentClassName="form-group" :span="12">
          <el-form-item label="发票类型" label-width="160px" prop="invoiceType">
            <el-radio-group v-model="lesseeQualificationsForm.invoiceType">
              <el-radio
                :key="item.value"
                :label="item.value"
                v-for="item in getDictData('invoice_type')"
              >{{item.label}}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="电子发票接受手机号" label-width="160px" prop="receivePhone">
            <el-input v-model="lesseeQualificationsForm.receivePhone" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="电子邮件" label-width="160px" prop="receiveEmail">
            <el-input v-model="lesseeQualificationsForm.receiveEmail" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="开票备注" label-width="160px" prop="invoicingRemark">
            <el-input v-model="lesseeQualificationsForm.invoicingRemark" placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>-->
      </el-descriptions>
    </el-form>

    <!-- 附件列表 -->
    <AccessoryList v-model="lesseeQualificationsForm.fileList"
      dict="asset_lessee_attach"
      mode="upload"></AccessoryList>
  </section>
</template>

<script lang="ts">
import { decisionTypeList, economicNatureList } from '@/views/assets/filterOptions'
import { ElForm } from 'element-ui/types/form'
import { Component, Model, Vue, Prop, Watch } from 'vue-property-decorator'
import AccessoryList, { Accessory } from '@/views/assets/components/astFileList/index.vue'
import InputNumber from '@/components/FormComment/inputNumber.vue'
import { BusinessModule } from '@/store/modules/businessDict'

export interface LesseeQualifications {
  announcementDay: string | number //	信息公告期(工作日)
  attachmentFileDTOList: any[] //	附件-委托出租服务协议
  attachmentFile2: string[] //		附件-租赁合同样稿
  attachmentFile3: string[] //		附件-浙交汇意向方报名所需文件
  attachmentFile4: string[] //	 附件-其他文件
  isReviewedWithLessor: number | string //	是否会同出租方审查，1：是，0：否
  leaseCond: string //	承租方资格条件
  rules: string //	公告期限及延牌规则
  depositClause: string //	保证金处理条款
  otherDisclosure: string //	其他披露事项
  otherTradingMethod: number | string //	其它交易方式
  intentionType: number | string //征集到承租方后（1：到期摘牌，2：即时成交）
  tenantUnsolicited: number | string //未征集到承租方后（1：信息发布终结，2：按约定工作日为周期延期延长信息披露，直至征集到意向承租方，3：按约定工作日为周期延长信息披露，4：变更公告内容，重新申请信息发布）
  tradeCond: string //	交易条件
  selectType: number | string //交易方式（1：动态报价，2：网络竞价，3：拍卖，9：其他）
  depositBank: string // 出租方指定账户开户行
  accountName: string // 出租方指定账户名
  bankAccount: string // 出租方指定账户
  invoiceType: number | string // 发票类型，1：普票，2：专票
  receivePhone: string // 电子发票接受手机号
  receiveEmail: string // 电子发票接受邮箱
  invoicingRemark: string // 电子发票备注
  //  projectName: string // 项目名称
  // projectContactAddress: string // string 项目联系地址
  // tenantUnsolicited: number | '' // number 未征集到意向承租方选项
  agreedWorkingDay: number | '' // number 约定工作日
  delayPeriod: number | '' //  延期周期
  unIntentionType: number | string //未征集到意向承租方选项
}
type FileKey = 'attachmentFile1' | 'attachmentFile2' | 'attachmentFile3' | 'attachmentFile4'

@Component({
  components: {
    AccessoryList,
    InputNumber
  }
})
export default class extends Vue {
  @Prop() private initData!: LesseeQualifications
  @Watch('initData')
  private changeInitdate() {
    this.lesseeQualificationsForm = Object.assign(this.lesseeQualificationsForm, this.initData)
    // 附件列表赋值
  }
  private isNo = [
    {
      label: '否',
      value: '0'
    },
    {
      label: '是',
      value: '1'
    }
  ]
  private lesseeQualificationsForm: any = {
    announcementDay: '',
    isReviewedWithLessor: '',
    leaseCond: `
1、中华人民共和国境内合法设立并有效存续的独立承担民事责任能力的企业法人，企业注册资本金800万元(含)以上:
2、在公告截止日前，要求标的竞租人(或全资或控股子公司或其法定代表人实际控制的公司)在浙江省内有5000平方米以上园区运营案例(须提供租赁合同等相关证明);
3、竞租人信誉良好，在公告发布截止日前未在全国工商总局企业信用公示系统(http://www.gsxt.gov.cn/indexhtml)公布的经营异常名录或严重违法企业名单中，且未被法院列入失信被执行人(以“中国执行信息公开网”http://zxgk.courtgov.cn/为准)(须提供指定网站截图并做出书面承诺);
4、不接受联合体参与。
5、意向承租方是否符合条件由出租方最终确认`,
    rules: `
    1、公告挂牌期:自信息披露起始日至信息披露期满日下午5时整止，期间征集意向承租方并办理线上报名登记手续。符合承租方条件的意向承租方应在信息披露期满日下午5时(报名截止时间)前在效。
“浙交汇汇”平台完成线上报名手续并完成保证金订单支付，逾期无
2、挂牌期满后，如未征集到合格竞买方，则:延长信息披露:不变更挂牌条件，按照10个工作日为一个周期延长; M最多延长1个周期(两选其一)。
3、挂牌期满后，如征集到一家(或一家以上)符合条件的竞买方，则按动态报价结果确定成交价和承租方。`,
    depositClause: `1.竞买活动结束后，未被确定为承租方的竞买方，且未出现“浙交汇”平台规定应当扣除或不予退还竞买保证金情形的，其已递交的保证金在竞价活动结束后5个工作日内无息原路返还至竞买方的“我的账户”中。
2.项目成交(即竞买方被确定为承租方)后，承租方递交的竞买保证金扣除承租方交易服务费后的余额自动转为立约保证金，金华产权交易所有限公司在项目成交后5个工作日内将立约保证金转付至出租方指定账户。`,
    otherDisclosure: `1、权证相关信息:《建设工程规划许可证》330703202000038金华市浙中公铁联运港有限公司，房屋设计用途为物流及工业用途。
2、是否办理抵押登记:是。抵押登记信息:正在办理中。
3、是否设立居住权:否。
4、出租标的现有装修及设施、设备情况:金华市浙中公铁联运港有限公司集拼仓储区沿街配套用房地面以上共3层，公共区域简装，其余毛坯状态，设置消防栓及消防水带，地上1层预设给排水管道，层高6米(设公共卫生间1间)，第2-3层每层高3米(每层设卫生间3间); 2#仓库2-3层(进出须借助村道通行)毛坯状态，用途工业及仓储，每层建筑面积约1787平方米，层高42米，配备载重3吨货梯套，消防等级丙类2项，已设置消防喷淋设备、消防栓及消防水带，每层设1个卫生间。
5、此次竞买仅为出租标的租赁权，不包括出租标的内设施、设备和物品。竞买方报名前需进行实地勘察，标的实际出租位置及面积，以现场实地勘察及咨询出租方为准。
6、其他事项见房租赁合同(样本)。`,
    otherTradingMethod: '',
    intentionType: '',
    tenantUnsolicited: '2',
    tradeCond: `1、租金支付方式:采用先付款后使用的原则，租金共分20期支付，以6个租赁月度为1期，按该租赁年度租金的50%支付。首期租金按成交年度租金的50%支付，在租赁合同签订后5个工作日内交纳至出租方指定账户。第二期起租金支付时间具体以租赁合同约定为准。
2、竞买保证金:50元。目成交(即有效报价)后，竞买保证金扣除承租方交易服务费后的余额自动转为立约保证金，金华产权交易所在项目成交后5个工作日内将立约保证金存放于出租方账户(不计息)。
3、履约保证金:30万元，租赁合同签订后5个工作日内交纳至出租方指定账户。
4、承租方须按照相关法律法规自行办妥经营证照、经营许可证等相关证照及手续，并自行完善相关配套设施，如依法需由出租方提供有关资料办理的，出租方协助提供。
5承租方须在承租前自行对出租标的进行全面了解(其中集拼仓储区2#仓库2-3层进出须借助村道通行，由承租方自行与相关村沟通)，并对营业所需的各项审批条件和规定进行充分自核。承租方参与竞买的行为将被认为已作充分的预判和决策，无论因何种原因导致不能获得营业开设审批(包括房屋规划用途、面积、产证情况和房屋既有结构、设计等因素在内)，或后续因为政策变化导致无法继续开设的各项经营风险，承租方承诺独立承担，出租方不对无法履行、投入成本、装修损失等承担任何形式的赔偿或补偿责任。
6、出租标的使用过程中所发生的装修、消防二次施工、水、电、煤气、通讯、有线电视、物业费(包括配套用房一层公共厕所管理保洁等工作及相关费用)、网络使用费等相关费用由承租方承担。承租方所租赁标的产生的水、电费用，按表核算。不能独立计算费用如消防用水、电或园区水电损耗费用，按照承租方实际租赁面积除以总可租用面积的比例进行分摊。电费按国家电网金东区供电分公司标准执行，水费根据自来水公司和国家有关规定执行。
7、出租标的按现状交付使用。
8、承租方承租标的后需进行装修的，装修方案需书面报出租方审核通过后方可进行。
9、承租方竞得后接出租方书面或微信通知后5日内签订房屋租赁合同，立约保证金转为履约保证金和首期租金的组成部分，不足部分承租方按合同约定补足;如出租方书面或微信通知后30个自然日内承租方仍未与出租方签订租赁合同的，除不可抗力等特殊原因外，本项目自动终结，立约保证金不予退还，出租方有权收回标的或收回后重新挂牌。
10、承租方竞得后不得变更签约人，不得抵押或改变出租用途(经营业态)等。
11房租赁合同中除承租方相关信息及“租金价格”条款等手填条款外，其他条款均已确定而不得修改。
12、本次出租标的为在建工程，披露面积数据摘自评估报告，面积数据仅做参考，成交后承租人不得提出补足面积或降低租金要求。一切以出租地现状进行公开招租。
13、出租标的正在办理抵押手续。
14、本次出租标的图片等仅作参考，交易所不能保证其品质，不承担瑕疵的担保责任，交易所通过文字方式对出租标的所作的介绍与评判均为参考性意见，不构成对出租标的的任何担保。
15、税收及有关费用的承担:本次出租行为所涉及的一切税、费由出租方和承租方各自依法承担。`,
    selectType: '',
    depositBank: '',
    accountName: '',
    bankAccount: '',
    invoiceType: '',
    receiveEmail: '',
    receivePhone: '',
    invoicingRemark: '',
    // projectName:'',
    // projectContactAddress: '',
    // tenantUnsolicited: '',
    agreedWorkingDay: '',
    delayPeriod: '',
    unIntentionType: '',
    fileList: [],
    startingPrice: '',
    reversePrice: '',
    freeQuoteStartTime: '',
    freeQuoteDay: '',
    limitQuoteStartTime: '',
    limitQuoteSecond: '',
    considerQuoteSecond: '',
    vestingSecond: '',
    markupRange: '',
    contQuote: '',
    initialQuote: '',
    dynamicContactName: '',
    dynamicContactPhone: '',
    competeSessionName: ''
  }

  private lesseeQualificationsFormRules = {
    announcementDay: [{ required: true, trigger: ['blur', 'change'], message: '请输入信息公告期' }],
    depositBank: [{ required: true, trigger: ['blur', 'change'], message: '请输入出租方指定账户开户行' }],
    areportDeptNameccountName: [{ required: true, trigger: ['blur', 'change'], message: '请输入出租方指定账户名' }],
    bankAccount: [{ required: true, trigger: ['blur', 'change'], message: '请输入出租方指定账户' }],

    isReviewedWithLessor: [{ required: true, trigger: ['blur', 'change'], message: '请选择' }],
    unIntentionType: [{ required: true, trigger: ['blur', 'change'], message: '请选择' }],
    intentionType: [{ required: true, trigger: ['blur', 'change'], message: '请选择' }],
    selectType: [{ required: true, trigger: ['blur', 'change'], message: '请选择' }],
    leaseCond: [{ required: false, trigger: ['blur', 'change'], message: '请选择内部决策情况' }],
    rules: [{ required: false, trigger: ['blur', 'change'], message: '请选择内部决策情况' }],
    depositClause: [{ required: false, trigger: ['blur', 'change'], message: '请选择内部决策情况' }],
    otherDisclosure: [{ required: false, trigger: ['blur', 'change'], message: '请选择内部决策情况' }],
    // otherTradingMethod: [{ required: true, trigger: ['blur', 'change'], message: '请选择内部决策情况' }],
    // intentionType: [{ required: true, trigger: ['blur', 'change'], message: '请选择' }],
    // tenantUnsolicited: [{ required: true, trigger: ['blur', 'change'], message: '请选择' }],
    tradeCond: [{ required: false, trigger: ['blur', 'change'], message: '请选择内部决策情况' }],
    // selectType: [{ required: true, trigger: ['blur', 'change'], message: '请选择交易方式' }],
    invoiceType: [{ required: true, trigger: ['blur', 'change'], message: '请选择' }],
    // receiveEmail: [{ required: true, trigger: ['blur', 'change'], message: '请选择内部决策情况' }],
    receivePhone: [
      { required: true, trigger: ['blur', 'change'], message: '请输入手机号' },
      { pattern: /^1[3456789]\d{9}$/, message: '手机号格式不对', trigger: ['blur', 'change'] }
    ],
    // invoicingRemark: [{ required: true, trigger: ['blur', 'change'], message: '请选择内部决策情况' }]
    delayPeriod: [{ required: true, trigger: ['blur', 'change'], message: '请输入延期周期' }], //  延期周期
    agreedWorkingDay: [{ required: true, trigger: ['blur', 'change'], message: '请输入约定工作日' }], // number 约定工作日
    tenantUnsolicited: [{ required: true, trigger: ['blur', 'change'], message: '请选择未征集到意向承租方选项' }], // number 未征集到意向承租方选项
    // projectContactAddress: [{ required: true, trigger: ['blur', 'change'], message: '请输入项目联系地址' }], // string 项目联系地址
    // projectName: [{ required: true, trigger: ['blur', 'change'], message: '请输入项目名称' }], // 项目名称
    contQuote: [{ required: true, trigger: ['blur', 'change'], message: '请选择' }],
    initialQuote: [{ required: true, trigger: ['blur', 'change'], message: '请选择' }],
    competeSessionName: [{ required: true, trigger: ['blur', 'change'], message: '请输入' }],
    vestingSecond: [{ required: false, trigger: ['blur', 'change'], message: '请输入' }],
    considerQuoteSecond: [{ required: false, trigger: ['blur', 'change'], message: '请输入' }],
    freeQuoteDay: [{ required: false, trigger: ['blur', 'change'], message: '请输入' }],
    freeQuoteStartTime: [{ required: true, trigger: ['blur', 'change'], message: '请输入' }],
    reversePrice: [{ required: true, trigger: ['blur', 'change'], message: '请输入' }],
    startingPrice: [{ required: false, trigger: ['blur', 'change'], message: '请输入' }],
    limitQuoteSecond: [{ required: false, trigger: ['blur', 'change'], message: '请输入' }],
    limitQuoteStartTime: [{ required: true, trigger: ['blur', 'change'], message: '请选择时间' }],
    markupRange: [{ required: true, trigger: ['blur', 'change'], message: '请输入' }],
    dynamicContactPhone: [
      { required: false, trigger: ['blur', 'change'], message: '请输入' },
      { pattern: /^1[3456789]\d{9}$/, message: '手机号格式不对', trigger: ['blur', 'change'] }
    ],
    dynamicContactName: [{ required: true, trigger: 'blur', message: '请输入' }]
  }

  private decisionTypeList = decisionTypeList

  private economicNatureList = economicNatureList
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  created() {
    // this.lesseeQualificationsForm = Object.assign(this.lesseeQualificationsForm, this.initData)
  }
  mounted() {
    if (this.$attrs.mode == 'reset') {
      Object.assign(this.lesseeQualificationsForm, (this.$attrs.detaildata as any).leaseCond)

      // this.lesseeQualificationsForm = (this.$attrs.detailInfo as any).leaseCond
    }
  }
  // 获取数据,先执行 validate 再执行 getData
  public getData() {
    // 附件赋值

    return this.lesseeQualificationsForm
  }

  public validate(): Promise<boolean> {
    let form = this.$refs.LesseeQualificationsForm as ElForm
    return form.validate().then(
      () => {
        if (this.lesseeQualificationsForm.fileList.length == 0) {
          this.$message.warning(`请上传承租方附件 !`)
          return Promise.reject(false)
        }
        // 校验附件是否上传
        // for (let index in this.accessoryList) {
        //   let accessory = this.accessoryList[index]
        //   if (!accessory.fileList.length && accessory.isRequired) {
        //     this.$message.warning(`请上传 ${accessory.fileName} !`)
        //     return Promise.reject(false)
        //   }
        // }
        return Promise.resolve(true)
      },
      () => {
        this.$message.warning('请完善承租方信息！')
        return Promise.reject(false)
      }
    )
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-form-item {
  margin-bottom: 0px !important;
}

// ::v-deep .department-name-input {
//   .el-form-item {
//     margin-bottom: 0px !important;
//   }
// }
::v-deep .el-descriptions-item__label.is-bordered-label {
  display: none;
}
::v-deep.el-descriptions .is-bordered .el-descriptions-item__cell {
  border: 0;
}
::v-deep.el-form-item--small .el-form-item__label {
  line-height: 20px;
}
::v-deep.el-select,
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
}
</style>
