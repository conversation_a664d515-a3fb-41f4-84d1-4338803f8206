<template>
  <Dialog :width="`${width}px`"
    :visible="visible"
    :close-on-click-modal="true"
    :destroy-on-close="true"
    :append-to-body="true"
    :show-close="false"
    @close="handleClose"
    custom-class="custom-cockipt-dialog-wrap">
    <div slot="body">
      <i class="body-close el-icon-circle-close"
        @click="handleClose" />
      <Ichnography :assetInfo="assetInfo" />
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import Ichnography from '@/views/main/map/components/Ichnography.vue'

@Component({
  components: {
    Dialog,
    Ichnography
  }
})
export default class Header extends Vue {
  @Prop({ default: '房屋详情' }) private title!: string // 标题
  @Prop() private visible!: boolean // 弹窗显隐
  @Prop({ default: 1000 }) private width?: string // 弹窗宽度
  @Prop() readonly assetInfo!: any // 弹窗详情数据

  // 关闭弹窗
  private handleClose() {
    this.$emit('update:visible', false)
  }
}
</script>

