<template>
  <section class="alert-record-wrap">
    <ul v-loading="loading"
      class="record-list">
      <li v-for="item of recordList"
        :key="item.id"
        class="mode-box">
        <div class="head">
          <span>变更时间：{{item.createTime}}</span>
          <span>变更人：{{userInfo.user_name}}</span>
        </div>

        <div class="conter">
          <span v-if="item.detailVo.alertName.length <= 11">{{item.detailVo.alertName}}</span>
          <el-tooltip v-else
            effect="dark"
            :content="item.detailVo.alertName"
            placement="top-start">
            <span>{{item.detailVo.alertName}}</span>
          </el-tooltip>

          <span
            :class="[{'hot':+item.detailVo.level === 1},{'yellow':+item.detailVo.level === 2},{'blue':+item.detailVo.level === 3}]">
            <i />
            {{getDictLable('alert_level',item.detailVo.level)}}
          </span>
          <span>{{getDictLable('alert_point_type', item.detailVo.alertType)}}</span>

          <span
            v-if="item.detailVo.alertDirection.length <= 26">{{item.detailVo.alertDirection}}</span>
          <el-tooltip v-else
            effect="dark"
            :content="item.detailVo.alertDirection"
            placement="top-start">
            <span>{{item.detailVo.alertDirection}}</span>
          </el-tooltip>
        </div>
      </li>

      <li v-for="item of recordList"
        :key="item.id"
        class="mode-box">
        <div class="head">
          <span>变更时间：{{item.createTime}}</span>
          <span>变更人：{{userInfo.user_name}}</span>
        </div>

        <div class="conter">
          <span>{{item.detailVo.alertName}}</span>
          <span
            :class="[{'hot':+item.detailVo.level === 1},{'yellow':+item.detailVo.level === 2},{'blue':+item.detailVo.level === 3}]">
            <i />
            {{getDictLable('alert_level',item.detailVo.level)}}
          </span>
          <span>{{getDictLable('alert_point_type', item.detailVo.alertType)}}</span>
          <span>{{item.detailVo.alertDirection}}</span>
        </div>
      </li>
    </ul>

    <div class="pagination-box">
      <el-pagination layout="prev, pager, next"
        background
        :page-size="pagination.size"
        :total="pagination.total"
        :current-page="pagination.current"
        @current-change="handleCurrentChange" />

      <el-button @click="handleClose"
        class="btn">关 闭</el-button>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { BusinessModule } from '@/store/modules/businessDict'
import { getpointrecordhistory } from '@/api/prewarning'
import { getLocalStorage } from '@/utils/cache'

@Component
export default class extends Vue {
  @Prop() private pointId!: string // 详情id
  @Prop({
    validator: (value: string) => {
      return ['add', 'edit', 'see'].indexOf(value) !== -1
    }
  })
  readonly type!: string // 组件类型(新增、编辑、查看)

  private loading = false
  private recordList = []
  private userInfo = {}
  private pagination = {
    size: 10,
    total: 0,
    current: 1
  }

  // 获取字典项
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }

  // 获取字典对应中文名称
  get getDictLable() {
    return (code: string, value: number) => {
      let levelList = this.getDictData(code).filter((item: any) => {
        return +value === +item.value
      })

      if (levelList.length) {
        return levelList[0].label
      } else {
        return ''
      }
    }
  }

  // 组件初始化
  private mounted() {
    this.initData()
    this.getUserInof()
  }

  // 获取用户信息
  private getUserInof() {
    let userInfo = getLocalStorage('saber-userInfo') || ''
    this.userInfo = JSON.parse(userInfo).content
  }

  // 获取详情数据
  @Loading('loading')
  private async initData() {
    let { data } = await getpointrecordhistory({
      pointRecordId: this.pointId,
      size: this.pagination.size,
      current: this.pagination.current
    })

    this.recordList = data.records || []
    this.pagination.total = data.total
    this.pagination.current = data.current
  }

  // 分页数据改变
  private handleCurrentChange(current: number) {
    this.pagination.current = current
    this.initData()
  }

  // 关闭弹窗
  private handleClose() {
    this.$emit('handleClose')
  }
}
</script>

<style scoped lang="scss">
.alert-record-wrap {
  $hot: #fb3f3f;
  $yellow: #e6a23c;
  $blue: #409eff;

  position: relative;
  .btn {
    min-width: 88px;
  }
  .hot {
    color: $hot !important;
    i {
      background: $hot;
      box-shadow: 0 0 0 2px rgba($color: $hot, $alpha: 0.2);
    }
  }
  .yellow {
    color: $yellow !important;
    i {
      background: $yellow;
      box-shadow: 0 0 0 2px rgba($color: $yellow, $alpha: 0.2);
    }
  }
  .blue {
    color: $blue !important;
    i {
      background: $blue;
      box-shadow: 0 0 0 2px rgba($color: $blue, $alpha: 0.2);
    }
  }

  .pagination-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    ::v-deep .el-pagination {
      padding: 0;
      button {
        &:nth-child(1) {
          margin-left: 0;
        }
      }
    }
  }

  .record-list {
    height: 490px;
    overflow-y: auto;
    margin-bottom: 20px;
    .mode-box {
      padding: 16px 0;
      border-bottom: 1px solid #eee;
    }
    .head {
      display: flex;
      margin-bottom: 8px;
      span {
        width: 301px;
        color: #91929e;
        font-size: 13px;
      }
    }
    .conter {
      display: flex;
      align-items: center;
      justify-content: space-between;
      i {
        display: inline-block;
        width: 10px;
        height: 10px;
        margin-left: 4px;
        margin-right: 2px;
        border-radius: 50%;
      }
      span {
        color: #3a3a3a;
        font-size: 15px;
        margin-right: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        &:nth-last-child(1) {
          margin-right: 0;
        }
        &:nth-child(1) {
          width: 162px;
        }
        &:nth-child(2) {
          width: 100px;
        }
        &:nth-child(3) {
          width: 100px;
        }
        &:nth-child(4) {
          flex: 1;
        }
      }
    }
  }
}
</style>