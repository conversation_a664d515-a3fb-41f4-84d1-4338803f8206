// 分配
<template>
  <Dialog title="审核信息" :append-to-body="true" width="600px" :visible="visible" @close="handleClose" center>
    <div slot="body" v-loading="loading">
      <el-form :model="auditOpinionForm" :rules="Formrules" ref="ruleForm" label-width="80px" class="demo-ruleForm">
        <el-form-item label="是否通过" prop="auditType">
          <!-- <el-input v-model="auditOpinionForm.auditType" placeholder="请输入审核意见"></el-input> -->
          <el-radio v-model="auditOpinionForm.auditType" label="1">通过</el-radio>
          <el-radio v-model="auditOpinionForm.auditType" label="2">驳回</el-radio>
        </el-form-item>
        <el-form-item label="审核意见" prop="auditNode">
          <el-input
            maxlength="300"
            show-word-limit
            :autosize="{ minRows: 4, maxRows: 6 }"
            type="textarea"
            v-model="auditOpinionForm.auditNode"
            placeholder="请输入审核意见"
          ></el-input>
        </el-form-item>
      </el-form>
      <AccessoryList title="审核附件" v-model="auditOpinionForm.fileList" dict="asset_verify_pass_attach" mode="upload" />
    </div>
    <div slot="footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="DetermineAllocation">确定</el-button>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import { LeaseAudit } from '@/api/assetsv2'
import { api } from 'v-viewer'
import AccessoryList, { Accessory } from '@/views/assets/components/astFileList/index.vue'
import { Throttle } from '../../../../../decorators'

export interface Transaction {
  applyId: number | string //标的申请id		false
  auditNode: string //审批意见		false
  auditType: number | string //审批类型：1-通过 2-驳回		false
  fileList: any[] //附件:array		true
  leaseId: string //标的id:关联ast_subject_lease表中的id		false
  orgCode: string //机构编号		false
  subjectNo: string //标的编号
}
@Component({
  components: {
    Dialog,
    AccessoryList
  }
})
export default class extends Vue {
  @Prop() private visible!: boolean
  @Prop() private detail!: any
  @Prop() private detaildata!: any
  private peopleList = [] //人员列表
  private loading = false
  private auditOpinionForm: Transaction = {
    applyId: '', //标的申请id		false
    auditNode: '', //审批意见		false
    auditType: '', //审批类型：1-通过 2-驳回		false
    fileList: [], //附件:array		true
    leaseId: '', //标的id:关联ast_subject_lease表中的id		false
    orgCode: '', //机构编号		false
    subjectNo: '' //标的编号
  }
  private Formrules = {
    auditType: [{ required: true, message: '请选择意见', trigger: ['blur', 'change'] }],
    auditNode: [{ required: true, message: '请选择意见', trigger: ['blur', 'change'] }]
  }
  //   auditOpinion
  private mounted() {
    // this.getUserList()
  }
  // 选择人员时加载数据
  private selectUser(id: any) {
    let list: any = this.peopleList.find((res: any) => {
      return res.id == id
    })
    // this.auditOpinionForm.leaseId = this.assetId
  }
  private handleClose() {
    this.$emit('update:visible', false)
  }
  //   验证表单
  @Throttle
  private async DetermineAllocation() {
    ;(this.$refs.ruleForm as any)
      .validate()
      .then((valid: any) => {
        if (this.auditOpinionForm.fileList.length == 0) {
          this.$message.info('请上传审核附件')
        } else {
          this.sendApi()
        }
      })
      .catch(() => {
        //
      })
  }
  //   发送请求
  private async sendApi() {
    let res: any = {}
    try {
      this.loading = true
      res = await LeaseAudit({
        ...this.auditOpinionForm,
        applyId: this.detaildata.applyId,
        leaseId: this.detaildata.id,
        orgCode: 'JH200',
        subjectNo: this.detaildata.subjectNo
      })
      if (res.success) {
        this.$message.success(res.msg)
        this.loading = false
        this.$emit('handleClose')
        this.handleClose()
      }
    } catch (e) {
      this.loading = false
    }
  }
}
</script>

<style lang="scss" scoped>
.center {
  text-align: center;
}
</style>
