// 项目表单

<template>
  <el-form ref="ProjectForm"
    :rules="projectFormRules"
    :model="projectForm"
    inline
    label-width="200px">
    <el-form-item label="类型"
      prop="projectCategory">
      <el-input disabled
        placeholder="类型"
        v-model="projectForm.projectCategory" />
    </el-form-item>
    <el-form-item label="项目编号"
      prop="projectCode">
      <el-input disabled
        placeholder="保存后自动生成"
        v-model="projectForm.projectCode" />
    </el-form-item>
    <el-form-item label="项目名称"
      prop="projectFullName">
      <el-input v-model="projectForm.projectFullName" />
    </el-form-item>
    <el-form-item label="建设规模及主要内容"
      prop="projectContent">
      <el-input v-model="projectForm.projectContent" />
    </el-form-item>
    <el-form-item label="建设地址"
      prop="projectAddress">
      <el-input v-model="projectForm.projectAddress" />
    </el-form-item>
    <el-form-item label="总投资额"
      prop="totalInvestment">
      <el-input v-model="projectForm.totalInvestment" />
    </el-form-item>
    <el-form-item label="建设起止年限"
      prop="startEndDate">
      <el-input v-model="projectForm.startEndDate" />
    </el-form-item>
    <el-form-item label="本年计划年份"
      prop="year">
      <el-input v-model="projectForm.year" />
    </el-form-item>
    <el-form-item label="预计头年年底累计完成投资额"
      prop="thisYearlyAccumulatedInvestment">
      <el-input v-model="projectForm.thisYearlyAccumulatedInvestment" />
    </el-form-item>
    <el-form-item label="本年计划投资额"
      prop="nextYearlyPlannedInvestment">
      <el-input v-model="projectForm.nextYearlyPlannedInvestment" />
    </el-form-item>
    <el-form-item label="本年度完成目标"
      prop="nextYearlyProgressTarget">
      <el-input v-model="projectForm.nextYearlyProgressTarget" />
    </el-form-item>
    <el-form-item label="业主单位"
      prop="implementSubject">
      <el-input v-model="projectForm.implementSubject" />
    </el-form-item>
    <el-form-item label="备注"
      prop="remark">
      <el-input v-model="projectForm.remark" />
    </el-form-item>
  </el-form>
</template>

<script lang="ts">
import { ElForm } from 'node_modules/element-ui/types/form'
import { Component, Vue } from 'vue-property-decorator'

export interface AgentProjectForm {
  implementSubject: string // 业主单位
  nextYearlyPlannedInvestment: string // 本年计划投资额
  nextYearlyProgressTarget: string // 本年度完成目标
  projectAddress: string // 建设地址
  projectCategory: string // 类型
  projectCode: string // 项目编号
  projectContent: string // 项目规模及主要内容
  projectFullName: string // 项目名称
  remark: string // 备注
  startEndDate: string // 建设起止年限
  thisYearlyAccumulatedInvestment: string // 预计头年年底累计完成投资额
  totalInvestment: string // 总投资额
  year: string // 本年计划年份
}

@Component
export default class extends Vue {
  public projectForm: AgentProjectForm = {
    implementSubject: '',
    nextYearlyPlannedInvestment: '',
    nextYearlyProgressTarget: '',
    projectAddress: '',
    projectCategory: '代建',
    projectCode: '',
    projectContent: '',
    projectFullName: '',
    remark: '',
    startEndDate: '',
    thisYearlyAccumulatedInvestment: '',
    totalInvestment: '',
    year: ''
  }

  private projectFormRules = {
    projectFullName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }]
  }

  public validate(): Promise<boolean> {
    let projectForm = this.$refs['ProjectForm'] as ElForm
    return projectForm.validate()
  }
}
</script>


<style scoped lang="scss">
</style>