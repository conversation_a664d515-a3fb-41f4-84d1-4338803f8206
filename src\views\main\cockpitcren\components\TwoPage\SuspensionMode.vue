// @click="tabsHandle(item, index+1)"

<template>
  <section class="suspension-mode-wrap">
    <div v-for="(item, index) of dataList"
      :key="index"
      :class="`modes${index+1}`"
      class="modes"
      @mouseenter="tabsMoveHandle(item, index+1)"
      @mouseleave="tabsLeaveHandle"
      @click="tabsCilckHandle(item)">
      <div class="wrp"
        :class="{'active':item.orgCode === orgActive.orgCode}">
        <span class="line" />
        <div class="text">{{item.orgName}}</div>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { getGroupList } from '@/api/public'
import { Loading } from '@/decorators'
import { isWindowFull } from '@/utils'

type typeOrgItem = {
  orgName: string
  orgCode: string
}

@Component({})
export default class extends Vue {
  // 选中集团
  private orgActive: {
    orgCode: string
  } = {
    orgCode: '0'
  }

  private timer:any = null

  // 渲染数据
  private dataList:typeOrgItem[] = []

  // 判断窗口是否处于全屏模式
  get getIsWindowFull() {
    return isWindowFull
  }

  // 初始化
  private mounted() {
    this.getOrgList()
    this.listenerDate()
  }

  // 获取 tabs 数据
  @Loading('loading')
  private async getOrgList() {
    let { data } = await getGroupList()

    // data = data.concat([
    //   {
    //     orgName: '尖峰集团',
    //     orgCode: '91330000704202954L'
    //   },
    // ])

    data.forEach((item: any) => {
      if (+item.orgCode == 0) {
        item.orgName = '金华市国资委'
      }
    })

    this.dataList = data
  }

  // 监听全局事件触发
  private listenerDate() {
    // 集团 切换
    this.$bus.$on('BusTabsChange', (orgActive: typeOrgItem) => {
      this.orgActive = orgActive
    })
  }

  // 模块点击
  private tabsCilckHandle(item:{orgCode: string, orgName: string}) {
    if(+item.orgCode == 0) return 

    clearTimeout(this.timer)
    this.$router.push({
      name: 'moduleDetail',
      query: { 
        module: 'CompanyDetail',
        orgCode: item.orgCode
      },
      params: { isWindowFull: this.getIsWindowFull() }
    })
  }

  // 模块鼠标移入
  private tabsMoveHandle(item: typeOrgItem, index: number) {
    this.tabsLeaveHandle()

    this.timer = setTimeout(() => {
      let obj = JSON.parse(JSON.stringify(item))

      obj.value = index
      if (obj.orgCode !== '') {
        this.$emit('tabsCilckHandle', obj)
      }
    }, 1000)
  }

  // 模块鼠标离开
  private tabsLeaveHandle() {
    clearTimeout(this.timer)
    this.timer = null
  }

  // 点击模块跳转到“企业画像”二级页面
  private routerOpen() {
    this.$router.push({
      path: '/moduledetail?module=CompanyDetail'
    })
  }
}
</script>

<style lang="scss" scoped>
.suspension-mode-wrap {
  position: relative;
  width: 100%;
  height: 100%;

  @keyframes keyModes0 {
    0% {
      left: 550px;
      top: 128px;
      opacity: 0;
      transform: scale(0);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes keyModes1 {
    0% {
      opacity: 0;
      transform: scale(0);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes keyModesLine2 {
    0% {
      width: 0px;
      opacity: 0;
    }
    50% {
      width: 0px;
      opacity: 0;
    }
    100% {
      width: 212px;
      opacity: 1;
    }
  }

  @keyframes keyModesLine3 {
    0% {
      width: 0px;
      opacity: 0;
    }
    50% {
      width: 0px;
      opacity: 0;
    }
    100% {
      width: 343px;
      opacity: 1;
    }
  }

  @keyframes keyModesLine4 {
    0% {
      width: 0px;
      opacity: 0;
    }
    50% {
      width: 0px;
      opacity: 0;
    }
    100% {
      width: 427px;
      opacity: 1;
    }
  }

  @keyframes keyModesLine5 {
    0% {
      width: 0px;
      opacity: 0;
    }
    50% {
      width: 0px;
      opacity: 0;
    }
    100% {
      width: 266px;
      opacity: 1;
    }
  }

  @keyframes keyModesLine6 {
    0% {
      width: 0px;
      opacity: 0;
    }
    50% {
      width: 0px;
      opacity: 0;
    }
    100% {
      width: 208px;
      opacity: 1;
    }
  }

  @keyframes keyModesLine7 {
    0% {
      width: 0px;
      opacity: 0;
    }
    50% {
      width: 0px;
      opacity: 0;
    }
    100% {
      width: 169px;
      opacity: 1;
    }
  }

  @keyframes keyModesLine8 {
    0% {
      width: 0px;
      opacity: 0;
    }
    50% {
      width: 0px;
      opacity: 0;
    }
    100% {
      width: 242px;
      opacity: 1;
    }
  }

  @keyframes keyModesLine9 {
    0% {
      width: 0px;
      opacity: 0;
    }
    50% {
      width: 0px;
      opacity: 0;
    }
    100% {
      width: 301px;
      opacity: 1;
    }
  }

  @keyframes keyModesLine10 {
    0% {
      width: 0px;
      opacity: 0;
    }
    50% {
      width: 0px;
      opacity: 0;
    }
    100% {
      width: 269px;
      opacity: 1;
    }
  }

  @keyframes keyModesLine11 {
    0% {
      width: 0px;
      opacity: 0;
    }
    50% {
      width: 0px;
      opacity: 0;
    }
    100% {
      width: 159px;
      opacity: 1;
    }
  }

  .modes {
    $wh: 124px;
    $color: #3eeeff;

    position: absolute;
    width: $wh;
    height: $wh;
    padding: 12px;
    border-radius: 50%;
    border: 2px solid $color;
    background-color: rgba($color: #021e5d, $alpha: 0.6);
    background-image: url('../../images/ring_org.gif');
    background-position: center center;
    background-size: 130% 130%;
    &:hover {
      box-shadow: 0 0 40px $color;
      background-image: url('../../images/ring_org_move.gif');
      background-size: 114% 114%;
    }
    .wrp {
      height: 100%;
      border-radius: 50%;
    }
    .active {
      $clr: #ffcd36;
      background: $color;
      .text {
        color: #174ad1;
      }
    }
    .text {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      padding: 20px;
      border-radius: 50%;
      color: #fff;
      font-size: 36px;
      cursor: pointer;
      font-weight: normal;
      font-family: 'FZZZHONGHJW';
      box-sizing: border-box;
      text-align: center;
    }
    .line {
      position: absolute;
      opacity: 1;
      background: linear-gradient(45deg, #00f6ff 20%, #ffcd36 80%);
    }
  }

  .modes1 {
    $wh: 220px;
    $color: #ffcd36;

    width: $wh;
    height: $wh;
    right: 410px;
    top: 70px;
    border: 3px solid $color;
    box-shadow: 0 0 60px $color;
    animation: keyModes1 5s ease-in-out;
    background-color: rgba($color: #021e5d, $alpha: 0.6);
    background-image: url('../../images/ring_jh_move.gif');
    background-position: center center;
    background-size: 102% 102%;
    &:hover {
      background-image: url('../../images/ring_jh_move.gif');
      background-size: 100% 100%;
      .text {
        border: none;
      }
    }
    .active {
      background: none;
      .text {
        color: $color;
      }
    }
    .text {
      color: $color;
      font-size: 47px;
      line-height: 64px;
    }
  }
  .modes2 {
    left: 161px;
    top: 67px;
    animation: keyModes0 3s ease-in-out;
    .line {
      position: absolute;
      width: 212px;
      height: 4px;
      right: -212px;
      top: 52%;
      transform: rotate(-3deg);
      animation: keyModesLine2 8s ease-in-out;
    }
  }
  .modes3 {
    left: 33px;
    top: 223px;
    animation: keyModes0 5s ease-in-out;
    .line {
      position: absolute;
      width: 343px;
      height: 4px;
      right: -335px;
      top: 8%;
      transform: rotate(-15deg);
      animation: keyModesLine3 8s ease-in-out;
    }
  }
  .modes4 {
    left: 23px;
    top: 422px;
    animation: keyModes0 6s ease-in-out;
    .line {
      position: absolute;
      width: 427px;
      height: 4px;
      right: -376px;
      top: -61%;
      transform: rotate(-32deg);
      animation: keyModesLine4 8s ease-in-out;
    }
  }
  .modes5 {
    left: 226px;
    top: 440px;
    animation: keyModes0 5s ease-in-out;
    .line {
      position: absolute;
      width: 266px;
      height: 4px;
      right: -207px;
      top: -53%;
      transform: rotate(-51deg);
      animation: keyModesLine5 8s ease-in-out;
    }
  }
  .modes6 {
    left: 403px;
    top: 489px;
    animation: keyModes0 7s ease-in-out;
    .line {
      position: absolute;
      width: 208px;
      height: 4px;
      right: -82px;
      top: -65%;
      transform: rotate(-69deg);
      animation: keyModesLine6 8s ease-in-out;
    }
  }
  .modes7 {
    $wh: 100px;
    $clr: #ffa001;

    left: 596px;
    top: 484px;
    // width: $wh;
    // height: $wh;
    border: 2px solid $clr;
    animation: keyModes0 5s ease-in-out;
    background-image: url('../../images/ring_ab.gif');
    background-position: center center;
    background-size: 124% 124%;
    &:hover {
      box-shadow: 0 0 40px $clr;
      background-image: url('../../images/ring_ab_move.gif');
      background-size: 100% 100%;
    }
    .active {
      .text {
        box-shadow: 0 0 40px $clr inset;
        background: rgba($color: $clr, $alpha: 1) !important;
      }
    }
    .text {
      color: $clr;
     font-size: 32px;
    }
    .line {
      position: absolute;
      width: 169px;
      height: 4px;
      right: -2px;
      top: -60%;
      transform: rotate(-99deg);
      animation: keyModesLine7 8s ease-in-out;
      background: linear-gradient(45deg, #ffa001 20%, #ffcd36 80%);
    }
  }
  .modes8 {
    $wh: 100px;
    $clr: #ffa001;

    left: 777px;
    top: 507px;
    // width: $wh;
    // height: $wh;
    border: 2px solid $clr;
    animation: keyModes0 5s ease-in-out;
    background-image: url('../../images/ring_ab.gif');
    background-position: center center;
    background-size: 124% 124%;
    &:hover {
      box-shadow: 0 0 40px $clr;
      background-image: url('../../images/ring_ab_move.gif');
      background-size: 100% 100%;
    }
    .active {
      .text {
        box-shadow: 0 0 40px $clr inset;
        background: rgba($color: $clr, $alpha: 1) !important;
      }
    }
    .text {
      color: $clr;
       font-size: 32px;
    }
    .line {
      position: absolute;
      width: 242px;
      height: 4px;
      right: 35px;
      top: -73%;
      transform: rotate(-120deg);
      animation: keyModesLine8 8s ease-in-out;
      background: linear-gradient(45deg, #ffa001 20%, #ffcd36 80%);
    }
  }
  .modes9 {
    $wh: 126px;
    // $clr: #cddc39;
     $clr: #ffa001;

    left: 930px;
    top: 449px;
    // width: $wh;
    // height: $wh;
    border: 2px solid $clr;
    animation: keyModes0 5s ease-in-out;
    // background-image: url('../../images/ring_yh.gif');
    background-image: url('../../images/ring_ab.gif');
    background-position: center center;
    background-size: 124% 124%;
    &:hover {
      box-shadow: 0 0 40px $clr;
      // background-image: url('../../images/ring_yh_move.gif');
      background-image: url('../../images/ring_ab_move.gif');
      background-size: 100% 100%;
    }
    .active {
      .text {
        box-shadow: 0 0 40px $clr inset;
        background: rgba($color: $clr, $alpha: 1) !important;
      }
    }
    .text {
      color: $clr;
      font-size: 32px;
    }
    .line {
      position: absolute;
      width: 301px;
      height: 4px;
      right: 98px;
      top: -46%;
      transform: rotate(-140deg);
      animation: keyModesLine9 8s ease-in-out;
      // background: linear-gradient(45deg, #cddc39 20%, #ffcd36 80%);
        background: linear-gradient(45deg, #ffa001 20%, #ffcd36 80%);
    }
  }
  .modes10 {
    $wh: 126px;
    $clr: #cddc39;

    left: 997px;
    top: 282px;
    // width: $wh;
    // height: $wh;
    border: 2px solid $clr;
    animation: keyModes0 4s ease-in-out;
    background-image: url('../../images/ring_yh.gif');
    background-position: center center;
    background-size: 124% 124%;
    &:hover {
      box-shadow: 0 0 40px $clr;
      background-image: url('../../images/ring_yh_move.gif');
      background-size: 100% 100%;
    }
    .active {
      .text {
        box-shadow: 0 0 40px $clr inset;
        background: rgba($color: $clr, $alpha: 1) !important;
      }
    }
    .text {
      color: $clr;
      font-size: 32px;
    }
    .line {
      position: absolute;
      width: 269px;
      height: 4px;
      right: 136px;
      top: 2%;
      transform: rotate(-165deg);
      animation: keyModesLine10 8s ease-in-out;
      background: linear-gradient(45deg, #cddc39 20%, #ffcd36 80%);
    }
  }

  .modes11 {
    $wh: 100px;
    $clr: #cddc39;

    left: 894px;
    top: 138px;
    // left: 915px;
    // top: 162px;
    // width: $wh;
    // height: $wh;
    border: 2px solid $clr;
    animation: keyModes0 4s ease-in-out;
    background-image: url('../../images/ring_yh.gif');
    background-position: center center;
    background-size: 124% 124%;
    &:hover {
      box-shadow: 0 0 40px $clr;
      background-image: url('../../images/ring_yh_move.gif');
      background-size: 100% 100%;
    }
    .active {
      .text {
        box-shadow: 0 0 40px $clr inset;
        background: rgba($color: $clr, $alpha: 1) !important;
      }
    }
    .text {
      color: $clr;
      font-size: 32px;
    }
    .line {
      position: absolute;
      width: 139px;
      height: 4px;
      right: 147px;
      top: 43%;
      transform: rotate(-176deg);
      animation: keyModesLine11 8s ease-in-out;
      background: linear-gradient(45deg, #cddc39 20%, #ffcd36 80%);
    }
  }
}
</style>