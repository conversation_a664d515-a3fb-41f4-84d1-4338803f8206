     <!-- 企业发债审批 -->
<template>
  <el-container direction="vertical"
    class="content">
    <search-bar :items="searchItems"
      @onSearch="handleSearch">
      <div>
        <el-button type="primary"
          @click="AddForm">新增</el-button>
      </div>
    </search-bar>
    <!-- <div class="debtApproval_header">
          <div>
            <el-input placeholder="请输入内容" v-model="searchContent">
              <el-button slot="append" icon="el-icon-search"></el-button>
            </el-input>
          </div>
          <div>
            <el-button class="finance_red_btn" @click="AddForm">新增</el-button>
            <el-button class="finance_white_btn">批量导入</el-button>
          </div>
        </div> -->
    <Grid @row-click="loaddetail"
      :columns="cols"
      ref="grid"
      :remote-url="remoteUrl"
      :show-pagination="true"
      :search-params="searchParams">
      <template slot="documentNo"
        slot-scope="scope">
        <span>{{ scope.row.documentNo }}</span>
      </template>
      <template slot="state"
        slot-scope="scope">
        <span v-if="scope.row.state == 1">审批中</span>
        <span v-else-if="scope.row.state == 2">审批通过</span>
        <span v-else-if="scope.row.state == 3"
          style="color: #df7575">审批不通过</span>
      </template>
      <!-- 决策机构 -->
      <!-- <div v-for="(item,index) in cols" :key="index" slot="item.slotName" slot-scope="scope">
            <span v-if="'filterOptions' in item">{{ scope.row[item.prop] }}</span>
          </div> -->

      <template slot="operatingBar"
        slot-scope="scope">
        <div class="operatingBar_content">

          <el-button type='text'
            @click.stop="loaddetail(scope.row)">查看</el-button>
          <el-button type='text'
            v-if="scope.row.approvalStatus!=1"
            @click.stop="loofinfo(scope.row)">编辑</el-button>
          <!-- <el-button type="text">删除</el-button> -->
        </div>
      </template>
    </Grid>
    <!-- 新增 -->
    <Dialog-add :mode="dialogMode"
      :visible.sync="showDialogAdd"
      v-if="showDialogAdd"
      @changshowDialogAdd="changeShowDialogAdd"
      :Diaformdata="Diaformdata" />
    <!-- 详情 -->
    <DialogDetail :DetailForm="pageData"
      :list="Diadetaillist"
      @changeShowDialogDetail="changeShowDialogDetail"
      :visible="showDialogDetail" />
  </el-container>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import Grid from '@/components/Grid/index.vue'
import DialogAdd from '../components/DialogDeb.vue'
import DialogDetail from '../components/DetailDeb.vue'

// import { DataDebissuance } from '../date'
import * as filterOptions from '../filterOptions'
import searchBar from '@/components/SearchBar/index.vue'
import { deepClone } from '../../../utils'

@Component({
  components: {
    Grid,
    DialogAdd,
    DialogDetail,
    searchBar
  }
})
export default class Container extends Vue {
  private Diaformdata: object = {}
  private pageData = {}
  private Diadetaillist: object = []
  private showDialogAdd = false
  private showDialogDetail = false
  private searchParams = {} //表格搜索条件
  private dialogMode = '' //弹窗模式
  private remoteUrl = 'fht-monitor/fin/bondIssuanceApproval/page'
  private searchItems = [
    {
      type: 'text',
      key: 'keyword',
      placeholder: '请输入关键字进行查询',
      width: '200px'
    }
  ]
  private cols = [
    {
      label: '单据编号',
      prop: 'documentNo',
      slotName: 'documentNo',
      minWidth: 150
    },
    {
      label: '申请状态',
      prop: 'approvalStatusDesc',
      minWidth: 90
    },
    {
      label: '决策机构',
      prop: 'decisionMakingBody',
      minWidth: 110
    },
    {
      label: '发行方',
      prop: 'publisher',
      minWidth: 110
    },
    {
      label: '文号',
      prop: 'referenceNo',
      minWidth: 120
    },
    {
      label: '签发人',
      prop: 'issuer',
      minWidth: 80
    },
    {
      label: '发债原因',
      prop: 'issuanceBondsReason',
      minWidth: 100
    },
    {
      label: '发行目的',
      prop: 'issuancePurpose',
      minWidth: 80
    },
    // {
    //   label: '发行币种',
    //   prop: 'issuanceCurrency',
    //   minWidth: 80,
    //   filterOptions: filterOptions.issuanceCurrency
    // },
    {
      label: '发行币种',
      prop: 'issuanceCurrencyDesc',
      minWidth: 80
      // filterOptions: filterOptions.assetCurrency
    },

    {
      label: '填报人',
      prop: 'filler',
      minWidth: 80
    },
    {
      label: '联系电话',
      prop: 'contactPhone',
      minWidth: 100
    },
    {
      label: '增信措施',
      prop: 'creditEnhancementMeasures',
      minWidth: 80
    },
    {
      label: '归还措施',
      prop: 'restitutionMeasures',
      minWidth: 80
    },
    {
      label: '其他补充',
      prop: 'otherSupplements',
      minWidth: 80
    },
    {
      label: '发券类型',
      prop: 'bondsType',
      minWidth: 80
    },
    {
      label: '债券金额（元）',
      prop: 'bondAmount',
      minWidth: 120
    },
    {
      label: '有效期',
      prop: 'validityStartDate',
      minWidth: 80
    },
    {
      label: '有效截止日期',
      prop: 'expiryDate',
      minWidth: 100
    },
    {
      label: '主要用途',
      prop: 'mainUses',
      minWidth: 80
    },
    {
      label: '发行利率',
      prop: 'issuanceRate',
      minWidth: 80
    },
    {
      label: '发行次数',
      prop: 'issuanceTimeNumberDesc',
      minWidth: 80
      // slotName:"issuanceTimeNumber",
      // filterOptions: filterOptions.issuanceTimeNumberType
    },
    {
      label: '发行形式',
      prop: 'issuanceFormDesc',
      minWidth: 80
    },
    {
      label: '发行对象',
      prop: 'issuanceTargetDesc',
      minWidth: 80
      //  filterOptions: filterOptions.issuanceTargetType
    },
    {
      label: '其他发行对象',
      // prop: 'decisionMakingBody',
      minWidth: 120
    },
    {
      label: '创建时间',
      prop: 'createTime',
      minWidth: 90
    },
    {
      label: '操作',
      prop: 'operatingBar',
      slotName: 'operatingBar',
      fixed: 'right',
      minWidth: 80
    }
  ]

  // search搜索
  private handleSearch(condition: any) {
    this.searchParams = condition
    let grid: any = this.$refs.grid
    grid.refresh()
  }
  // 新增按钮
  private AddForm() {
    this.Diaformdata = {
      id: ''
    }
    this.dialogMode = 'add'
    this.showDialogAdd = true
  }
  // 改变新增显示隐藏
  private changeShowDialogAdd(state: boolean) {
    if (state == false) {
      ;(this.$refs.grid as Grid).refresh(true)
    }
    this.showDialogAdd = state
  }
  // 改变详情显示隐藏
  private changeShowDialogDetail(state: boolean) {
    this.showDialogDetail = state
  }
  //点击查看
  private loofinfo(row: any) {
    row.year = row.year + ''
    this.Diaformdata = row
    this.dialogMode = 'edit'
    this.showDialogAdd = true
  }
  //  操作
  private loaddetail(row: any) {
    this.Diaformdata = deepClone(row)
    this.dialogMode = 'see'
    this.showDialogAdd = true
  }
}
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
}
</style>
