<template>
  <section v-loading="loading"
    class="atlas-mind-wrap">
    <div class="options-box">
      <el-button v-if="!isEdit"
        class="primary"
        @click="editHandle(true)">编 辑</el-button>
      <div v-else>
        <el-button class="primary"
          @click="addNode">新 增</el-button>
        <el-button type="warning"
          class="delete"
          @click="removeNode">删 除</el-button>
        <el-button type="success"
          class="save"
          @click="saveMind">保 存</el-button>
      </div>
    </div>

    <js-mind ref="jsMind"
      id="jsmind"
      height="100%"
      :values="mind"
      :options="options" />

    <!-- 新增节点 -->
    <AddMindNode v-if="visibleAddNode"
      :visible.sync="visibleAddNode"
      :selectedNode="selectedNode"
      :getNodeData="getNodeData"
      @updataHandle="updataAddNodeHandle" />
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { Loading, Confirm } from '@/decorators'
import AddMindNode from './AddMindNode.vue'
import jm from 'vue-jsmind'
Vue.use(jm)

@Component({
  components: {
    AddMindNode
  }
})
export default class extends Vue {
  private isEdit = false
  private loading = false
  private visibleAddNode = false
  private selectedNode = {}

  // 获取所有节点
  get getNodeData() {
    return this.jm.get_data()
  }

  // 思维导图实例
  private jm: any = null

  // 思维导图基础配置
  private options = {
    editable: false,
    theme: 'danger',
    mode: 'side'
  }

  // 定义思维导图的名称、作者、版本、加载数据等配置信息
  private mind = {
    meta: {
      name: '龙猫',
      author: '<EMAIL>',
      version: '1.0'
    },
    format: 'node_array',
    data: [
      { id: 'root', isroot: true, topic: '金华市国有资本运营有限公司' },

      { id: 'root1', parentid: 'root', topic: '金华市xxx有限公司', direction: 'right' },
      { id: 'root1_1', parentid: 'root1', topic: 'xxxx有限公司' },
      { id: 'root1_2', parentid: 'root1', topic: 'xxxx有限公司' },

      { id: 'root2', parentid: 'root', topic: '金华市xxx有限公司', direction: 'right' },

      { id: 'root3', parentid: 'root', topic: '金华市xxx有限公司', direction: 'right' }
    ]
  }

  // 组件初始化
  private mounted() {
    this.jm = (this.$refs.jsMind as any).jm
    this.jm.show(this.mind)
  }

  // 启动编辑、禁用模式
  private editHandle(isEdit = false) {
    this.loading = true

    if (isEdit) {
      this.jm.enable_edit()
      this.isEdit = true
    } else {
      this.jm.disable_edit()
      this.isEdit = false
    }

    setTimeout(() => {
      this.loading = false
    }, 1000)
  }

  // 新增节点：前置判断
  private addNode() {
    let selectedNode = this.jm.get_selected_node()
    if (!selectedNode) {
      this.$message({
        type: 'warning',
        message: '请先选择一个节点!'
      })
      return
    }

    this.selectedNode = selectedNode
    this.visibleAddNode = true
  }

  // 新增节点：调用
  private updataAddNodeHandle(data: any[]) {
    if (!Array.isArray(data)) return

    data.forEach((item) => {
      let nodeid = item.id
      let topic = item.name
      this.jm.add_node(this.selectedNode, nodeid, topic)
    })
  }

  // 删除节点：前置判断
  private removeNode() {
    let selectedNode = this.jm.get_selected_node()
    if (!selectedNode) {
      this.$message({
        type: 'warning',
        message: '请先选择一个节点!'
      })
      return
    }

    this.updataRemoveNodeHandle(selectedNode)
  }

  // 删除节点：调用
  @Confirm({
    content: '是否删除该节点？'
  })
  private updataRemoveNodeHandle(selectedNode: any) {
    this.jm.remove_node(selectedNode)
    this.$message.success('删除成功')
  }

  // 保存
  @Loading('loading')
  private async saveMind() {
    let data = this.getNodeData
    this.editHandle(false)
  }
}
</script>

<style scoped lang="scss">
.atlas-mind-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  .options-box {
    position: absolute;
    right: 0;
    top: 0;
    z-index: 10;
    .primary {
      color: #fff;
      background: #409eff;
      border: 1px solid #409eff;
    }
  }
}
</style>
