<template>
  <div class="grid">
    <el-table v-loading="(defaultLoading && loading) || pageLoading"
      ref="grid"
      v-bind="$attrs"
      v-on="$listeners"
      :element-loading-background="loadingBackground"
      :border="border"
      :stripe="stripe"
      :height="height"
      :data="$attrs.data || remoteData">
      <el-empty slot="empty"
        :image-size="120"
        description="暂无数据" />

      <el-table-column v-if="$attrs['show-selection']"
        width="50"
        align="center"
        type="selection" />
      <el-table-column v-if="$attrs['show-index']||true"
        type="index"
        :fixed="$attrs['show-index-fixed']"
        align="center"
        width="60">
        <template slot="header">
          <span>序号</span>
        </template>
      </el-table-column>
      <template v-for="(column, columnIndex) in $attrs.columns">
        <el-table-column
          v-if="column.filterOptions && (column.hasOwnProperty('hidden') ? !column.hidden : true)"
          v-bind="column"
          :min-width="column.minWidth || 120"
          class-name="column-filter"
          :show-overflow-tooltip="column.overflowTooltip || overflowTooltip"
          :key="column.prop + column.label">
          <template v-if="column.searchKey"
            slot="header">
            <el-menu menu-trigger="hover"
              class="grid-filter-menu"
              mode="horizontal"
              :collapse-transition="false">
              <el-submenu popper-class="grid-filter-popper"
                index="sub-index"
                :popper-append-to-body="true">
                <template slot="title">
                  <span>
                    <span :id="column.prop + column.searchKey"
                      :ref="column.prop">
                      {{ column.default ? getFilterDefaultLabel(column) : column.label }}
                    </span>
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </span>
                </template>
                <el-menu-item @click.native="handleFilterChange(null, column)">
                  {{ column.menuDefault || '全部' }}
                </el-menu-item>
                <template v-for="(item, index) of column.filterOptions">
                  <el-menu-item v-if="item.children"
                    :key="item.value + item.label"
                    :index="'index-' + index">
                    <el-submenu :index="'sub' + index"
                      @click.native="handleFilterChange(item.label, column, item.value)">
                      <template slot="title">
                        {{ item.label }}
                      </template>
                      <el-menu-item v-for="(subItem, subIndex) of item.children"
                        :key="subItem.value + subItem.label"
                        :index="'index-' + index + '-' + subIndex"
                        @click.native="handleFilterChange(subItem.label, column, subItem.value)">
                        {{ subItem.label }}
                      </el-menu-item>
                    </el-submenu>
                  </el-menu-item>
                  <el-menu-item v-else
                    :key="item.value + item.label"
                    :index="'index-' + index"
                    @click.native="handleFilterChange(item.label, column, item.value)">
                    {{ item.label }}
                  </el-menu-item>
                </template>
              </el-submenu>
            </el-menu>
          </template>
          <template slot-scope="scope">
            <div v-if="!column.renderStatus">
              <slot v-if="column.slotName"
                :name="column.slotName"
                :row="scope.row" />
              <span v-else>{{ scope.row[column.prop] || '-' }}</span>
            </div>
            <el-tag
              v-else-if="column.renderStatus && column.renderStatus(scope.row[column.prop], scope.row).length <= 2"
              :disable-transitions="true"
              :type="column.renderStatus(scope.row[column.prop], scope.row)[0]">
              {{ column.renderStatus(scope.row[column.prop], scope.row)[1] }}
              <slot :name="column.slotName"
                :row="scope.row" />
            </el-tag>
            <el-tooltip
              v-else-if="column.renderStatus && column.renderStatus(scope.row[column.prop], scope.row).length === 3"
              placement="top-start"
              :disabled="column.renderStatus(scope.row[column.prop], scope.row)[2] ? false : true">
              <div slot="content">{{ column.renderStatus(scope.row[column.prop], scope.row)[2] }}
              </div>
              <el-tag :disable-transitions="true"
                :type="column.renderStatus(scope.row[column.prop], scope.row)[0]">
                {{ column.renderStatus(scope.row[column.prop], scope.row)[1] }}
                <slot :name="column.slotName"
                  :row="scope.row" />
              </el-tag>
            </el-tooltip>
            <div v-else>
              <slot :name="column.slotName"
                :row="scope.row" />
            </div>
          </template>
        </el-table-column>
        <el-table-column v-else-if="!column.hidden"
          :key="columnIndex"
          :show-overflow-tooltip="column.overflowTooltip || overflowTooltip"
          :class-name="column.labelAlign ? `column--${column.labelAlign}` : ''"
          :min-width="column.minWidth || 120"
          v-bind="column">
          <template slot-scope="scope">
            <el-tag v-if="column.type === 'status' && column.renderStatus"
              :disable-transitions="true"
              :type="column.renderStatus(scope.row[column.prop])[0]">
              {{ column.renderStatus(scope.row[column.prop])[1] }}
            </el-tag>
            <slot v-else-if="column.slotName"
              :name="column.slotName"
              :row="scope.row"
              :index="scope.$index" />
            <tooltip v-else-if="!$attrs['default-expand-all'] && column.showTooltip"
              placement="top-start"
              :content="
                column.render
                  ? column.render(scope.row)
                  : scope.row[column.prop] === undefined || scope.row[column.prop] === ''
                  ? '-'
                  : scope.row[column.prop]
              "></tooltip>
            <span v-else-if="$attrs['default-expand-all']">
              {{
                column.render
                  ? column.render(scope.row)
                  : scope.row[column.prop] === undefined || scope.row[column.prop] === ''
                  ? '-'
                  : scope.row[column.prop]
              }}
            </span>

            <span v-else>
              {{
                column.render
                  ? column.render(scope.row)
                  : scope.row[column.prop] === undefined || scope.row[column.prop] === ''
                  ? '-'
                  : scope.row[column.prop]
              }}
            </span>
          </template>
        </el-table-column>
      </template>
    </el-table>

    <div class="footer">
      <div class="left">
        <slot name="bottomLeft" />
      </div>
      <el-pagination clsss="pagination"
        v-if="$attrs['show-pagination']"
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="pagination.pageNo"
        :page-size="pagination.pageSize"
        :total="pagination.totalPage"
        :page-sizes="pageSizes"
        ref="page"
        :style="{
          flex: !!$slots['bottomLeft'] ? 'unset' : 1
        }"
        v-bind="$props"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Emit } from 'vue-property-decorator'
import { ElTable } from 'element-ui/types/table'
import { ElTableColumn } from 'element-ui/types/table-column'
import request from '@/api/request'
import Tooltip from './components/Tooltip/index.vue'

export interface Grid {
  refresh(isCurrent?: boolean): void // 刷新第一页数据
  toggleRowExpansion(row: ElTableColumn, expanded: boolean): void
  toggleRowSelection(row: any): void
}

export interface TagStatus {
  type: string
  label: string
}

@Component({
  components: { Tooltip }
})
export default class extends Vue implements Grid {
  @Prop(String) private remoteUrl?: string // remote table data url
  @Prop(Object) private searchParams!: Record<string, any>
  @Prop({ default: () => [10, 30, 50] }) private pageSizes!: number[]
  @Prop({
    default: () => {
      return {}
    }
  })
  private searchAdvancedParams?: object

  @Prop({ default: 'POST' }) private method?: string
  @Prop({ default: true }) private stripe?: boolean
  @Prop({ default: false }) private isAdvanced?: boolean
  @Prop({ default: 'data.records' }) private listKey!: string
  @Prop({ default: true }) private border?: boolean
  @Prop({ default: true }) private defaultLoading!: boolean
  @Prop({ default: true }) private overflowTooltip!: boolean
  @Prop({
    default: () => {
      return {}
    }
  })
  private defaultKeyValues?: any // 默认初始化key-value
  @Prop({
    default: '100%'
  })
  private height!: string | number // height传0，则grid自适应高度
  @Prop({ default: '' }) private loadingBackground?: string

  private loading = false
  private pageLoading = false // 点击分页按钮，触发loading
  private remoteData: Array<object> = []
  private pagination = {
    pageNo: 1,
    pageSize: 20,
    totalPage: 1
  }
  private cachedFilterRefs: {
    [key: string]: {
      dom?: HTMLElement
      label?: string
    }
  } = {}

  // 开始请求数据
  @Emit('onFetchData') onFetchDataTrigger() {
    //
  }

  @Emit('loadCompleted') loadComplete(data: any = {}) {
    return data
  } // 表格数据请求相应后触发父组件事件

  @Emit('onFilter')
  private onFilter(key: string, val: number) {
    return {
      key,
      val,
      params: this.searchParams
    }
  }

  private created() {
    this.$set(this.pagination, 'pageSize', this.pageSizes[0] || 20)
  }

  private mounted() {
    this.fetchHandler()
  }

  private handleSizeChange(size: number) {
    this.pagination.pageSize = size
    this.pageLoading = true
    this.fetchHandler()
  }

  private handleCurrentChange(pageNo: number) {
    this.pagination.pageNo = pageNo
    this.pageLoading = true
    this.fetchHandler()
  }

  private async fetchHandler() {
    if (!this.remoteUrl) return
    this.loading = true
    let params = {}
    if (this.isAdvanced) {
      params = Object.assign(this.searchAdvancedParams || {}, {
        current: this.pagination.pageNo,
        size: this.pagination.pageSize
      })
    } else {
      params = Object.assign(this.searchParams || {}, {
        current: this.pagination.pageNo,
        size: this.pagination.pageSize
      })
    }

    try {
      this.onFetchDataTrigger()
      let res = (await request(this.remoteUrl, params, {
        method: this.method
      })) as any
      if (res.success && res.data) {
        if (this.listKey.indexOf('.') !== -1) {
          let d = res
          this.listKey.split('.').forEach((vv: string) => {
            try {
              d = d[vv]
            } catch (e) {
              d = []
            }
          })
          if (this.defaultKeyValues && Object.keys(this.defaultKeyValues).length > 0) {
            for (let index in this.defaultKeyValues) {
              for (let item of d) {
                item[index] = this.defaultKeyValues[index]
              }
            }
          }
          this.remoteData = d
          this.pagination.totalPage = res.data.total
        } else {
          // 暂未处理需要分页的情况，等系统有这种格式的返回值时再处理
          this.remoteData = Object.prototype.toString.call(res[this.listKey]) === '[object Object]' ? [] : res[this.listKey]
        }
        this.$emit('onLoaded', res.data)
      } else {
        this.clearAll()
      }

      setTimeout(() => {
        if (this.$refs.grid) {
          ;(this.$refs.grid as ElTable).doLayout()
        }
        this.loading = false
      }, 0)

      this.pageLoading = false
      this.loadComplete(res.data)
    } catch (e) {
      this.loading = false
      this.pageLoading = false
      this.clearAll()
      this.loadComplete()
    }
  }

  refresh(isCurrent = false) {
    if (!isCurrent) {
      this.pagination.pageNo = 1
      let ref
      for (let key in this.cachedFilterRefs) {
        ref = this.cachedFilterRefs[key].dom
        if (ref) {
          ref.innerHTML = this.cachedFilterRefs[key].label as string
        }
      }
      this.cachedFilterRefs = {}
    }
    // this.remoteData = []
    setTimeout(() => {
      this.fetchHandler()
    }, 0)
  }

  public clearAll() {
    this.remoteData = []
    this.pagination = {
      pageNo: 1,
      pageSize: 20,
      totalPage: 1
    }
  }

  private handleFilterChange(label: string, column: any, val: number) {
    if (!this.searchParams) return

    // 用户一旦更改筛选条件，则清空默认值
    delete column.default

    let searchKey = column['searchKey']
    this.searchParams[searchKey] = label ? val : ''
    let ref = this.cachedFilterRefs[column.prop]
    if (!ref) {
      let refs = this.$refs[column.prop] as Array<any>
      ref = this.cachedFilterRefs[column.prop] = {
        // dom: this.$refs[column.prop][refs.length - 1],
        dom: document.getElementById(column.prop + column.searchKey) || undefined,
        label: column.label
      }
    } else if (ref.dom && ref.dom.innerHTML === label) {
      return
    }
    if (ref.dom) {
      ref.dom.innerHTML = label && column.prop ? label : column.label
    }
    this.pagination.pageNo = 1
    this.onFilter(column.searchKey, val)
    this.refresh(true)
  }

  private getFilterDefaultLabel(column: { label: string; default: any; filterOptions: Array<{ value: any; label: string }> }) {
    if (!column.filterOptions) {
      return column.label
    }
    let target = column.filterOptions.find((item) => {
      return item.value === column.default
    })

    return target ? target.label : column.label
  }

  public clearSelection() {
    ;(this.$refs.grid as ElTable).clearSelection()
  }

  public toggleAllSelection() {
    ;(this.$refs.grid as ElTable).toggleAllSelection()
  }

  public toggleRowSelection(row: any) {
    ;(this.$refs.grid as ElTable).toggleRowSelection(row, true)
  }

  public updateRowData(rowIndex: number, rowData: object) {
    if (this.remoteData[rowIndex]) {
      this.remoteData[rowIndex] = rowData as any
      this.$set(this.remoteData, rowIndex, rowData)
    }
  }

  private isPopoverDisable(refName: string) {
    if (!this.$refs[refName]) {
      return true
    }

    return (this.$refs[refName] as any[])[0].offsetHeight / 20 <= 1
  }

  public toggleRowExpansion(row: ElTableColumn, expanded: boolean) {
    ;(this.$refs.grid as ElTable).toggleRowExpansion(row, expanded)
  }
}
</script>

<style lang="scss" scoped>
.grid {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  box-shadow: 0 0 10px #f0f2f533;
  border-radius: 8px;
  ::v-deep .el-table {
    border-radius: 4px;
  }

  ::v-deep .el-button--small {
    font-size: 14px;
  }

  // 解决table行固定后覆盖滚动条问题
  // ::v-deep .el-table__fixed,
  // ::v-deep .el-table__fixed-right {
  //   height: auto !important;
  //   bottom: 8px !important;
  // }

  ::v-deep .column--center {
    .cell {
      text-align: center !important;
    }
  }
  ::v-deep .column--right {
    .cell {
      text-align: right !important;
    }
  }
  ::v-deep .column--left {
    .cell {
      text-align: left !important;
    }
  }
  ::v-deep .el-table__header {
    position: relative;
    &::before {
      content: '';
      position: absolute;
      bottom: 0px;
      left: 20px;
      right: 20px;
      height: 2px;
      background: #f0f2f5;
      z-index: 1;
    }
  }

  .has-border {
    ::v-deep .el-table__cell {
      border: none;
    }

    ::v-deep td.el-table__cell:last-child,
    ::v-deep th.el-table__cell.is-leaf:last-child {
      border-right: none;
    }

    ::v-deep .el-table__header th .cell {
      color: #515151;
      font-weight: bold;
      .el-submenu__title {
        color: #515151;
      }
    }

    ::v-deep .el-table__header th:nth-child(1) {
      border-left: none;
      .cell {
        padding-left: 20px !important;
      }
    }

    ::v-deep .el-table__fixed-right-patch {
      box-sizing: border-box;
    }
  }

  .el-tag {
    width: 60px;
    height: 20px;
    border: none;
    padding: 0;
    text-align: center;
    &.el-tag--danger {
      background: rgba(245, 108, 108, 0.05);
      color: #f56c6c;
    }

    &.el-tag--success {
      background: rgba(70, 128, 255, 0.05);
      color: #1f66ff;
    }

    &.el-tag--info {
      background: rgba(96, 98, 102, 0.05);
      color: #515151;
    }

    &.el-tag--warning {
      background: rgba(96, 98, 102, 0.05);
      color: #303133;
    }
  }

  .footer {
    background: #fff;
    padding: 0 18px;
    display: flex;
    justify-content: space-between;
    .left {
      padding-left: 6px;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      & > div {
        display: flex;
        align-items: center;
      }
    }

    .el-pagination {
      width: 70%;
      text-align: right;
      padding: 12px 0 12px 0;
      margin: 0;
    }
  }
}
</style>

<style lang="scss">
.grid {
  .el-table {
    th {
      background: #ffffff;
      color: #303133;
      font-size: 14px;

      .grid-filter-menu {
        border-bottom: none;

        .el-submenu__title {
          color: #303133;
          line-height: normal;
          height: auto;
          padding: 0;
          border: none;
        }

        .el-submenu__icon-arrow {
          display: none;
        }

        .el-icon-arrow-down {
          position: relative;
          &:before {
            content: '';
            position: absolute;
            border-top: 3px solid #515151;
            border-left: 3px solid transparent;
            border-right: 3px solid transparent;
            height: 0;
            top: 0px;
            bottom: 0;
            left: 0;
            width: 0;
            margin: auto;
          }
        }
      }
    }

    td {
      font-size: 14px;
      padding: 10px 0;
      height: 70px;
      &:first-child {
        padding-left: 10px !important;
      }

      .cell {
        position: relative;
        line-height: normal;
        color: #303133;
        max-height: 32px;
        line-height: 15px;
        .grid-ovflow-popover {
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          white-space: normal;
        }
      }
    }
  }

  .el-pagination {
    .btn-prev,
    .btn-next,
    .el-pager li {
      background: transparent;
    }

    .el-pager li {
      min-width: 30px;
    }
    .el-pagination__editor.el-input {
      width: 38px;
    }
    .el-select .el-input {
      width: 86px;
    }
  }
}

.grid-filter-popper {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  max-height: 500px;

  .el-menu--popup {
    min-width: 80px;
    .el-menu-item {
      text-align: center;
      height: 30px;
      line-height: 30px;
      font-size: 12px;
      &:hover {
        color: #1f66ff;
      }
      &.is-active {
        color: #515151;
      }
    }
  }
  .el-menu--popup-bottom-start {
    margin-top: 0;
  }
  .el-submenu.is-active > .el-submenu__title {
    color: #515151;
  }

  .el-submenu__icon-arrow {
    right: -4px;
    margin-top: -6px;
  }

  .el-submenu {
    .el-menu-item {
      min-width: 80px;
    }

    .el-menu {
      margin-left: 10px;
    }
  }

  .el-submenu__title {
    font-size: 12px;
    height: 30px;
    line-height: 30px;
  }
}
</style>
