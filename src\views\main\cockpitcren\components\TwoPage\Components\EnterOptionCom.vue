/* 每日运营动态：详情 */

<template>
  <section v-loading="loading"
    element-loading-background="rgba(0, 0, 0, 0)"
    class="cockpit-enter-option-wrap">
    <!-- 头部tabs切换 -->
    <div class="tabs-box">
      <span v-for="(item,index) of tabsList"
        :key="index"
        :class="{'active':+tabsActive === index+1}"
        @click="onChangeTabs(item.value)">{{item.name|| '——'}}</span>
    </div>

    <!-- echarts 视图 -->
    <div class="refEcharts"
      ref="refEcharts" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { colorSixList, echartConfigure } from '@/views/main/cockpitcren/baseData'
import { getEntExtHistory } from '@/api/cockpit'
import * as echarts from 'echarts'

type EChartsOption = echarts.EChartsOption

@Component({
  components: {}
})
export default class extends Vue {
  @Prop() private type!: string
  @Prop() private yesterday!: string

  private loading = false
  private tabsActive = '1'
  private detailData: any[] = []
  private tabsList = [
    {
      name: '近一周',
      value: '1'
    },
    {
      name: '近一个月',
      value: '2'
    },
    {
      name: '近一年',
      value: '3'
    }
  ]

  // echarts 视图数据
  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private seriesData: any[] = []
  private legendData: string[] = []
  private xAxisData: string[] = []
  private yAxisData: any[] = []
  private option: EChartsOption = {}

  // 初始化
  private mounted() {
    this.chartDom = this.$refs.refEcharts as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    this.filterData()
  }

  // 渲染数据
  @Loading('loading')
  private async filterData() {
    let { data } = await getEntExtHistory({
      date: this.yesterday,
      type: this.type,
      period: this.tabsActive
    })

    // 车辆检测：排除掉为 0 的数据
    if (+this.type === 4 && Array.isArray(data.list) && data.list.length) {
      data.list.forEach((item: any) => {
        if (Array.isArray(item.list) && item.list.length) {
          item.list = item.list.filter((itemList: any) => {
            return +itemList.value
          })
        }
      })
    }
    // end

    this.detailData = data.list || []

    // 组装数据
    let legendData: string[] = []
    let xAxisData: string[] = []
    let yAxisData: any[] = []
    let seriesData: any[] = []

    this.detailData.forEach((item, index) => {
      legendData.push(item.name)

      let list: string[] = []
      let values: number[] = []

      if (Array.isArray(item.list) && item.list.length) {
        values = []
        item.list.forEach((itemData: { label: string; value: number }) => {
          list.push(itemData.label)
          values.push(+itemData.value)
        })
        xAxisData = list
      }

      // 判断图表左侧是否需要展示刻度线
      let splitLineShow = true
      if (this.detailData.length === 1) {
        splitLineShow = true
      } else {
        splitLineShow = index === this.detailData.length - 1 ? false : true
      }
      // end

      yAxisData.push({
        type: 'value',
        name: item.unit,
        nameTextStyle: {
          align: 'right'
        },
        axisLabel: {
          fontSize: 12
        },
        axisLine: {
          lineStyle: {
            color: '#5db0ea'
          }
        },
        splitLine: {
          show: splitLineShow,
          lineStyle: {
            color: 'rgba(0, 103, 177, 0.4)'
          }
        }
      })

      // 判断图表右侧是否需要展示尺度
      let yAxisIndex: number | null = 0
      if (this.detailData.length === 1) {
        yAxisIndex = 0
      } else {
        yAxisIndex = index === this.detailData.length - 1 ? 1 : null
      }
      // end

      seriesData.push({
        name: item.name,
        type: 'line',
        symbol: 'circle',
        yAxisIndex: yAxisIndex,
        tooltip: {
          valueFormatter: function (value: number) {
            return `${value} ${item.unit}`
          }
        },
        data: values
      })
    })

    this.legendData = legendData
    this.xAxisData = xAxisData
    this.yAxisData = yAxisData
    this.seriesData = seriesData
    this.initEcharts()
  }

  // 渲染 echarts 视图
  private initEcharts() {
    let legendData = this.legendData
    let xAxisData = this.xAxisData
    let yAxisData = this.yAxisData
    let seriesData = this.seriesData
    let tooltipData: any = Object.assign(
      {
        trigger: 'axis'
      },
      echartConfigure.tooltipBody
    )

    this.option = {
      color: colorSixList,
      tooltip: tooltipData,
      legend: {
        textStyle: {
          color: '#fff'
        },
        data: legendData
      },
      grid: {
        top: '14%',
        left: '0%',
        right: '0%',
        bottom: '0%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          axisLabel: {
            fontSize: 12,
            margin: 14,
            align: 'center',
            formatter: (parmes: any) => {
              let str = parmes.split('-')
              return `{a|${str[0]}}/{b|${str[1]}/${str[2]}}`
            },
            rich: {
              a: {
                fontSize: 13,
                color: '#fff'
              },
              b: {
                fontSize: 13,
                color: '#fff'
              }
            }
          },
          axisLine: {
            lineStyle: {
              color: '#5db0ea'
            }
          },
          data: xAxisData
        }
      ],
      yAxis: yAxisData,
      series: seriesData
    }

    this.myChart && this.myChart.setOption(this.option, true)
  }

  // tabs 切换
  private onChangeTabs(value: string) {
    this.tabsActive = value
    this.filterData()
  }
}
</script>

<style scoped lang="scss">
.cockpit-enter-option-wrap {
  position: relative;
  width: 100%;
  height: 500px;
  padding-bottom: 20px;
  .refEcharts {
    width: 100%;
    height: 100%;
  }

  .tabs-box {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 10;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    span {
      color: #fff;
      padding: 0 0 4px 0;
      margin-right: 20px;
      cursor: pointer;
      border-bottom: 2px solid #062879;
    }
    .active {
      color: #00e8ff;
      border-bottom: 2px solid #00e8ff;
    }
  }
}
</style>