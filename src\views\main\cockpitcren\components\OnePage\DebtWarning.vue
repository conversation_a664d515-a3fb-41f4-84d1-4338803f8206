/* 智慧预警 */

<template>
  <section class="debt-warning-wrap">
    <TitleCom title="智慧预警"
      module="DebtWarning" />

    <div v-loading="loading"
      class="content-box">
      <ul ref="tableList"
        class="ul-box"
        @mouseenter="mouseenterHandle"
        @mouseleave="mouseleaveHandle">
        <li v-for="(item, index) of tableData"
          :key="index"
          :class="[{'reds':+item.warningLevel === 1}, {'yellows':+item.warningLevel === 2}, {'blues':+item.warningLevel === 3}]"
          @click="openDetail(item)">
          <i />
          <span class="text-ellipsis-1">{{item.parentMetricsCodeDesc}}</span>
          <span class="text-ellipsis-1">{{item.ruleName}}</span>
        </li>
      </ul>
    </div>

    <!-- 详情弹窗 -->
    <CockiptDialog v-if="visibleDetail"
      :visible.sync="visibleDetail"
      :year="+year+1"
      :code="tabActive.code"
      :closeModal="false"
      :columns="columns"
      :remoteUrl="remoteUrl"
      :searchParams="searchParams"
      title="智慧预警"
      unit="" />

    <!-- 预警消息提示 -->
    <div class="new-debtwargin-box"
      :class="[{'move-debtwargin-enter':newDebtWarning}, {'move-debtwargin-leave':!newDebtWarning}]">
      <div class="content yellows"
        :class="{'reds':+newDebtWarningData.warningLevel === 1}">
        <div class="til">
          <img v-if="+newDebtWarningData.warningLevel === 1"
            src="@/views/main/cockpitcren/images/yujing_red.png" />
          <img v-if="+newDebtWarningData.warningLevel === 2"
            src="@/views/main/cockpitcren/images/yujing_yellow.png" />
          <img v-if="+newDebtWarningData.warningLevel === 3"
            src="@/views/main/cockpitcren/images/yujing_blue.png" />
        </div>
        <div class="text">
          <p>{{newDebtWarningData.companyName}}</p>
          <p>{{newDebtWarningData.parentMetricsCodeDesc}}</p>
          <p>{{newDebtWarningData.ruleName}}</p>
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { getPrewarningEvents } from '@/api/prewarning'
import { Loading } from '@/decorators'
import { isWindowFull } from '@/utils'
import TitleCom from '@/views/main/cockpitcren/components/Public/TitleCom.vue'
import CockiptDialog from '@/views/main/cockpitcren/components/Public/CockiptDialog.vue'

type typeTabItem = {
  id: string
  code: string
  name: string
}

@Component({
  components: {
    TitleCom,
    CockiptDialog
  }
})
export default class extends Vue {
  private year = ''
  private timer: any
  private debtTimer: any
  private listHeight = 80
  private currentIndex = 0
  private middleIndex = 2
  private loading = false
  private listDom: any = {}
  private tableData: any[] = []
  private tabActive: typeTabItem = {
    id: '',
    code: '',
    name: ''
  }

  // 详情表格数据
  private newDebtWarning = false
  private visibleDetail = false
  private newDebtWarningData = {}
  private remoteUrl = '/fht-monitor/ds/ewm/unDisposed/page'
  private searchParams = {
    year: '',
    status: 0,
    companyCode: 0
  }
  private columns = [
    {
      prop: 'createTime',
      label: '预警时间',
      minWidth: 110
    },
    {
      prop: 'eventNo',
      label: '预警编号'
    },
    {
      prop: 'ruleName',
      label: '预警名称',
      minWidth: 100
      //slotName: 'slotDebtRuleName'
    },
    {
      label: '预警等级',
      slotName: 'slotDebtAlertLevel'
    },
    {
      prop: 'parentMetricsCodeDesc',
      label: '所属业务'
    },
    {
      prop: 'corpName',
      label: '企业名称',
      minWidth: 100
    },
    {
      prop: 'eventDesc',
      label: '预警描述',
      minWidth: 140
    }
  ]

  // 判断窗口是否处于全屏模式
  get getIsWindowFull() {
    return isWindowFull
  }

  // 组件初始化
  private mounted() {
    this.listDom = this.$refs['tableList'] as HTMLDivElement
    this.year = String((this as any).$moment(new Date()).add('year', 0).format('YYYY') - 1)
    this.initData()

    // this.debtTimer = setInterval(() => {
    //   this.initData()
    // }, 10000)

    // 中间各tabs切换触发数据更新
    this.$bus.$on('BusTabsChange', (data: typeTabItem) => {
      this.tabActive = data
    })

    // 时间改变时触发数据更新
    this.$bus.$on('BusYearTimeChange', (year: string) => {
      this.year = year
    })
  }

  // 初始化数据
  @Loading('loading')
  private async initData() {
    let { data } = await getPrewarningEvents({
      current: 1,
      size: 100,
      metricsId: 4,
      year: +this.year + 1,
      companyCode: +this.tabActive.code
    })

    // 判断是否有新的预警数据过来
    if (this.tableData.length && Array.isArray(data.records) && data.records.length > this.tableData.length) {
      this.newDebtWarning = true
      this.newDebtWarningData = data.records[0]

      setTimeout(() => {
        this.newDebtWarning = false
      }, 8000)
    }

    this.tableData = data.records || []
    this.scrollTable()
  }

  // 开启定时器，让 tablist 滚动起来
  private scrollTable() {
    clearInterval(this.timer)
    let dataLen = this.tableData.length
    if (!dataLen || dataLen <= this.middleIndex) return

    this.timer = setInterval(() => {
      this.currentIndex++
      if (this.currentIndex > this.middleIndex && this.currentIndex < dataLen) {
        this.listDom.scrollTo(0, (this.currentIndex - this.middleIndex) * this.listHeight)
      } else if (this.currentIndex === dataLen) {
        this.currentIndex = 0
        this.listDom.scrollTo(0, 0)
      }
    }, 8000)
  }

  // 列表鼠标移入，停止动画
  private mouseenterHandle() {
    clearInterval(this.timer)
    this.timer = null
  }

  // 列表鼠标移出，开始动画
  private mouseleaveHandle() {
    this.scrollTable()
  }

  // 打开详情弹窗
  private openDetail(item: any) {
    this.searchParams.companyCode = +this.tabActive.code
    this.searchParams.year = this.year
    this.visibleDetail = true
  }

  // 清除定时器，还原相关数据
  private clearTimer() {
    clearInterval(this.debtTimer)
    clearInterval(this.timer)
    this.debtTimer = null
    this.timer = null
    this.currentIndex = 0
    this.listDom.scrollTo(0, 0)
  }

  // 组件销毁
  private destroyed() {
    this.clearTimer()
  }
}
</script>

<style scoped lang="scss">
.debt-warning-wrap {
  position: relative;

  @keyframes keyYellowMove {
    0% {
      box-shadow: 0 0 50px inset #ffbd20;
    }
    50% {
      box-shadow: 0 0 10px inset #ffbd20;
    }
    100% {
      box-shadow: 0 0 50px inset #ffbd20;
    }
  }

  @keyframes keyRedMove {
    0% {
      box-shadow: 0 0 50px inset #ff4a20;
    }
    50% {
      box-shadow: 0 0 10px inset #ff4a20;
    }
    100% {
      box-shadow: 0 0 50px inset #ff4a20;
    }
  }

  @keyframes keyBlueMove {
    0% {
      box-shadow: 0 0 50px inset #409eff;
    }
    50% {
      box-shadow: 0 0 10px inset #409eff;
    }
    100% {
      box-shadow: 0 0 50px inset #409eff;
    }
  }

  .content-box {
    position: relative;
    .ul-box {
      height: 280px;
      margin-top: -10px;
      overflow-y: auto;
      scroll-behavior: smooth;
      &::-webkit-scrollbar {
        display: none;
      }
      li {
        display: flex;
        align-items: center;
        padding: 10px 14px;
        font-size: 34px;
        margin-bottom: 14px;
        border-radius: 6px;
        cursor: pointer;
        i,
        span {
          margin-right: 20px;
        }
        &:nth-last-child(1) {
          margin-bottom: 0;
        }
        & i:nth-child(1) {
          width: 50px;
          height: 46px;
          margin-left: 23px;
        }
        & span:nth-child(2) {
          width: 24%;
        }
        & span:nth-child(3) {
          flex: 1;
          margin-right: 10px;
        }
        & span:nth-child(4) {
          flex: 1;
          margin-right: 0;
        }
      }
      .reds {
        box-shadow: 0 0 40px inset #fb3f3f;
        animation: keyRedMove 5s infinite;
        i {
          background: url('../../images/yujing_red.png') no-repeat center center;
          background-size: 100% 100%;
        }
      }
      .yellows {
        box-shadow: 0 0 40px inset #e6a23c;
        animation: keyYellowMove 5s infinite;
        i {
          background: url('../../images/yujing_yellow.png') no-repeat center center;
          background-size: 100% 100%;
        }
      }
      .blues {
        box-shadow: 0 0 40px inset #409eff;
        animation: keyBlueMove 5s infinite;
        i {
          background: url('../../images/yujing_blue.png') no-repeat center center;
          background-size: 100% 100%;
        }
      }
    }
  }
}

@keyframes keyDebtwarginLeaveMove {
  0% {
    left: 50%;
    width: inherit;
    height: inherit;
    opacity: 1;
  }
  100% {
    left: 84%;
    width: 0;
    height: 0;
    opacity: 0;
  }
}

.new-debtwargin-box {
  position: fixed;
  top: 50%;
  left: 84%;
  z-index: 9999;
  width: 0;
  height: 0;
  opacity: 0;
  transform: translate(-50%, -50%);
  color: #fff;
  font-size: 50px;
  background: #00257a;
  border-radius: 10px;
  overflow: hidden;
  transition: 3s ease;

  .yellows {
    box-shadow: 0 0 80px inset #ffbd20;
  }
  .reds {
    box-shadow: 0 0 80px inset #ff4a20;
  }
  .content {
    padding: 40px;

    box-sizing: border-box;
    p {
      margin: 0;
    }
    img {
      width: 80px;
    }

    .til {
      text-align: center;
    }
    .text {
      text-align: center;
      p {
        line-height: 80px;
      }
    }
  }
}

.move-debtwargin-enter {
  left: 50%;
  width: initial;
  height: initial;
  opacity: 1;
}

.move-debtwargin-leave {
  animation: keyDebtwarginLeaveMove 2s ease;
}
</style>

