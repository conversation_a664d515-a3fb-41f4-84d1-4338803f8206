<template>
  <el-container class="container-wrap"
    direction="vertical">
    <!-- 头部 -->
    <el-header v-show="getHasHeader">
      <Header :isCollapse="isCollapse"
        @changeIsMain="changeIsMain" />
    </el-header>

    <el-container class="content-box">
      <Aside v-show="getHasAside"
        :isCollapse="isCollapse" />

      <div class="logo-box"
        :class="{'logo-collapse-box' : isCollapse}">
        <div class="logoms">
          <div v-if="!isCollapse"
            class="til"
            :class="{'tilms': !isShowCollapse}">金华市国资委数智监管系统</div>

          <!-- 侧边栏缩放按钮 -->
          <i v-if="isShowCollapse"
            class="icon-navicon collapse"
            :class="{'collapsems' : isCollapse}"
            @click="setCollapse" />
        </div>
      </div>

      <!-- 内容 -->
      <Main v-if="isMain" />
    </el-container>
  </el-container>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { removeLocalStorage, removeToken, setInvalidTokenTime, getInvalidTokenTime, getStore, setStore } from '@/utils/cache'
import { refreshToken } from '@/api/public'
import { getToken } from '@/components/Uploader/config'
import { MessageBox } from 'element-ui'
import Header from './header.vue'
import Aside from './aside.vue'
import Main from './main.vue'

@Component({
  components: {
    Aside,
    Header,
    Main
  }
})
export default class Container extends Vue {
  private isMain = false
  private isCockpit = false
  private isWindowFull = false
  private isCollapse = false

  // 跟长时间未操作页面、token刷新相关数据
  private timer: any = null
  private refreshTimer: any = null
  private detect = 3 * 60 * 1000 // 间隔多少分钟检测用户是否操作了页面
  private detectTime = 50 // 未操作多少分钟时提示长时间未操作
  private refreshTime = 40 // 多少分钟刷新token一次

  // 是否显示公共头部
  get getHasHeader() {
    return !this.isWindowFull
  }

  // 是否显示公共侧边栏
  get getHasAside() {
    let { meta } = this.$route

    if (meta && meta.hasAside) {
      return false
    } else {
      return true
    }
  }

  get isShowCollapse() {
    let { meta } = this.$route

    if (meta && meta.hasAside) {
      return false
    } else {
      return true
    }
  }

  // 数据初始化
  private created() {
    /*
     * 接口60分钟token失效
     * 1、未做任何页面操作时间超过50分钟，提示退出
     * 2、55分钟刷新token一次
     */
    this.detectToken()
    this.refreshTokenToken()
  }

  // 侧边栏缩放
  private setCollapse() {
    this.isCollapse = !this.isCollapse
  }

  // 组件初始化
  private mounted() {
    this.$bus.$on('BusWindowFullScreen', (data: boolean) => {
      this.isWindowFull = data
    })
  }

  // 如果长时间未操作，则需要重新登录
  private detectToken() {
    setInvalidTokenTime()

    document.onmousedown = () => {
      setInvalidTokenTime()
    }

    clearInterval(this.timer)
    this.timer = setInterval(() => {
      if (!getToken()) {
        clearInterval(this.timer)
        return
      }

      let lastTime = Number(getInvalidTokenTime())
      let invalidTime = (new Date().getTime() - Number(lastTime)) / (60 * 1000)
      invalidTime = Number(invalidTime.toFixed(2))

      if (invalidTime > this.detectTime) {
        clearInterval(this.timer)
        this.logOutMethod()
        MessageBox.alert('长时间未操作，请重新登录', '提示', {
          showClose: false,
          type: 'warning'
        }).then(() => {
          location.reload()
        })
      }
    }, this.detect)
  }

  // 刷新token
  private refreshTokenToken() {
    clearInterval(this.refreshTimer)

    if (!getToken()) return

    this.refreshTimer = setInterval(async () => {
      // 刷新

      let json = getStore('refreshToken')
      if (json && json.content) {
        let res = (await refreshToken(json.content)) as { access_token?: string; refresh_token?: string }
        if (res.access_token) {
          setStore('token', res['access_token'])
          res['refresh_token'] && setStore('refreshToken', res['refresh_token'])
        }
      }
    }, this.refreshTime * 60 * 1000)
  }

  // 清除token，方便刷新后退出系统
  private logOutMethod() {
    removeToken()
    removeLocalStorage('saber-token')
  }

  // 加载完业务字典等各页面必须数据的时候在去显示 main 组件
  private changeIsMain() {
    this.isMain = true
  }
}
</script>

<style lang="scss" scoped>
.container-wrap {
  height: 100%;
  ::v-deep .el-header {
    padding: 0;
  }
}

.content-box {
  flex: 1;
  height: calc(100% - 60px);
  .logo-box {
    position: fixed;
    left: 0;
    top: 0;
    width: 220px;
    height: 60px;
    color: #fff;
    font-size: 17px;
    font-weight: 400;
    background: #ce4c4c;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    .logoms {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: center;
      .til {
        width: 100%;
        font-weight: 500;
        transform: translateY(14px);
      }
      .tilms {
        transform: translateY(0);
      }
      .collapse {
        font-size: 22px !important;
        transform: translateY(6px);
        cursor: pointer;
      }
      .collapsems {
        transform: translateY(0);
      }
    }
  }

  .logo-collapse-box {
    width: 64px !important;
  }
}
</style>
