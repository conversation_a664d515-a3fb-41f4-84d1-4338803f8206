// echarts 图表共有参数
export const echartConfigure = {
  colorText: '#5db0ea', // 主体文字颜色
  legendSize: 34, // legend 数字大小
  textSize: 34, // x、y 轴数字大小
  borderWidth: 4, // 线的宽度
  animationDuration: 3000, // 初始动画时长
  fontFamilyNumber: '', // 数字字体
  tooltip: {
    // tooltip提示样式
    backgroundColor: 'rgba(0, 103, 177, 1)',
    borderColor: 'rgba(0, 103, 177, 1)',
    padding: 20,
    extraCssText: 'border-radius: 8px;',
    textStyle: {
      color: '#fff',
      fontSize: 34,
    }
  },
  tooltipBody: {
    // tooltip 放置body上的提示样式
    renderMode: 'html',
    appendToBody: true,
    backgroundColor: 'rgba(0, 103, 177, 1)',
    borderColor: 'rgba(0, 103, 177, 1)',
    textStyle: {
      color: '#fff',
      fontSize: 13
    }
  }
}

// 资产分类
export const assetsTypes = [
  {
    code: 'asset_amount',
    name: '总资产'
  },
  {
    code: 'asset_net',
    name: '净资产'
  },
  {
    code: 'turnover',
    name: '总营收'
  },
  {
    code: 'profit_amount',
    name: '总利润'
  },
  {
    code: 'asset_debt_amount',
    name: '总负债'
  }
]

// 项目进度tabs
export const projectProgressList = [
  {
    code: '0',
    name: '年度预算'
  },
  {
    code: '1',
    name: '计划备案'
  },
  {
    code: '2',
    name: '合同备案'
  },
  {
    code: '3',
    name: '资金支付'
  }
]

// 时间单位
export const dateUnitList = [
  {
    code: '0',
    name: '月'
  },

  {
    code: '2',
    name: '年'
  }
]

// 项目投资类型
export const projectInvestmentTypeList = [
  {
    // 空格符串，如果是空字符串的话el-tab会转换为0
    code: ' ',
    name: '总体'
  },
  {
    code: 'ZF',
    name: '政府'
  },
  {
    code: 'QY',
    name: '企业'
  },
  {
    code: 'DJ',
    name: '代建'
  }
]

// 计划投资 tab 类型
export const plansInvestmentTypeList = [
  // {
  //   // 空格符串，如果是空字符串的话el-tab会转换为0
  //   code: ' ',
  //   name: '合计'
  // },
  // {
  //   code: '2',
  //   name: '功能'
  // },
  // {
  //   code: '1',
  //   name: '经营'
  // }
  // {
  //   code: 'ZF',
  //   name: '政府'
  // }
]

// 预警类型
export const earlyWarningList = [
  {
    code: '1',
    name: '蓝色预警'
  },
  {
    code: '2',
    name: '黄色预警'
  },
  {
    code: '3',
    name: '红色预警'
  },
  {
    code: '4',
    name: '橙色预警'
  }
]

// 集团列表
export const companyList = [
  {
    code: '0',
    id: 'ALL',
    name: '国资总况'
  },
  {
    code: '7',
    id: 'GYYY',
    name: '国资运营'
  },
  {
    code: '1',
    id: 'CT',
    name: '城投集团'
  },
  {
    code: '4',
    id: 'JT',
    name: '交投集团'
  },

  {
    code: '2',
    id: 'KT',
    name: '金投集团'
  },
  {
    code: '5',
    id: 'GD',
    name: '轨道集团'
  }
]

// 绩效评价
export const indicatorList = [
  { type: 'competitiveness', name: '盈利能力', max: 100 },
  { type: 'antiRisk', name: '资产质量', max: 100 },
  { type: 'effect', name: '债务风险', max: 100 },
  { type: 'control', name: '经营增长', max: 100 },
  { type: 'creativity', name: '发展能力', max: 100 }
]

// 调色板（8个色块）
export const colorSixList = ['#E6B607', '#EF702E', '#35AAF6', '#00AEA5', '#EC342F', '#2D5AE5', '#8600ae', '#3cef2e']

//
export const AssetCompositionData = [
  { value: 1048, name: '流动资产净额' },
  { value: 735, name: '固定资产净额' },
  { value: 580, name: '流动资产' }
]

export const TitlesOfMembersData = [82, 59, 68, 35, 59]
export const UtilizationOfAssetsData = [123, 435, 356, 674, 434, 234, 943]
export const ScaleOfCapitalUtilizationData = [
  {
    name: '国资总况',
    type: 'line',
    symbol: 'circle',
    data: [120, 132, 101, 134, 90, 230, 210]
  },
  {
    name: '城投集团',
    type: 'line',
    symbol: 'circle',
    data: [220, 182, 191, 234, 290, 330, 310]
  },
  {
    name: '交投集团',
    type: 'line',
    symbol: 'circle',
    data: [150, 232, 201, 154, 190, 330, 410]
  },
  {
    name: '金投集团',
    type: 'line',
    symbol: 'circle',
    data: [320, 332, 301, 334, 390, 330, 320]
  },
  {
    name: '轨道集团',
    type: 'line',
    symbol: 'circle',
    data: [820, 932, 901, 934, 1290, 1330, 1320]
  },
  {
    name: '水务集团',
    type: 'line',
    symbol: 'circle',
    data: [820, 932, 901, 934, 1290, 1330, 1320]
  },
  {
    name: '社发集团',
    type: 'line',
    symbol: 'circle',
    data: [820, 932, 901, 934, 1290, 1330, 1320]
  }
]
export const SanZhongYiDaData = [
  {
    name: '国资总况',
    type: 'line',
    symbol: 'circle',
    data: [120, 132, 101, 134, 90, 230, 210]
  },
  {
    name: '城投集团',
    type: 'line',
    symbol: 'circle',
    data: [220, 182, 191, 234, 290, 330, 310]
  },
  {
    name: '交投集团',
    type: 'line',
    symbol: 'circle',
    data: [150, 232, 201, 154, 190, 330, 410]
  },
  {
    name: '金投集团',
    type: 'line',
    symbol: 'circle',
    data: [320, 332, 301, 334, 390, 330, 320]
  },
  {
    name: '轨道集团',
    type: 'line',
    symbol: 'circle',
    data: [820, 932, 901, 934, 1290, 1330, 1320]
  },
  {
    name: '水务集团',
    type: 'line',
    symbol: 'circle',
    data: [820, 932, 901, 934, 1290, 1330, 1320]
  },
  {
    name: '社发集团',
    type: 'line',
    symbol: 'circle',
    data: [820, 932, 901, 934, 1290, 1330, 1320]
  }
]
export const ProjectProfitabilityRankingData = [234, 434, 596, 774, 935, 1323]

export const ProjectImplementationOverviewData = [
  {
    code: 'JHHT4633',
    name: '项目1',
    typeStr: '水利水电',
    unitName: '城投',
    constructionTypeStr: '新建',
    principal: '倪达青',
    createDate: '1995.6.24',
    predictSum: '547.3',
    realitySum: '436.6',
    rate: '85.7',
    excessBudget: '-'
  },
  {
    code: 'JHHT4633',
    name: '项目1',
    typeStr: '水利水电',
    unitName: '城投',
    constructionTypeStr: '新建',
    principal: '倪达青',
    createDate: '1995.6.24',
    predictSum: '547.3',
    realitySum: '436.6',
    rate: '85.7',
    excessBudget: '-'
  },
  {
    code: 'JHHT4633',
    name: '项目1',
    typeStr: '水利水电',
    unitName: '城投',
    constructionTypeStr: '新建',
    principal: '倪达青',
    createDate: '1995.6.24',
    predictSum: '547.3',
    realitySum: '436.6',
    rate: '85.7',
    excessBudget: '-'
  },
  {
    code: 'JHHT4633',
    name: '项目1',
    typeStr: '水利水电',
    unitName: '城投',
    constructionTypeStr: '新建',
    principal: '倪达青',
    createDate: '1995.6.24',
    predictSum: '547.3',
    realitySum: '436.6',
    rate: '85.7',
    excessBudget: '-'
  },
  {
    code: 'JHHT4633',
    name: '项目1',
    typeStr: '水利水电',
    unitName: '城投',
    constructionTypeStr: '新建',
    principal: '倪达青',
    createDate: '1995.6.24',
    predictSum: '547.3',
    realitySum: '436.6',
    rate: '85.7',
    excessBudget: '-'
  },
  {
    code: 'JHHT4633',
    name: '项目1',
    typeStr: '水利水电',
    unitName: '城投',
    constructionTypeStr: '新建',
    principal: '倪达青',
    createDate: '1995.6.24',
    predictSum: '547.3',
    realitySum: '436.6',
    rate: '85.7',
    excessBudget: '-'
  },
  {
    code: 'JHHT4633',
    name: '项目1',
    typeStr: '水利水电',
    unitName: '城投',
    constructionTypeStr: '新建',
    principal: '倪达青',
    createDate: '1995.6.24',
    predictSum: '547.3',
    realitySum: '436.6',
    rate: '85.7',
    excessBudget: '-'
  },
  {
    code: 'JHHT4633',
    name: '项目1',
    typeStr: '水利水电',
    unitName: '城投',
    constructionTypeStr: '新建',
    principal: '倪达青',
    createDate: '1995.6.24',
    predictSum: '547.3',
    realitySum: '436.6',
    rate: '85.7',
    excessBudget: '-'
  },
  {
    code: 'JHHT4633',
    name: '项目rqwerweqtwertwtqwwretqwretewtyqterwyqertwweytqerwtqe1',
    typeStr: '水利水电',
    unitName: '城投',
    constructionTypeStr: '新建',
    principal: '倪达青',
    createDate: '1995.6.24',
    predictSum: '547.3',
    realitySum: '436.6',
    rate: '85.7',
    excessBudget: '-'
  },
  {
    code: 'JHHT4633',
    name: '项目1',
    typeStr: '水利水电',
    unitName: '城投',
    constructionTypeStr: '新建',
    principal: '倪达青',
    createDate: '1995.6.24',
    predictSum: '547.3',
    realitySum: '436.6',
    rate: '85.7',
    excessBudget: '-'
  },
  {
    code: 'JHHT4633',
    name: '项目1',
    typeStr: '水利水电',
    unitName: '城投',
    constructionTypeStr: '新建',
    principal: '倪达青',
    createDate: '1995.6.24',
    predictSum: '547.3',
    realitySum: '436.6',
    rate: '85.7',
    excessBudget: '-'
  },
  {
    code: 'JHHT4633',
    name: '项目1',
    typeStr: '水利水电',
    unitName: '城投',
    constructionTypeStr: '新建',
    principal: '倪达青',
    createDate: '1995.6.24',
    predictSum: '547.3',
    realitySum: '436.6',
    rate: '85.7',
    excessBudget: '-'
  },
  {
    code: 'JHHT4633',
    name: '项目1',
    typeStr: '水利水电',
    unitName: '城投',
    constructionTypeStr: '新建',
    principal: '倪达青',
    createDate: '1995.6.24',
    predictSum: '547.3',
    realitySum: '436.6',
    rate: '85.7',
    excessBudget: '-'
  },
  {
    code: 'JHHT4633',
    name: '项目1',
    typeStr: '水利水电',
    unitName: '城投',
    constructionTypeStr: '新建',
    principal: '倪达青',
    createDate: '1995.6.24',
    predictSum: '547.3',
    realitySum: '436.6',
    rate: '85.7',
    excessBudget: '-'
  },
  {
    code: 'JHHT4633',
    name: '项目1',
    typeStr: '水利水电',
    unitName: '城投',
    constructionTypeStr: '新建',
    principal: '倪达青',
    createDate: '1995.6.24',
    predictSum: '547.3',
    realitySum: '436.6',
    rate: '85.7',
    excessBudget: '-'
  },
  {
    code: 'JHHT4633',
    name: '项目1',
    typeStr: '水利水电',
    unitName: '城投',
    constructionTypeStr: '新建',
    principal: '倪达青',
    createDate: '1995.6.24',
    predictSum: '547.3',
    realitySum: '436.6',
    rate: '85.7',
    excessBudget: '-'
  }
]

export const ProjectImplementationData = [40, 43, 12, 53, 32, 45]

export const ProjectAmountDistributionData = [129, 131, 165, 142, 157, 167, 214, 176, 65, 32, 12]
export const MembershipStatusData = [
  {
    name: '在编人员',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#E6B60700' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#E6B60744' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    data: [120, 132, 101, 134, 90, 230, 210]
  },
  {
    name: '提拔人员',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#EF702E00' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#EF702E44' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    data: [220, 182, 191, 234, 290, 330, 310]
  },
  {
    name: '离岗人员',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#35AAF600' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#35AAF644' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    data: [150, 232, 201, 154, 190, 330, 410]
  },
  {
    name: '辞退人员',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#00AEA500' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#00AEA544' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    data: [320, 332, 301, 334, 390, 330, 320]
  }
]
export const MajorDistributionData = [
  {
    value: 34,
    name: '信息与通信工程'
  },
  {
    value: 23,
    name: '初应用统计中'
  },
  {
    value: 36,
    name: '应用经济学'
  },
  {
    value: 55,
    name: '工商管理'
  },
  {
    value: 59,
    name: '人力资源'
  },
  {
    value: 165,
    name: '财务管理'
  },
  {
    value: 25,
    name: '研究生'
  },
  {
    value: 38,
    name: '审计学'
  },
  {
    value: 48,
    name: '经济管理'
  }
]
export const IndexOverviewData = [
  {
    name: '国资总况',
    type: 'line',
    symbol: 'circle',
    data: [120, 132, 101, 134, 90, 230, 210]
  },
  {
    name: '城投集团',
    type: 'line',
    symbol: 'circle',
    data: [220, 182, 191, 234, 290, 330, 310]
  },
  {
    name: '交投集团',
    type: 'line',
    symbol: 'circle',
    data: [150, 232, 201, 154, 190, 330, 410]
  },
  {
    name: '金投集团',
    type: 'line',
    symbol: 'circle',
    data: [320, 332, 301, 334, 390, 330, 320]
  },
  {
    name: '轨道集团',
    type: 'line',
    symbol: 'circle',
    data: [820, 932, 901, 934, 1290, 1330, 1320]
  },
  {
    name: '水务集团',
    type: 'line',
    symbol: 'circle',
    data: [820, 932, 901, 934, 1290, 1330, 1320]
  },
  {
    name: '社发集团',
    type: 'line',
    symbol: 'circle',
    data: [820, 932, 901, 934, 1290, 1330, 1320]
  }
]
export const GeneralAssessmentData = [
  {
    name: '国资总况',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#E6B60700' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#E6B60744' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    data: [4552935, 4552935, 4552935, 4552935, 4552935]
  },
  {
    name: '城投集团',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#EF702E00' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#EF702E44' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    data: [1735860, 1735860, 1735860, 1735860, 1735860]
  },
  {
    name: '交投集团',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#35AAF600' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#35AAF644' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    data: [0, 0, 0, 0, 0]
  },
  {
    name: '金投集团',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#00AEA500' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#00AEA544' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    data: [107490, 107490, 107490, 107490, 107490]
  },
  {
    name: '轨道集团',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#EC342F00' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#EC342F44' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    data: [7249, 7249, 7249, 7249, 7249]
  },
  {
    name: '水务集团',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#E6060700' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#E6060744' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    lineStyle: {
      width: 3
    },
    data: [4552935, 1735860, 0, 107490, 7249]
  },
  {
    name: '社发集团',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#06B60700' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#06B60744' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    lineStyle: {
      width: 3
    },
    data: [4552935, 1735860, 0, 107490, 7249]
  }
]
export const EducationalBackgroundDistributionData = [
  {
    value: 14,
    name: '小学',
    itemStyle: { color: 'RGBA(74, 101, 226, 1)' }
  },
  {
    value: 23,
    name: '初中',
    itemStyle: { color: 'RGBA(25, 111, 209, 1)' }
  },
  {
    value: 36,
    name: '高中',
    itemStyle: { color: 'RGBA(220, 135, 51, 1)' }
  },
  {
    value: 55,
    name: '中专',
    itemStyle: { color: 'RGBA(213, 81, 128, 1)' }
  },
  {
    value: 59,
    name: '大专',
    itemStyle: { color: 'RGBA(55, 161, 223, 1)' }
  },
  {
    value: 165,
    name: '本科',
    itemStyle: { color: 'RGBA(160, 224, 209, 1)' }
  },
  {
    value: 25,
    name: '研究生',
    itemStyle: { color: '#E60607' }
  },
  {
    value: 18,
    name: '博士',
    itemStyle: { color: '#06B607' }
  }
]

export const AgeOfMembersData = [
  {
    value: 20,
    name: '60岁以上',
    itemStyle: {
      color: 'RGBA(230, 182, 7, 1)'
    },
    title: {
      offsetCenter: ['20%', '-100%']
    },
    detail: {
      valueAnimation: true,
      offsetCenter: ['85%', '-100%']
    }
  },
  {
    value: 40,
    name: '50-60岁',
    itemStyle: {
      color: 'RGBA(234, 61, 167, 1)'
    },
    title: {
      offsetCenter: ['20%', '-80%']
    },
    detail: {
      valueAnimation: true,
      offsetCenter: ['85%', '-80%']
    }
  },
  {
    value: 60,
    name: '40-50岁',
    itemStyle: {
      color: 'RGBA(99, 158, 74, 1)'
    },
    title: {
      offsetCenter: ['20%', '-60%']
    },
    detail: {
      valueAnimation: true,
      offsetCenter: ['85%', '-60%']
    }
  },
  {
    value: 60,
    name: '30-40岁',
    itemStyle: {
      color: 'RGBA(13, 184, 150, 1)'
    },
    title: {
      offsetCenter: ['20%', '-40%']
    },
    detail: {
      valueAnimation: true,
      offsetCenter: ['85%', '-40%']
    }
  },
  {
    value: 60,
    name: '20-30岁',
    itemStyle: {},
    title: {
      offsetCenter: ['20%', '-20%']
    },
    detail: {
      valueAnimation: true,
      offsetCenter: ['85%', '-20%']
    }
  }
]

export const ProfitOnAssetsData = [
  // {
  //   name: '国资总况',
  //   type: 'line',
  //   symbol: 'none',
  //   smooth: true,
  //   lineStyle: {
  //     width: 3
  //   },
  //   areaStyle: {
  //     origin: 'start',
  //     shadowBlur: {
  //       shadowColor: 'rgba(0, 0, 0, 0.5)',
  //       shadowBlur: 10
  //     },
  //     color: {
  //       type: 'linear',
  //       x: 0,
  //       y: 0,
  //       x2: 0,
  //       y2: 1,
  //       colorStops: [
  //         {
  //           offset: 1,
  //           color: '#E6B60700' // 0% 处的颜色
  //         },
  //         {
  //           offset: 0,
  //           color: '#E6B60744' // 100% 处的颜色
  //         }
  //       ],
  //       global: false // 缺省为 false
  //     }
  //   },
  //   data: [120, 132, 101, 134, 90, 230, 210]
  // },
  {
    name: '城投集团',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#EF702E00' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#EF702E44' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    data: [220, 182, 191, 234, 290, 330, 310]
  },
  {
    name: '交投集团',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#35AAF600' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#35AAF644' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    data: [150, 232, 201, 154, 190, 330, 410]
  },
  {
    name: '金投集团',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#00AEA500' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#00AEA544' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    data: [320, 332, 301, 334, 390, 330, 320]
  },
  {
    name: '轨道集团',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#EC342F00' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#EC342F44' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    data: [820, 932, 901, 934, 1290, 1330, 1320]
  },
  {
    name: '水务集团',
    type: 'line',
    symbol: 'circle',
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#E6B60700' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#E6B60744' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    smooth: true,
    lineStyle: {
      width: 3
    },
    data: [820, 932, 901, 934, 1290, 1330, 1320]
  },
  {
    name: '社发集团',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#c6Bd8700' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#c6Bd8744' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    lineStyle: {
      width: 3
    },
    data: [820, 932, 901, 934, 1290, 1330, 1320]
  }
]

export const ProjectNumberData = [23, 65, 21, 43]

export const ItemAmountData = [23, 65, 21, 43]

export const AppointAndDismissData = [
  { value: 1048, name: '其他重要人事任免' },
  { value: 735, name: '企业中层及以上任免' },
  { value: 580, name: '单位领导班子任免' },
  { value: 484, name: '控股、参股企业委派代办任免' },
  { value: 300, name: '推荐董事会监事会成员' }
]

export const MatterNumberData = [
  { value: 1048, name: '产权转让' },
  { value: 735, name: '对外投资' },
  { value: 580, name: '其他重要事项' },
  { value: 300, name: '机构调整' }
]

export const DepositAndLoanScaleData = [
  {
    value: 1048,
    name: '存款金额',
    itemStyle: { color: 'RGBA(74, 101, 226, 1)' }
  },
  {
    value: 735,
    name: '贷款金额',
    itemStyle: { color: 'RGBA(96, 203, 179, 1)' }
  }
]

export const ProjectImplementationBarData = [24387, 6412.5, 8121, 12493]

export const ProjectClassificationData = [
  {
    value: 1048,
    name: '房屋建筑',
    itemStyle: { color: 'RGBA(74, 101, 226, 1)' }
  },
  {
    value: 735,
    name: '铁路工程',
    itemStyle: { color: 'RGBA(205, 134, 46, 1)' }
  },

  {
    value: 735,
    name: '水利水电',
    itemStyle: { color: 'RGBA(46, 102, 162, 1)' }
  },
  {
    value: 735,
    name: '市政',
    itemStyle: { color: 'RGBA(96, 203, 179, 1)' }
  },
  {
    value: 735,
    name: '其他',
    itemStyle: { color: 'RGBA(213, 81, 128, 1)' }
  }
]

export const ProjectClassificationPieData = [
  {
    value: 148,
    name: '房屋建筑',
    itemStyle: { color: 'RGBA(74, 101, 226, 1)' }
  },
  {
    value: 98,
    name: '铁路工程',
    itemStyle: { color: 'RGBA(205, 134, 46, 1)' }
  },
  {
    value: 123,
    name: '水利水电',
    itemStyle: { color: 'RGBA(46, 102, 162, 1)' }
  },
  {
    value: 75,
    name: '市政',
    itemStyle: { color: 'RGBA(96, 203, 179, 1)' }
  },
  {
    value: 107,
    name: '其他',
    itemStyle: { color: 'RGBA(213, 81, 128, 1)' }
  }
]

export const FinancialIndicatorLineData = [
  {
    type: 'line',
    symbol: 'circle',
    smooth: true,
    data: [120, 182, 191, 234, 290, 330, 410],
    lineStyle: {
      width: 4
    },
    label: {
      show: true,
      color: '#fff',
      fontWeight: 'blod',
      fontSize: 16
    },
    itemStyle: {}
  }
]
export const LiabilitiesLineChartsData = [
  {
    name: '国资总况',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#E6B60700' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#E6B60744' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    data: [120, 132, 101, 134, 90, 230, 210]
  },
  {
    name: '城投集团',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#EF702E00' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#EF702E44' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    data: [220, 182, 191, 234, 290, 330, 310]
  },
  {
    name: '交投集团',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#35AAF600' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#35AAF644' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    data: [150, 232, 201, 154, 190, 330, 410]
  },
  {
    name: '金投集团',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#00AEA500' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#00AEA544' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    data: [320, 332, 301, 334, 390, 330, 320]
  },
  {
    name: '轨道集团',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#EC342F00' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#EC342F44' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    data: [820, 932, 901, 934, 1290, 1330, 1320]
  },
  {
    name: '水务集团',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#E6060700' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#E6060744' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    lineStyle: {
      width: 3
    },
    data: [820, 932, 901, 934, 1290, 1330, 1320]
  },
  {
    name: '社发集团',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#06B60700' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#06B60744' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    lineStyle: {
      width: 3
    },
    data: [820, 932, 901, 934, 1290, 1330, 1320]
  }
]
export const CompositeIndexLineChartsData = [
  {
    name: '国资总况',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#E6B60700' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#E6B60744' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    data: [120, 132, 101, 134, 90, 230, 210]
  },
  {
    name: '城投集团',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#EF702E00' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#EF702E44' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    data: [220, 182, 191, 234, 290, 330, 310]
  },
  {
    name: '交投集团',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#35AAF600' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#35AAF644' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    data: [150, 232, 201, 154, 190, 330, 410]
  },
  {
    name: '金投集团',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#00AEA500' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#00AEA544' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    data: [320, 332, 301, 334, 390, 330, 320]
  },
  {
    name: '轨道集团',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    lineStyle: {
      width: 3
    },
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#EC342F00' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#EC342F44' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    data: [820, 932, 901, 934, 1290, 1330, 1320]
  },
  {
    name: '水务集团',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#E6060700' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#E6060744' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    lineStyle: {
      width: 3
    },
    data: [820, 932, 901, 934, 1290, 1330, 1320]
  },
  {
    name: '社发集团',
    type: 'line',
    symbol: 'circle',
    smooth: true,
    areaStyle: {
      origin: 'start',
      shadowBlur: {
        shadowColor: 'rgba(0, 0, 0, 0.5)',
        shadowBlur: 10
      },
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 1,
            color: '#06B60700' // 0% 处的颜色
          },
          {
            offset: 0,
            color: '#06B60744' // 100% 处的颜色
          }
        ],
        global: false // 缺省为 false
      }
    },
    lineStyle: {
      width: 3
    },
    data: [820, 932, 901, 934, 1290, 1330, 1320]
  }
]
