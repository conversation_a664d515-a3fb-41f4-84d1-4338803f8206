const path = require('path')
const CompressionWebpackPlugin = require('compression-webpack-plugin');
const productionGzipExtensions = ['js', 'css', 'json', 'txt', 'html', 'ico', 'svg'];
const isProduction = process.env.NODE_ENV === 'production';

module.exports = {
  assetsDir: 'static',
  parallel: false,
  publicPath: process.env.NODE_ENV === 'development' ? '/' : '/monitor/',
  productionSourceMap: false,
  outputDir: path.resolve(__dirname, './monitor/'),
  configureWebpack: config => {
    // 生产环境下开启gzip压缩
    if (isProduction) {
      config.plugins.push(
        new CompressionWebpackPlugin({
          algorithm: 'gzip',
          test: new RegExp('\\.(' + productionGzipExtensions.join('|') + ')$'),
          threshold: 10240, // 只有大小大于10kb的资源会被处理
          minRatio: 0.8, // 只有压缩率小于这个值的资源才会被处理
          deleteOriginalAssets: false // 是否删除原文件，默认为false
        })
      )
    }
  },
  css: {
    extract: { ignoreOrder: true }
  },
  devServer: {
    host: '0.0.0.0',
    port: 8081,
    open: true,
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      '/api': {
        /*
         * 修改 target 代理值，在项目根目录新建 .env.local 文件，写入：
         * PROXY_TARGET=http://tpm1-gmd.mdguanjia.com/
         * .env.local文件会被git忽略
         */
        target: process.env.PROXY_TARGET || 'http://dpm1-jh-fgw.mdguanjia.com/', //后台接口地址
        changOrigin: true,
        pathRewrite: {
          '^/api': ''
        }
      }
    }
  }
}
