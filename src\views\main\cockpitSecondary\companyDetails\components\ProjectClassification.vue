/** 项目分类情况 */

<template>
  <div class="project-classification">
    <div id="ProjectClassification" />
    <div class="halo"></div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { ProjectClassificationData, companyList } from '@/views/main/cockpitSecondary/baseData'
import { capitalProfile } from '@/api/cockpit'
import * as echarts from 'echarts'
import { deepClone } from '@/utils'

type EChartsOption = echarts.EChartsOption

@Component
export default class extends Vue {
  private loading = false
  private maxValue = 100
  private year = ''
  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}
  private tabList = []
  private echartsDatas: any[] = []
  private seriesData: any[] = deepClone(ProjectClassificationData)
  private tabActive: {
    code: string
    name: string
  } = {
    code: '',
    name: ''
  }

  get sum() {
    return this.seriesData.reduce((sum: any, currentValue: any) => {
      return (sum += currentValue.value || 0)
    }, 0)
  }

  // 获取集团列表
  get getCompanyList() {
    let list: string[] = []
    companyList.forEach((item) => {
      list.push(item.name)
    })
    return list
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('ProjectClassification') as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    // this.initData()
    this.initEcharts()
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await capitalProfile()
    if (!Array.isArray(data) || !data.length) return
    this.echartsDatas = data
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 初始化数据
  private initEcharts() {
    let textSize = 22
    let textColor = '#999'
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    this.option = {
      color: ['#E6B607', '#EF702E', '#35AAF6', '#00AEA5', '#EC342F', '#2D5AE5'],
      title: {
        top: '45%',
        left: 'center',
        text: `${this.sum}亿元`,
        subtext: '总金额',
        textStyle: {
          color: 'rgba(255, 170, 69, 1)',
          fontWeight: 'bold',
          fontSize: textSize * 1.8
        },
        subtextStyle: {
          color: '#fff',
          fontSize: textSize * 1.4
        }
      },
      legend: {
        show: false,
        type: 'scroll',
        orient: 'vertical',
        right: 20,
        top: 'center',
        bottom: 0,
        padding: 0,
        itemHeight: textSize / 1.4,
        itemWidth: textSize / 1.4,
        itemStyle: {
          borderCap: 'butt'
        },
        lineStyle: {
          type: [0, 10],
          dashOffset: 5
        },
        textStyle: {
          color: '#999',
          fontSize: textSize / 1.4
        }
      },
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '50%'],
          avoidLabelOverlap: false,
          // stillShowZeroSum: false,
          name: '项目预选',
          selectedMode: 'single',
          label: {
            show: true,
            position: 'outside',
            fontSize: 24,
            color: '#5db0ea',
            formatter: ({ data }: { data: any }) => {
              // ${
              //   +(data.value / this.sum).toFixed(1) * 100
              // }%
              return `{a|${data.value}亿元} \n {hr|}\n {b|${data.name}${+((data.value / this.sum) * 100).toFixed(1)}%}`
            },
            rich: {
              a: {
                fontSize: '40px',
                color: '#fff',
                fontWeight: 'bold',
                opacity: 0.8
              },
              b: {
                fontSize: '28px',
                color: '#20DCF9',
                fontWeight: 'bold'
              },
              hr: {
                borderColor: '#8C8D8E',
                width: '100%',
                borderWidth: 1,
                height: 0
              }
            }
          },
          itemStyle: {
            borderRadius: 0,
            borderWidth: 10
          },
          labelLine: {
            length: 20,
            length2: 20,
            maxSurfaceAngle: 80,
            smooth: 0.2,
            lineStyle: {
              width: 3
            }
          },
          data: this.seriesData
        }
      ]
    }

    this.myChart && this.myChart.setOption(this.option)
  }
  // 年份切换
  private yearHandler(year: string) {
    this.year = year
    // this.initData()
    // this.initEcharts()
  }
}
</script>

<style scoped lang="scss">
#ProjectClassification {
  position: relative;
  z-index: 1000;
  width: 100%;
  height: 100%;
}
.project-classification {
  width: 100%;
  height: 100%;
  .halo {
    position: absolute;
    z-index: -1;
    top: 50%;
    left: 50%;
    width: 300px;
    height: 300px;
    transform: translate(-50%, -50%);
    background: url('../../images/halo.png');
    background-size: 100% 100%;
    will-change: width, height, transform;
    animation: move 1000s linear forwards infinite;
  }

  @keyframes move {
    0% {
      transform: translate(-50%, -50%) rotateZ(0deg);
      width: 305px;
      height: 305px;
    }
    50% {
      transform: translate(-50%, -50%) rotateZ(360deg);
      width: 305px;
      height: 305px;
    }
    100% {
      transform: translate(-50%, -50%) rotateZ(0deg);
      width: 305px;
      height: 305px;
    }
  }
}
</style>