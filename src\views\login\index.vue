<template>
  <div class="login-wrap">
    <div class="content-box">
      <div class="login-box-left">
        <img src="@/assets/images/login/center_left.png" />
      </div>
      <div class="login-box-right"
        v-loading="loading">
        <div class="content-title">
          <h2>欢迎登录</h2>
          <!-- <p class="register-wrap">
            <span>没有账号？</span>
            <span class="registered"
              @click="registered">立即注册</span>
          </p> -->
        </div>

        <!-- 扫码登录 -->
        <qrcode-login v-if="isQrcodeLoginVisible"
          @onExit="isQrcodeLoginVisible = false" />

        <!-- 登陆 -->
        <el-form v-else-if="loginMode"
          class="login-form login-form-box"
          :model="loginForm">
          <div class="login-form-row">
            <input name="username"
              v-model="loginForm.username"
              placeholder="请输入手机号或用户名"
              autocomplete="on"
              @focus="inputFocus" />
            <img class="login-ipt-ic"
              src="@/assets/icons/svg/head.svg" />
          </div>
          <div class="login-form-row">
            <input v-model="loginForm.password"
              name="password"
              autocomplete="on"
              placeholder="请输入密码"
              type="password"
              @keyup.enter="login"
              @focus="inputFocus" />
            <img class="login-ipt-ic"
              src="@/assets/icons/svg/password.svg" />
          </div>
          <div class="error-tip">
            <span v-if="isErrorVisible">请输入正确的账号</span>
          </div>
          <div class="login-href">
            <a @click="toggleMode">忘记密码</a>
          </div>
          <div class="login-btns">
            <el-button type="primary"
              class="login-btns-btn"
              size="medium"
              @click="handleLogin"
              :loading="isLoginLoading">登 录</el-button>
          </div>
        </el-form>

        <!-- 忘记密码 -->
        <el-form v-else
          class="login-form"
          :model="resetForm">
          <div class="reset-form-row">
            <input v-model="resetForm.phone"
              placeholder="请输入手机号"
              maxlength="13"
              autocomplete="off"
              disableautocomplete />
            <span class="login-ipt-ic">手机号</span>
          </div>

          <div class="reset-form-row">
            <graph-code v-model="graphCode"
              :phone="resetForm.phone" />
            <span class="login-ipt-ic">图形验证码</span>
          </div>

          <div class="reset-form-row">
            <input v-model="resetForm.sms"
              maxlength="8"
              placeholder="请输入验证码"
              autocomplete="off"
              disableautocomplete />
            <span class="login-ipt-ic">验证码</span>
            <el-button :class="isSmsCountting ? 'btn-get-code is-disabled' : 'btn-get-code'"
              @click.native="handleSendVerificationCode">
              {{ isSmsCountting ? smsWaitingSeconds + '秒后可重新发送' : '获取验证码' }}
            </el-button>
          </div>
          <div class="reset-form-row">
            <input v-model="resetForm.newPassword"
              placeholder="请设置6~12位密码"
              type="password"
              maxlength="12"
              @keyup.enter="reset"
              autocomplete="new-password"
              disableautocomplete />
            <span class="login-ipt-ic">密码</span>
          </div>
          <div class="error-tip">
            <span v-if="resetFieldErrorMsg">{{ resetFieldErrorMsg }}</span>
          </div>
          <div class="login-href">
            <a @click="toggleMode">返回登录</a>
          </div>
          <div class="login-btns">
            <el-button type="primary"
              @click="reset"
              size="medium"
              :loading="isResetLoading">完成并登录</el-button>
          </div>
        </el-form>
      </div>
    </div>

    <div class="copyright-box">Copyright © 金华市国资委 版权所有</div>

    <!-- 修改密码 -->
    <!-- <ModifyPwd v-if="isModifyPwdDlgVisible"
      :visible.sync="isModifyPwdDlgVisible"
      :phone="loginForm.username"
      @modifyPwdSuccess="reLogin" /> -->
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { UserModule } from '@/store/modules/user'
import { Dictionary } from 'vue-router/types/router'
import { isValidPhone } from '@/utils/validate'
import { loginByUsername } from '@/api/public'
import md5 from 'js-md5'
import { setLocalStorage, setStore } from '@/utils/cache'
import { getUserInfo } from '@/api/public'
import { api } from 'v-viewer'
@Component({
  components: {
    // ModifyPwd,
    // QrcodeLogin,
    // GraphCode
  }
})
export default class extends Vue {
  private loginForm = {
    username: 'admin',
    password: 'JH.FHKJ2024&8'
  }
  private isErrorVisible = false
  private redirect?: string
  private isLoginLoading = false
  private isResetLoading = false
  private loginMode = true
  private resetFieldErrorMsg = '' // 忘记密码错误提示语句
  private resetForm = {
    phone: '',
    sms: '',
    newPassword: ''
  }
  private isSmsCountting = false // 验证码倒计时
  private smsWaitingSeconds = 60
  private smsTimer: any
  private isModifyPwdDlgVisible = false
  private isAppQrcodeShow = true
  private isQrcodeLoginVisible = false
  private graphCode = ''
  private loading = false

  // 点击登陆前置判断
  private async handleLogin() {
    if (!this.loginForm.username || !this.loginForm.password) {
      this.isErrorVisible = true
      return
    }

    this.isLoginLoading = true
    try {
      let res = (await loginByUsername({
        username: this.loginForm.username,
        password: md5(this.loginForm.password)
      })) as { access_token?: string; refresh_token?: string }
      if (res['access_token']) {
        setLocalStorage(
          'saber-token',
          JSON.stringify({
            content: res['access_token'],
            dataType: 'string',
            datetime: new Date().valueOf()
          })
        )

        setLocalStorage(
          'saber-userInfo',
          JSON.stringify({
            content: res,
            dataType: 'object',
            datetime: new Date().valueOf()
          })
        )
        this.$router.replace({ path: '/' })
      }
    } finally {
      this.loadUserinfo()
      this.isLoginLoading = false
    }
  }
  // 登录成功之后 请求userinfo
  private async loadUserinfo() {
    try {
      let res = await getUserInfo()
      if (res.success) {
        setLocalStorage(
          'userinfo',
          JSON.stringify({
            content: res.data,
            dataType: 'object',
            datetime: new Date().valueOf()
          })
        )
      }
    } catch (e) {
      console.error(e)
    }
  }
  // 点击登陆
  private async login() {
    this.isLoginLoading = true
    this.isErrorVisible = false
    // try {
    //   let res = await UserModule.Login(this.loginForm)
    //   if (res && res.code === 411) {
    //     this.isModifyPwdDlgVisible = true
    //     return
    //   }
    //   this.jumpToWorkbench()
    // } finally {
    //   this.isResetLoading = false
    //   this.isLoginLoading = false
    // }
  }

  // 跳转到首页
  private jumpToWorkbench() {
    // 设置登录成功跳转到金华驾驶舱
    let params: Dictionary<string> = { fromLogin: 'true' }
    this.$router.replace({
      path: '/',
      params
    })
  }

  // 免费注册
  private registered() {
    this.$router.push({
      path: '/register'
    })
  }

  // 忘记密码
  private toggleMode() {
    this.isErrorVisible = false
    this.resetFieldErrorMsg = ''
    this.loginMode = !this.loginMode
  }

  // 重置密码并登录
  private async reset() {
    if (!isValidPhone(this.resetForm.phone)) {
      this.resetFieldErrorMsg = '请输入正确的手机号'
      return
    }
    if (!this.resetForm.sms) {
      this.resetFieldErrorMsg = '请输入正确的验证码'
      return
    }
    if (!this.resetForm.newPassword) {
      this.resetFieldErrorMsg = '请输入正确的密码'
      return
    }
    if (this.resetForm.newPassword.length < 6) {
      this.resetFieldErrorMsg = '请设置6~12位密码'
      return
    }
    this.resetFieldErrorMsg = ''
    // let resetRes: AjaxResponse = await forgetPassword(this.resetForm)
    // if (!resetRes.success) {
    //   this.$message.warning(resetRes.msg || '密码设置失败')
    //   return
    // }
    // this.loginForm = {
    //   username: this.resetForm.phone,
    //   password: this.resetForm.newPassword
    // }
    this.login()
  }

  // 获取验证码
  private async handleSendVerificationCode() {
    if (this.isSmsCountting) {
      return
    }
    if (!isValidPhone(this.resetForm.phone)) {
      this.resetFieldErrorMsg = '请输入正确的手机号'
      return
    }
    if (!this.graphCode) {
      this.$message.warning('请输入图形验证码')
      return
    }
    this.resetFieldErrorMsg = ''
    this.loading = true
    // try {
    //   let res = await sendGraphMessage({
    //     phone: this.resetForm.phone,
    //     accessId: getUuid(),
    //     graphCode: this.graphCode
    //   })
    //   if (res.success) {
    //     this.isSmsCountting = true
    //     this.smsTimer = setInterval(() => {
    //       if (this.smsWaitingSeconds === 1) {
    //         this.isSmsCountting = false
    //         this.smsWaitingSeconds = 60
    //         clearInterval(this.smsTimer)
    //         return
    //       }
    //       --this.smsWaitingSeconds
    //     }, 1000)
    //   } else {
    //     this.$message.warning(res.msg || '验证码发送错误')
    //   }
    // } catch (e) {
    //   console.error(e)
    // } finally {
    //   this.loading = false
    // }
  }

  // 修改密码重新登陆
  private async reLogin(newPwd: string) {
    this.isLoginLoading = true
    // try {
    //   await UserModule.Login({
    //     username: this.loginForm.username,
    //     password: newPwd
    //   })
    //   this.jumpToWorkbench()
    // } catch (e) {
    //   console.error(e)
    // }
    this.isLoginLoading = false
  }

  // input 输入框获得焦点，去除报错信息
  private inputFocus() {
    this.isErrorVisible = false
  }

  // 隐藏底部“扫描下载...”信息栏
  private handleQrcodeClose() {
    this.isAppQrcodeShow = false
  }
}
</script>

<style lang="scss" scoped>
.login-wrap {
  height: 100%;
  position: relative;
  background: url('../../assets/images/login/bg.png') left top no-repeat;
  background-size: 100% 100%;

  .header-logo {
    display: inline-block;
    outline: none;
    position: fixed;
    left: 34px;
    top: 14px;
    width: 10vw;
    max-width: 160px;
    img {
      width: 80px;
    }
  }

  .content-box {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    z-index: 2;
    width: 1080px;
    height: 460px;
    display: flex;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    .login-box-left {
      width: 680px;
      height: 100%;
      img {
        width: 100%;
        height: 100%;
      }
    }

    .login-box-right {
      width: 320px;
      padding: 40px;
      position: relative;

      .content-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #2f3038;
        margin-bottom: 30px;
        position: relative;
        h2,
        p {
          margin: 0;
        }
        h2 {
          font-size: 30px;
        }
        .register-wrap {
          font-size: 14px;
          position: absolute;
          left: 128px;
          bottom: 4px;
        }
        .registered {
          color: #4680ff;
          cursor: pointer;
        }
      }

      .qrcode-scan-wrap {
        display: flex;
        position: absolute;
        right: 10px;
        top: 10px;
        cursor: pointer;
      }

      .login-form {
        .login-form-row {
          position: relative;
          margin: 30px 0;
        }

        .reset-form-row {
          position: relative;
          margin: 16px 0;

          .btn-get-code {
            position: absolute;
            top: 5px;
            right: 0;
            width: 110px;
            height: 36px;
            outline: none;
            border-bottom: 0 none;
            border-radius: 0;
            padding: 0;
            border: none;
            color: #4680ff;
            font-size: 13px;
            font-weight: normal;
            &.is-disabled {
              background: #eef1f6;
              color: #bfcbd9;
            }
            &:hover {
              background: none;
            }
          }
        }

        input {
          color: #545353;
          font-size: 14px;
          width: 100%;
          height: 40px;
          text-indent: 100px;
          border: 0 none;
          border-bottom: 1px solid #e1e7f0;
          border-radius: 3px;
          background: #fff;
        }

        ::v-deep .graph-code {
          input {
            padding-left: 0;
            padding-right: 0;
            &::-webkit-input-placeholder {
              color: #545353;
            }
          }
          img {
            top: 0;
          }
        }

        input:focus,
        ::v-deep .graph-code input:focus {
          border-bottom: 1px solid #a5bbcd;
        }

        .login-ipt-ic {
          position: absolute;
          top: 11px;
          left: 12px;
          font-size: 14px;
          color: #545353;
        }
      }

      .login-form-box {
        input {
          text-indent: 50px !important;
        }
        .login-ipt-ic {
          top: 14px !important;
        }
      }

      .login-href {
        display: flex;
        justify-content: flex-end;
        margin-top: -24px;
        a {
          color: #4680ff;
          font-size: 14px;
          cursor: pointer;
          margin-left: 10px;
        }
      }

      .login-btns {
        text-align: center;
        margin-top: 30px;

        .el-button {
          line-height: 200%;
          border: none;
          width: 100%;
          font-size: 16px;
          font-weight: 500;
          background: #ce4c4c !important;
        }
      }
    }
  }

  .el-carousel {
    height: 100%;
  }

  .login-bg {
    width: 100%;
    height: 100%;
  }

  .copyright-box {
    position: fixed;
    bottom: 10px;
    width: 100%;
    color: #545353;
    font-size: 14px;
    text-align: center;
  }

  .qrcode-wrap {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 150px;
    z-index: 2;

    .qrcode-bg {
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 100px;
      z-index: -1;
      background-color: rgba(0, 0, 0, 0.3);
    }
    /* 
    .qrcode {
      height: 100%;
      background: url('../../assets/login_qrcode.png') no-repeat center center;
      background-size: contain;
      text-align: right;

      img {
        margin: 60px 20px;
        width: 20px;
        height: 20px;
        cursor: pointer;
      }
    } */
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.7s;
  }
  .fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
    opacity: 0;
  }
}

.service-agreement {
  text-align: center;
  color: #6e6e6e;
  font-size: 12px;
  margin-top: 20px;
  .service {
    color: #4680ff;
    cursor: pointer;
  }
}

.error-tip {
  height: 20px;
  color: #f30;
  font-size: 12px;
  line-height: 20px;
}
</style>
