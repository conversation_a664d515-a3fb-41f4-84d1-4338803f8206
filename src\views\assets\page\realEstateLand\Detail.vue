<template>
  <Dialog title="房产土地详情" width="1000px"  :visible="visible" @close="handleClose" @open="handleOpen" v-loading="loading">
    <div slot="body">
      <el-descriptions title="" :column="3" border>
        <el-descriptions-item v-for="(item, index) in list" :key="index" :label="item.title">{{ item.label }}</el-descriptions-item>
        <el-descriptions-item label="附件资料">
          <el-link type="primary" @click="openuploader(data.appendixs)">查看（{{ data.appendixs.length }}）</el-link>
        </el-descriptions-item>
        <el-descriptions-item label="房屋图片" span="2">
          <el-link type="primary" @click="openuploader(data.pictures)">查看（{{ data.pictures.length }}）</el-link>
        </el-descriptions-item>
        <el-descriptions-item label="备注" span="3">{{ data.remark }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <div slot="footer" class="footer">
      <!-- <el-button>打印</el-button> -->
      <el-button type="primary" @click="handleClose">关闭</el-button>
    </div>
  </Dialog>
</template>
<script lang="ts">
import { Component, Emit, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import { List } from 'echarts'
import { houseLandInforDetail } from '@/api/assets'
@Component({
  name: 'Container',
  components: {
    Dialog
  }
})
export default class Container extends Vue {
  @Prop({ default: () => [] }) private list!: Array<{ title: string; label: string }>
  @Prop() private visible!: boolean
  @Prop() private data!: boolean
  private loading = false
  // 关闭
  @Emit('changeShowDetail')
  private handleClose() {
    return false
  }
  // 打开文件
  @Emit('showUpdaload')
  private openuploader(list: []) {
    return List
  }
  // private mounted(){
  //       this.gethouseLandInforDetail()
  // }
  // 打开弹窗事件
  private handleOpen() {
    this.gethouseLandInforDetail()
    this.loading = true
  }
  // 获取房产土地详情
  private async gethouseLandInforDetail() {
    // houseLandInforDetail
    try {
      let res = await houseLandInforDetail({
        companyCode: '1',
        sysAssetNo: '2'
        // size: 3,
      })
      if (res.success) {
        this.data = res.data.records
        this.loading = false
      }
    } catch (e) {
      console.error(e)
      this.loading = false
    }
  }
}
</script>

<style lang="scss" scoped>
// checkTheDetails
::v-deep.el-descriptions-item__label {
  font-size: 14px;
  color: #909399;
}
::v-deep.el-descriptions .is-bordered .el-descriptions-item__cell {
  font-size: 14px;
  font-weight: 600;
}
.footer {
  display: flex;
  justify-content: space-between;
}
</style>
