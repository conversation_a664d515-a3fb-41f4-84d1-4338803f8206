<template>
  <iframe :src="src"
    ref="iframe"
    width="100%"
    height="100%"
    frameborder="0"
    allowfullscreen />
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { adminRouterUrl } from '@/utils/index'

@Component({})
export default class HouseCertificate extends Vue {
  private src = ''

  created() {
    this.src = adminRouterUrl('/business/assets/filingManagement/afterwards')
  }
}
</script>