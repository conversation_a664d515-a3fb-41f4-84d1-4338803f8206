<!-- 投资项目计划备案 -->
<template>
  <el-container class="records"
    direction="vertical">
    <search-bar :items="searchItems"
      v-if="mode=='index'"
      @onSearch="handleSearch">
      <el-button v-if="isCompany"
        class="primary-buttom"
        type="primary"
        @click="onAdd">新增</el-button>
    </search-bar>

    <!-- 表格 -->
    <Grid ref="grid"
      :columns="cols"
      :show-pagination="true"
      :overflow-tooltip="true"
      :show-index="true"
      :remote-url="remoteUrl"
      :search-params="searchParams"
      @row-click="onSee">
      <template #approvalStatusSlot="{row}">
        <div
          :class="[row.approvalStatus === 1 && 'audit-success', row.approvalStatus === 1 && 'audit-pending', row.approvalStatus === 1 && 'audit-danger']">
          {{ row.approvalStatus }}</div>
      </template>
      <template #operationSlot="{row}"
        v-if="mode=='index'">
        <Operation :list="operationList"
          :row="row" />
      </template>
    </Grid>

    <!-- 新增 -->
    <AddRecordDialog v-if="AddRecordDialogVisible"
      :visible.sync="AddRecordDialogVisible"
      @success="refreshGrid" />

    <!-- 调整 -->
    <AdjustRecordDialog v-if="AdjustRecordDialogVisible"
      :visible.sync="AdjustRecordDialogVisible"
      :detail="currentRow"
      @success="refreshGrid" />

    <!-- 查看 -->
    <RecordDetailDialog v-if="recordDetailDaologVisible"
      :visible.sync="recordDetailDaologVisible"
      :mode="recordDetailMode"
      :detail="currentRow"
      @success="refreshGrid" />
  </el-container>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import Grid from '@/components/Grid/index.vue'
import searchBar from '@/components/SearchBar/index.vue'
import Operation, { OperationItem } from '@/components/Operation/index.vue'
import AddRecordDialog from './components/AddRecordDialog.vue'
import AdjustRecordDialog from '../RecordsAdjustment/components/AdjustRecordDialog.vue'
import RecordDetailDialog from './components/RecordDetailDialog.vue'
import Uploader from '@/components/Uploader/index.vue'
import { Confirm } from '@/decorators'
import { deletePlanFiling } from '@/api/projectInvestment'

interface RecordItem {
  approvalStatus: string // 审批状态
  auditCompletionTime: string // 审批完成时间
  boardResolution: string // 董事会决议
  businessSubmissionTime: string // 企业提交时间
  createDept: number // 填报单位名称
  createTime: string // 创建时间
  createUser: number // 创建人
  enterpriseSubmitter: string // 企业提交人
  id: number
  investmentPlanReport: string // 投资计划报告
  isDeleted: number // 是否删除
  orgCode: string
  orgName: string
  otherDecisionDocuments: string // 其他决策文件
  projectSchedule: string // 项目计划
  recordNumber: string // 备案编号
  remark: string // 备注
  reviewFeedback: string // 反馈
  status: number
  updateTime: string
  updateUser: number
  year: string // 年份
}

@Component({
  components: {
    Grid,
    searchBar,
    Operation,
    Uploader,
    AddRecordDialog,
    RecordDetailDialog,
    AdjustRecordDialog
  }
})
export default class Project extends Vue {
  @Prop({ default: () => 'index' }) private mode?: 'index' | 'audit'
  private readonly remoteUrl = '/fht-monitor/invest/plan/page'

  private AddRecordDialogVisible = false
  private AdjustRecordDialogVisible = false

  // 查看或审批
  private recordDetailDaologVisible = false
  private recordDetailMode = 'see'

  private currentRow: any = {}

  // 附件列表
  private accessoryList: string[] = []
  private accessoryTitle = ''
  private accessoryVisible = false

  private searchItems = [
    {
      type: 'text',
      key: 'keyword',
      placeholder: '请输入项目编码/项目名称进行查询',
      width: '330px'
    },
    {
      type: 'year',
      key: 'year',
      label: '年度',
      placeholder: ''
    }
  ]

  private searchParams = {
    planType: 1
  }

  // 操作按钮
  private operationList: OperationItem[] = [
    {
      label: '查看',
      click: this.onSee,
      style: 3,
      visible: (row: any) => {
        return true
      }
    },
    {
      label: '审批',
      style: 2,
      click: this.onAudit,
      visible: (row: any) => {
        return !this.isCompany
      }
    },
    {
      label: '编辑',
      style: 2,
      click: this.onAdjust,
      visible: (row: any) => {
        return false
      }
    }
    // ,
    // {
    //   label: '删除',
    //   style: 1,
    //   click: this.onDelete,
    //   visible: (row: any) => {
    //     return this.isCompany
    //   }
    // }
  ]

  private onSee(row: any) {
    // 查看
    this.recordDetailMode = 'see'
    this.currentRow = row
    this.recordDetailDaologVisible = true
  }

  private onAudit(row: any) {
    // 审批
    this.recordDetailMode = 'audit'
    this.currentRow = row
    this.recordDetailDaologVisible = true
  }

  private onAdjust(row: any) {
    // 调整
    this.currentRow = row
    this.AdjustRecordDialogVisible = true
  }

  @Confirm({
    title: '提示',
    content: '是否确认删除该备案？',
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private async onDelete(row: any) {
    let res = await deletePlanFiling({ id: row.id })
    if (res.success) {
      this.$message.success(res.msg || '删除成功!')
      this.refreshGrid()
    }
  }

  private cols = [
    {
      prop: 'planNo',
      label: '备案编号',
      minWidth: 160
    },
    {
      prop: 'circulationStatusSlot',
      label: '业务状态'
    },
    {
      prop: 'year',
      label: '年份'
    },

    {
      prop: 'orgName',
      label: '填报单位名称',
      minWidth: 200
    },

    {
      prop: 'createUserName',
      label: '提交人',
      minWidth: 160
    },
    {
      prop: 'createTime',
      label: '提交时间',
      minWidth: 160
    },

    {
      prop: '',
      label: '操作',
      labelAlign: 'center',
      slotName: 'operationSlot',
      width: this.mode == 'audit' ? 0 : 50,
      fixed: 'right'
    }
  ]

  // 是否是企业端
  get isCompany() {
    return true
  }

  // 新增
  private onAdd() {
    this.AddRecordDialogVisible = true
  }

  private handleSearch(condition: any) {
    this.searchParams = Object.assign(condition, this.searchParams)
    this.refreshGrid()
  }

  // 表格更新
  private refreshGrid(isCurrent = false) {
    ;(this.$refs.grid as Grid).refresh(isCurrent)
  }

  // 账单展示
  private onAccessoryShow(accessoryList: any, accessoryTitle: string) {
    this.accessoryList = accessoryList
    this.accessoryTitle = accessoryTitle
    this.accessoryVisible = true
  }

  // 分割附件url
  private splitUrl(url = '') {
    return (url || '').split(',').filter((item) => item !== '') || []
  }
}
</script>

<style lang="scss" scoped>
.records {
  height: 100%;

  .accessory {
    color: #409eff;
    cursor: pointer;
  }
}
</style>