<!-- 土地证书 -->
<template>
  <el-container class="container" direction="vertical">
    <search-bar :items="searchItems" @onSearch="handleSearch" >
      <el-button type="primary">导入</el-button>
      <!-- <el-button type="primary">导出</el-button> -->
    </search-bar>
    <grid
      :remote-url="remoteUrl"
      :columns="cols"
      :show-pagination="true"
      :search-params="searchParams"
      :overflow-tooltip="true"
      @row-click="loaddetail"
      show-selection="true"
      show-index="true"
      show-index-fixed="left"
      ref="grid"
    >
      <template slot="appendixs" slot-scope="scope">
        <el-link
          type="text"
          @click="uploaderDetail(scope.row.appendixs)"
        >{{ scope.row.appendixs.length }}</el-link>
      </template>
      <template slot="operatingBar" slot-scope="scope">
        <el-button type="text" @click.stop="loaddetail(scope.row)">查看</el-button>
      </template>
    </grid>

    <detail-asset-info
    :detailInfo='Diadetaillist'
      v-if="visvileDetail"
      :visible.sync="visvileDetail"
    >
    </detail-asset-info>
  </el-container>
  
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import Grid from '@/components/Grid/index.vue'
import SearchBar from '@/components/SearchBar/index.vue'
import Uploader from '@/components/Uploader/index.vue'
import { BusinessModule } from '@/store/modules/businessDict'
import DetailAssetInfo from "./Detail.vue"
@Component({
  components: {
    Grid,
    SearchBar,
    DetailAssetInfo,
    Uploader
  }
})
export default class HouseCertificate extends Vue {
  // 文件上传
  private showUploader = false
  private UploaderList = []
  // 详细表格数据
  private appendixsLength!: number
  private Diadetaillist: object = []
  private visvileDetail = false
  private remoteUrl = '/fht-monitor/asset/lease/list'
  private searchItems = [
  
    {
      type: 'text',
      key: 'keyword',
      placeholder: '坐落/编号',
      width: '200px'
    }
  ]
  private cols = [
        {
      label: '合同名称',
      width: 160,
      prop: 'contractTypeDesc'
    },
    {
      label: '合同号',
      width: 160,
      prop: 'contractNo'
    },
       {
      prop: 'orgName',
      label: '管理单位',
      minWidth: 100
    },
    
    {
      prop: 'deptName',
      label: '	管理部门',
      minWidth: 80
    },
    {
      prop: 'address',
      label: '出租地址',
      minWidth: 100
    },
    {
      label: '经营性质',
      minWidth: 160,
      prop: 'businessNature'
    },
    {
      prop: 'certNo',
      label: '证件号'
    },
    {
      prop: 'cardTypeDesc',
      label: '证件类型'
    },
    {
      prop: 'years',
      label: ' 租赁期限',
      minWidth: 140
    },

    {
      prop: 'useArea',
      label: '使用面积',
      minWidth: 110
    },
    {
      prop: 'totalRentFee',
      label: '总金额(元)',
      
    },
    {
      prop: 'totalUnPaidFee',
      label: '未收总金额',
      minWidth: 110
    },
    {
      prop: 'unPaidDepositFee',
      label: '未收保证金',
      minWidth: 110
    },
    

    {
      prop: 'totalPaidFee',
      label: '实收总金额',
      minWidth: 110
    },
    {
      prop: 'totalFloor',
      label: '总楼层',
      minWidth: 100
    },
    {
      prop: 'startDate',
      label: '开始时间',
    },
    {
      prop: 'endDate',
      label: '结束时间'
    },
    {
      prop: 'signDate',
      label: '签约日期'
    },
    {
      prop: 'renterName',
      label: '承租人'
    },
    {
      prop: 'rentalArea',
      label: '租赁面积'
    },
    {
      prop: 'rentStatusDesc',
      label: '合同状态'
    },
    {
      prop: 'receivableDepositFee',
      label: '应收保证金'
    },
    {
      prop: 'paymentTypeDesc',
      label: '付款方式'
    },
    //资产信息    分摊面积 冻结理由 注销原因  管理单位  是否抵押 抵押银行 抵押时间 贷款项目 备注

    {
      prop: 'leaseWayDesc',
      label: '出租方式'
    },
    {
      prop: 'inceptRentFee',
      label: '起始年租金'
    },
    {
      prop: 'houseCode',
      label: '房屋编号'
    },
    {
      prop: 'flatDoor',
      label: '门牌号'
    },
    {
      prop: 'depositFee',
      label: '保证金',
      minWidth: 100
    },
    {
      prop: 'currentFloor',
      label: '当前楼层',
      minWidth: 100
    },

    {
      prop: 'fileCount',
      label: '附件数'
    },
    {
      prop: 'remark',
      label: '备注'
    },

    {
      slotName: 'operatingBar',
      label: '操作',
      minWidth: 50,
      fixed: 'right'
    }
  ]
  private searchParams = {
  }
  private isCertificateDetailVisible = false
  private detialItems = [
    {
      label: '管理单位'
    },
    {
      labe: '使用权人'
    },
    {
      label: '土地证号'
    },
    {
      label: '土地登记地址'
    },
    {
      label: '档案编号'
    },
    {
      label: '产权证书状态'
    },
    {
      label: '产权状态'
    },
    {
      label: '登记土地用途'
    },
    {
      label: '发证日期'
    },
    {
      label: '使用年限'
    },
    {
      label: '终止日期'
    },
    {
      label: '使用权类型'
    },
    {
      label: '使用权面积(㎡)'
    },
    {
      label: '分摊面积(㎡)'
    },
    {
      label: '独有面积(㎡)'
    },
    {
      label: '冻结理由'
    },
    {
      label: '注销理由'
    },
    {
      label: '注销时间'
    }
  ]
  // 是显示附件

  private uploaderDetail(List: any) {
    this.UploaderList = List
    this.showUploader = true
  }
  // 详情附件
  private showUpdaload(list: any) {
    this.uploaderDetail = list
    this.showUploader = true
  }
  // 改变详情显示隐藏
  private changeShowDialogDetail(state: boolean) {
    this.visvileDetail = state
  }
  private handleSearch(condition: any) {
    this.searchParams = condition
    ;(this.$refs['grid'] as any).refresh()
  }
  // 字典
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  //  操作
  private loaddetail(row: any) {
    // 被排除数组
    this.Diadetaillist = row
    this.visvileDetail = true
    
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
}
</style>