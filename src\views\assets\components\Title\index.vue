<template>
  <div class="title-content">
    <div class="title-start">{{title}}</div>
    <div class="title-end">
        <slot></slot>
    </div>
    
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

@Component({

  components: {}
})
export default class extends Vue {
    @Prop({ default: '标题' }) private title!: string // 标题
}
</script>

<style lang="scss" scoped>
.title-content {
  display: flex;
  justify-content: space-between;
  justify-items: center;
  color: #303133;
  margin: 15px 10px;
  .title-start {
    font-size: 16px;
    font-weight: bold;
  }
}
</style>