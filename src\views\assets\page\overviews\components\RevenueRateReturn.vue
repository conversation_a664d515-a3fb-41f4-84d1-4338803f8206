<template>
  <section class="echarts-dom-wrap">
    <div v-loading="loading"
      ref="chartDom"
      class="chartDom"
      :class="{'hide': !interfaceData.length}" />

    <el-select v-model="searchValue"
      placeholder="请选择商圈"
      class="search-box"
      :loading="loadingOptions">
      <el-option v-for="item in options"
        :key="item.code"
        :label="item.name"
        :value="item.code">
      </el-option>
    </el-select>

    <el-empty description="暂无数据"
      class="empty-none-data"
      :class="{'none': interfaceData.length}" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { assetsRegionZoneList, assetsRentTrendsYear } from '@/api/assetsv2'
import { Loading, Throttle } from '@/decorators'
import * as echarts from 'echarts'

let chartDom = null
let myChart: any = null

@Component({})
export default class extends Vue {
  @Prop() private orgCode!: string // 集团code
  @Prop() private areaCode!: string // 区域code
  @Prop() private year!: string // 年份

  private loading = false
  private loadingOptions = false
  private searchValue = ''
  private legendData: string[] = []
  private xAxisData: string[] = []
  private seriesData: any[] = []
  private interfaceData: any[] = []
  private options: any[] = []

  // 监听搜索条件改变
  @Watch('orgCode', { deep: true })
  @Watch('year', { deep: true })
  @Watch('searchValue', { deep: true })
  private watchFormData() {
    this.initData()
  }

  // 区域code改变时获取商圈下拉数据
  @Watch('areaCode', { deep: true })
  private watchAreaCode() {
    this.searchValue = ''
    this.getBusinessDistrict()
  }

  // 组件初始化
  private mounted() {
    this.$nextTick(() => {
      chartDom = this.$refs.chartDom
      myChart = echarts.init(chartDom as HTMLElement)

      this.initData()
    })
  }

  // 通过区域code获取商圈下拉数据
  @Loading('loadingOptions')
  private async getBusinessDistrict() {
    let { data } = await assetsRegionZoneList({
      code: this.areaCode
    })

    if (Array.isArray(data) && data.length) this.searchValue = data[0].code

    this.options = data || []
    this.initData()
  }

  // 获取数据
  @Throttle
  @Loading('loading')
  private async initData() {
    let { data } = await assetsRentTrendsYear({
      zoneCode: this.searchValue,
      orgCode: this.orgCode,
      year: this.year
    })

    this.interfaceData = data || []

    // let data = [
    //   {
    //     name: '城投集团',
    //     list: [
    //       {
    //         label: '2020',
    //         value: 100
    //       },
    //       {
    //         label: '2021',
    //         value: 45
    //       },
    //       {
    //         label: '2022',
    //         value: 67
    //       }
    //     ]
    //   },
    // ]

    // 组装数据
    let legendData: string[] = []
    let xAxisData: string[] = []
    let seriesData: any[] = []

    Array.isArray(data) &&
      data.forEach((item) => {
        legendData.push(item.name)

        let data: number[] = []
        let obj = {}

        // 样式2：各集团
        if (!item.isLine) {
          if (Array.isArray(item.list) && item.list.length) {
            item.list.forEach((itemList: { value: string }) => {
              data.push(+itemList.value)
            })

            obj = {
              name: item.name,
              type: 'bar',
              barWidth: 20,
              label: {
                show: true,
                position: 'top'
              },
              tooltip: {
                valueFormatter: function (value: number) {
                  return value + ' 元/m².天'
                }
              },
              data: data
            }
          }
        }

        // 样式1：营收趋势
        if (item.isLine) {
          if (Array.isArray(item.list) && item.list.length) {
            item.list.forEach((itemList: { value: string }) => {
              data.push(+itemList.value)
            })

            obj = {
              name: item.name,
              type: 'line',
              symbol: 'circle',
              yAxisIndex: 1,
              tooltip: {
                valueFormatter: function (value: number) {
                  return value + ' %'
                }
              },
              data: data
            }
          }
        }

        seriesData.push(obj)
      })

    if (Array.isArray(data) && data.length) {
      Array.isArray(data[0].list) &&
        data[0].list.forEach((item: { label: string }) => {
          xAxisData.push(item.label)
        })
    }

    this.legendData = legendData
    this.xAxisData = xAxisData
    this.seriesData = seriesData
    this.initEcharts()
  }

  // 初始化echarts组件
  private initEcharts() {
    let legendData = this.legendData
    let xAxisData = this.xAxisData
    let seriesData = this.seriesData

    let option = {
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        top: '26%',
        left: '2%',
        right: '1%',
        bottom: '0%',
        containLabel: true
      },
      legend: {
        data: legendData
      },
      xAxis: [
        {
          type: 'category',
          data: xAxisData,
          axisPointer: {
            type: 'shadow'
          },
          axisLabel: {
            formatter: '{value} 年'
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: '元/m².天',
          axisLabel: {
            formatter: '{value}'
          }
        },
        {
          type: 'value',
          name: '%',
          splitLine: {
            show: false
          },
          axisLabel: {
            formatter: '{value}'
          }
        }
      ],
      series: seriesData
    }

    myChart && myChart.setOption && myChart.setOption(option)
  }
}
</script>

<style scoped lang="scss">
.chartDom {
  width: 100%;
  height: 100%;
}

.search-box {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
}
</style>