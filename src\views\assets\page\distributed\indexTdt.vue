<template>
  <el-container v-loading="loading"
    element-loading-text="资产地图加载中，请稍候..."
    direction="vertical"
    class="asset-map">
    <!-- 地图容器 -->
    <div id="mapDiv" />

    <!-- 头部搜索 -->
    <ToolList ref="tool"
      :isSearch="false"
      @loadmark="addMarkers"
      class="asset-map-tool" />

    <!-- 地图说明 -->
    <el-card class="asset-map-description">
      <div class="asset-map-description-body">
        <el-link @click="changeType('0')"
          :type="ischeck == 0 ? 'primary' : ''">
          <i class="el-icon-location el-icon--right"
            style="color:#cb5a5a;"></i>
          空置
        </el-link>
        <el-link @click="changeType('1')"
          :type="ischeck == 1 ? 'primary' : ''">
          <i class="el-icon-location el-icon--right"
            style="color:#40c2c6;"></i>
          出租
        </el-link>
        <el-link @click="changeType('2')"
          :type="ischeck == 2 ? 'primary' : ''">
          <i class="el-icon-location el-icon--right"
            style="color:#338fd5;"></i>
          自用
        </el-link>
        <el-link @click="changeType('')"
          :type="ischeck == 2 ? 'primary' : ''">
          <i class="el-icon-refresh el-icon--right"
            style="color:#333;"></i>
          重置
        </el-link>
      </div>
    </el-card>

    <!-- 弹窗：附属资产详情 -->
    <AssetDetail v-if="visibleAssetDetail"
      :visible.sync="visibleAssetDetail"
      :markerinfo="markerInfo"
      @loaddetail="loaddetail"
      class="asset-map-detail" />

    <!-- 弹窗：资产详情 -->
    <DetailCom v-if="visibleDetailCom"
      :visible.sync="visibleDetailCom"
      :id="detailRow.id" />
  </el-container>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { assetsInfoMap } from '@/api/assetsv2'
import { Throttle } from '@/decorators'
import { coordinateTransformation } from '@/utils'
import ToolList from '@/views/assets/page/distributed/v2/ToolList.vue'
import AssetDetail from '@/views/assets/page/distributed/v2/AssetDetail.vue'
import DetailCom from '@/views/assets/page/manage/components/DetailCom.vue'

// 定义地图相关变量
let map: any
let control: any
declare const T: any

// 定义空置、出租、自用分类图标
let iconKz = new T.Icon({
  iconUrl: require('@/assets/images/assets/house.png'),
  iconSize: new T.Point(24, 30),
  iconAnchor: new T.Point(10, 25)
})

let iconCz = new T.Icon({
  iconUrl: require('@/assets/images/assets/houseUnused.png'),
  iconSize: new T.Point(24, 30),
  iconAnchor: new T.Point(10, 25)
})

let iconZy = new T.Icon({
  iconUrl: require('@/assets/images/assets/houseSelf.png'),
  iconSize: new T.Point(24, 30),
  iconAnchor: new T.Point(10, 25)
})

@Component({
  components: {
    ToolList,
    DetailCom,
    AssetDetail
  }
})
export default class extends Vue {
  private loading = false
  private visibleDetailCom = false
  private visibleAssetDetail = false
  private ischeck: string | number = 4
  private detailRow = {}
  private markerInfo = {}
  private params = {
    assetTypes: [1, 2],
    raidus: 1
  } // 资产请求参数
  private zoom = 14 // 地图缩放级别
  private lngLat = [119.663154, 29.100489] // 金华市经纬度

  // 初始化数据
  private mounted() {
    this.initMap()
  }

  // 加载地图
  private initMap() {
    map = new T.Map('mapDiv')
    map.centerAndZoom(new T.LngLat(this.lngLat[0], this.lngLat[1]), this.zoom)

    // 禁止双击地图放大
    map.disableDoubleClickZoom()

    // 获取资产数据，然后添加到地图上
    this.getAssetsData()
  }

  // 获取资产数据，然后添加到地图上
  @Throttle
  private async getAssetsData() {
    this.loading = true
    let res = await assetsInfoMap(this.params)
    let listMark = res.data || []

    this.addMarkers(listMark)
  }

  // 向地图上添加自定义标注和信息窗口
  private addMarkers(listMark: Array<any>) {
    // 添加标注前先清空原有标注点
    map.clearOverLays()

    // 关闭附属资产详情弹窗
    this.closeAssetDetail()

    // 批量添加标注
    let marker: any = null

    listMark.forEach((item: any) => {
      if (item.longitude == '' || item.latitude == '' || item.longitude == undefined || item.latitude == undefined) {
        return ''
      }

      // 百度地图经纬度转换为天地图经纬度
      let lngLatData = coordinateTransformation(item.longitude, item.latitude)
      item.longitude = lngLatData.lng
      item.latitude = lngLatData.lat

      // 0-空置 1-出租 2-自用
      if (item.type == 0) {
        marker = new T.Marker(new T.LngLat(item.longitude, item.latitude), { icon: iconKz })
      } else if (item.type == 1) {
        marker = new T.Marker(new T.LngLat(item.longitude, item.latitude), { icon: iconCz })
      } else if (item.type == 2) {
        marker = new T.Marker(new T.LngLat(item.longitude, item.latitude), { icon: iconZy })
      }

      map.addOverLay(marker)

      // 标注点点击，展示信息窗口
      let content: any = `
        <div class="tdt-windows-assetbox">
          <div class="info-title">${item.bizName}</div>
          <div class="info-header">
            <div>
              <img src="https://img2.baidu.com/it/u=1419977712,1630679449&fm=253&fmt=auto&app=120&f=JPEG?w=902&h=500" alt="">
            </div>
            <div class="info-header-content">
                <p style="font-size:22px;color: #303133;" class="info-header-title"> <span class="${
                  item.assetType == 1 ? 'info-tag1' : 'info-tag1'
                }">${item.assetType == 1 ? '房产' : '土地'}</span></p>
            <div>
                <p class="font2"><span class="font1">所属单位：</span>${item.orgName || '-'}</p>
                <p class="font2"><span class="font1">经营类别：</span>${item.purposes.join(',') || '-'}</p>
                <p class="font2"><span class="font1">资产用途：</span>${item.mangeTypeStr || '-'}</p>
            </div>
            </div>
          </div>
          <div class="info-table">
            <div>
              <p class="font1">出租率</p>
              <p class="font2">${Number(item.occupancyRate).toFixed(2) || '-'}%</p>
            </div>
            <div>
              <p class="font1">房间数量</p>
              <p class="font2">${item.itemNum || '-'}间</p>
            </div>
            <div>
              <p class="font1">账面价值(已出租)</p>
              <p class="font2">${Number(item.totalOriginalValue).toFixed(2) || '-'}万</p>
            </div>
        </div>
        <div class="asset-bottom">
          <span class="asset-detail-link" id="${item.id}_assetmap_detail">资产详情</span>
        </div>
      </div>
      `
      this.addMarkClick(item, content, marker)
    })

    setTimeout(() => {
      this.loading = false
    }, 3000)
  }

  // 标注点点击：展示信息窗口，给“资产详情”按钮绑定事件
  private addMarkClick(item: any, content: any, marker: any) {
    let that = this as any

    marker.addEventListener('click', function (e: any) {
      // 设置点击位置为新的中心点
      map.centerAndZoom(new T.LngLat(item.longitude, item.latitude), that.zoom)

      // 展示信息窗口
      let point = e.lnglat
      marker = new T.Marker(point)
      let markerInfoWin = new T.InfoWindow(content, { offset: new T.Point(0, -20) })
      map.openInfoWindow(markerInfoWin, point)

      // 信息窗口关闭，同步关闭"附属资产详情弹窗"
      markerInfoWin.addEventListener('clickclose', function (e: any) {
        that.closeAssetDetail()
      })

      // 展示附属资产详情弹窗
      that.visibleAssetDetail = true
      that.markerInfo = item

      // 点击“资产详情”按钮，弹窗显示详情信息
      let detailDom: HTMLElement | null = document.getElementById(`${item.id}_assetmap_detail`)

      detailDom &&
        detailDom.addEventListener('click', function () {
          that.visibleDetailCom = true
          that.detailRow = item
        })
    })
  }

  // 查看详情
  private loaddetail(detailInfo: any) {
    this.visibleDetailCom = true
    this.detailRow = detailInfo
  }

  // 改变类型
  private changeType(type: string | number) {
    ;(this.$refs.tool as any).assetsInfoMapbyType(type)
    this.ischeck = type
    this.loading = true
  }

  // 关闭附属资产详情弹窗
  private closeAssetDetail() {
    this.visibleAssetDetail = false
    this.markerInfo = {}
  }
}
</script>

<style scoped lang="scss">
.asset-map {
  width: 100%;
  height: 100%;
  position: relative;
}

.asset-map-detail {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1000;
  height: 96%;
  width: 400px;
}

.asset-map-tool {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1000;
  width: 420px;
}

#mapDiv {
  width: 100%;
  height: 100%;
  background: #eee;
}

::v-deep.asset-map-description {
  position: absolute;
  bottom: -2px;
  left: 0px;
  z-index: 500;
  .el-link {
    font-size: 18px;
  }
  .el-card__body {
    padding: 10px 7px 10px 10px;
    .el-link {
      margin-right: 10px;
      .el-link--inner {
        font-size: 16px;
      }
    }
  }
  .asset-map-description-body {
    display: flex;
    font-size: 25px;
  }
}
</style>

<style lang="scss">
#mapDiv {
  .tdt-bottom {
    display: none;
  }
  .tdt-infowindow-content-wrapper {
    border-radius: 4px;
    border: 1px solid #bdbcbc;
  }
  .tdt-infowindow-content {
    width: 400px !important;
    margin: 10px 20px 50px;
  }
  .tdt-infowindow-close-button {
    top: 4px;
    right: 6px;
    font-size: 28px;
  }
}

.tdt-windows-assetbox {
  background: #fff;
  width: 100%;
  height: 180px;
  display: flex;
  flex-direction: column;
  .info-title {
    font-size: 18px;
    font-weight: 400;
    margin-bottom: 6px;
  }
  .info-header {
    display: flex;
    height: 100px;
  }
  .info-header img {
    width: 100px;
    object-fit: cover;
    height: 100%;
  }
  .info-header-content {
    padding: 0px 0px;
    display: flex;
    flex-direction: row-reverse;
    height: 100px;
    text-align: start;
    font-size: 12px;
    justify-content: start;
    width: 100%;
    div {
      flex: 3;
      padding: 0px 10px;
      display: flex;
      flex-direction: column;
      height: 100px;
      font-size: 12px;
      justify-content: start;
      width: 100%;
    }
  }
  p {
    padding: 0;
    margin: 0;
    font-size: 12px;
  }
  .info-table {
    padding: 10px 0;
    display: flex;
    justify-content: space-between;
  }
  .font2 {
    color: #303133;
    font-size: 14px;
    height: 33%;
  }
  .font1 {
    color: #909399;
    font-size: 14px;
  }
  .asset-bottom {
    text-align: center;
    border-top: 1px solid #efefef;
    padding: 6px 0 20px;
  }
  .asset-detail-link {
    font-size: 16px;
    line-height: 30px;
    color: #b43c3c;
    cursor: pointer;
  }
  .info-header-title {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    .info-tag1 {
      text-align: center;
      padding: 0px 6px;
      border: 1px solid #ce4c4c;
      border-radius: 2px;
      border-radius: 2px;
      font-size: 12px;
      font-weight: 600;
      color: #ce4c4c;
      min-width: 30px;
    }
    .info-tag2 {
      padding: 0 6px;
      border: 1px solid #3dc0c5;
      border-radius: 2px;
      border-radius: 2px;
      font-size: 10px;
      color: #3dc0c5;
      font-weight: 600;
      min-width: 30px;
      text-align: center;
    }
  }
}
</style>