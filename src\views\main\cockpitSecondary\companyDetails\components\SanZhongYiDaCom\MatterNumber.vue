/** 重大事项次数 */
<template>
  <div class="wrapper">
    <div id="MatterNumber" />
    <div class="title">重大项目安排事项</div>
    <div class="halo"></div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { MatterNumberData, colorSixList } from '@/views/main/cockpitSecondary/baseData'
import { capitalProfile } from '@/api/cockpit'
import * as echarts from 'echarts'
import { deepClone } from '@/utils'

type EChartsOption = echarts.EChartsOption

@Component
export default class extends Vue {
  private loading = false
  private maxValue = 100
  private year = ''
  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}
  private tabList = []
  private echartsDatas: any[] = []
  private seriesData: any[] = [
    { value: 0, name: '产权转让' },
    { value: 0, name: '对外投资' },
    { value: 0, name: '机构调整' },
    { value: 0, name: '其他重要事项' }
  ]
  private realData: any = {
    CT: [
      { value: 7, name: '产权转让' },
      { value: 5, name: '对外投资' },
      { value: 0, name: '机构调整' },
      { value: 2, name: '其他重要事项' }
    ],
    KT: [
      { value: 6, name: '产权转让' },
      { value: 4, name: '对外投资' },
      { value: 0, name: '机构调整' },
      { value: 1, name: '其他重要事项' }
    ],
    SW: [
      { value: 4, name: '产权转让' },
      { value: 2, name: '对外投资' },
      { value: 0, name: '机构调整' },
      { value: 1, name: '其他重要事项' }
    ],
    JT: [
      { value: 3, name: '产权转让' },
      { value: 3, name: '对外投资' },
      { value: 0, name: '机构调整' },
      { value: 0, name: '其他重要事项' }
    ],
    GD: [
      { value: 1, name: '产权转让' },
      { value: 1, name: '对外投资' },
      { value: 0, name: '机构调整' },
      { value: 0, name: '其他重要事项' }
    ],
    SF: [
      { value: 0, name: '产权转让' },
      { value: 0, name: '对外投资' },
      { value: 0, name: '机构调整' },
      { value: 0, name: '其他重要事项' }
    ]
  }

  // 当前年月
  get getCurrentDate() {
    let date = new Date()
    return `截止到${date.getFullYear()}年${date.getMonth() + 1}月`
  }

  get currentTabCode(): any {
    return this.$route.query ? this.$route.query.id || '' : ''
  }

  get sum() {
    return this.seriesData.reduce((sum: any, currentValue: any) => {
      return (sum += +currentValue.value || 0)
    }, 0)
  }

  // 筛选数据
  private filterData() {
    this.seriesData = this.realData[this.currentTabCode] || [
      { value: 0, name: '产权转让' },
      { value: 0, name: '对外投资' },
      { value: 0, name: '机构调整' },
      { value: 0, name: '其他重要事项' }
    ]
    this.initEcharts()
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('MatterNumber') as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    this.filterData()
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await capitalProfile()
    if (!Array.isArray(data) || !data.length) return
    this.echartsDatas = data
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 初始化数据
  private initEcharts() {
    let textSize = 22
    let textColor = '#999'
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    this.option = {
      color: ['#E6B607', '#EF702E', '#35AAF6', '#00AEA5', '#EC342F', '#2D5AE5'],
      title: {
        top: '43%',
        left: 'center',
        text: this.sum,
        subtext: '总量',
        textStyle: {
          color: '#E6B607',
          fontWeight: 'bold',
          fontSize: textSize * 1
        },
        subtextStyle: {
          color: '#fff',
          fontWeight: 'bold',
          fontSize: textSize * 0.8
        }
      },
      legend: {
        show: false
      },
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          type: 'pie',
          center: ['50%', '55%'],
          radius: ['30%', '57%'],
          avoidLabelOverlap: false,
          // stillShowZeroSum: false,
          selectedMode: 'single',
          label: {
            show: true,
            position: 'outside',
            fontSize: textSize * 0.6,
            width: 60,
            color: '#5db0ea',
            fontWeight: 'bold'
          },
          itemStyle: {
            borderRadius: 0,
            borderWidth: 10
          },
          // labelLine: {
          //   length: 30,
          //   length2: 60,
          //   maxSurfaceAngle: 80,
          //   lineStyle: {
          //     width: 3,
          //   },
          // },
          data: this.seriesData
        }
      ]
    }

    this.myChart && this.myChart.setOption(this.option)
  }
}
</script>

<style scoped lang="scss">
#MatterNumber {
  width: 100%;
  height: 100%;
}
.wrapper {
  position: relative;
  width: 320px;
  height: 240px;
  .title {
    position: absolute;
    left: 0;
    top: 0;
    font-size: 26px;
    font-weight: bold;
  }
  .halo {
    position: absolute;
    z-index: -1;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -42%);
    width: 140px;
    height: 140px;
    border-radius: 50%;
    border: 12px solid RGBA(20, 42, 82, 1);
    box-shadow: 0 0 15px 0 #37a1df;
    animation: move 3s linear forwards infinite;
  }

  @keyframes move {
    0% {
      box-shadow: 0 0 15px 0 #37a1dfaa;
    }
    50% {
      box-shadow: 0 0 35px 0 #37a1df;
    }
    100% {
      box-shadow: 0 0 15px 0 #37a1dfaa;
    }
  }
}
</style>