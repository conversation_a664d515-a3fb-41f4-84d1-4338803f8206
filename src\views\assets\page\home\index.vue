<template>
  <section v-loading="loading" class="assets-home-wrap">
    <div class="content-box">
      <div v-for="(item, index) of linkList" :key="index" class="mode-box mode_link" @click="quickLinkClick(item)">
        <i :class="item.icon"></i>
        {{ item.name.split('(')[0] }}
      </div>
      <div class="mode-box mode_add" @click="showAddDialog">+编辑快捷方式</div>
    </div>
    <DialogAdd v-if="Addvisible" :visible.sync="Addvisible" @initData="initData" />
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { getLocalStorage, setLocalStorage } from '@/utils/cache'
import DialogAdd from './Dialogadd.vue'
import { entrymenuList } from '@/api/assetsv2'
@Component({
  components: {
    DialogAdd
  }
})
export default class extends Vue {
  private loading = false
  private Addvisible = false
  private quickList: any = []
  get linkList() {
    let list = this.quickList.filter((res: any) => {
      return res.children == undefined
    })
    return list
  }
  private linkRemoteList: any = []
  // 获取本地保存的侧边栏数据
  get getAsiderMenu() {
    let asider = []
    let asiderMenu = JSON.parse(getLocalStorage('saber-asiderMenu') || '')
    if (Array.isArray(asiderMenu) && asiderMenu.length) {
      let menuList = asiderMenu[0]
      if (Array.isArray(menuList.children) && menuList.children.length) {
        asider = menuList.children
      }
    }
    return asider
  }

  // 数据初始化
  private created() {
    this.initData()
  }

  // 初始化数据
  private async initData() {
    this.loading = true
    try {
      let res = await entrymenuList({})
      if (res.success) {
        this.linkRemoteList = res.data
        this.loading = false
        this.quickList = res.data
        this.$forceUpdate()
      }
    } catch (e) {
      console.error(e)
      this.loading = false
    }
    // let list: any = JSON.parse(getLocalStorage('checked-url') || ' ')
    // this.quickList.splice(0, 0)
  }
  //  打开弹窗
  private showAddDialog() {
    this.Addvisible = true
  }
  private quickLinkClick(data: any) {
    if (data.path.includes('http')) {
      window.open(data.path, '__blank')
    } else {
      this.$router.push(data.path)
    }
  }
}
</script>

<style scoped lang="scss">
.assets-home-wrap {
  // background: hsla(0, 0%, 100%, 0.5);
  position: relative;
  background: #fff;
  min-height: 100%;
  overflow-y: auto;
  // padding: ;
  box-sizing: border-box;

  .content-box {
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    justify-content: space-evenly;
    // grid-template-columns: ; 
    // // justify-content: flex-start;
    // justify-content: space-between;
    flex-wrap: wrap;

    .mode-box {
      margin: 5% 10%;
      // width: 9%;
      // min-width: 15%;
      border-radius: 3% 3% 0 0;
      border: 1px solid transparent;
      box-shadow: 4px 4px 8px 0px rgba(1, 1, 2, 0.43);
      height: 120px;
      padding: 10px 10px;
      text-decoration: none;
      white-space: wrap;
      text-overflow: ellipsis;
      overflow: hidden;
      // background-color: aquamarine;
      text-align: center;
      cursor: pointer;
      font-size: 20px;
      font-weight: 600;
    }

    .mode-box {
      // border-bottom: 1px solid #ce4c4c;
      background: #f8f7f7;
      color: #36292f;

      i {
        font-weight: 500;
        color: #ce4c4c;
      }
    }

    .mode_link:hover {
      transition: cubic-bezier(0.075, 0.82, 0.165, 1);
      // transition: ba;
      // border-bottom: 1px solid #ce4c4c;
      background: #ce4c4c;
      box-shadow: 400px 400px 800px 00px rgb(255, 251, 252);
      color: #fff;

      i {
        color: #fff;

      }
    }

    .mode_link {
      display: flex;
      flex-direction: column;
      text-align: center;
      justify-items: center;

      i {
        font-size: 60px;
      }
    }

    .mode_add {
      position: relative;
      text-align: center;
      cursor: pointer;
      font-size: 1em;
      line-height: 120px;
    }
  }

}
</style>
