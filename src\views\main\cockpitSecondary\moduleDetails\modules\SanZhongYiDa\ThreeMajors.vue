<template>
  <section class="three-majors-content-wrap">
    这个是留给原型最终确认后用的
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'

@Component
export default class extends Vue {
  //
}
</script>

<style scoped lang="scss">
.three-majors-content-wrap {
  position: relative;
  font-size: 30px;
  height: 500px;
  padding: 50px 60px;
  background: url('../../../images/panel_bg3.png') no-repeat left top;
  background-size: 100% 100%;
}
</style>




