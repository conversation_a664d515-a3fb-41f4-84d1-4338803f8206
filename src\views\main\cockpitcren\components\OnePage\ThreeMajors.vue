/* 三重一大监管 */

<template>
  <section class="three-majors-wrap">
    <TitleCom title="三重一大监管"
      module="SanZhongYiDa" />

    <div class="content-box">
      <img class="thow"
        @click="nextCarousel"
        src="@/views/main/cockpitcren/images/thow1.png" />
      <el-carousel ref="carousel"
        :autoplay="false"
        :interval="5000"
        trigger="click"
        indicator-position="none"
        class="carousel-box">
        <el-carousel-item>
          <div class="mode-box">
            <div class="vice vice-left">
              <TrinityMajorMatter />
            </div>
            <div class="vice vice-right">
              <TrinityMajorPeople />
            </div>
          </div>
        </el-carousel-item>
        <el-carousel-item>
          <div class="mode-box">
            <div class="vice vice-left">
              <TrinityMajorCadre />
            </div>
            <div class="vice vice-right">
              <TrinityMajorProjects />
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { ElCarousel } from 'element-ui/types/carousel'
import { isWindowFull } from '@/utils'
import TitleCom from '@/views/main/cockpitcren/components/Public/TitleCom.vue'
import TrinityMajorMatter from '@/views/main/cockpitcren/components/OnePage/TrinityMajorMatter.vue'
import TrinityMajorPeople from '@/views/main/cockpitcren/components/OnePage/TrinityMajorPeople.vue'
import TrinityMajorCadre from '@/views/main/cockpitcren/components/OnePage/TrinityMajorCadre.vue'
import TrinityMajorProjects from '@/views/main/cockpitcren/components/OnePage/TrinityMajorProjects.vue'

@Component({
  components: {
    TitleCom,
    TrinityMajorMatter,
    TrinityMajorPeople,
    TrinityMajorCadre,
    TrinityMajorProjects
  }
})
export default class extends Vue {
  // 判断窗口是否处于全屏模式
  get getIsWindowFull() {
    return isWindowFull
  }

  // 手动切换三重一大内容
  private nextCarousel() {
    ;(this.$refs['carousel'] as ElCarousel).next()
  }
}
</script>

<style scoped lang="scss">
.three-majors-wrap {
  position: relative;
  width: 100%;
  height: 500px;
  background: url('../../images/panel_bg3.png') no-repeat left top;
  background-size: 100% 100%;

  @keyframes keyMoveThow {
    0% {
      opacity: 1;
      transform: translateX(0);
    }
    50% {
      opacity: 0.5;
      transform: translateX(-20px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .content-box {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    font-size: 40px;
    padding: 50px 60px;
    box-sizing: border-box;
    .thow {
      position: absolute;
      right: -15px;
      bottom: 180px;
      z-index: 2;
      width: 100px;
      cursor: pointer;
      animation: keyMoveThow 5s infinite;
      &:hover {
        animation-play-state: paused;
      }
    }

    .mode-box {
      display: flex;
      height: 100%;
      .vice {
        overflow: hidden;
        box-sizing: border-box;
      }
      .vice-left {
        width: 40%;
      }
      .vice-right {
        width: 60%;
      }
    }
  }

  ::v-deep .el-carousel {
    height: 100%;
    .el-carousel__container {
      height: 100%;
    }
    .el-carousel__arrow {
      width: 0;
      overflow: hidden;
    }
  }
}
</style>