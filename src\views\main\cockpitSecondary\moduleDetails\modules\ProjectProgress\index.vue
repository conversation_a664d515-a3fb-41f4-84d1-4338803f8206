/* 投资管理 */

<template>
  <section class="project-progress">
    <div class="project-progress__content">
      <el-row :span="24"
        :gutter="48">
        <el-col :span="7">
          <!-- 项目投资概况、计划完成情况 -->
          <div class="pis-relative cockipt-approach-left-top">
            <!-- 数据改变提示层 -->
            <img v-if="visibleFlicker"
              class="cockipt-bg-flicker"
              src="@/views/main/cockpitcren/images/panel_bg1_cg.png" />

            <CommonModuleWrapper :borderGbMode="5"
              :activeTabs="activeTabs"
              :componentsName="projectInvestPlanCompletion"
              @tabChange="projectInvestmentTypeChange" />
          </div>

          <!-- 项目投资分类 -->
          <div class="pis-relative cockipt-approach-left-bottom">
            <!-- 数据改变提示层 -->
            <img v-if="visibleFlicker"
              class="cockipt-bg-flicker"
              src="@/views/main/cockpitcren/images/panel_bg2_cg.png" />

            <CommonModuleWrapper title="项目投资分类"
              :height="400"
              :activeTabs="activeTabs"
              :borderGbMode="4"
              bizAppCode="projectSion"
              detailsPath="/wel/index?bizAppCode=projectSion&path=/business/projectSion/planQuery"
              componentsName="NewProjectClassification" />
          </div>
        </el-col>
        <el-col :span="10">
          <!-- 各集团tabs  -->
          <CommonTabs :orgCode="activeTabs"
            module="ProjectProgress"
            class="cockipt-approach-middel-top"
            @commonTabsHandle="commonTabsHandle" />

          <!-- 年度计划投资情况 -->
          <div class="pis-relative cockipt-approach-middel-top">
            <!-- 数据改变提示层 -->
            <img v-if="visibleFlicker"
              class="cockipt-bg-flicker"
              src="@/views/main/cockpitcren/images/panel_bg6_cg.png" />

            <CommonModuleWrapper title="年度计划投资情况"
              tabsPosition="title"
              tabType="plansInvestment"
              :height="550"
              :activeTabs="activeTabs"
              :borderGbMode="6"
              bizAppCode="projectSion"
              detailsPath="/wel/index?bizAppCode=projectSion&path=/business/projectSion/planFiling"
              componentsName="PlannedInvestment"
              @tabChange="plansInvestmentTypeChange" />
          </div>

          <!-- 历年总投资趋势 -->
          <div class="pis-relative cockipt-approach-middel-bottom">
            <!-- 数据改变提示层 -->
            <img v-if="visibleFlicker"
              class="cockipt-bg-flicker"
              src="@/views/main/cockpitcren/images/panel_bg6_cg.png" />

            <CommonModuleWrapper title="历年总投资趋势"
              :height="560"
              :activeTabs="activeTabs"
              :borderGbMode="6"
              bizAppCode="projectSion"
              detailsPath="/wel/index?bizAppCode=projectSion&path=/business/projectSion/planQuery"
              componentsName="InvestmentTrend" />
          </div>
        </el-col>

        <el-col :span="7">
          <!-- 项目开工概况、重大项目进度 -->
          <div class="pis-relative cockipt-approach-right-top">
            <!-- 数据改变提示层 -->
            <img v-if="visibleFlicker"
              class="cockipt-bg-flicker"
              src="@/views/main/cockpitcren/images/panel_bg1_cg.png" />

            <CommonModuleWrapper :borderGbMode="5"
              :activeTabs="activeTabs"
              :componentsName="newProjectProgress" />
          </div>

          <!-- 建设时序占比 -->
          <div class="pis-relative cockipt-approach-right-bottom">
            <!-- 数据改变提示层 -->
            <img v-if="visibleFlicker"
              class="cockipt-bg-flicker"
              src="@/views/main/cockpitcren/images/panel_bg2_cg.png" />

            <CommonModuleWrapper title="建设时序占比"
              :height="390"
              :activeTabs="activeTabs"
              :borderGbMode="4"
              bizAppCode="projectSion"
              detailsPath="/wel/index?bizAppCode=projectSion&path=/business/projectSion/planQuery"
              componentsName="ConstructionSequence" />
          </div>
        </el-col>
      </el-row>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import CommonModuleWrapper, { ComponentItem } from '@/views/main/cockpitSecondary/components/CommonModuleWrapper.vue'
import { companyList } from '@/views/main/cockpitcren/baseData'
import CommonTabs from '@/views/main/cockpitSecondary/components/CommonActiveTabs.vue'

@Component({
  components: {
    CommonModuleWrapper,
    CommonTabs
  }
})
export default class extends Vue {
  private companyList = companyList
  private activeTabs = '0'
  private visibleFlicker = false

  private projectInvestPlanCompletion: ComponentItem[] = [
    {
      name: 'ProjectInvestment',
      height: 220,
      title: '项目投资概况',
      tabsPosition: 'title',
      tabType: 'projectInvestment',
      bizAppCode: 'projectSion',
      detailsPath: '/wel/index?bizAppCode=projectSion&path=/business/projectSion/planQuery'
    },
    {
      name: 'PlanCompletion',
      height: 520,
      title: '计划完成情况',
      bizAppCode: 'projectSion',
      detailsPath: '/wel/index?bizAppCode=projectSion&path=/business/projectSion/planFiling'
    }
  ]

  private newProjectProgress: ComponentItem[] = [
    {
      name: 'NewProject',
      height: 220,
      title: '项目开工概况',
      bizAppCode: 'projectSion',
      detailsPath: '/wel/index?bizAppCode=projectSion&path=/business/projectSion/planQuery'
    },
    {
      name: 'ProjectProgress',
      height: 530,
      title: '重大项目进度',
      bizAppCode: 'projectSion',
      detailsPath: '/wel/index?bizAppCode=projectSion&path=/business/projectSion/index'
    }
  ]

  private created () {
    // 如果链接上带有集团code，需要让tabs选中该集团
    let { query} = this.$route
    if(query && query.orgCode) {
      this.activeTabs = (query.orgCode as string) || '0'
    }
    // end
  }

  // 各个集团 tabs 切换触发
  private commonTabsHandle(activeTab: string) {
    this.activeTabs = activeTab
    this.flickerHandle()
  }

  // 项目概况类型变化
  private projectInvestmentTypeChange(tab: { code: string }) {
    this.$bus.$emit('projectInvestmentTypeChange', tab.code.trim())
  }

  // 计划投资tab切换
  private plansInvestmentTypeChange(tab: { code: string }) {
    this.$bus.$emit('plansInvestmentTypeChange', tab.code.trim())
  }

  // 触发背景闪烁效果
  private flickerHandle() {
    this.visibleFlicker = true
    setTimeout(() => {
      this.visibleFlicker = false
    }, 3000)
  }
}
</script>

<style scoped lang="scss">
::v-deep .common-tabs-default-wrap {
  margin: 30px 0;
}
</style>




