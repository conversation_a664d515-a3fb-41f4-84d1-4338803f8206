/* 资产构成 */

<template>
  <section class="asset-composition">
    <div class="asset-composition__content">
      <el-row :span="24"
        :gutter="24">
        <el-col :span="7">
          <CommonModuleWrapper v-for="company in leftCompanyList"
            :key="company.id"
            :companyId="company.id"
            :title="company.label"
            :seriesData="useData"
            subtext="总数"
            :hasTabs="false"
            tabType="company"
            :chartId="company.id"
            :height="360"
            :ringWidth="260"
            componentsName="ProportionOfRing" />
        </el-col>
        <el-col :span="10">
          <CommonModuleWrapper :title="getRankingTitle"
            :height="490"
            :seriesData="rankingList.map(item => item.value)"
            :companyList="rankingList.map(item => item.name)"
            :loading="loading"
            componentsName="RankingChart" />
          <CommonModuleWrapper :title="getRankingTitle"
            :height="100"
            :loading="loading"
            :source="summarizeData"
            :hasBorder="false"
            :indexLabel="currentItem.label"
            :hasTitle="false"
            componentsName="Summarize" />
          <CommonModuleWrapper :title="getRankingTitle"
            :height="620"
            :loading="loading"
            :hasTitle="false"
            :source="reverseRankingList"
            :indexLabel="currentItem.label"
            componentsName="IncrementRanking" />
        </el-col>
        <el-col :span="7">
          <CommonModuleWrapper v-for="company in rightCompanyList"
            :key="company.id"
            :companyId="company.id"
            :title="company.label"
            :seriesData="useData"
            subtext="总数"
            :hasTabs="false"
            tabType="company"
            :chartId="company.id"
            :height="360"
            :ringWidth="260"
            componentsName="ProportionOfRing" />
        </el-col>
      </el-row>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import CountTo from 'vue-count-to'
import CommonModuleWrapper from '@/views/main/cockpitSecondary/components/CommonModuleWrapper.vue'
import { deepClone } from '@/utils'
import { FinancialIndicatorLineData } from '@/views/main/cockpitSecondary/baseData'
import { capitalProfile } from '@/api/cockpit'
import { Loading } from '@/decorators'

export interface TabItem {
  label: string
  value: string | number
  percent: number
  unit: string
}

@Component({
  components: {
    CountTo,
    CommonModuleWrapper
  }
})
export default class extends Vue {
  private loading = false
  private data = deepClone(FinancialIndicatorLineData)
  private currentYear = new Date().getFullYear()
  private currentCompanyIndex = '0'
  private currentItem: any = {
    label: '资产总额',
    value: '',
    percent: 0,
    unit: '亿元',
    prop: 'asset_amount'
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  private leftCompanyList = [
    {
      label: '城投集团',
      id: 'CT'
    },
    {
      label: '金投集团',
      id: 'KT'
    },
    {
      label: '水务集团',
      id: 'SW'
    }
  ]

  private rightCompanyList = [
    {
      label: '交投集团',
      id: 'JT'
    },
    {
      label: '轨道集团',
      id: 'GD'
    },
    {
      label: '社发集团',
      id: 'SF'
    }
  ]

  get yearList() {
    let year = +this.getMomentTime()
    return [year - 6, year - 5, year - 4, year - 3, year - 2, year - 1, year]
  }

  private useData = [
    {
      value: 1048,
      name: '预留',
      itemStyle: { color: 'RGBA(74, 101, 226, 1)' }
    },
    {
      value: 735,
      name: '出租',
      itemStyle: { color: 'RGBA(96, 203, 179, 1)' }
    },
    {
      value: 735,
      name: '空置',
      itemStyle: { color: 'RGBA(46, 102, 162, 1)' }
    }
  ]

  get useDataSum() {
    return this.useData.reduce((sum, currentItem) => (sum += +currentItem.value), 0)
  }

  private companyList = [
    {
      code: '1',
      name: '城投集团',
      rate: 13.6,
      sum: 5234,
      amount: '2345.32'
    },
    {
      code: '4',
      name: '交投集团',
      rate: -13.6,
      sum: 5234,
      amount: '2345.32'
    },
    {
      code: '2',
      name: '金投集团',
      rate: -13.6,
      sum: 5234,
      amount: '2345.32'
    },
    {
      code: '5',
      name: '轨道集团',
      rate: -13.6,
      sum: 5234,
      amount: '2345.32'
    },
    {
      code: '3',
      name: '水务集团',
      rate: -13.6,
      sum: 5234,
      amount: '2345.32'
    },
    {
      code: '6',
      name: '社发集团',
      rate: -13.6,
      sum: 5234,
      amount: '2345.32'
    }
  ]

  private currentIndex = 0
  private tabsList: any[] = [
    {
      label: '资产总额',
      value: '',
      percent: 0,
      unit: '亿元',
      prop: 'asset_amount'
    },
    {
      label: '资产净额',
      value: '',
      percent: 0,
      unit: '亿元',
      prop: 'asset_net'
    },
    {
      label: '营业收入',
      value: '',
      percent: 0,
      unit: '亿元',
      prop: 'turnover'
    },

    {
      label: '利润总额',
      value: '',
      percent: 0,
      unit: '万元',
      prop: 'profit_amount'
    },

    {
      label: '有效投资',
      value: '',
      percent: 0,
      unit: '万元',
      prop: 'effective_invest'
    },
    {
      label: '净资产收益率',
      value: '',
      percent: 0,
      unit: '%',
      prop: 'asset_net_rate'
    },
    {
      label: '净资产负债率',
      value: '',
      percent: 0,
      unit: '%',
      prop: 'asset_debt_rate'
    },
    {
      label: '保值增值率',
      value: '',
      percent: 0,
      unit: '%',
      prop: 'asset_rise_date'
    }
  ]
  private summarizeData: any = {}
  private reverseRankingList: any[] = []
  private rankingList: any[] = [
    {
      code: '1',
      id: 'CT',
      name: '城投集团',
      value: 0
    },
    {
      code: '4',
      id: 'JT',
      name: '交投集团',
      value: 0
    },
    {
      code: '2',
      id: 'KT',
      name: '金投集团',
      value: 0
    },
    {
      code: '5',
      id: 'GD',
      name: '轨道集团',
      value: 0
    },
    {
      code: '3',
      id: 'SW',
      name: '水务集团',
      value: 0
    },
    {
      code: '6',
      id: 'SF',
      name: '社发集团',
      value: 0
    }
  ]
  private dataList: any[] = []

  // 当前年月
  get getCurrentDate() {
    let date = new Date()
    return `截止到${date.getFullYear()}年${date.getMonth() + 1}月`
  }

  get getRankingTitle() {
    return `${this.currentItem.label}排行情况`
  }

  created() {
    this.$bus.$on('BusCompanyTabs', (currentIndex: number) => {
      this.currentCompanyIndex = currentIndex + ''
      this.filterData()
    })
    // this.onItemClick(0, this.tabsList[0])
    this.capitalProfile()
  }

  // 获取财务指标
  @Loading('loading')
  private async capitalProfile() {
    let res = await capitalProfile()
    if (res.success) {
      this.dataList = res.data || []
      this.filterData()
    }
  }

  // 筛选数据
  private filterData() {
    // 筛选出当前年份数据
    let currentYearList = this.dataList.filter((item) => item.year === +this.currentYear)

    // 筛选出 tabsList 数据
    this.tabsList.forEach((tab) => {
      // 筛选出国资总况数据
      let currentCompanyData = currentYearList.find(
        (item) => item.itemCode === tab.prop && item.companyCode === this.currentCompanyIndex
      ) || {
        itemDesc: '0.00',
        itemName: '',
        itemValue: ''
      }
      let allData = currentYearList.find((item) => item.itemCode === tab.prop && item.companyCode === '0') || {
        itemDesc: '0.00',
        itemName: '',
        itemValue: ''
      }
      tab.allData = allData
      tab.value = tab.unit === '%' ? (+currentCompanyData.itemValue).toFixed(2) : currentCompanyData.itemValue
      tab.percent = +(+currentCompanyData.itemDesc * 100).toFixed()
    })

    // 计算六大集团各指数排名
    this.rankingList.forEach((company) => {
      let indexData = currentYearList.find((item) => item.itemCode === this.currentItem.prop && item.companyCode === company.code) || {
        itemDesc: '0.00',
        itemName: '',
        itemValue: ''
      }
      company.value = this.currentItem.unit === '%' ? +(+indexData.itemValue * 100).toFixed(2) : +(+indexData.itemValue).toFixed(2)
      company.rate = +(+indexData.itemDesc * 100).toFixed(2)
      company.sum = (
        currentYearList.find((item) => item.itemCode === 'asset_count' && item.companyCode === company.code) || {
          itemDesc: '0.00',
          itemName: '',
          itemValue: ''
        }
      ).itemValue
    })

    this.rankingList = this.rankingList.sort((a: any, b: any) => a.value - b.value)
    this.reverseRankingList = [...this.rankingList].reverse()
    // 筛选出 summarizeData
    this.summarizeData = {
      rate: +this.tabsList[this.currentIndex].allData.itemDesc * 100,
      name: this.rankingList[this.rankingList.length - 1].name,
      companyRate: this.rankingList[this.rankingList.length - 1].rate || 6.9,
      sum: +this.tabsList[this.currentIndex].allData.itemValue,
      companySum: this.rankingList[this.rankingList.length - 1].value
    }

    // 筛选出各集团数据
    // this.companyList.forEach((company) => {
    //   // 筛选出各集团信息
    //   let currentCompanyDataList = this.dataList.filter((item) => company.code === item.companyCode)
    //   // 筛选出各集团信息后按年度分类 按指数
    //   let indexList: Record<string, any[]> = {}
    //   currentCompanyDataList.forEach((item) => {
    //     if (indexList[item.itemCode]) {
    //       indexList[item.itemCode].push(item)
    //     } else {
    //       indexList[item.itemCode] = []
    //     }
    //   })
    //   for (let index in indexList) {
    //     indexList[index] = indexList[index]
    //       .sort((a: any, b: any) => b.year - a.year)
    //       .map((item) => {
    //         return (+item.itemValue).toFixed(2)
    //       })
    //   }
    //   company.data = indexList
    // })
  }

  private onItemClick(index: any, data: { label: string }) {
    this.currentIndex = index
    this.currentItem = data
    this.loading = true
    setTimeout(() => {
      this.loading = false
    }, 1000)
  }
}
</script>

<style scoped lang="scss">
</style>




