/**
  组件描述:  项目投资概况
*/
<template>
  <section v-loading="loading"
    class="project-investment">
    <div class="project-investment__item"
      v-for="(item, index) in indexList"
      :key="index">
      <span>{{ item.indicatorName }}</span>
      <span class="value">{{ item.indicatorValue }}</span>
      <span :class="['rate', +item.indicatorRate > 0 && 'up', +item.indicatorRate < 0 && 'down']">
        <!-- <span>同比</span>
        <span><i v-if="+item.indicatorRate > 0">+</i>{{ item.indicatorRate }}%</span> -->
      </span>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import { getInvestmentProfile, ProjectProgressDataItem } from '@/api/cockpit'
import { Loading } from '@/decorators'

@Component
export default class extends Vue {
  @Prop({ default: '0' }) private activeTabs!: string

  private loading = false

  // 参数
  private params: any = {
    orgCode: '0',
    year: String(new Date().getFullYear()),
    projectProperty: ''
  }

  // 指标列表
  private indexList: ProjectProgressDataItem[] = []

  @Watch('activeTabs', { immediate: true })
  private activeTabsChange() {
    this.params.orgCode = this.activeTabs
    this.fetchData()
  }

  mounted() {
    this.fetchData()
    this.listenerDate()
    this.$bus.$on('projectInvestmentTypeChange', (projectProperty: string) => {
      this.params.projectProperty = projectProperty
      this.fetchData()
    })
  }

  // 监听时间变化
  private listenerDate() {
    this.$bus.$on('BusYearTimeChange', (year: string) => {
      this.params.year = year
      this.fetchData()
    })

    this.$bus.$on('BusMoonTimeChange', (month: string) => {
      this.params.month = month
      this.fetchData()
    })
  }

  @Loading('loading')
  private async fetchData() {
    let res = await getInvestmentProfile(this.params)
    if (res.success) {
      this.indexList = [...res.data]
    }
  }
}
</script>


<style scoped lang="scss">
.project-investment {
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 100%;
  transform: translateY(40px);
  &__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    font-size: 36px;
    span:first-child {
      font-weight: 400;
    }

    .value {
      color: #01e3f0;
      font-size: 48px;
      font-family: 'PangMenZhengDao';
      margin-top: 24px;
    }

    .rate {
      color: #909399;
      font-weight: bold;
      display: flex;
      align-items: center;
      span {
        font-weight: bold !important;
      }
      span:first-child {
        font-size: 30px;
        line-height: 30px;
      }
      span:last-child {
        margin-left: 8px;
      }
    }
    .up {
      color: #ff368b;
    }
    .down {
      color: #5aff3a;
    }
  }
}
</style>