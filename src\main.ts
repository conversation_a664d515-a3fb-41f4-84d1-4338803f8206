import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import '@/permission'
import { loadStyle } from '@/utils'
import '@/directives/permission'

// 引入自定义 sass 库
import '@/assets/scss/scssScript.scss'

// 引入自定义字体库
import '@/assets/fonts/fonts.css'

// 动态加载阿里云字体库
import { iconfontUrl, iconfontVersion } from '@/config/env'
iconfontVersion.forEach(ele => {
  loadStyle(iconfontUrl.replace('$key', ele))
})

// 引入主题样式
import './theme/index.scss'

// 全局设置权限函数
import { PermissionModule } from '@/store/modules/permissionDict'
Vue.prototype.$permission = function (code: string) {
  return PermissionModule.dictLaodData.includes(code)
}

// 引入附件文件打开方法（ 使用方式：this.$file.open(文件链接地址) ）
import file from '@/utils/file'
Vue.prototype.$file = file

// 引入时间日期插件
import moment from 'moment'
moment.locale('zh-cn')
Vue.prototype.$moment = moment

// 全局引入 vue $bus
import bus from '@/utils/bus'
Vue.prototype.$bus = bus

// 全量引入 ElementUI
import ElementUI from 'element-ui'
import VXETable from 'vxe-table'
import 'vxe-table/lib/style.css'
import Print from 'vue-print-nb'
Vue.use(Print)
Vue.use(VXETable)
// 全局自定义公共组件
import GlobalComponents from '@/components/index'
Vue.use(GlobalComponents)

// 自定义主题
import '@/assets/scss/element-variables.scss'
Vue.use(ElementUI, { size: 'small', zIndex: 3000 })

// 引入预览图片插件
import Viewer from 'v-viewer'
import 'viewerjs/dist/viewer.css'
Vue.use(Viewer, {
  defaultOptions: {
    zIndex: 9999
  }
})

// 监听路由变化，页面使用 public beforeRouteLeave(to: any, from: any, next: any) {... next()}
import { Component } from 'vue-property-decorator'
Component.registerHooks(['beforeRouteEnter', 'beforeRouteLeave', 'beforeRouteUpdate'])

Vue.config.productionTip = false

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
