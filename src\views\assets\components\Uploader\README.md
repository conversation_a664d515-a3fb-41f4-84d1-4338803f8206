## 自定义 props

| 名称          |                说明                |                                                                              默认值 |
| :------------ | :--------------------------------: | ----------------------------------------------------------------------------------: |
| title         |              弹窗标题              |                                                                            图片上传 |
| init-list     |           初始化图片列表           |                                                                                  [] |
| uploadable    |             是否可上传             |                                                                                true |
| uploader-tags |              图片标签              |                                                                                  [] |
| visible       |              是否可见              |                                                                                  [] |
| max-limit     |          上传图片数目限制          |                                                                                  15 |
| uploader-tip  |            温馨提示文案            | 目前最多支持\${maxLimit}张，支持 JPG/JPEG/PNG，可以拖动图片进行排序，支持批量上传。 |
| show-cover    |        是否显示首图封面标记        |                                                                                true |
| is-private    |       是否存储在私有 bucket        |                                                                               false |
| is-attachment |       是否可上传 word 等文件       |                                                                               false |
| is-tips       |      是否使用自定义温馨提示语      |                                                         false 结合 slot="tips" 使用 |
| is-attachment |   是否支持上传 word、excl 等文件   |                                                                               false | 返回带 name 和 url 的数组 |
| url-key       | 传入数据源为对象时，图片地址的键值 |                                                                                 url |

```
<uploader
  :init-list="picList"
  :uploadable="true"
  :visible="uploaderDlgVisible"
  :uploader-tags="uploaderTags"
  :uploader-tip=""
  :max-limit="15"
  :show-cover="true"
  :is-private="true"
  :url-key="picUrl"
  @uploadComplete="handleUploadComplete"
/>
```

#### 或者使用 v-model 代替 init-list 和 uploadComplate

```
<uploader
  title="退房图片"
  v-model="throwLeaseImgList"
  :uploadable="true"
  :visible="uploaderDlgVisible"
  :uploader-tags="uploaderTags"
  :uploader-tip=""
  :is-private="true"
  :max-limit="15"
  :url-key="picUrl"
  :show-cover="true"
/>
```
