// export function replaceOssStr(str: string) {
//   return str.replace(
//     /http:\/\/[^/]+\.oss-cn-jinhua-jhzwy-d01-a\.inner\.jhszwy\.net/gi,
//     'https://szjg.gzw.jinhua.gov.cn:9443/oss'
//   )
// }


export function replaceOssStr(str:any) {
  let strUrl = ''
  let replacedUrl = ''

  // 前置判断下 str 是否是一个对象，如果是，就提取里面的 data；如果不是就直接使用 str
  if(Object.prototype.toString.call(str) == '[object Object]') {
    if(str.data) {
      strUrl = str.data
    }
  } else if(Object.prototype.toString.call(str) == '[object String]') {
    strUrl = str
  } 
  // end

  // 判断是否是特定链接，只有链接中包含 jhsgzw-jhgzszjgxt-oss 才做匹配
  if(strUrl && strUrl.includes('jhsgzw-jhgzszjgxt-oss')) {
    // 匹配开头部分，直到第一个 /（不包括协议中的双斜杠）
    // 例如：http://xxxxx/ 这里的第一个“/”会被匹配
    const pattern = /^([^:]+:\/\/[^/]+)\//;

    // 替换为指定的地址 + 后面的路径
    replacedUrl = strUrl.replace(pattern, 'https://szjg.gzw.jinhua.gov.cn:9443/oss/');
  } else {
    replacedUrl = strUrl 
  }
  // end
  
  return replacedUrl;
}
