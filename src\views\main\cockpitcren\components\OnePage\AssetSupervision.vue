/* 资产监管 */

<template>
  <section v-loading="loading"
    class="assett-supervision-wrap">
    <TitleCom title="综合监管"
      module="FinancialIndicator" />

    <div v-if="dataList.length"
      class="content-box">
      <div v-for="(item, index) of dataList"
        :key="index"
        class="mode"
        @click="openDetail(item, index)">
        <p class="text">{{item.name}}</p>
        <CountTo :decimals="2"
          :startVal='0'
          :endVal='item.value'
          :duration='1500'
          class="count" />
        <i class="fs-30 m-f-l-20">亿元</i>
      </div>
    </div>
    <div v-else
      class="custom-data-none">暂无数据</div>

    <!-- 详情弹窗 -->
    <CockiptGridDialog v-if="visibleDetail"
      :visible.sync="visibleDetail"
      :year="year - 1"
      :closeModal="false"
      :code="tabActive.code"
      :columns="columns"
      :remoteUrl="remoteUrl"
      :searchParams="searchParams"
      :title="titleCockipt" />
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { capitalComponent } from '@/api/cockpit'
import { isWindowFull, bigNumberFormat } from '@/utils'
import CountTo from 'vue-count-to'
import TitleCom from '@/views/main/cockpitcren/components/Public/TitleCom.vue'
import CockiptGridDialog from '@/views/main/cockpitcren/components/Public/CockiptDialog.vue'

type typeTabItem = {
  id: string
  code: string
  name: string
}
type typeItem = {
  code: string
  value: number
  name: string
}

@Component({
  components: {
    CountTo,
    TitleCom,
    CockiptGridDialog
  }
})
export default class extends Vue {
  private loading = false
  private year = ''
  private titleCockipt = ''
  private dataList: any[] = []
  private interfaceData: any[] = []
  private tabActive: typeTabItem = {
    id: '',
    code: '',
    name: ''
  }

  // 详情表格数据
  private visibleDetail = false
  private remoteUrl = '/fht-monitor/ds/screen/data/asset'
  private searchParams = {}
  private columns: any[] = []
  // 固定资产数据
  private columnsGd = [
    {
      prop: 'qymc',
      label: '企业名称',
      minWidth: 140
    },
    {
      prop: 'zcmc',
      label: '资产名称',
      minWidth: 140
    },
    {
      prop: 'gyjz',
      label: '公允价值'
    },
    {
      prop: 'zcyz',
      label: '资产原值'
    },
    {
      prop: 'ljzj',
      label: '累计折旧'
    },
    {
      prop: 'ljzjl',
      label: '累计折旧率'
    },
    {
      prop: 'jzzb',
      label: '减值准备'
    }
  ]
  // 流动资产数据
  private columnsLd = [
    {
      prop: 'qymc',
      label: '企业名称',
      minWidth: 140
    },
    {
      prop: 'hbzj',
      label: '货币资金'
    },
    {
      prop: 'yszk',
      label: '应收账款'
    },
    {
      prop: 'yfkx',
      label: '预付款项'
    },
    {
      prop: 'ch',
      label: '存货'
    },
    {
      prop: 'qtysk',
      label: '其他应收款'
    },
    {
      prop: 'qtldzc',
      label: '其他流动资产'
    }
  ]
  // 非流动资产数据
  private columnsFld = [
    {
      prop: 'qymc',
      label: '企业名称',
      minWidth: 180
    },
    {
      prop: 'zqtz',
      label: '债权投资',
      minWidth: 180
    },
    {
      prop: 'kgcsjrzc',
      label: '可供出售金融资产',
      minWidth: 130
    },
    {
      prop: 'cydqzc',
      label: '持有到期投资',
      minWidth: 100
    },
    {
      prop: 'cqysk',
      label: '长期应收款',
      minWidth: 100
    },
    {
      prop: 'cqgqtz',
      label: '长期股权投资',
      minWidth: 100
    },
    {
      prop: 'qtqygjtz',
      label: '其他权益工具投资',
      minWidth: 130
    },
    {
      prop: 'qtfldjrzc',
      label: '其他非流动金融资产',
      minWidth: 140
    },
    {
      prop: 'tzxfdc',
      label: '投资性房地产',
      minWidth: 130
    },
    {
      prop: 'zjgc',
      label: '在建工程',
      minWidth: 100
    },
    {
      prop: 'wxzc',
      label: '无形资产',
      minWidth: 100
    },
    {
      prop: 'sy',
      label: '商誉'
    },
    {
      prop: 'cqdtfy',
      label: '长期待摊费用',
      minWidth: 130
    },
    {
      prop: 'dysdszc',
      label: '递延所得税资产',
      minWidth: 130
    },
    {
      prop: 'qtfldzc',
      label: '其他非流动资产',
      minWidth: 130
    }
  ]

  // 判断窗口是否处于全屏模式
  get getIsWindowFull() {
    return isWindowFull
  }

  // 万元数据转化成亿元
  get getBigNumberFormat() {
    return (num: string | number) => {
      return bigNumberFormat(+num).value || 0
    }
  }

  // 组件初始化
  private mounted() {
    this.year = String((this as any).$moment(new Date()).add('year', 0).format('YYYY') - 1)
    this.initData()

    // 中间各tabs切换触发数据更新
    this.$bus.$on('BusTabsChange', (data: typeTabItem) => {
      this.tabActive = data
      this.filterData()
    })

    // 时间改变时触发数据更新
    this.$bus.$on('BusYearTimeChange', (year: string) => {
      this.year = year
      this.yearHandel(year)
    })
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let year = this.year

    // 需要删除
    if (+year === 2022) {
      year = String(+year - 1)
    }
    // end

    let { data } = await capitalComponent({
      year: year,
      companyCode: +this.tabActive.code
    })

    this.interfaceData = (data as any[]) || []
    this.filterData()
  }

  // 筛选相关数据
  private filterData() {
    let filterSeries = this.interfaceData.filter((item) => {
      return +item.companyCode === +this.tabActive.code
    })

    let datas: Array<typeItem> = []
    filterSeries.forEach((item) => {
      datas.push({
        value: +this.getBigNumberFormat(+item.itemValue),
        name: item.itemName,
        code: item.itemCode
      })
    })

    this.dataList = datas
  }

  // 打开详情页面
  private openDetail(item: typeItem) {
    let type = 0
    switch (item.code) {
      case 'fixed_asset':
        type = 1
        this.columns = this.columnsGd
        break
      case 'current_asset':
        type = 2
        this.columns = this.columnsLd
        break
      case 'intangible_asset':
        type = 3
        this.columns = this.columnsFld
        break
    }

    this.titleCockipt = item.name
    // this.visibleDetail = true // 先暂时关闭
  }

  // 年份切换
  private yearHandel(year: string) {
    this.initData()
  }
}
</script>

<style scoped lang="scss">
.assett-supervision-wrap {
  position: relative;

  .custom-data-none {
    line-height: 120px;
  }

  .content-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .mode {
      flex: 1;
      height: 120px;
      overflow: hidden;
      cursor: pointer;
      i {
        font-weight: normal;
        font-family: 'PangMenZhengDao';
      }
      .text {
        margin: 0;
        font-size: 36px;
        margin-bottom: 14px;
      }
      .count {
        overflow: hidden;
        font-size: 50px;
        font-weight: normal;
        font-family: 'PangMenZhengDao';
      }
    }

    & .mode:nth-child(1) {
      .count,
      i {
        color: #ff368b;
      }
    }
    & .mode:nth-child(2) {
      margin-right: 10px;
      .count,
      i {
        color: #ffcd36;
      }
    }
    & .mode:nth-child(3) {
      .count,
      i {
        color: #3eeeff;
      }
    }
  }
}
</style>