<template>
  <section class="header-wrap"
    :class="{'header-collapse-wrap': isCollapse}">
    <div class="content-box">
      <!-- 左侧 -->
      <div class="conter-left">
        <div class="conter home-box">
          <el-button type="primary"
            size="small"
            icon="el-icon-s-home"
            style="font-size: 24px; padding: 10px;"
            @click="goHome" />
        </div>
        <div class="conter menu-box">
          <i class="icons el-icon-menu" />
          <span class="name">{{activeMenu.name}}</span>
          <div v-loading="loading"
            class="menu-list">
            <div v-for="item of menuList"
              :key="item.id"
              class="mode"
              :class="{'active':item.id === menuId}"
              @click="toBusiness(item)">
              <!-- <i :class="item.source" /> -->
              <img class="imgs"
                :src="require(`@/assets/images/wel/${item.code}.png`)" />
              <span>{{item.name}}</span>
              <el-tooltip v-if="!item.bizAppScope"
                class="lock-box"
                effect="dark"
                content="未开通权限，请联系管理员"
                placement="top-start">
                <i class="el-icon-lock" />
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧 -->
      <div class="conter-right">
        <!-- 流程中心 -->
        <el-badge class="badge">
          <el-tooltip effect="dark"
            content="流程中心"
            placement="bottom">
            <i class="iconfont iconicon_subordinate"
              @click="commandHandle('process')" />
          </el-tooltip>

        </el-badge>

        <!-- 系统消息按钮 -->
        <el-badge class="badge">
          <el-tooltip effect="dark"
            content="消息通知"
            placement="bottom">
            <el-badge :hidden="unReadCount === 0"
              :value="unReadCount"
              class="badge-message">
              <i class="el-icon-bell"
                @click="commandHandle('message')" />
            </el-badge>
          </el-tooltip>
        </el-badge>

        <!-- 用户头像、退出信息 -->
        <div class="user-info">
          <el-avatar size="small"
            class="m-r-4"
            :src="getUserInfo.avatar" />
          <el-dropdown class="m-l-4"
            @command="commandHandle">
            <span class="el-dropdown-link">
              <span class="user-name">{{getUserInfo.user_name}}</span>
              <i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="(item,index) of navbarList"
                :key="index"
                :divided="item.type === 'logOffUser' || item.type === 'logOut'"
                :command="item.type">
                {{item.name}}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { getHeaderMenu, getNoticeList } from '@/api/public'
import { Loading, Confirm } from '@/decorators'
import { BusinessModule } from '@/store/modules/businessDict'
import { getLocalStorage, setLocalStorage, removeLocalStorage, removeToken } from '@/utils/cache'
import { logUserUnable } from '@/api/public'

@Component({
  name: 'Header',
  components: {}
})
export default class Header extends Vue {
  @Prop({ default: false }) private isCollapse?: boolean

  private menuList: any[] = []
  private process = process.env
  private menuId = ''
  private loading = false
  private unReadCount = 0
  private activeMenu = {}
  private navbarList: Array<{
    name: string
    type: string
  }> = [
    {
      name: '首页',
      type: 'index'
    },
    {
      name: '个人信息',
      type: 'mine'
    },
    {
      name: '我的后台',
      type: 'backstage'
    },
    {
      name: '退出登录',
      type: 'logOut'
    }
  ]

  // 判断是生产还是开发环境
  get getProcess() {
    return process.env.NODE_ENV === 'development'
  }

  // 生产环境下，VUE_APP_BASE_URL 需要动态赋值
  get getAppBaseUrl() {
    let VUE_APP_BASE_URL = ''

    if (process.env.NODE_ENV === 'production') {
      if (window.location.origin.includes('dpm1-jh-fht-admin.mdguanjia.com')) {
        VUE_APP_BASE_URL = 'http://dpm1-jh-fht-admin.mdguanjia.com/'
      } else if (window.location.origin.includes('szjg.gzw.jinhua.gov.cn')) {
        VUE_APP_BASE_URL = 'https://szjg.gzw.jinhua.gov.cn:6443/'
      } else if (window.location.origin.includes('10.24.161.131')) {
        VUE_APP_BASE_URL = 'http://10.24.161.131:9990/'
      }
    }

    return VUE_APP_BASE_URL
  }

  // 获取用户信息
  get getUserInfo() {
    let userInfo = getLocalStorage('saber-userInfo') || ''
    return JSON.parse(userInfo).content
  }

  // 组件初始化
  private mounted() {
    this.initData()
    this.getNoticeList()

    // 触发业务模块切换
    this.$bus.$on('BustoBusiness', (params: { code: string; path: string }) => {
      let { code, path } = params

      // 跳转到外部链接
      if (path.includes('http')) {
        window.location.href = path
        return
      }

      // 跳转到业务系统
      if (code) {
        let headerMenu = getLocalStorage('saber-headerMenu') || ''

        if (!headerMenu) return

        console.log('🚀 进进进2', code, path)
        let headerMenuContent = JSON.parse(headerMenu).content

        if (Array.isArray(headerMenuContent) && headerMenuContent.length) {
          let find = headerMenuContent.find((item) => {
            return code === item.code
          })

          find.id && this.toBusiness(find, path)
        }
      }
    })
  }

  // 获取消息数字
  private async getNoticeList() {
    let res = await getNoticeList()
    this.unReadCount = res.data.unReadCount || 0
  }

  // 获取导航 menu 数据
  private async initData() {
    let data: any[] = []
    try {
      let res = await getHeaderMenu()
      data = res.data || []
      this.menuList = data || []

      this.getAsideMenu()
      this.activeMenuMode()
      this.getBusinessModule()
    } catch (error) {
      this.logOut()
    }
  }

  // 获取选中的模块
  private activeMenuMode() {
    let activeMenuId = getLocalStorage('saber-activeMenuId') || ''

    if (activeMenuId) {
      let menuId = JSON.parse(activeMenuId).content

      let menu = this.menuList.filter((item) => {
        return item.id === menuId
      })

      if (Array.isArray(menu) && menu.length) this.activeMenu = menu[0]
    }
  }

  // 获取对应模块侧边栏数据
  private getAsideMenu() {
    let activeMenuId = getLocalStorage('saber-activeMenuId') || ''
    if (activeMenuId) {
      this.menuId = JSON.parse(activeMenuId).content
      this.$bus.$emit('BusChangeMenu', this.menuId)
    }
  }

  // 获取各个业务系统字典项
  private async getBusinessModule() {
    let activeMenuId = getLocalStorage('saber-activeMenuId') || ''

    if (activeMenuId) {
      this.menuId = JSON.parse(activeMenuId).content

      let list = this.menuList.filter((item) => {
        return this.menuId === item.id
      })

      if (!list || !list.length || !list[0].bizAppCode) return

      await BusinessModule.getDictLoad(list[0].bizAppCode)
      this.$emit('changeIsMain')
    }
  }

  // 点击各模块，跳转到各个业务系统
  @Loading('loading')
  private async toBusiness(item: any, bizAppPath?: string) {
    if (!item.bizAppScope) {
      this.$message.warning('未开通权限，请联系管理员')
      return
    }

    let businessList = ['business', 'report'] // 这个是基础框架业务系统关键路由

    this.menuId = item.id

    if (item.name === '首页') {
      window.location.href = this.getAppBaseUrl + item.bizAppPath
      return
    }

    let activeMenuId = {
      content: item.id,
      dataType: 'string',
      dateTime: new Date().getTime()
    }

    setLocalStorage('saber-activeMenuId', JSON.stringify(activeMenuId))

    if (this.getProcess) {
      // 开发环境
      let isAdmin = false // 是否为admin的内容业务系统
      let path = bizAppPath || item.bizAppPath.split('/#/')

      if (Array.isArray(path) && path.length > 1) {
        for (let i = 0; i < businessList.length; i++) {
          if (path[1].split('/')[0] === businessList[i]) {
            isAdmin = true
            break
          }
        }

        if (isAdmin) {
          window.location.href = `${this.getAppBaseUrl}#/${path[1]}`
        } else {
          window.location.href = `${window.location.origin}/#/${path[1]}`
        }
      }
    } else {
      // 获取跳转链接，如果没有传入，就采用选中模块的 bizAppPath 作为跳转地址
      let bizAppPathUrl = bizAppPath || item.bizAppPath

      // 生产环境
      if (bizAppPathUrl) {
        // 通过模块的 item.bizAppPath，去判断是走 admin 还是走 monitor 框架
        if (item.bizAppPath.includes('/monitor/')) {
          if (bizAppPathUrl && bizAppPathUrl.includes('/monitor/')) {
            // 有写 monitor 关键字
            window.location.href = `${window.location.origin}${bizAppPathUrl}`
          } else {
            // 没有写 monitor 关键字
            window.location.href = `${window.location.origin}/monitor/#${bizAppPathUrl}`
          }
        } else {
          // 跳转到 admin 框架业务系统
          window.location.href = `${window.location.origin}/#${bizAppPathUrl}`
        }
      } else {
        // 跳转该业务系统首页
        window.location.href = window.location.origin
      }
    }

    // 点击头部导航，触发全局事件
    this.activeMenuMode()
    this.getBusinessModule()
    this.$bus.$emit('BusChangeMenu', item.id)
  }

  // 点击右侧下拉菜单触发
  private commandHandle(command: string) {
    switch (command) {
      case 'index':
        window.location.href = `${this.getAppBaseUrl}#/wel/index`
        break
      case 'mine':
        window.location.href = `${this.getAppBaseUrl}#/info/index`
        break
      case 'process':
        window.location.href = `${this.getAppBaseUrl}#/plugin/workflow/process/todo`
        break
      case 'message':
        window.location.href = `${this.getAppBaseUrl}#/plugin/message/message`
        break
      case 'backstage':
        window.location.href = `${this.getAppBaseUrl}#/desk/notice`
        break
      case 'logOut':
        this.logOutHandle()
        break
    }
  }

  // 基础框架首页
  private goHome() {
    window.location.href = this.getAppBaseUrl
  }

  // 退出登录
  @Confirm({
    title: '提示',
    content: '退出系统, 是否继续?'
  })
  private async logOutHandle() {
    this.logOut()
  }

  // 清除 cookie 退出系统
  private logOut() {
    removeToken()
    removeLocalStorage('saber-token')
    if (process.env.NODE_ENV === 'development') {
      window.location.href = `${window.location.origin}/#/login`
    } else {
      window.location.href = `${this.getAppBaseUrl}#/login`
    }
  }
}
</script>

<style scoped lang="scss">
.header-collapse-wrap {
  padding-left: 64px !important;
}

.header-wrap {
  $color: #ce4c4c;

  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 200px;
  background: $color;

  .user-name {
    color: #fff;
    font-size: 16px;
  }

  .content-box {
    width: 100%;
    height: 60px;
    display: flex;
    box-sizing: border-box;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    //border-bottom: 1px solid $color;
    /* box-shadow: 10px 0px 10px -10px #9b5c5c inset; */
  }

  .conter-left {
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .conter {
      height: 100%;
      display: flex;
      padding: 0 10px;
      justify-content: center;
      align-items: center;
      &:nth-child(1) {
        padding-left: 0;
      }
    }
    .icons {
      color: #fff;
      font-size: 26px;
      cursor: pointer;
    }
    .menu {
      height: 100%;
      margin: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      margin-right: 20px;
      cursor: pointer;
    }
    .active {
      color: $color;
    }
    .menu-box {
      position: relative;
      cursor: pointer;
      .name {
        color: #ffff;
        font-size: 14px;
        font-weight: 500;
      }
      &:hover {
        background: #fff;
        .icons {
          color: $color !important;
        }
        .name {
          color: $color !important;
        }
        .menu-list {
          opacity: 1;
          transform: scale(1);
        }
      }
      .menu-list {
        position: absolute;
        left: 0px;
        top: 60px;
        z-index: 9999999999;
        width: 480px;
        padding: 8px 4px 2px 10px;
        background: #fff;
        border-top: none;
        border-radius: 0 0 4px 4px;
        box-shadow: 0 0 6px rgba($color: $color, $alpha: 0.2);

        opacity: 0;
        transform: scale(0);
        transition: 0.5s;
        transform-origin: 0 0 0;

        display: flex;
        align-items: center;
        flex-wrap: wrap;
        .mode {
          width: 90px;
          height: 70px;
          overflow: hidden;
          display: flex;
          position: relative;
          text-align: center;
          align-items: center;
          flex-direction: column;
          justify-content: center;
          box-sizing: border-box;
          border-radius: 4px;
          background: #f7f9fa;
          margin: 0 6px 6px 0;
          cursor: pointer;
          .lock-box {
            position: absolute;
            top: 6px;
            right: 6px;
            color: #bbb;
            font-size: 16px;
          }
          &:hover {
            i,
            span {
              color: $color;
            }
            box-shadow: 1px 1px 1px 1px $color;
          }
          i {
            color: $color;
            font-size: 26px;
          }
          span {
            font-size: 14px;
          }
        }
        .imgs {
          width: 30px;
          margin-bottom: 4px;
        }
        .active {
          // background: rgba($color: $color, $alpha: 1);
          // border-bottom: none;
          i,
          span {
            color: $color;
          }
          box-shadow: 1px 1px 1px 1px $color;
        }
      }
    }
    i {
      font-size: 16px;
    }
    span {
      padding: 0 4px;
    }
  }

  ::v-deep .badge-message {
    .el-badge__content.is-fixed {
      top: -4px !important;
      right: 4px !important;
    }
  }

  .conter-right {
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .badge {
      color: #fff;
      font-size: 18px;
      margin-left: 15px;
      cursor: pointer;
    }
    .user-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-left: 20px;
      i {
        color: #fff;
      }
      ::v-deep .el-avatar {
        width: 30px;
        height: 30px;
        background: #ce4c4c;
        img {
          width: 100%;
          height: 100%;
          padding: 2px;
          border-radius: 100%;
          box-sizing: border-box;
          border: 1px solid #eee;
        }
      }
    }
  }
}
</style>
