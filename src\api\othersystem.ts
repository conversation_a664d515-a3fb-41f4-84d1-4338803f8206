import { method } from 'lodash'
import { searchAst } from './assetsv2'
import request from './request'
import { RemoteResponse } from '@/types'
import axios from 'axios'
// 百度地图配置安全密钥
let _AMapSecurityapiConfig: any = {
  securityJsCode: 'WB2SMc7f3KuyeIHq4eLXGCB7ut0H1hEN'
}
/**
 * @title  百度地图POI搜索
 * @param query
 */
// export const getV2BaiduPOI = (query: string): any => {
//   axios.get(
//     `https://api.map.baidu.com/place/v2/suggestion?query=${query}&region=金华市&city_limit=true&output=json&ak=${_AMapSecurityapiConfig.securityJsCode}`
//   )
// }
export const getV2BaiduPOI = (query: string): Promise<RemoteResponse> => {
  return request(
    `https://api.map.baidu.com/place/v2/suggestion?query=${encodeURIComponent(query)}&region=${encodeURIComponent('金华市')}&city_limit=true&output=json&ak=${_AMapSecurityapiConfig.securityJsCode}`,
    {},
    {
      method: 'GET'
    }
  )
}
