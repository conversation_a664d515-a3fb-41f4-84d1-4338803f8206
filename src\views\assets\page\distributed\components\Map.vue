<template>
  <section class="distributed-map-wrap">
    <div id="assetmap" />
    <tool-list
      class="assets-map-comtool"
      :show-put-away="showPutAway"
      :show-type="showType"
      @changeShowPutAway="changeShowPutAway"
      :Markerinfo="Markerinfo"
      @changeShowType="changeShowType"
      @changeMarkerinfo="changeMarkerinfo"
    />
  </section>
</template>

<script lang="ts">
import { random } from 'lodash'
import { Component, Vue } from 'vue-property-decorator'
import toolList from './ToolList.vue'
import { assetInformationbuild, assetInformationHouse, assetMapPoints } from '@/api/assets'
import { styleJson } from './baseData/styleJson'

let GMAp={}
declare const BMapGL: any // 声明 declare
declare const AMap: any // 声明 declare

@Component({
  components: {
    toolList
  }
})
export default class Container extends Vue {
  private showPutAway = false //显示数据栏
  private showType = false //显示具体详情？数据
  private map: any = Object.freeze({})
  private Markerinfo = {}
  private markLists: any = Object.freeze([])
  // 组件初始化
  private layer: any = Object.freeze({})
private assetsData=Object.freeze([])
  private assetMapPointsParams: any = {

  }
    private mapLocation = [119.647444, 29.079059]
  private mounted() {
    this.initMap()
    // this.getAssetInformationbuild()
  }
  // 拉取地图锚点数据
  // 拉取接口远程数据（住宅）
  private async getAssetInformationHouse() {
    let params = {
      size: 999
    }
    try {
      let res = await assetInformationHouse(params)
      if (res.success) {
        // this.option = res.data
      }
    } catch (e) {
      console.error(e)
    }
  }
  // 拉取接口远程数据(楼宇)
  private async getAssetInformationbuild() {
    let params = {
      size: 999,
      bizStatus: 1,
      current: 1
    }
    try {
      let res = await assetInformationbuild(params)
      if (res.success) {
        // this.option = res.data
      }
    } catch (e) {
      console.error(e)
    }
  }
  // 地图初始化
  private initMap() {
      ;(window as any).assetmap = new BMapGL.Map('assetmap', {
      minZoom: 10
    })
     this.setMap()
  }
  private setMap() {
    let map = (window as any).assetmap
    let mapLocation = this.mapLocation
    // map.setMapStyleV2({ styleJson: styleJson }) // 设置地图主题(https://lbsyun.baidu.com/customv2/help.html)
    map.centerAndZoom(new BMapGL.Point(mapLocation[0], mapLocation[1]), 14) // 设置中心点坐标和地图级别、地图3D视角
    map.enableScrollWheelZoom(true) // 开启鼠标滚轮缩放
    map.setHeading(64.5)
    map.setTilt(0)
    
  }
  
  
  // 请求远程列表数据
  private async getAssetMapPoints() {
    try {
      let res = await assetMapPoints(this.assetMapPointsParams)
      if (res.success) {
        this.loadmark(res.data)

      }
    } catch (e) {
      console.error(e)
    }
  }
  //  批量添加标记点
  private loadMarkerList(
    MapList: Array<{
      assetNo: string
      bizStatus: string
      latitude: string
      longitude: string
      sysAssetNo:string
    }>
  ) {
    Object.freeze(this.markLists)
    // this.layer.remove(this.markLists)
    this.markLists = []
    for (let i = 0; i < MapList.length; i++) {
      //  排除0点
      if (Number(MapList[i].longitude) !== 0 && Number(MapList[i].latitude) !== 0) {
        //
        let marker: any = new AMap.LabelMarker({
          name: MapList[i].bizStatus + MapList[i].assetNo,
          position: [Number(MapList[i].longitude), Number(MapList[i].latitude)],
          icon: {
            type: 'image',
            size: [25, 34],
            image: MapList[i].bizStatus == '住宅' ? require('@/assets/images/assets/zhu.png') : require('@/assets/images/assets/lou.png'),
            imageSize: [6, 9],
            // imageOffset: new AMap.Pixel(-95, -3),
            anchor: 'bottom-center'
          }
        })
        ;(marker.Markerinfo = MapList[i]),
          marker.on('click', (e: any) => {
            this.bandEvent(e)
          })
        this.markLists.push(marker)
      }
    }
    // 添加事件

    // this.map.add(this.markLists)
    // 增加图层

    // this.layer.add(this.markLists)
  }
  private loadmark(list:Array<any>){

    let len = list.length
    this.removeOverlay()

    let max = 500
   if(list.length>0){
      this.setNewCenter(list[0])
   }
    // this.removeOverlay()
    for (let i = 0; i < Math.ceil(len / max); i++) {
      setTimeout(() => {
        this.addMarkers(i * max, (i + 1) * max,list)
      }, i * 10 * 50)
    }
  }
  private bandEvent(e: any) {
    this.showPutAway = true
    setTimeout(() => {
      this.showType = true
      this.Markerinfo = e
    }, 100)

    // let infoWindow = new AMap.InfoWindow({
    //   //挂载的组件
    //   content: (this.$refs.infoWindow as Vue & { $el: () => any }).$el,
    //   anchor: 'middle-left',
    //   offset: new AMap.Pixel(5, -10)
    // })


    // })
  }

  // 地图基础控件添加
    private addMarkers(start: number, end: number,lists:Array<any>) {
    let map = (window as any).assetmap
    let that = this as any
    let assetsList = JSON.parse(JSON.stringify(lists))
    let list = assetsList.slice(start, end)
    list.forEach((item: any) => {
      let point = new BMapGL.Point(+item.longitude, +item.latitude)
      let marker = new BMapGL.Marker(point)
      map.addOverlay(marker)
      let opts = {
        title:item.assetNo
      }
      let content=`<div>${item.assetName}</div>`
      let infoWindow = new BMapGL.InfoWindow(content, opts)

      // 点击图标开启信息窗口
      marker.addEventListener('click', function () {
        map.openInfoWindow(infoWindow, point)
that.bandEvent(item)
   
      })
    })
  }
  // 设置中心点
  private setNewCenter(item:any) {
    let map=(window as any).assetmap
    let point = new BMapGL.Point(item.longitude, item.latitude);
    map.setCenter(point); // 设置地图中心点
}
  changeShowPutAway(state: boolean) {
    this.showPutAway = state
  }
  private changeShowType(state: boolean) {
    this.showType = state
  }
  private changeMarkerinfo(changeMarkerinfo: any) {
    this.assetMapPointsParams = changeMarkerinfo
    this.getAssetMapPoints()
  }
   private removeOverlay() {
    let map = (window as any).assetmap
    map.clearOverlays()
  }
  // 组件销毁
  private destroyed() {
   (window as any).assetmap==null
  }
}
</script>

<style lang="scss" scoped>

#assetmap{
  width: 100%;
  height: 100%;
}
.distributed-map-wrap {
  padding: 0 !important;
  position: relative;
  width: 100%;
  height: 100%;
  .assets-map-comtool {
    z-index: 99;
    position: absolute;
    top: 5px;
    left: 5px;
  }
  #assetsContainer {
    width: 100%;
    height: 100%;
    background: #eee;
  }
}
</style>
