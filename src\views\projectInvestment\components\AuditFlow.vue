<template>
  <section class="audit-step-wrapper">
    <!-- 流程列表 class="audit-step" -->
    <el-skeleton animated
      style="margin-top:20px;"
      :loading="stepLoading">
      <template slot="template">
        <el-skeleton-item variant="circle"
          style="width: 20px; height: 20px; border-radius: 50%;margin-top:6px;" />
        <el-skeleton-item variant="p"
          style="width: 460px; height: 20px;margin-left:10px;" />
        <br />
        <el-skeleton-item variant="circle"
          style="width: 20px; height: 20px; border-radius: 50%;margin-top:6px;" />
        <el-skeleton-item variant="p"
          style="width: 520px; height: 20px;margin-left:10px;" />
        <br />
        <el-skeleton-item variant="circle"
          style="width: 20px; height: 20px; border-radius: 50%;margin-top:6px;" />
        <el-skeleton-item variant="p"
          style="width: 460px; height: 20px;margin-left:10px;" />
        <br />
        <el-skeleton-item variant="circle"
          style="width: 20px; height: 20px; border-radius: 50%;margin-top:6px;" />
        <el-skeleton-item variant="p"
          style="width: 520px; height: 20px;margin-left:10px;" />
      </template>
      <el-steps class="audit-step"
        direction="vertical"
        :active="active">
        <el-step v-for="(item, index) of flowAuditNodeList"
          :key="index"
          :class="{
                  'icon-error': item.status !== 0 && item.auditStaus === 0,
                  'icon-active': item.status === 0,
                  'icon-success': (item.status !== 0 && item.auditStaus === 1) || index === 0
                }">
          <span slot="icon"
            class="icon"></span>
          <!-- 已完成 -->
          <div v-if="item.status === 1"
            slot="title"
            :class="{ reject: item.auditStaus === 0 }"
            style="margin-bottom: 15px; line-height: 26px;"
            class="audit-step-item finished">
            <p class="text"
              style="display: inline-block; vertical-align: middle; margin:2px 0; margin-right: 10px;">
              {{ item.roleName }}({{ item.userName }})
              {{ item.createTime }}
            </p>
            <div v-if="index !== 0 && item.status !== 2"
              class="text"
              style="display: inline-block; vertical-align: middle; margin:2px 0; margin-right: 10px;">
              <p class="text"
                style="display: inline-block; vertical-align: middle; margin:2px 0; margin-right: 10px;">
                {{ auditStausList[item.auditStaus] }}</p>
              <p v-if="item.auditNote.length <= 26"
                class="text opinion"
                style="display: inline-block; vertical-align: middle; margin:2px 0; margin-right: 10px;">
                审批意见：{{ item.auditNote }}</p>
              <el-tooltip v-else
                effect="dark"
                :content="item.auditNote"
                placement="top-start">
                <p class="text opinion ellipsis"
                  style="display: inline-block; vertical-align: middle; margin:2px 0; margin-right: 10px;">
                  审批意见：{{ item.auditNote }}</p>
              </el-tooltip>
            </div>
          </div>

          <!-- 审批 - 正在进行 -->
          <div v-if="mode === 'audit' && item.status === 0"
            slot="title"
            style="margin-bottom: 15px; line-height: 26px;"
            class="audit-step-item current">
            <h4 style="margin:0;">{{ item.roleName }}({{ item.userName }})</h4>
            <el-form :model="auditForm"
              :rules="auditRules"
              ref="auditForm"
              label-width="100px"
              class="form-box">
              <el-form-item prop="result"
                label="审批状态："
                class="btns">
                <el-radio-group v-model="auditForm.result"
                  @change="changeResult">
                  <el-radio class="audit-cb"
                    :label="1">通过</el-radio>
                  <el-radio class="audit-cb"
                    :label="0">驳回</el-radio>
                </el-radio-group>
                <el-form-item v-if="!auditForm.result && rejectNodeCodeList.length >= 2"
                  prop="rejectNodeCode"
                  label>
                  <el-select ref="nodeCodeSelector"
                    v-model="auditForm.rejectNodeCode"
                    placeholder="选择驳回人">
                    <el-option v-for="rejectNodeCode in rejectNodeCodeList"
                      :key="rejectNodeCode['code']"
                      :label="rejectNodeCode['roleName']"
                      :value="rejectNodeCode['code']" />
                  </el-select>
                </el-form-item>
              </el-form-item>
              <el-form-item prop="auditNote"
                label="审批意见：">
                <el-input v-model.trim="auditForm.auditNote"
                  placeholder="请输入"
                  show-word-limit
                  type="textarea"
                  :rows="1"
                  :maxlength="100">
                  <div
                    v-if="!auditForm.result && rejectNodeCodeList.length >= 2 && getAbortMessage(auditForm.rejectNodeCode)"
                    slot="prepend">
                    {{ getAbortMessage(auditForm.rejectNodeCode) }}
                  </div>
                </el-input>
              </el-form-item>
            </el-form>
          </div>

          <div v-if="mode !== 'audit' && item.status === 0"
            slot="title"
            style="margin-bottom: 15px; line-height: 26px;"
            class="audit-step-item">{{ item.roleName }} 待审批</div>
          <div v-if="item.status === 2"
            slot="title"
            style="margin-bottom: 15px; line-height: 26px;"
            class="audit-step-item">{{ item.roleName }} 待审批</div>
        </el-step>
      </el-steps>
    </el-skeleton>
  </section>
</template>

<script lang="ts">
import { Loading } from '@/decorators'
import { ElForm } from 'node_modules/element-ui/types/form'
import { Component, Prop, Vue } from 'vue-property-decorator'

@Component
export default class extends Vue {
  @Prop() private mode!: 'see' | 'audit'

  private active = 0

  // 常量数据
  private auditStausList = Object.freeze({
    0: '驳回',
    1: '通过'
  })

  // 表单数据
  private auditForm: Record<string, string | number> = {
    result: 1,
    auditNote: '通过',
    rejectNodeCode: ''
  }

  private auditRules = {
    auditNote: [{ required: true, trigger: 'blur', message: '请输入审批意见' }]
  }

  private rejectNodeCodeList: { code: number; roleName: string }[] = []

  private stepLoading = false

  private flowAuditNodeList = [
    {
      status: 1,
      roleName: '申请人',
      userName: '系统管理员',
      nodeCode: 0,
      createTime: '2022-06-21',
      auditStaus: null,
      auditNote: ''
    },
    {
      status: 0,
      roleName: '岗位1',
      userName: '审核人1',
      nodeCode: 10,
      createTime: '',
      auditStaus: null,
      auditNote: ''
    }
  ]

  // 切换审批状态时
  private changeResult(val: string | number) {
    // let fromRef = this.$refs.auditForm as ElForm
    if (~~val === 1) {
      this.auditForm.auditNote = '通过'
    } else {
      this.auditForm.auditNote = ''
    }
    // fromRef.clearValidate()
  }

  // 根据code 获取驳回人信息
  private getAbortMessage(code: any) {
    let rejectNode = this.rejectNodeCodeList.find((item) => item.code === code)
    if (!rejectNode) return false
    return `驳回到${rejectNode.roleName || ''}`
  }

  // 获取驳回及审批进度数据
  @Loading('stepLoading')
  private async getFlowInfo() {
    // let { data } = await flowInfo({
    //   bizId: this.bizId,
    //   instanceId: this.instanceId
    // })
    // this.flowAbortList = data.flowAbortList || []
    // this.rejectNodeCodeList = data.rejectFlowNodeList || []
    // this.flowAuditNodeList = data.flowAuditNodeList || []
    this.setActive()
  }

  /*
   * 设置业务状态 active
   * 1、this.type 为 'audit' 时，从 item.status = 0 处截取
   * 2、this.type 为 'see' 时，从最后一个 item.status = 1 处截取
   */
  private setActive() {
    for (let i = 0; i < this.flowAuditNodeList.length; i++) {
      if (this.mode === 'audit') {
        if ((this.flowAuditNodeList[i] as any).status === 0) {
          this.active = i + 1
          break
        }
      } else {
        if ((this.flowAuditNodeList[i] as any).status === 1) {
          this.active = i + 1
        }
      }
    }
  }
}
</script>


<style scoped lang="scss">
$activeColor: #4680ff;
$errorColor: #fb3f3f;
$successColor: #67c23a;
$defaultColor: #c1c1c1;
.audit-step-wrapper {
  padding: 0 20px;
}
.audit-step {
  min-height: 50px;
  margin-top: 10px;
  ::v-deep .el-step__icon {
    width: 16px;
    height: 16px;
    background-color: #f5f7fa;
    border: none;
    margin-top: 8px;
  }
  ::v-deep .el-step__icon-inner {
    width: 10px;
    height: 10px;
    color: transparent;
    border-radius: 50%;
    background-color: #dcdfe6;
  }
  .is-finish {
    ::v-deep .el-step__icon {
      background-color: #dae6ff;
    }
    ::v-deep .el-step__icon-inner {
      background-color: #4680ff;
    }
  }
  ::v-deep .el-step__line {
    left: 7px;
    top: 8px;
    bottom: -8px;
    background: #f5f7fa;
    .el-step__line-inner {
      border-color: #dae6ff;
    }
  }
  ::v-deep .el-form {
    overflow: hidden;
    padding-bottom: 10px;
    .el-form-item {
      margin-bottom: 10px;
      .el-form-item__label {
        color: rgba(0, 0, 0, 0.85);
      }
    }
    .el-input__count {
      height: 30px;
      bottom: 1px;
    }
  }

  ::v-deep .el-step.is-vertical .el-step__title {
    padding-bottom: 0;
  }
  ::v-deep .el-step.is-vertical .el-step__main {
    padding-left: 0;
  }
  .audit-step-item {
    line-height: 30px;
    background: #f5f7fa;
    border-radius: 2px;
    color: #c0c4cc;
    font-size: 12px;
    padding: 0 10px;
    margin-bottom: 15px;
    font-weight: normal;

    &.current {
      h4 {
        font-size: 12px;
        color: #4680ff;
        line-height: 30px;
        font-weight: normal;
        margin: 0;
      }
    }
  }
  .is-finish {
    ::v-deep .audit-step-item {
      background: #f8faff;
      color: #303133;
    }
  }
  .icon {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: $defaultColor;
    // border: 2px solid rgba($color: $defaultColor, $alpha: 0.05);
  }
  ::v-deep .el-step__icon {
    background-color: rgba($color: $defaultColor, $alpha: 0.2) !important;
  }
  .icon-success {
    .icon {
      background: $successColor;
      // border: 2px solid rgba($color: $successColor, $alpha: 0.05);
    }
    ::v-deep .el-step__icon {
      background-color: rgba($color: $successColor, $alpha: 0.2) !important;
    }
    ::v-deep .audit-step-item {
      color: #303133;
      background-color: rgba($color: $successColor, $alpha: 0.05) !important;
    }
  }
  .icon-active {
    .icon {
      background: $activeColor;
      // border: 2px solid rgba($color: $successColor, $alpha: 0.05);
    }
    ::v-deep .el-step__icon {
      background-color: rgba($color: $activeColor, $alpha: 0.2) !important;
    }
    ::v-deep .audit-step-item {
      background-color: rgba($color: $activeColor, $alpha: 0.05) !important;
    }
  }
  .icon-error {
    .icon {
      background: $errorColor;
      // border: 2px solid rgba($color: $successColor, $alpha: 0.05);
    }
    .reject {
      background: rgba($color: $errorColor, $alpha: 0.05);
    }
    ::v-deep .el-step__icon {
      background-color: rgba($color: $errorColor, $alpha: 0.2) !important;
    }
    ::v-deep .audit-step-item {
      background-color: rgba($color: $errorColor, $alpha: 0.2) !important;
    }
  }
}
</style>