<template>
  <section v-loading="loading"
    class="echarts-overviews-wrap">
    <div class="header-box">
      <el-form ref="form"
        :inline="true"
        :model="formData">
        <el-form-item>
          <el-select v-model="formData.orgCode"
            placeholder="公司名称">
            <el-option v-for="item in optionOrgCode"
              :key="item.deptCode"
              :label="item.deptName"
              :value="item.deptCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-date-picker v-model="formData.year"
            type="year"
            placeholder="年份"
            value-format="yyyy">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </div>

    <el-divider class="divider-box" />

    <ProjectSummary :year="formData.year"
      :orgCode="formData.orgCode" />

    <div v-if="isShow"
      class="conter-box">
      <div class="modules">
        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>xxxxxx</span>
          </h4>
          <div class="cter">
            xxxxxx
          </div>
        </div>

        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>xxxxxxx</span>
          </h4>
          <div class="cter">
            xxxxxx
          </div>
        </div>
      </div>

      <div class="modules">
        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>xxxxxx</span>
          </h4>
          <div class="cter">
            xxxxxx
          </div>
        </div>

        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>xxxxxx</span>
          </h4>
          <div class="cter">
            xxxxxx
          </div>
        </div>
      </div>

      <div class="modules">
        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>xxxxxx</span>
          </h4>
          <div class="cter">
            xxxxx
          </div>
        </div>

        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>xxxxxx</span>
          </h4>
          <div class="cter">
            xxxxxx
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { getOrgCodeList } from '@/api/public'
import ProjectSummary from './components/ProjectSummary.vue'

@Component({
  components: {
    ProjectSummary
  }
})
export default class extends Vue {
  private isShow = true
  private loading = false
  private optionOrgCode: any[] = []
  private timer: any = null
  private formData: {
    orgCode: string | null
    year: string | null
  } = {
    year: '',
    orgCode: ''
  }

  // 数据初始化
  private created() {
    this.formData.year = (this as any).$moment(new Date()).add('year', 0).format('YYYY')
    this.getOrgCodeList()
  }

  // 组件初始化
  private mounted() {
    window.addEventListener('resize', () => {
      clearTimeout(this.timer)
      this.isShow = false
      this.loading = true
      this.timer = setTimeout(() => {
        this.isShow = true
        this.loading = false
      }, 500)
    })
  }

  // 获取下级集团列表
  private async getOrgCodeList() {
    let { data } = await getOrgCodeList()
    this.optionOrgCode = data || []
  }

  // 组件销毁
  private destroyed() {
    clearTimeout(this.timer)
    this.timer = null
  }
}
</script>