<template>
  <div ref="chartDom" class="chartDom" />
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import * as echarts from 'echarts'

let chartDom = null
let myChart: any = null

@Component({})
export default class extends Vue {
  @Prop() private echarsData!: any

  // 组件初始化
  private mounted() {
    this.$nextTick(() => {
      chartDom = this.$refs.chartDom
      myChart = echarts.init(chartDom as HTMLElement)

      this.initEcharts()
    })
  }

  // 初始化echarts组件
  private initEcharts() {
    let xdata: any = this.echarsData.list
    let list: any = this.echarsData.name
    let option = {
      xAxis: {
        type: 'category',
        data: xdata
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          data: list,
          type: 'bar',
          label: {
            show: true,
            position: 'top'
          }
        }
      ]
    }

    myChart && myChart.setOption && myChart.setOption(option)
  }
}
</script>

<style scoped lang="scss">
.chartDom {
  width: 100%;
  height: 100%;
}
</style>
