<template>
  <Dialog
    title="企业资产减值准备财务核销备案--详情"
    width="980px"
    :visible="visible"
    :close-on-click-modal="true"
    :destroy-on-close="true"
    :append-to-body="true"
    @close="handleClose"
    @open="openDiaHandle"
  >
    <div slot="body" class="descriptions-body">
      <Descriptions v-bind="$attrs"></Descriptions>
      <!-- <el-divider></el-divider> -->
      <!-- <Stel></Stel>     -->
      <h5>不良资产内容</h5>
      <Grid
        :columns="cols"
        ref="grid"
        :data="detailFrom.badAssetsDetailVOList"
        :show-pagination="false"
          :show-index="true"
      >
      <template #assetTypeSlot="{row}" >
               <span v-if="row.assetType==1">实物资产类</span>
               <span v-if="row.assetType==2">往来款项类</span>
               <span v-if="row.assetType==3">对外投资类</span>
               <span v-if="row.assetType==4">资产核销</span>
           </template>
      </Grid>

      <AccessoryFileList
        v-model="detailFrom.attachmentFileDTOList"
        dict="financial_writeoff_attach"
      />
    </div>
    <div slot="footer">
      <el-button @click="closeDetail">关闭</el-button>
      <!-- <el-button class="finance_red_btn">确认审批</el-button> -->
    </div>
  </Dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import Descriptions from './Descriptions/index.vue'
import Dialog from '@/components/Dialog/index.vue'
import Stel from './Stel/index.vue'
import AccessoryFileList from './AccessoryFileList.vue'
import Grid from '@/components/Grid/index.vue'
@Component({
  components: {
    Descriptions,
    Dialog,
    Stel,
    Grid,
    AccessoryFileList
  }
})
export default class Container extends Vue {
  @Prop() private visible!: boolean
  @Prop() private fileList!: boolean
  @Prop() private dict!: boolean
  @Prop() private detailFrom!: {}
  private cols = [
    {
      prop: 'assetName',
      label: '资产名称',
      width: 160
    },
    {
      prop: 'assetType',
      label: '不良资产类型',
      width: 160,
      slotName:"assetTypeSlot"
    },
    {
      prop: 'totalAccumulatedDepreciation',
      label: '累计折旧总额',
      width: 160
    },
    {
      prop: 'totalNetBookValue',
      label: '账面净值总额',
      width: 160
    },
    {
      prop: 'totalOriginalBookValue',
      label: '账面原值总值',
      width: 160
    },
  ]
  private handleClose() {
    // this.visible = false
    this.$emit('changeShowDetail', false)
  }
  private closeDetail() {
    this.handleClose()
    // this.$emit("visible:update",false)
  }
  openDiaHandle() {
    // this.visible = true
  }
}
</script>

<style lang="scss" scoped>
.descriptions-body {
  ::v-deep.el-descriptions__body .el-descriptions__table .el-descriptions-item__cell {
    font-size: 14px;
    color: #303133;
  }
}
</style>
