<!-- 投资看板 -->
<template>
  <el-container class="container"
    direction="vertical">
    <el-row class="investment-row">
      <el-col class="investment-col"
        :span="12">
        <div class="title">投资完成情况</div>
        <div class="finish-kpi">
          <div class="finish-kpi-row">
            <div class="finish-kpi-col"
              style="background:#f46b87;">
              <div class="finish-kpi-title">计划投资额(估算)</div>
              <div class="finish-kpi-val">0.00万</div>
            </div>

            <div class="finish-kpi-col"
              style="background: #56afeb;">
              <div class="finish-kpi-title">合同额(已签)</div>
              <div class="finish-kpi-val">0.00万</div>
            </div>

            <div class="finish-kpi-col"
              style="background: #fb9b5e;">
              <div class="finish-kpi-title">实际投资(计量)</div>
              <div class="finish-kpi-val">0.00万</div>
            </div>
          </div>

          <div class="finish-kpi-row">
            <div class="finish-kpi-col"
              style="background: #5dd3c3;">
              <div class="finish-kpi-title">请款报审(已审)</div>
              <div class="finish-kpi-val">0.00万</div>
            </div>

            <div class="finish-kpi-col"
              style="background: #c091f9;">
              <div class="finish-kpi-title">支付金额(已付)</div>
              <div class="finish-kpi-val">0.00万</div>
            </div>

            <div class="finish-kpi-col"
              style="background: #dfcc6e;">
              <div class="finish-kpi-title">欠付额(未支付)</div>
              <div class="finish-kpi-val">0.00万</div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col class="investment-col"
        :span="12">
        <div class="title">质量安全事故分析</div>
        <div ref="graph1"
          class="graph"></div>
      </el-col>
    </el-row>

    <el-row class="investment-row">
      <el-col class="investment-col"
        :span="12">
        <div class="title">五算分析</div>
        <div class="graph"
          ref="graph2"></div>
      </el-col>
      <el-col class="investment-col"
        :span="12">
        <div class="title">进度情况</div>
        <div class="graph"
          ref="graph3"></div>
      </el-col>
    </el-row>
  </el-container>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import * as echarts from 'echarts'

@Component
export default class Investment extends Vue {
  private mounted() {
    this.initGraph1()
    this.initGraph2()
    this.initGraph3()
  }

  private initGraph1() {
    let option = {
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['项目1', '项目2', '项目3', '项目4', '项目5', '项目6', '项目7', '项目8']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '质量事故',
          type: 'line',
          symbol: 'circle',
          stack: 'Total',
          showSymbol: false,
          data: [2, 5, 4, 1, 7, 2, 3, 1],
          itemStyle: {
            color: '#FF8888'
          }
        },
        {
          name: '安全事故',
          type: 'line',
          symbol: 'circle',
          showSymbol: false,
          stack: 'Total',
          data: [4, 2, 5, 4, 1, 7, 2, 3],
          itemStyle: {
            color: '#FFE29B'
          }
        }
      ]
    }
    let chart = echarts.init(this.$refs.graph1 as any)
    chart.setOption(option)
  }

  private initGraph2() {
    let option = {
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['投资估算', '设计概算', '合同额', '设计变更额', '请款报审', '支付金额', '欠付金额', '结算合同额']
      },
      yAxis: {},
      series: [
        {
          symbolSize: 20,
          data: [
            [10.0, 8.04],
            [8.07, 6.95],
            [13.0, 7.58],
            [9.05, 8.81],
            [11.0, 8.33],
            [14.0, 7.66],
            [13.4, 6.81],
            [10.0, 6.33],
            [14.0, 8.96],
            [12.5, 6.82],
            [9.15, 7.2],
            [11.5, 7.2],
            [3.03, 4.23],
            [12.2, 7.83],
            [2.02, 4.47],
            [1.05, 3.33],
            [4.05, 4.96],
            [6.03, 7.24],
            [12.0, 6.26],
            [12.0, 8.84],
            [7.08, 5.82],
            [5.02, 5.68]
          ],
          type: 'scatter'
        }
      ]
    }
    let chart = echarts.init(this.$refs.graph2 as any)
    chart.setOption(option)
  }

  private initGraph3() {
    let option = {
      legend: {},
      tooltip: {},
      dataset: {
        source: [
          ['product', '实际进度', '计划进度'],
          ['项目1', 22.3, 100],
          ['项目2', 53.1, 100],
          ['项目3', 36.4, 100],
          ['项目4', 72.4, 88],
          ['项目5', 53.1, 100],
          ['项目6', 53.1, 100]
        ]
      },
      xAxis: { type: 'category' },
      yAxis: {},
      // Declare several bar series, each will be mapped
      // to a column of dataset.source by default.
      series: [
        {
          type: 'bar',
          itemStyle: {
            color: 'rgb(255, 140, 37)'
          }
        },
        {
          type: 'bar',
          itemStyle: {
            color: 'rgb(134, 176, 237)'
          }
        }
      ]
    }
    let chart = echarts.init(this.$refs.graph3 as any)
    chart.setOption(option)
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  .investment-row {
    background: #fff;
    flex: 1;
    padding: 20px;
    .investment-col {
      height: 100%;
      .graph {
        flex: 1;
        height: 100%;
      }
    }
  }
  .title {
    line-height: 24px;
    border-left: 6px solid #b43c3c;
    padding-left: 10px;
    font-size: 20px;
    color: #2f3038;
  }

  .finish-kpi {
    height: 100%;
    display: flex;
    flex-direction: column;
    .finish-kpi-row {
      flex: 1;
      display: flex;
      .finish-kpi-col {
        margin: 3% 3% 3% 3%;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        box-shadow: 1px 1px 5px #b5b8c8;
        border-radius: 3px;
        .finish-kpi-title {
          font-size: 15px;
          color: #fff;
          position: absolute;
          left: 10px;
          top: 10px;
        }
        .finish-kpi-val {
          color: #fff;
          font-size: 18px;
          font-weight: bold;
          text-align: center;
        }
      }
    }
  }
}
</style>