<template>
  <Dialog
    title="查看"
    width="980px"
    :visible="visible"
    :close-on-click-modal="true"
    :destroy-on-close="true"
    :append-to-body="true"
    @close="handleClose"
    @open="openDiaHandle"
  >
    <div slot="body" class="descriptions-body">
      <Descriptions v-bind="$attrs"></Descriptions>
      <!-- <el-divider></el-divider> -->
      <!-- <Stel></Stel>     -->
      <AccessoryFileList v-model="fileList" :dict="dict" />

    </div>
    <div slot="footer">
        <el-button @click="closeDetail" >关闭</el-button>
        <!-- <el-button class="finance_red_btn">确认审批</el-button> -->
    </div>
  </Dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import Descriptions from './Descriptions/index.vue'
import Dialog from '@/components/Dialog/index.vue'
import Stel from './Stel/index.vue'
import AccessoryFileList from "./AccessoryFileList.vue"
@Component({
  components: {
    Descriptions,
    Dialog,
    Stel,
    AccessoryFileList
  }
})
export default class Container extends Vue {
  @Prop() private visible!: boolean
  @Prop() private fileList!: boolean
  @Prop( ) private dict!: boolean

  private handleClose() {
    // this.visible = false
    this.$emit("changeShowDetail",false)
  }
  private closeDetail(){
    this.handleClose()
    // this.$emit("visible:update",false)
  }
  openDiaHandle() {
    // this.visible = true
    
  }
}
</script>

<style lang="scss" scoped>
.descriptions-body {
  ::v-deep.el-descriptions__body .el-descriptions__table .el-descriptions-item__cell {
    font-size: 14px;
    color: #303133;
  }
}
</style>
