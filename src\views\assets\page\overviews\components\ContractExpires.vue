<template>
  <section class="echarts-dom-wrap">
    <div v-loading="loading"
      ref="chartDom"
      class="chartDom"
      :class="{'hide': !interfaceData.length}" />

    <el-empty description="暂无数据"
      class="empty-none-data"
      :class="{'none': interfaceData.length}" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { assetsContractExpire } from '@/api/assetsv2'
import { Loading } from '@/decorators'
import * as echarts from 'echarts'

let chartDom = null
let myChart: any = null

@Component({})
export default class extends Vue {
  @Prop() private orgCode!: string // 集团code
  @Prop() private year!: string // 年份

  private loading = false
  private legendData: string[] = []
  private yAxisData: string[] = []
  private seriesData: any[] = []
  private interfaceData = []

  // 监听搜索条件改变
  @Watch('orgCode', { deep: true })
  @Watch('year', { deep: true })
  private watchFormData() {
    this.initData()
  }

  // 组件初始化
  private mounted() {
    this.$nextTick(() => {
      chartDom = this.$refs.chartDom
      myChart = echarts.init(chartDom as HTMLElement)

      this.initData()
    })
  }

  // 获取数据
  @Loading('loading')
  private async initData() {
    let { data } = await assetsContractExpire({
      orgCode: this.orgCode,
      year: this.year
    })

    this.interfaceData = data || []

    // let data = [
    //   {
    //     name: '六个月内到期',
    //     total: 999,
    //     list: [
    //       {
    //         label: '国资',
    //         value: 121
    //       },
    //       {
    //         label: '城投',
    //         value: 232
    //       },
    //       {
    //         label: '交投',
    //         value: 98
    //       }
    //     ]
    //   },
    // ]

    // 组装数据
    let arrAll: any[] = []
    let names: string[] = []
    let yAxisData: string[] = []
    let seriesData: any[] = []

    Array.isArray(data) &&
      data.forEach((item, index) => {
        arrAll = arrAll.concat(item.list)
        yAxisData.push(`${item.name}\n${item.total} 笔`)

        if (!index && Array.isArray(item.list)) {
          item.list.forEach((ide: { label: string }) => {
            names.push(ide.label)
          })
        }
      })

    names.forEach((item) => {
      let list: number[] = []
      arrAll.forEach((itemAll) => {
        if (item === itemAll.label) {
          list.push(+itemAll.value)
        }
      })

      let obj = {
        name: item,
        type: 'bar',
        stack: 'total',
        barWidth: 50,
        label: {
          show: true,
          fontSize: 14
        },
        data: list
      }

      seriesData.push(obj)
    })

    this.yAxisData = yAxisData
    this.seriesData = seriesData
    this.initEcharts()
  }

  // 初始化echarts组件
  private initEcharts() {
    let yAxisData = this.yAxisData
    let seriesData = this.seriesData

    let option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        valueFormatter: function (value: string) {
          return value + ' 笔'
        }
      },
      grid: {
        top: '2%',
        left: '2%',
        right: '6%',
        bottom: '0%',
        containLabel: true
      },
      xAxis: {
        type: 'value'
      },
      yAxis: {
        type: 'category',
        data: yAxisData,
        axisLabel: {
          fontSize: 13
        }
      },
      series: seriesData
    }

    myChart && myChart.setOption && myChart.setOption(option)
  }
}
</script>

<style scoped lang="scss">
.chartDom {
  width: 100%;
  height: 100%;
}
</style>