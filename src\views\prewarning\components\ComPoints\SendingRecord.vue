<template>
  <section v-loading="loading"
    class="sending-record-wrap">
    <div v-if="smsSendList.length"
      class="message-log-box">
      <div class="content-box">
        <div v-for="(item, index) of smsSendList"
          :key="index"
          class="mode">
          <div class="takeover">
            <p class="m-r-10">发送时间：{{ item.sendTime }}</p>

            <p class="receiver text-ellipsis-1">
              <span>接收人：</span>
              <span v-for="(itemTakeover, i) of item.contractVOList"
                :key="i">
                {{ itemTakeover.companyName }} {{ itemTakeover.contactName }}
                {{ itemTakeover.contactPhone }}；
              </span>
            </p>

            <el-popover v-if="item.contractVOList.length > 1"
              placement="left-end"
              title="接收人"
              trigger="hover"
              popper-class="custom-details-warning-popover-wrap">
              <div class="popover-box">
                <p v-for="(itemTakeover, j) of item.contractVOList"
                  :key="j">
                  <span>{{ itemTakeover.companyName }}</span>
                  <span>{{ itemTakeover.contactName }}</span>
                  <span>{{ itemTakeover.contactPhone }}</span>
                </p>
              </div>
              <el-button slot="reference"
                type="text"
                class="p-0">查看更多</el-button>
            </el-popover>
          </div>
          <div class="info">{{ item.smsContent }}</div>
        </div>
      </div>
    </div>

    <el-empty v-else
      description="暂无数据" />

    <div v-if="smsSendList.length"
      class="pagination-box">
      <el-pagination layout="prev, pager, next"
        :background="false"
        :page-size="pagination.size"
        :total="pagination.total"
        :current-page="pagination.current"
        @current-change="handleCurrentChange" />
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { getSmsSendList } from '@/api/prewarning'

@Component
export default class extends Vue {
  @Prop() private params!: any // 详情数据

  private loading = false
  private smsSendList: any[] = []
  private pagination = {
    size: 10,
    total: 0,
    current: 1
  }

  // 数据初始化
  private created() {
    this.initData()
  }

  // 获取详情数据
  @Loading('loading')
  private async initData() {
    let { data } = await getSmsSendList({
      eventNo: this.params.eventNo,
      size: this.pagination.size,
      current: this.pagination.current
    })

    this.smsSendList = data.records || []
    this.pagination.total = data.total
    this.pagination.current = data.current
  }

  // 分页数据改变
  private handleCurrentChange(current: number) {
    this.pagination.current = current
    this.initData()
  }
}
</script>

<style scoped lang="scss">
.sending-record-wrap {
  $hot: #fb3f3f;
  $yellow: #e6a23c;
  $blur: #409eff;
  $colorLable: #8c8ea4;
  $colorContent: #53545b;

  position: relative;
  p {
    margin: 0;
  }
  .hot {
    color: $hot;
  }
  .yellow {
    color: $yellow;
  }
  .blue {
    color: $blur;
  }

  .message-log-box {
    overflow-y: auto;
    font-size: 14px;
    font-family: Avenir, Helvetica, Arial, sans-serif;
    .content-box {
      padding: 16px;
      padding-bottom: 0;
      background: #f9fafc;
      border-radius: 4px;
      border-radius: 4px;
      max-height: 520px;
      overflow-y: auto;
      & .mode:nth-last-child(1) {
        border-bottom: none;
        padding-bottom: 0;
      }
      .mode {
        padding-bottom: 16px;
        margin-bottom: 16px;
        border-bottom: 1px solid #ccccd7;
      }
      .takeover {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 6px;
        & p:nth-child(1) {
          width: 500px;
        }
        .receiver {
          width: 272px;
        }
      }
      .info {
        color: #0a1629;
        line-height: 24px;
        text-indent: -8px;
      }
    }
  }

  .pagination-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
    ::v-deep .el-pagination {
      padding: 0;
      button {
        &:nth-child(1) {
          margin-left: 0;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.custom-details-warning-popover-wrap {
  .popover-box {
    p {
      text-indent: -8px;
      line-height: 10px;
      display: flex;
      align-items: center;
      span:nth-child(1) {
        width: 100px;
      }
      span:nth-child(2) {
        width: 80px;
      }
    }
  }
}
</style>