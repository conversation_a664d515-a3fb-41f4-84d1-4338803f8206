/* 预警趋势 */

<template>
  <section v-loading="loading"
    class="early-warning-trend-wrap">
    <CommonTitle :title="title"
      :isRotuerThrow="true"
      :routerObj="{
        bizAppCode: 'alert',
        detailsPath: '/prewarning/events'
      }"
      class="title-box" />

    <!-- 头部tabs切换 -->
    <div class="tabs-box">
      <div>
        <span v-for="(item,index) of dateTypeList"
          :key="index"
          :class="{'active':+dateType === +item.value}"
          @click="onChangeYear(item.value)">{{item.label}}</span>
      </div>
      <div>
        <span v-for="(item,index) of warningTypeList"
          :key="index"
          :class="{'active':+warningType === index+1}"
          @click="onChangeEarly(item.value)">{{item.label}}</span>
      </div>
    </div>

    <!-- echarts 视图 -->
    <div class="content-box"
      ref="refEcharts" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { echartConfigure } from '@/views/main/cockpitcren/baseData'
import { debtInfaceTrendsOverYear } from '@/api/cockpit'
import { Loading } from '@/decorators'
import * as echarts from 'echarts'
import CommonTitle from '@/views/main/cockpitSecondary/components/CommonTitle.vue'

type EChartsOption = echarts.EChartsOption

@Component({
  components: {
    CommonTitle
  }
})
export default class extends Vue {
  @Prop() private title!: string // 标题
  @Prop() private tabActive!: string // 选中集团

  private loading = false
  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private seriesData: any[] = []
  private option: EChartsOption = {}
  private legendData: string[] = []
  private xAxisData: string[] = []
  private tooltipTime = ''
  private xName = ''

  // 头部两个tabs切换数据
  private dateType = 3
  private warningType = 1
  private dateTypeList = [
    {
      label: '历年',
      value: 1
    },
    {
      label: '本年',
      value: 2
    },
    {
      label: '本月',
      value: 3
    }
  ]
  private warningTypeList = [
    {
      label: '风险等级',
      value: 1
    },
    {
      label: '预警分类',
      value: 2
    }
  ]

  // 数据变化，渲染视图
  @Watch('tabActive', { deep: true })
  private changeTabActive() {
    this.initData()
  }

  // 组件初始化
  private mounted() {
    this.chartDom = this.$refs.refEcharts as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)
  }

  // 历年、本年、本月tabs切换
  private onChangeYear(value: number) {
    this.dateType = value
    this.initData()
  }
  // 风险等级、预警分类tabs切换
  private onChangeEarly(value: number) {
    this.warningType = value
    this.initData()
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await debtInfaceTrendsOverYear({
      warningType: this.warningType,
      dateType: this.dateType,
      orgCode: this.tabActive
    })

    data = data || []

    // 组装echarts数据
    if (!Array.isArray(data) || !data.length) return

    // 根据风险等级、预警分类去设置样式
    let type = ''
    let stack = ''

    if (+this.warningType === 1) {
      switch (+this.dateType) {
        case 1:
          type = 'bar'
          stack = ''
          break
        case 2:
          type = 'bar'
          stack = 'total'
          break
        case 3:
          type = 'bar'
          stack = 'total'
          break
      }
    }

    if (+this.warningType === 2) {
      type = 'bar'
      stack = 'total'
    }
    // end

    // 设置 X 轴显示文案
    if (+this.dateType === 1) {
      this.xName = '年'
    } else if (+this.dateType === 2) {
      this.xName = '月'
    } else {
      this.xName = '天'
    }
    // end

    // 单独设置 tooltip 标题
    let tooltipStr = String(data[0].label).split('-')

    if (Array.isArray(tooltipStr) && tooltipStr.length === 2) {
      this.tooltipTime = `${tooltipStr[0]}-`
    } else if (Array.isArray(tooltipStr) && tooltipStr.length === 3) {
      this.tooltipTime = `${tooltipStr[0]}-${tooltipStr[1]}-`
    } else {
      this.tooltipTime = ''
    }
    // end

    // 设置相关数据
    let legendList: string[] = []
    let xAxisList: string[] = []
    let seriesList: any[] = []
    Array.isArray(data) &&
      data.forEach((item, index) => {
        if (+this.dateType === 1) {
          xAxisList.push(item.label)
        } else if (+this.dateType === 2) {
          let label = item.label.split('-')
          xAxisList.push(String(~~label[1]))
        } else if (+this.dateType === 3) {
          let label = item.label.split('-')
          xAxisList.push(String(~~label[2]))
        }
        item.list.forEach((itemChild: any) => {
          !index && legendList.push(itemChild.label)
        })
      })

    legendList.forEach((item, index) => {
      let arr: number[] = []
      Array.isArray(data) &&
        data.forEach((itemData: any) => {
          if (item === itemData.list[index].label) {
            arr.push(itemData.list[index].value)
          }
        })

      // 设置柱状图线条宽度
      let barWidth = 20
      if (+this.dateType === 1) {
        barWidth = 30
      } else if (+this.dateType === 1) {
        barWidth = 25
      } else {
        barWidth = 20
      }
      // end

      seriesList.push({
        name: item,
        type: type,
        stack: stack,
        barWidth: barWidth,
        data: arr,
        lineStyle: {
          width: 6
        },
        itemStyle: {
          borderRadius: [70, 70, 0, 0]
        }
      })
    })
    // end

    this.legendData = legendList
    this.xAxisData = xAxisList
    this.seriesData = seriesList
    this.initEcharts()
  }

  // 初始化数据
  private initEcharts() {
    let textSize = echartConfigure.textSize
    let legendSize = echartConfigure.legendSize
    let legendData = this.legendData
    let xAxisData = this.xAxisData
    let seriesData = this.seriesData
    let colorList = ['#fb3f3f', '#ff7500', '#F5B331', '#249DF7']
    let xName = this.xName

    this.option = {
      color: [
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(240, 73, 148, 0.1)'
            },
            {
              offset: 0.3,
              color: 'rgba(240, 73, 148, 0.7)'
            },
            {
              offset: 1,
              color: 'rgba(240, 73, 148, 1)'
            }
          ]
        },
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(255, 117, 0, 0.1)'
            },
            {
              offset: 0.3,
              color: 'rgba(255, 117, 0, 0.7)'
            },
            {
              offset: 1,
              color: 'rgba(255, 117, 0, 1)'
            }
          ]
        },
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(246, 181, 48, 0.1)'
            },
            {
              offset: 0.3,
              color: 'rgba(246, 181, 48, 0.7)'
            },
            {
              offset: 1,
              color: 'rgba(246, 181, 48, 1)'
            }
          ]
        },
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(36, 157, 247, 0.1)'
            },
            {
              offset: 0.3,
              color: 'rgba(36, 157, 247, 0.7)'
            },
            {
              offset: 1,
              color: 'rgba(36, 157, 247, 1)'
            }
          ]
        }
      ],
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#00000000',
        shadowColor: '#00000000',
        borderWidth: 0,
        appendToBody: true,

        formatter: (params: any) => {
          let dom = ''
          let title = ''
          let totalValue = 0
          let year = params[0].axisValue || ''

          params.forEach((item: any, index: number) => {
            totalValue += +item.value

            dom += `
              <div style="display: flex; align-items: center; justify-content: space-between;">
                <span style="padding-right: 40px;">${item.seriesName}</span>
                <span style="color:${colorList[index]};">${item.value}</span>个
              </div>
              `
          })

          return `
            <div style="font-size:14px; line-height: 30px; color: #fff; border-radius:8px;border:2px solid #0C3EB6; padding: 0px 10px; display: flex;flex-direction: column; background: linear-gradient(0deg, rgba(0, 103, 177, 1), rgba(0, 103, 177, 1));">
              <div style="font-weight:bold; display: flex; align-items: center; justify-content: space-between;">
                <strong>${this.tooltipTime}${year}</strong>
                <strong style="color:#00F6FF;">${totalValue}</strong>
              </div>
              ${dom}
            </div>
          `
        }
      },
      legend: {
        show: false,
        top: 10,
        right: 10,
        padding: 0,
        width: 1000,
        itemHeight: 24,
        itemWidth: 24,
        itemStyle: {
          borderCap: 'butt'
        },
        lineStyle: {
          type: [0, 10],
          dashOffset: 5
        },
        textStyle: {
          color: 'rgba(0, 166, 245, 0.7)',
          fontSize: legendSize / 1.1,
          fontWeight: 'bold'
        },
        data: legendData
      },
      grid: {
        top: '30%',
        left: '0%',
        right: '4%',
        bottom: '6%',
        containLabel: true
      },
      xAxis: [
        {
          name: xName,
          type: 'category',
          data: xAxisData,
          nameTextStyle: {
            color: '#5db0ea',
            align: 'left',
            fontWeight: 'bold',
            fontSize: textSize * 0.8
          },
          axisPointer: {
            type: 'shadow'
          },
          axisLabel: {
            fontSize: textSize * 1.1,
            fontWeight: 'bold'
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#5db0ea'
            }
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: '数量(个)',
          min: 0,
          nameTextStyle: {
            color: '#5db0ea',
            align: 'left',
            fontWeight: 'bold',
            fontSize: textSize * 0.8
          },
          axisLabel: {
            fontSize: textSize * 1.1,
            fontWeight: 'bold'
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#5db0ea'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0, 103, 177, 0.6)'
            }
          }
        }
      ],
      series: seriesData,
      animationDelay: function (idx) {
        return idx * 300
      }
    }

    this.myChart && this.myChart.setOption(this.option, true)
  }
}
</script>

<style scoped lang="scss">
.early-warning-trend-wrap {
  position: relative;
  height: 100%;
  .title-box {
    position: absolute;
  }

  .content-box {
    position: absolute;
    z-index: 1;
    width: 100%;
    height: 100%;
  }

  .tabs-box {
    position: absolute;
    z-index: 2;
    top: 100px;
    width: 100%;
    font-size: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    span {
      padding: 4px 0 4px 0;
      margin-right: 30px;
      cursor: pointer;
      &:nth-last-child(1) {
        margin-right: 0;
      }
    }
    .active {
      color: #41eeff;
      border-bottom: 3px solid #41eeff;
    }
  }
}
</style>


