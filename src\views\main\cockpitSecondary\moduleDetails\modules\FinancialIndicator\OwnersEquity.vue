/* 所有者权益 */

<template>
  <section class="owners-equity-wrap">
    <CommonTitle :title="title"
      :isRotuerThrow="true"
      :routerObj="typeBizObj" />

    <div class="halo" />

    <div class="pie-pric-box">
      <h6 class="title">总额</h6>
      <p class="pric">
        <CountTo :decimals="2"
          :startVal='0'
          :endVal='+getBigNumberFormat(assetsPric)'
          :duration='1500' />
        <i>亿元</i>
      </p>
    </div>

    <div class="content-box"
      ref="refEcharts" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { echartConfigure, colorSixList } from '@/views/main/cockpitcren/baseData'
import { deepClone } from '@/utils'
import { bigNumberFormat } from '@/utils'
import * as echarts from 'echarts'
import CountTo from 'vue-count-to'
import CommonTitle from '@/views/main/cockpitSecondary/components/CommonTitle.vue'

type EChartsOption = echarts.EChartsOption

@Component({
  components: {
    CountTo,
    CommonTitle
  }
})
export default class extends Vue {
  @Prop() private title!: string // 标题
  @Prop() private echartsData!: any // 渲染数据
  @Prop({
    default: () => {
      return {}
    }
  })
  private typeBizObj?: object // 标题跳转

  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private seriesData: any[] = []
  private option: EChartsOption = {}
  private assetsPric = ''
  private assetsRote = ''

  // 数据变化，渲染视图
  @Watch('echartsData', { deep: true })
  private changeEchartsData() {
    this.initData()
  }

  // 万元数据转化成亿元
  get getBigNumberFormat() {
    return (num: string | number) => {
      return bigNumberFormat(+num).value || 0
    }
  }

  // 组件初始化
  private mounted() {
    this.chartDom = this.$refs.refEcharts as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)
  }

  // 请求数据接口
  private async initData() {
    let data = deepClone(this.echartsData)

    Array.isArray(data.list) &&
      data.list.forEach((item: any) => {
        item.value = this.getBigNumberFormat(item.value)
      })

    // 组装echarts数据
    this.assetsPric = data.value
    this.assetsRote = data.rote
    this.seriesData = data.list

    this.initEcharts()
  }

  // 初始化数据
  private initEcharts() {
    let textSize = echartConfigure.textSize
    let seriesData = this.seriesData

    this.option = {
      color: colorSixList,
      tooltip: {
        trigger: 'item',
        backgroundColor: '#00000000',
        shadowColor: '#00000000',
        borderWidth: 0,
        formatter: (params: any) => {
          let data = params.data
          let dom = ''
          let that = this as any

          if (+data.rote > 0) {
            dom = `
              <div style="display: flex; justify-content: space-between;">
                <span style="padding-right: 40px;">同比</span>
                <span style="color: #fb3f3f;">
                  <img style="width:26px;" src="${require('../../../images/thows.png')}" />${+data.rote}%
                </span>
              </div>
            `
          } else {
            dom = `
              <div style="display: flex; justify-content: space-between;">
                <span style="padding-right: 40px;">同比</span>
                <span style="color: #00a92b;">
                  <img style="width:26px;" src="${require('../../../images/thowx.png')}" />${+data.rote}%
                </span>
              </div>
            `
          }

          return `
            <div style="font-size:40px; line-height: 70px; color: #fff; border-radius:10px;border:2px solid #0C3EB6; padding: 20px; display: flex;flex-direction: column; background: linear-gradient(0deg, rgba(0, 103, 177, 1), rgba(0, 103, 177, 1));">
              <div style="font-weight:bold;">${data.name}</div>
              <div style="display: flex; justify-content: space-between;">
                <span style="padding-right: 40px;">金额</span>
                <span>${data.value}亿元</span>
              </div>
              ${dom}
            </div>
          `
        }
      },
      grid: {
        left: '0%',
        right: '0%',
        top: '0%',
        bottom: '0%',
        containLabel: true
      },
      legend: {
        show: false
      },
      series: [
        {
          name: '',
          type: 'pie',
          radius: ['40%', '60%'],
          label: {
            show: true,
            alignTo: 'edge',
            color: '#fff',
            fontSize: textSize / 1.2,
            minMargin: 30,
            edgeDistance: 10,
            lineHeight: 11,
            formatter: function (params: any) {
              let dom = ''
              let names = params.data.name
              let values = params.data.value
              let rotes = params.data.rote

              if (+params.data.rote > 0) {
                dom = `{name|${names}}\n{pric|${values}}{unit|亿元}\n{tbs|同比 +${rotes}%}`
              } else if (+params.data.rote < 0) {
                dom = `{name|${names}}\n{pric|${values}}{unit|亿元}\n{tbx|同比 ${rotes}%}`
              } else {
                dom = `{name|${names}}\n{pric|${values}}{unit|亿元}`
              }

              // return dom
              return dom
            },
            rich: {
              name: {
                fontSize: 30,
                padding: [0, 0, 50, 0],
                fontWeight: 'bold'
              },
              pric: {
                color: '#40EEFF',
                fontSize: 40,
                fontWeight: 'normal',
                fontFamily: 'PangMenZhengDao'
              },
              unit: {
                color: '#40EEFF',
                fontSize: 26,
                padding: [10, 0, 0, 4],
                fontWeight: 'normal',
                fontFamily: 'PangMenZhengDao'
              },
              tbs: {
                color: '#fb3f3f',
                fontSize: 30,
                padding: [60, 0, 0, 0],
                fontFamily: 'digital-7'
              },
              tbx: {
                color: '#00a92b',
                fontSize: 30,
                padding: [60, 0, 0, 0],
                fontFamily: 'digital-7'
              }
            }
          },
          labelLine: {
            lineStyle: {
              width: 3
            }
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          data: seriesData
        },
        {
          name: '',
          type: 'pie',
          radius: ['40%', '60%'],
          data: seriesData,
          label: {
            show: true,
            position: 'inside',
            color: '#fff',
            fontSize: 24,
            fontWeight: 'normal',
            fontFamily: 'PangMenZhengDao',
            formatter: (params) => {
              return params.percent ? `${params.percent}%` : ''
            }
          }
        }
      ]
    }

    this.myChart && this.myChart.setOption(this.option, true)
  }
}
</script>

<style scoped lang="scss">
.owners-equity-wrap {
  position: relative;
  height: 100%;
  @keyframes move {
    0% {
      transform: translate(-50%, -50%) rotateZ(0deg);
    }
    50% {
      transform: translate(-50%, -50%) rotateZ(360deg);
    }
    100% {
      transform: translate(-50%, -50%) rotateZ(0deg);
    }
  }
  .content-box {
    position: relative;
    z-index: 10;
    width: 100%;
    height: 92%;
    box-sizing: border-box;
    transform: translateY(-34px);
  }
  .pie-pric-box {
    position: absolute;
    top: 360px;
    width: 100%;
    text-align: center;
    h6,
    p {
      margin: 0;
    }

    .title {
      font-size: 42px;
    }
    .pric {
      color: #40eeff;
      span {
        font-size: 50px;
        font-weight: normal;
        font-family: 'PangMenZhengDao';
      }
      i {
        margin-left: -19px;
        font-weight: normal;
        font-family: 'PangMenZhengDao';
      }
    }

    .s {
      color: #fb3f3f;
    }
    .x {
      color: #00a92b;
    }
  }

  .halo {
    position: absolute;
    z-index: 1;
    top: 426px;
    left: 50%;
    width: 500px;
    height: 500px;
    transform: translate(-50%, -50%);
    background: url('../../../images/halo.png');
    background-size: 100% 100%;
    will-change: width, height, transform;
    //animation: move 20s linear forwards infinite;
  }
}
</style>