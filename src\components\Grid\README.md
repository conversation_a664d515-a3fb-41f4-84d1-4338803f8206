## 自定义表格

- 引入方式：

```
import Grid from '@/components/NewGrid/index.vue'
```

- 调用方式：

```
<Grid ref="gridTable"
  :remote-url="remoteUrl"
  :columns="columns"
  :show-pagination="true"
  :overflow-tooltip="true"
  :searchParams="searchParams"
  ......>
</Grid>
```

### 常规参数

| 参数名称            |              说明               | 默认值 |
| :----------------- | :----------------------------- | ----- |
| columns            |  定义表格列											|    [] |
| data               |  数据源												 |    [] |
| remote-url         |  远程加载数据接口 								 |    空 |
| show-pagination    |  是否显示分页										|  false |
| show-selection     |  是否显示多选框									 |  false |
| search-params      |  表格数据搜索条件								 |    {} |
| default-loading    |  是否使用默认的 loading 状态				|  true |
| overflow-tooltip    |  每列文本超出，是否使用 tip 提示   	|  false |
| default-key-values |  返回数据列表每个对象增加初始值    	|    {} |
| @loadCompleted     |  表格数据加载完成后执行函数				  |  noop |

**以上是常用的特有属性和方法，至于其他 element-ui el-table 组件的属性和方法，已通过 v-bind="$attrs" 和 v-on="listeners" 组件通信方式自行绑定，请自行参考 element-ui el-table 组件官网**

### 表格更新方法
```
let isCurrent = true	// true，从当前页开始加载表格，否则从第一页开始
this.$refs['Grid ref'].refresh(isCurrent)
```


### 实例场景1
用于 form 表单搜索，展示 tabel 搜索结果（带分页信息），至于分页的操作你不用关心，自定义表格内部已经处理好了，你只需要关心表格的属性配置和表格的更新时机即可

```
<template>
	<el-form :model="formData">
		......
		<el-button type="primary" @click="onSearchForm">搜 索</el-button>
	</el-form>
	
	<Grid ref="gridTable"
	  :remote-url="remoteUrl"	
	  :columns="columns"
	  :show-pagination="true"
	  :overflow-tooltip="true"
	  :searchParams="formData">
    <template slot="operationSlotName" slot-scope="scope">
      <el-button @click="operationEdit(scope.row)">编辑</el-button>
    </template>
	</Grid>
</template>

<script lang="ts">
	import Grid from '@/components/NewGrid/index.vue'
	@Component({ Grid })
	
	export default class extends Vue {
		private remoteUrl = '/fht-ams/crm/customer/tenant/queryByPage'
    private formData = {
      name: '',
      age: ''
    }
		private tableColumns: Array<any> = [
      { prop: 'name', label: '姓名' },
      { prop: 'age', label: '年龄' },
      {
        label: '操作',
        width: 120,
        fixed: 'right',
        slotName: 'operationSlotName'
      }
    ]

    // 表单搜索
    private onSearchForm() {
      (this.$refs.gridTable as Grid).refresh()
    }
	}
</script>
```


### 实例场景2
用于通过接口请求获取不带分页的表格数据（数据一般不多不需要分页信息），而且还有可能是同一个接口下，有着多个表格的数据；

比如，页面中有多个表格，它们的数据来源都是同一个接口下的不同数组对象，这个时候通过配置自定义表格的 data 属性比较适用

```
<Grid ref="gridTable1"
  :data="tabelData1"	
  :columns="columns1"
  :overflow-tooltip="true">
</Grid>

<Grid ref="gridTable2"
  :data="tabelData2"	
  :columns="columns2"
  :overflow-tooltip="true">
</Grid>
```