export default {
    "type": "page",
    "title": "page1",
    "body": [
        {
            "type": "tpl",
            "tpl": "这是你刚刚新增的页面。",
            "inline": false
        },
        {
            "type": "crud",
            "api": "",
            "columns": [
                {
                    "name": "id",
                    "label": "ID",
                    "type": "text"
                },
                {
                    "name": "engine",
                    "label": "渲染引擎",
                    "type": "text"
                },
                {
                    "type": "operation",
                    "label": "操作",
                    "buttons": [
                        {
                            "label": "编辑",
                            "type": "button",
                            "actionType": "dialog",
                            "level": "link",
                            "dialog": {
                                "title": "编辑",
                                "body": {
                                    "type": "form",
                                    "api": "xxx/update",
                                    "body": [
                                        {
                                            "name": "id",
                                            "label": "ID",
                                            "type": "input-text"
                                        },
                                        {
                                            "name": "engine",
                                            "label": "渲染引擎",
                                            "type": "input-text"
                                        }
                                    ]
                                }
                            }
                        }
                    ]
                }
            ],
            "bulkActions": [
                {
                    "type": "button",
                    "level": "danger",
                    "label": "批量删除",
                    "actionType": "ajax",
                    "confirmText": "确定要删除？",
                    "api": "/xxx/batch-delete"
                },
                {
                    "type": "button",
                    "label": "批量编辑",
                    "actionType": "dialog",
                    "dialog": {
                        "title": "批量编辑",
                        "size": "md",
                        "body": {
                            "type": "form",
                            "api": "/xxx/bacth-edit",
                            "body": [
                                {
                                    "label": "字段1",
                                    "text": "字段1",
                                    "type": "input-text"
                                }
                            ]
                        }
                    }
                }
            ],
            "itemActions": [
            ],
            "features": [
                "create",
                "filter",
                "bulkDelete",
                "bulkUpdate",
                "update"
            ],
            "errorMsg": {
                "config": {
                    "url": "",
                    "method": "post",
                    "data": "{\"type\":\"crud\",\"api\":\"\",\"columns\":[{\"name\":\"id\",\"label\":\"ID\",\"type\":\"text\"},{\"name\":\"engine\",\"label\":\"渲染引擎\",\"type\":\"text\"}],\"bulkActions\":[],\"itemActions\":[],\"features\":[\"create\",\"filter\",\"bulkDelete\",\"bulkUpdate\",\"update\"]}",
                    "headers": {
                        "Accept": "application/json, text/plain, */*",
                        "Content-Type": "application/json"
                    },
                    "transformRequest": [
                        undefined
                    ],
                    "transformResponse": [
                        undefined
                    ],
                    "timeout": 0,
                    "withCredentials": true,
                    "adapter": undefined,
                    "xsrfCookieName": "XSRF-TOKEN",
                    "xsrfHeaderName": "X-XSRF-TOKEN",
                    "maxContentLength": -1,
                    "maxBodyLength": -1,
                    "validateStatus": undefined
                },
                "request": {
                },
                "response": {
                    "data": "<html>\n<head><title>405 Not Allowed</title></head>\n<body bgcolor=\"white\">\n<center><h1>405 Not Allowed</h1></center>\n</body>\n</html>",
                    "status": 405,
                    "statusText": "",
                    "headers": {
                        "accept-ranges": "bytes",
                        "content-length": "131",
                        "date": "Thu, 05 May 2022 02:39:09 GMT",
                        "retry-after": "0",
                        "server": "Varnish",
                        "via": "1.1 varnish",
                        "x-cache": "MISS",
                        "x-cache-hits": "0",
                        "x-fastly-request-id": "e49f029c4c75c5c2a35449dc1a1facbc3a94a555",
                        "x-served-by": "cache-tyo11972-TYO",
                        "x-timer": "S1651718349.154720,VS0,VE0"
                    },
                    "config": {
                        "url": "",
                        "method": "post",
                        "data": "{\"type\":\"crud\",\"api\":\"\",\"columns\":[{\"name\":\"id\",\"label\":\"ID\",\"type\":\"text\"},{\"name\":\"engine\",\"label\":\"渲染引擎\",\"type\":\"text\"}],\"bulkActions\":[],\"itemActions\":[],\"features\":[\"create\",\"filter\",\"bulkDelete\",\"bulkUpdate\",\"update\"]}",
                        "headers": {
                            "Accept": "application/json, text/plain, */*",
                            "Content-Type": "application/json"
                        },
                        "transformRequest": [
                            undefined
                        ],
                        "transformResponse": [
                            undefined
                        ],
                        "timeout": 0,
                        "withCredentials": true,
                        "adapter": undefined,
                        "xsrfCookieName": "XSRF-TOKEN",
                        "xsrfHeaderName": "X-XSRF-TOKEN",
                        "maxContentLength": -1,
                        "maxBodyLength": -1,
                        "validateStatus": undefined
                    },
                    "request": {
                    }
                },
                "isAxiosError": true,
                "toJSON": undefined
            },
            "headerToolbar": [
                {
                    "label": "新增",
                    "type": "button",
                    "actionType": "dialog",
                    "level": "primary",
                    "dialog": {
                        "title": "新增",
                        "body": {
                            "type": "form",
                            "api": "POST:",
                            "body": [
                                {
                                    "type": "input-text",
                                    "name": "id",
                                    "label": "ID"
                                },
                                {
                                    "type": "input-text",
                                    "name": "engine",
                                    "label": "渲染引擎"
                                }
                            ]
                        }
                    }
                },
                "bulkActions",
                "pagination"
            ]
        }
    ]
}