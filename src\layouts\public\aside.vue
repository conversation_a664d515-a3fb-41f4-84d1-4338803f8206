<template>
  <el-aside class="aside-wrap"
    :class="{'aside-collapse-wrap': isCollapse}">
    <div class="logo-box">
      <span>金华市国资委数智监管系统</span>
    </div>

    <div class="menu-box">
      <el-menu unique-opened
        :show-timeout="220"
        :default-active="defaultActive"
        :collapse="isCollapse"
        class="el-menu-vertical-wrap">
        <!-- 循环开始 -->
        <div v-for="(item, index) of menuList"
          :key="index">
          <!-- 判断是否有子集 -->

          <!-- 有子集 -->
          <template v-if="Array.isArray(item.children) && item.children.length">
            <el-submenu class="menu-first-level"
              :index="String(index+1)">
              <template slot="title">
                <i :class="item.source" />
                <span v-if="!isCollapse">{{item.name}}</span>
              </template>

              <!-- 循环子集 -->
              <div v-for="(itemChildren, indexChildren) of item.children"
                :key="indexChildren">
                <!-- 判断子集是否有子集 -->

                <!-- 有子集 -->
                <template
                  v-if="Array.isArray(itemChildren.children) && itemChildren.children.length">
                  <el-submenu :index="`${String(index+1)}-${String(indexChildren+1)}`">
                    <template slot="title">
                      <i :class="itemChildren.source" />
                      <span class="child">{{itemChildren.name}}</span>
                    </template>

                    <!-- 循环子集的子集 -->
                    <el-menu-item
                      v-for="(itemChildrenChild, indexChildrenChild) of itemChildren.children"
                      :key="indexChildrenChild"
                      :index="`${String(index+1)}-${String(indexChildren+1)}-${String(indexChildrenChild+1)}`"
                      @click="onMenuChilk(itemChildrenChild, `${String(index+1)}-${String(indexChildren+1)}-${String(indexChildrenChild+1)}`)">
                      <i :class="itemChildrenChild.source" />
                      <span class="child">{{itemChildrenChild.name}}</span>
                    </el-menu-item>
                  </el-submenu>
                </template>

                <!-- 没有子集 -->
                <template v-else>
                  <el-menu-item class="menu-sub-level"
                    :index="`${String(index+1)}-${String(indexChildren+1)}`"
                    @click="onMenuChilk(itemChildren, `${String(index+1)}-${String(indexChildren+1)}`)">
                    <i :class="itemChildren.source" />
                    <span>{{itemChildren.name}}</span>
                  </el-menu-item>
                </template>
              </div>
            </el-submenu>
          </template>

          <!-- 没有子集 -->
          <template v-else>
            <el-menu-item class="menu-first-level"
              :index="String(index+1)"
              @click="onMenuChilk(item, String(index+1))">
              <i :class="item.source" />
              <span slot="title">{{item.name}}</span>
            </el-menu-item>
          </template>
        </div>
      </el-menu>
    </div>
  </el-aside>
</template>

<script lang="ts">
import { Component, Vue, Watch, Prop } from 'vue-property-decorator'
import { getAsideMenu } from '@/api/public'
import { getLocalStorage, setLocalStorage } from '@/utils/cache'

@Component({
  name: 'Aside'
})
export default class Aside extends Vue {
  @Prop({ default: false }) private isCollapse?: boolean

  // 侧边栏数据
  private queryId = ''
  private defaultActive = ''
  private asiderMenuData: any[] = []
  private menuList = []
  private process = process.env

  // 路由改变时，需要重新定位选中的项
  @Watch('$route')
  private routeChange() {
    this.asiderActiveHandle()
  }

  // 组件初始化
  private mounted() {
    this.$bus.$on('BusChangeMenu', (id: string) => {
      this.initData()
    })

    this.initData()
  }

  // 获取导航 menu 数据
  private async initData() {
    let activeMenuId = getLocalStorage('saber-activeMenuId') || ''
    let menuId = JSON.parse(activeMenuId).content
    let { data } = await getAsideMenu(menuId)

    this.asiderMenuData = data || []
    setLocalStorage('saber-asiderMenu', JSON.stringify(this.asiderMenuData))

    // 这里需要做第一个页面的跳转处理
    let dataChild = data[0]
    let onePath = ''

    // 递归查找子集的第一个页面
    function filterOnePath(obj: any) {
      if (Array.isArray(obj.children) && obj.children.length) {
        obj.children.forEach((item: any, index: number) => {
          !index && filterOnePath(item)
        })
      } else {
        onePath = obj.path
      }
    }

    filterOnePath(dataChild)

    // 如果是 monitor 框架项目，需要排除下面的代码 ['cockpit', 'asset', 'prewarning']
    let pathEliminate = ['cockpit']
    if (!pathEliminate.includes(dataChild.code)) {
      this.$router.push(onePath)
    }
    // end

    this.asiderActiveHandle()
    // end
  }

  // 根据链接地址，设置侧边栏选中模块
  private asiderActiveHandle() {
    let data = this.asiderMenuData

    if (!Array.isArray(data) || !data.length) return
    if (!Array.isArray(data[0].children) || !data[0].children.length) return

    let menuList = data[0].children || []
    this.menuList = menuList
    let pathname = window.location.href
    let pathnameArr = pathname.split('#')

    // 从链接地址提取和侧边栏path一致的路由
    if (Array.isArray(pathnameArr) && pathnameArr.length > 1) {
      pathname = pathnameArr[1]
    } else {
      pathname = pathnameArr[0]
    }

    // 排除 search 参数
    pathname = pathname.split('?')[0]

    // 通过侧边栏路由和地址栏路由，做选中效果
    for (let i = 0; i < menuList.length; i++) {
      if (pathname === menuList[i].path) {
        this.defaultActive = String(`${i + 1}`)
      }

      if (Array.isArray(menuList[i].children) && menuList[i].children.length) {
        for (let j = 0; j < menuList[i].children.length; j++) {
          if (pathname === menuList[i].children[j].path) {
            this.defaultActive = String(`${i + 1}-${j + 1}`)
            break
          }

          if (Array.isArray(menuList[i].children[j].children) && menuList[i].children[j].children.length) {
            for (let k = 0; k < menuList[i].children[j].children.length; k++) {
              if (pathname === menuList[i].children[j].children[k].path) {
                this.defaultActive = String(`${i + 1}-${j + 1}-${k + 1}`)
              }
            }
          }
        }
      }
    }
  }

  // 侧边栏点击
  private onMenuChilk(item: any, index: string) {
    // 跳外部链接（新窗口打开）
    if (item.path.indexOf('http') > -1 && item.isOpen === 2) {
      window.open(item.path)
      return
    }

    // 跳外部链接（本地 router-view 打开）
    if (item.path.indexOf('http') > -1 && item.isOpen === 1) {
      this.$router.push({
        path: '/myiframe/urlPath',
        query: {
          name: item.name,
          url: item.path
        }
      })
      return
    }

    // 跳内部业务系统路由
    this.defaultActive = String(index)
    this.$router.push({
      path: item.path
    })
  }

  // 组件销毁
  private destroyed() {
    this.defaultActive = ''
  }
}
</script>

<style scoped lang="scss">
$color: #b43c3c;
$activeColor: #ce4c4c;
$activeBg: rgba(
  $color: #b43c3c,
  $alpha: 0.2
);

::v-deep .el-submenu__icon-arrow {
  top: 55% !important;
}

.aside-wrap {
  position: relative;
  margin-top: -60px;
  width: 220px !important;
  display: flex;
  justify-content: flex-start;
  flex-direction: column;
}

.aside-collapse-wrap {
  width: 64px !important;
}

.logo-box {
  color: #fff;
  font-size: 18px;
  font-weight: 400;
  background: $activeColor;
  box-sizing: border-box;
  span {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 60px;
  }
}

.menu-box {
  flex: 1;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none;
  }
  .iconfont {
    margin-right: 6px;
  }
}

.el-menu-vertical-wrap {
  height: 100%;
  overflow-y: auto;
  border-right: none;
  &::-webkit-scrollbar {
    display: none;
  }

  .child {
    font-size: 14px;
    font-weight: normal;
  }

  .menu-first-level {
    font-size: 16px;
    font-weight: 500;
    &.is-active {
      ::v-deep .el-submenu__title {
        color: #303133;
      }
    }
    ::v-deep .el-submenu__title {
      color: #909399;
    }
  }

  .menu-sub-level {
    font-weight: normal;
  }

  .el-menu-item {
    cursor: pointer;
    border-bottom: 1px solid #f2f2f2;
    color: #909399;
    &:hover {
      background: none;
      i,
      span {
        color: $color;
      }
    }
    &.is-active {
      background: #ffeceb;
      border-right: 4px solid $activeColor;
      color: $activeColor;
    }
  }

  ::v-deep .el-submenu__title {
    font-size: 16px;
    border-bottom: 1px solid #f2f2f2 !important;
    &:hover {
      background: none;
      color: $activeColor;
      .iconfont {
        color: $activeColor;
      }
    }
    .el-menu-item span {
      font-size: 14px;
    }
  }

  .icon-one {
    margin-left: 2px;
    margin-right: 4px;
  }

  .is-opened {
    background: none !important;
  }
}
</style>
