<template>
  <Dialog title="租赁合同详情" width="1000px" :visible="visible" @close="handleClose" @open="changeList">
    <div slot="body">
      1256
      <el-descriptions title="">
        <el-descriptions-item v-for="(item, index) in list" :key="index" :label="item.title">{{ item.label }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <div slot="footer" class="footer">
      <el-button @click="showUpdaload">
        附件
        <span>({{ appendixsLength.length }})</span>
      </el-button>
      <el-button type="primary" @click="handleClose">关闭</el-button>
    </div>
  </Dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue, Watch, Emit } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
@Component({
  name: 'Detail',
  components: {
    Dialog
  }
})
export default class Container extends Vue {
  @Prop() private visible!: boolean
  @Prop() private row!: any
  @Prop() private cols!: object[]
  @Prop() private Nolist!: Array<string>
  private list: any = []
  @Emit('changeShowDetail')
  private handleClose() {
    return false
  }
  private changeList() {
    // 附件个数（）
    // this.appendixsLength = this.row.appendixs
    let list: { title: string; label: string }[] = []
    this.cols.forEach((item: any, index: number) => {
      if (index != this.cols.length - 1 && !this.Nolist.includes(item.label)) {
        let label: any = ''
        if (!item.filterOptions) {
          label = this.row[item.prop]
        } else {
          label = item.filterOptions.find((item: any) => {
            return (this.row[item.prop] = item.value)
          })

          label = label.label
        }
        list.push({
          title: item.label,
          label: label
        })
        label = null
      }
    })
    this.list = list
  }
}
</script>

<style lang="scss" scoped>
// checkTheDetails
::v-deep.el-descriptions-item__label {
  font-size: 14px;
  color: #909399;
}
</style>
