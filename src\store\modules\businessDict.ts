// 获取各业务字典项数据

import store from '@/store/index'
import { VuexModule, Module, Action, Mutation, getModule } from 'vuex-module-decorators'
import { infaceBusinessDictData } from '@/api/public'

// 自定义类型声明
type typeOption = {
  value: string,
  text: string,
  children?: any[]
}
type typeItemFace = {
  label: string
  value: string | number,
  children?: any[]
}
// end

export interface BusinessDict {
  dictLaodData: object
}

@Module({ dynamic: true, store, name: 'Business' })
class Business extends VuexModule implements BusinessDict {
  public dictLaodData = {}

  // 赋值字典项数据
  @Mutation
  private SET_BUSINESS_DICT(params: any[]) {
    let dictLaodData: any = {}

    Array.isArray(params) && params.forEach((item) => {
      let list: Array<typeItemFace> = []
      if (Array.isArray(item.children)) {
        list = item.children
        dictLaodData[item.key] = list || []
      }
    })

    this.dictLaodData = dictLaodData
  }

  // 获取字典项数据
  @Action
  public async getDictLoad(bizAppCode = '') {
    let { data } = await infaceBusinessDictData({
      bizAppCode: bizAppCode
    })

    data = data || []
    this.SET_BUSINESS_DICT(data)
  }
}

export const BusinessModule = getModule(Business)