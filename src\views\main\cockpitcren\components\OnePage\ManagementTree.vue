/* 管理树 */

<template>
  <section v-loading="loading"
    class="management-tree-wrap">
    <!-- 头部 -->
    <div class="header-box">
      <div class="mode">
        <div class="til">
          <strong>总资产</strong>
          <div class="info"
            :class="+getDescData('asset_amount') > 0?'x':'s'">
            <img v-if="+getDescData('asset_amount') > 0"
              src="@/views/main/cockpitcren/images/thows.png" />
            <img v-if="+getDescData('asset_amount') < 0"
              src="@/views/main/cockpitcren/images/thowx.png" />
            <span v-if="getDescData('asset_amount')">{{getDescData('asset_amount')}}%</span>
          </div>
        </div>
        <div class="count">
          <CountTo :decimals="2"
            :startVal='0'
            :endVal='+getBigNumberFormat(getAssettsData("asset_amount"))'
            :duration='1500' />
          <i>亿元</i>
        </div>
      </div>

      <div class="mode">
        <div class="til">
          <strong>净资产</strong>
          <div class="info"
            :class="+getDescData('asset_net') > 0?'x':'s'">
            <img v-if="+getDescData('asset_net') > 0"
              src="@/views/main/cockpitcren/images/thows.png" />
            <img v-if="+getDescData('asset_net') < 0"
              src="@/views/main/cockpitcren/images/thowx.png" />
            <span v-if="getDescData('asset_net')">{{getDescData('asset_net')}}%</span>
          </div>
        </div>
        <div class="count">
          <CountTo :decimals="2"
            :startVal='0'
            :endVal='+getBigNumberFormat(getAssettsData("asset_net"))'
            :duration='1500' />
          <i>亿元</i>
        </div>
      </div>

      <div class="mode">
        <div class="til">
          <strong>总营收</strong>
          <div class="info"
            :class="+getDescData('turnover') > 0?'x':'s'">
            <img v-if="+getDescData('turnover') > 0"
              src="@/views/main/cockpitcren/images/thows.png" />
            <img v-if="+getDescData('turnover') < 0"
              src="@/views/main/cockpitcren/images/thowx.png" />
            <span v-if="getDescData('turnover')">{{getDescData('turnover')}}%</span>
          </div>
        </div>
        <div class="count">
          <CountTo :decimals="2"
            :startVal='0'
            :endVal='+getBigNumberFormat(getAssettsData("turnover"))'
            :duration='1500' />
          <i>亿元</i>
        </div>
      </div>

      <div class="mode">
        <div class="til">
          <strong>总利润</strong>
          <div class="info"
            :class="+getDescData('profit_amount') > 0?'x':'s'">
            <img v-if="+getDescData('profit_amount') > 0"
              src="@/views/main/cockpitcren/images/thows.png" />
            <img v-if="+getDescData('profit_amount') < 0"
              src="@/views/main/cockpitcren/images/thowx.png" />
            <span v-if="getDescData('profit_amount')">{{getDescData('profit_amount')}}%</span>
          </div>
        </div>
        <div class="count">
          <CountTo :decimals="2"
            :startVal='0'
            :endVal='+getBigNumberFormat(getAssettsData("profit_amount"))'
            :duration='1500' />
          <i>亿元</i>
        </div>
      </div>

      <div v-if="false"
        class="mode">
        <div class="til">
          <strong>总负债</strong>
          <div class="info"
            :class="+getDescData('asset_debt_amount') > 0?'x':'s'">
            <img v-if="+getDescData('asset_debt_amount') > 0"
              src="@/views/main/cockpitcren/images/thows.png" />
            <img v-if="+getDescData('asset_debt_amount') < 0"
              src="@/views/main/cockpitcren/images/thowx.png" />
            <span
              v-if="getDescData('asset_debt_amount')">{{getDescData('asset_debt_amount')}}%</span>
          </div>
        </div>
        <div class="count">
          <CountTo :decimals="2"
            :startVal='0'
            :endVal='+getBigNumberFormat(getAssettsData("asset_debt_amount"))'
            :duration='1500' />
          <i>亿元</i>
        </div>
      </div>
    </div>

    <!-- 中部 -->
    <div class="content-box">
      <div class="map-box">
        <div v-if="isMapContent"
          class="modes-list">
          <div v-for="(item, index) of modeList"
            :key="index"
            class="modes"
            :class="[`modes${index}`, {'active-modes':index === modeActiveNum}]"
            @mouseenter="enterModes(index)"
            @mouseleave="cycleActiveMode(index)"
            @click="jumpToDetailePage(item.details)">
            <p class="text">
              <span>{{item.name}}</span>
            </p>
            <div class="info"
              :class="{'info-inner': item.inside}">
              <img src="@/views/main/cockpitcren/images/zx.png" />
              <div class="conter-box">
                <div class="conter">
                  <i class="icontop el-icon-caret-left" />
                  <i class="iconbtom el-icon-caret-right" />
                  <p v-for="(itemInfo, indexInfo) of item.list"
                    :key="indexInfo"
                    class="topit">
                    <strong>{{itemInfo.text}}</strong>
                    <span>{{itemInfo.count}}</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <video v-show="isMapContent"
          autoplay
          muted
          id="videoMode"
          class="video-box">
          <source src="https://fh-ka.oss-cn-hangzhou.aliyuncs.com/form.webm"
            type="video/webm">
        </video>
        <video v-show="isMapContent && isVideoLoop"
          autoplay
          muted
          loop
          id="videoModeLoop"
          class="video-box">
          <source src="https://fh-ka.oss-cn-hangzhou.aliyuncs.com/form.webm"
            type="video/webm">
        </video>
      </div>
    </div>

    <!-- 底部 -->
    <div class="footer-box">
      <div class="carousel-box">
        <el-carousel ref="carousel"
          :autoplay="false"
          :interval="4000"
          type="card"
          height="80px"
          indicator-position="none"
          @change="toggleTab">
          <el-carousel-item v-for="(item, index) in tabList"
            :key="index">
            <div class="medium">{{ item.name }}</div>
          </el-carousel-item>
        </el-carousel>

        <img src="@/views/main/cockpitcren/images/thow2.png"
          class="img prev"
          @click="changCarousel('prev')" />
        <img src="@/views/main/cockpitcren/images/thow1.png"
          class="img next"
          @click="changCarousel('next')" />
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { isWindowFull, bigNumberFormat } from '@/utils'
import { Loading } from '@/decorators/index'
import { capitalProfile } from '@/api/cockpit'
import { companyList } from '@/views/main/cockpitcren/baseData'
import { ElCarousel } from 'element-ui/types/carousel'
import CountTo from 'vue-count-to'

type typeItem = {
  id: string
  code: number
  name: string
}

@Component({
  components: {
    CountTo
  }
})
export default class extends Vue {
  private loading = false
  private year = ''

  // 头部数据
  private tabList: any[] = companyList
  private capitalProfileData: any[] = []

  // 中部内容数据
  private modeTimer: any
  private modeActiveNum = -1
  private profileData: any[] = []
  private isMapContent = true
  private isVideoLoop = false
  private videoMode: any = {}
  private videoModeLoop: any = {}
  private activeItem: typeItem = {
    code: 0,
    id: '',
    name: ''
  }
  private modeList = [
    {
      name: '企业画像',
      details: 'CompanyDetail',
      list: [
        {
          text: '一级企业',
          count: '6 家'
        },
        {
          text: '二级企业',
          count: '62 家'
        },
        {
          text: '三级企业',
          count: '51 家'
        },
        {
          text: '四级企业',
          count: '13 家'
        },
        {
          text: '五级企业',
          count: '3 家'
        }
      ]
    },
    {
      name: '财务监管',
      details: 'FinancialIndicator',
      list: [
        {
          text: '净资产收益率',
          count: '89.23%'
        }
      ]
    },
    // {
    //   name: '资产监管',
    //   details: 'AssetComposition',
    //   list: [
    //     {
    //       text: '保值增值率',
    //       count: '49.22%'
    //     }
    //   ]
    // },
    {
      name: '项目投资',
      details: 'ProjectProgress',
      list: [
        {
          text: '项目完成率',
          count: '48.45%'
        }
      ]
    },
    {
      name: '三重一大',
      details: 'SanZhongYiDa',
      list: [
        {
          text: '重大事项决策',
          count: '47 次'
        },
        {
          text: '重要干部任免',
          count: '44 次'
        },
        {
          text: '重大项目投资',
          count: '55 亿元'
        },
        {
          text: '大额资金使用',
          count: '57.48 亿元'
        }
      ]
    },
    {
      name: '绩效评价',
      details: 'CompositeIndex',
      list: [
        {
          text: '各企业绩效评价',
          count: '89'
        }
      ]
    },
    {
      name: '债务监管',
      inside: true,
      details: 'Liabilities',
      list: [
        {
          text: '负债率',
          count: '15.34%'
        }
      ]
    },
    {
      name: '智慧预警',
      inside: true,
      details: 'DebtWarning',
      list: [
        {
          text: '处置数',
          count: '543个'
        },
        {
          text: '处置率',
          count: '78.35%'
        }
      ]
    },
    {
      name: '不动产管理',
      inside: true,
      details: 'RealEstate',
      list: [
        {
          text: '出租率',
          count: '76.23%'
        },
        {
          text: '空置率',
          count: '23.43%'
        }
      ]
    }
  ]
  $bus: any

  // 判断窗口是否处于全屏模式
  get getIsWindowFull() {
    return isWindowFull
  }

  // 万元数据转化成亿元
  get getBigNumberFormat() {
    return (num: string | number) => {
      return bigNumberFormat(+num).value || 0
    }
  }

  // 获取对应增长率
  get getDescData() {
    return (code: string) => {
      let data = this.profileData.find((item) => {
        return item.itemCode === code
      })

      let num = data && (+data.itemDesc * 100).toFixed(2)

      return Math.floor(+num)
    }
  }

  // 获取对应资产总览
  get getAssettsData() {
    return (code: string) => {
      let data = this.profileData.find((item) => {
        return item.itemCode === code
      })

      return (data && String(+data.itemValue)) || ''
    }
  }

  // 组件初始化
  private mounted() {
    this.videoMode = document.getElementById('videoMode')
    this.videoModeLoop = document.getElementById('videoModeLoop')
    this.year = String((this as any).$moment(new Date()).add('year', 0).format('YYYY') - 1)
    this.videoPalyHandle()
    this.capitalProfile()
    setTimeout(() => {
      this.cycleActiveMode(0)
    }, 8000)

    // 时间改变时触发数据更新
    this.$bus.$on('BusYearTimeChange', (year: string) => {
      this.year = year
      this.filterCapitalProfile()
    })
  }

  // 获取资产数据
  @Loading('loading')
  private async capitalProfile() {
    let { data } = await capitalProfile({
      year: this.year
    })

    this.capitalProfileData = data || []
    this.toggleTab()
  }

  // 开启定时器，做树上模块的选中效果
  private cycleActiveMode(index: number) {
    this.clearModeTimer()
    this.modeActiveNum = index
    this.modeTimer = setInterval(() => {
      this.modeActiveNum++
      if (this.modeActiveNum >= this.modeList.length) {
        this.modeActiveNum = 0
      }
    }, 5000)
  }

  // 鼠标移入各模块，暂停定时器轮播
  private enterModes(index: number) {
    this.clearModeTimer()
    this.modeActiveNum = index
  }

  // 切换tab
  private toggleTab(index = 0) {
    let tabCode = this.tabList[index]
    let code = tabCode.code
    let activeItem = this.tabList.filter((item) => {
      return +item.code === +code
    })

    if (activeItem.length) this.activeItem = activeItem[0]
    this.filterCapitalProfile()
  }

  // 筛选对应资产数据
  private filterCapitalProfile() {
    this.profileData = this.capitalProfileData.filter((item) => {
      return +item.year === +this.year - 1 && +item.companyCode === +this.activeItem.code
    })

    this.$bus.$emit('BusTabsChange', this.activeItem)
  }

  // 视频播放
  private videoPalyHandle() {
    setTimeout(() => {
      this.isVideoLoop = true
    }, 8000)
  }

  // 手动切换三重一大内容
  private changCarousel(str: 'prev' | 'next') {
    let carouselDom = this.$refs['carousel'] as ElCarousel

    if (str === 'prev') carouselDom.prev()
    if (str === 'next') carouselDom.next()
  }

  // 跳转详情页面
  private jumpToDetailePage(module: string) {
    this.$router.push({
      name: 'moduleDetail',
      query: { module: module },
      params: { isWindowFull: this.getIsWindowFull() }
    })
  }

  // 组件销毁
  private destroyed() {
    this.clearModeTimer()
  }

  // 停止模块选中定时器
  private clearModeTimer() {
    clearInterval(this.modeTimer)
    this.modeTimer = null
  }
}
</script>

<style scoped lang="scss">
.management-tree-wrap {
  position: relative;
  display: flex;
  justify-content: space-between;
  flex-direction: column;

  width: 100%;
  height: 1200px;

  @keyframes keyModes0 {
    0% {
      left: 786px;
      top: 328px;
      opacity: 0;
    }
    100% {
      left: 746px;
      top: 403px;
      opacity: 1;
    }
  }
  @keyframes keyModes1 {
    0% {
      left: 786px;
      top: 328px;
      opacity: 0;
    }
    100% {
      left: 477px;
      top: 492px;
      opacity: 1;
    }
  }
  @keyframes keyModes2 {
    0% {
      left: 786px;
      top: 328px;
      opacity: 0;
    }
    100% {
      left: 330px;
      top: 290px;
      opacity: 1;
    }
  }
  @keyframes keyModes3 {
    0% {
      left: 786px;
      top: 328px;
      opacity: 0;
    }
    100% {
      left: 484px;
      top: 81px;
      opacity: 1;
    }
  }
  @keyframes keyModes4 {
    0% {
      left: 786px;
      top: 328px;
      opacity: 0;
    }
    100% {
      left: 780px;
      top: 87px;
      opacity: 1;
    }
  }
  @keyframes keyModes5 {
    0% {
      left: 786px;
      top: 328px;
      opacity: 0;
    }
    100% {
      left: 1063px;
      top: 122px;
      opacity: 1;
    }
  }
  @keyframes keyModes6 {
    0% {
      left: 786px;
      top: 328px;
      opacity: 0;
    }
    100% {
      left: 1186px;
      top: 286px;
      opacity: 1;
    }
  }
  @keyframes keyModes7 {
    0% {
      left: 786px;
      top: 328px;
      opacity: 0;
    }
    100% {
      left: 1086px;
      top: 493px;
      opacity: 1;
    }
  }
  @keyframes keyModes8 {
    0% {
      left: 786px;
      top: 328px;
      opacity: 0;
    }
    100% {
      left: 778px;
      top: 502px;
      opacity: 1;
    }
  }

  @keyframes keyModesLanbel1 {
    0% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-50px);
    }
    100% {
      transform: translateY(0);
    }
  }
  @keyframes keyModesLanbel2 {
    0% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(50px);
    }
    100% {
      transform: translateY(0);
    }
  }

  @keyframes keyMoveThow1 {
    0% {
      opacity: 1;
      transform: translateX(0);
    }
    50% {
      opacity: 0.5;
      transform: translateX(-20px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }
  @keyframes keyMoveThow2 {
    0% {
      opacity: 1;
      transform: translateX(0);
    }
    50% {
      opacity: 0.5;
      transform: translateX(20px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .header-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
    padding: 0 60px;
    p {
      margin: 0;
    }

    .mode {
      flex: 1;
      height: 102px;
      padding: 14px 16px;
      text-align: left;
      margin-right: 10px;
      overflow: hidden;
      &:nth-last-child(1) {
        margin-right: 0;
      }
    }
    .s {
      color: #00a12a;
    }
    .x {
      color: #fb3f3f;
    }

    .til {
      font-size: 34px;
      strong {
        margin-right: 10px;
      }
      .info {
        display: inline-block;
        font-size: 33px;
        img {
          width: 22px;
          margin-right: 4px;
        }
      }
    }
    .count {
      color: #3eeeff;
      font-size: 54px;
      font-weight: normal;
      font-family: 'PangMenZhengDao';
      i {
        font-size: 30px;
        margin-left: -20px;
      }
    }
  }

  .content-box {
    position: relative;
    flex: 1;
    height: 800px;
    .map-box {
      position: relative;
      width: 110%;
      height: 110%;
      transform: translateX(-5%) translateY(-5%);

      .video-box {
        position: absolute;
        left: 0;
        top: 0;
        z-index: 1;
        width: 100%;
        height: 100%;
        filter: hue-rotate(206.087deg) saturate(1) brightness(1.7);
      }

      .modes-list {
        position: relative;
        left: -5%;
        top: 58px;
        z-index: 2;
      }

      .modes {
        $cor: #3eeeff;
        $cobg1: #021e5d;
        $cobg2: #0090fe;

        position: absolute;
        padding: 14px;
        border: 2px solid rgba($color: $cor, $alpha: 0.6);
        border-radius: 50%;
        cursor: pointer;
        font-weight: normal;
        font-family: 'FZZZHONGHJW';
        background: rgba($color: $cobg1, $alpha: 0.7);
        &:hover {
          z-index: 100;
          box-shadow: 0 0 30px $cor;
          animation-play-state: paused;
          background: rgba($color: $cobg1, $alpha: 0.8);
          border: 2px solid rgba($color: $cor, $alpha: 1);
          .text {
            width: 140px;
            height: 140px;
            color: #eeb500;
            font-size: 48px;
            line-height: 48px;
            border: 2px solid rgba($color: $cobg2, $alpha: 1);

            // transform: scale(1.2);
            // color: #eeb500;
            // font-size: 48px;
            // line-height: 48px;
            // border: 1px solid rgba($color: $cobg2, $alpha: 1);
          }
          .info {
            z-index: 30;
            opacity: 1;
            width: inherit;
            height: inherit;
          }
        }
        .text {
          position: relative;
          display: flex;
          align-items: center;
          text-align: center;
          flex-direction: column;
          justify-content: center;
          width: 110px;
          height: 110px;
          padding: 10px;
          font-size: 42px;
          line-height: 46px;
          border-radius: 50%;
          margin: 0;
          transition: 0.5s ease;
          border: 2px solid rgba($color: $cobg2, $alpha: 0.4);
          & span:nth-child(1) {
            margin-top: 10px;
          }
        }
        .info {
          width: 0;
          height: 0;
          opacity: 0;
          position: absolute;
          right: -544px;
          top: -100px;
          z-index: 10;
          overflow: hidden;
          //transition: 1s ease;
          img {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
          }
          .conter-box {
            padding: 15px 30px 15px 124px;
          }
          .conter {
            position: relative;
            display: flex;
            align-items: center;
            text-align: left;
            flex-direction: column;
            justify-content: left;
            min-width: 500px;
            padding: 20px 20px 20px 40px;
            box-sizing: border-box;
            border: 2px solid $cor;
            border-radius: 10px;
            box-shadow: 0 0 30px $cor;
            background: rgba($color: $cobg1, $alpha: 0.8);
            i {
              position: absolute;
              color: $cor;
            }
            .icontop {
              left: -4px;
              top: -4px;
              transform: rotate(45deg);
            }
            .iconbtom {
              right: -4px;
              bottom: -4px;
              transform: rotate(45deg);
            }
            .topit {
              margin: 0;
              width: 100%;
              display: flex;
              align-items: center;
              justify-content: space-between;
              strong {
                display: inline-block;
                vertical-align: middle;
                color: #eeb500;
                font-size: 40px;
                min-width: 100px;
                font-weight: normal;
                font-family: none;
                margin-right: 16px;
              }
              span {
                display: inline-block;
                vertical-align: middle;
                font-weight: bold;
                font-size: 40px;
                font-family: none;
              }
            }
          }
        }
        .info-inner {
          right: 81px;
          top: -85px;
          img {
            transform: rotateY(180deg);
          }
          .conter-box {
            padding: 15px 124px 15px 30px;
          }
        }
      }
      .modes0 {
        left: 746px;
        top: 403px;
        animation: keyModes0 8s ease-in-out, keyModesLanbel2 8s linear infinite;
      }
      .modes1 {
        left: 477px;
        top: 492px;
        animation: keyModes1 8s ease-in-out, keyModesLanbel1 10s linear infinite;
      }
      .modes2 {
        left: 330px;
        top: 290px;
        animation: keyModes2 7s ease-in-out, keyModesLanbel2 10s linear infinite;
      }
      .modes3 {
        left: 484px;
        top: 81px;
        animation: keyModes3 5s ease-in-out, keyModesLanbel2 10s linear infinite;
      }
      .modes4 {
        left: 780px;
        top: 87px;
        animation: keyModes4 5s ease-in-out, keyModesLanbel2 6s linear infinite;
      }
      .modes5 {
        left: 1063px;
        top: 122px;
        animation: keyModes5 6s ease-in-out, keyModesLanbel1 10s linear infinite;
      }
      .modes6 {
        left: 1186px;
        top: 286px;
        animation: keyModes6 7s ease-in-out, keyModesLanbel2 10s linear infinite;
      }
      .modes7 {
        left: 1086px;
        top: 493px;
        animation: keyModes7 7s ease-in-out, keyModesLanbel1 10s linear infinite;
        .text {
          font-size: 34px;
        }
      }
      .modes8 {
        left: 778px;
        top: 502px;
        animation: keyModes8 6s ease-in-out, keyModesLanbel2 7s linear infinite;
        .text {
          font-size: 36px !important;
        }
      }

      .active-modes {
        $cor: #3eeeff;
        $cobg1: #021e5d;
        $cobg2: #0090fe;

        z-index: 100;
        box-shadow: 0 0 30px $cor;
        animation-play-state: paused;
        background: rgba($color: $cobg1, $alpha: 0.8);
        border: 2px solid rgba($color: $cor, $alpha: 1);
        .text {
          width: 140px;
          height: 140px;
          color: #eeb500;
          font-size: 48px;
          line-height: 48px;
          border: 2px solid rgba($color: $cobg2, $alpha: 1);
        }
        .info {
          z-index: 30;
          opacity: 1;
          width: auto;
          height: auto;
          overflow: inherit;
        }
      }
    }
  }

  .footer-box {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 200px;
    display: flex;
    justify-content: center;

    .carousel-box {
      position: relative;
      width: 75%;
      .img {
        position: absolute;
        top: 107px;
        width: 100px;
        cursor: pointer;
      }
      .prev {
        left: -64px;
        animation: keyMoveThow2 5s infinite;
      }
      .next {
        right: -64px;
        animation: keyMoveThow1 5s infinite;
      }

      .medium {
        font-size: 50px;
        text-align: center;
        line-height: 140px;
        margin-top: 68px;
        background: url('../../images/tree_btn_active.png') no-repeat center center;
        background-size: 100% 100%;
      }

      & .medium:nth-child(2) {
        transform: rotateX(-10deg);
      }
    }

    ::v-deep .el-carousel {
      height: 100%;
      .el-carousel__container {
        height: 100% !important;
        transform-style: preserve-3d;
        .el-carousel__mask {
          background: none;
        }
        .el-carousel__item {
          opacity: 0.6;
        }
        .is-active {
          opacity: 1;
        }
      }
    }
  }
}
</style>