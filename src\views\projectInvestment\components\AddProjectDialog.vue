// 新增项目弹框
<template>
  <Dialog :title="title"
    width="880px"
    :visible="visible"
    :close-on-click-modal="true"
    :destroy-on-close="true"
    :append-to-body="true"
    @close="closeDlg"
    @open="openDiaHandle">
    <div slot="body"
      class="">
      <component ref="ProjectForm"
        :is='componentName'></component>
    </div>
    <div slot="footer">
      <el-button @click="closeDlg">取消</el-button>
      <el-button class="primary-buttom"
        type="primary"
        @click="submitForm">新增</el-button>
    </div>
  </Dialog>
</template>
<script lang="ts">
import { Component, Emit, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import Uploader from '@/components/Uploader/index.vue'
import AgentProjectForm from './ProjectForm/AgentProjectForm.vue'
import CompanyProjectForm from './ProjectForm/CompanyProjectForm.vue'
import GovProjectForm from './ProjectForm/GovProjectForm.vue'

type ProjectForm = AgentProjectForm | CompanyProjectForm | GovProjectForm

@Component({
  components: {
    Dialog,
    Uploader,
    AgentProjectForm,
    CompanyProjectForm,
    GovProjectForm
  }
})
export default class extends Vue {
  @Prop() private visible!: boolean
  @Prop() private type!: 'agent' | 'company' | 'gov'
  @Prop({ default: 'add' }) private mode!: 'edit' | 'add'

  // 弹窗关闭
  @Emit('close')
  private onClose() {
    //
  }

  private componentNameMap = {
    agent: {
      name: 'AgentProjectForm',
      title: '项目-代建'
    },
    company: {
      name: 'CompanyProjectForm',
      title: '项目-公司'
    },
    gov: {
      name: 'GovProjectForm',
      title: '项目-政府'
    }
  }

  get componentName() {
    return this.componentNameMap[this.type]['name']
  }

  get title() {
    return `${this.mode === 'edit' ? '编辑' : '新增'}${this.componentNameMap[this.type]['title']}`
  }

  private openDiaHandle() {
    // 
  }

  private submitForm() {
    // 校验
    let projectForm = this.$refs.ProjectForm as ProjectForm
    projectForm.validate().then((res) => {
      if (!res) return
      this.closeDlg()
      this.$emit('success', projectForm.projectForm)
    })
  }

  // 关闭弹窗
  private closeDlg() {
    this.$emit('update:visible', false)
    this.onClose()
  }
}
</script>

<style lang="scss" scoped>
</style>
