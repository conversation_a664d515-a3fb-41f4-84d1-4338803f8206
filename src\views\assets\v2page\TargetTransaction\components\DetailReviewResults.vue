// 出租房信息
<template>
  <el-container class="table-wrapper" direction="vertical">
    <el-descriptions
      title="核实信息(产交所 )"
      :column="2"
      border
      :labelStyle="{
        width: '130px'
      }"
    >
      <el-descriptions-item label="审核意见">{{ detail.auditRecord.sysAuditNote }}</el-descriptions-item>
      <el-descriptions-item label="核实时间">{{ detail.auditRecord.sysAuditDate }}</el-descriptions-item>
      <el-descriptions-item label="意见" :span="2">{{ detail.auditRecord.sysAuditStatusDesc }}</el-descriptions-item>
      <el-descriptions-item label="审核附件" :span="2">
        <AccessoryList v-model="detail.auditRecord.fileList" mode="see" title="" />
      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions
      title="挂牌信息（浙交汇）"
      :column="2"
      border
      :labelStyle="{
        width: '130px'
      }"
    >
      <el-descriptions-item label="承租方姓名">{{ detail.auditRecord.renterName }}</el-descriptions-item>
      <el-descriptions-item label="浙交汇项目编号">{{ detail.auditRecord.zjhSubjectNo }}</el-descriptions-item>
      <el-descriptions-item label="挂牌状态">{{ detail.auditRecord.listingAuditStatusDesc }}</el-descriptions-item>
      <el-descriptions-item label="更新时间">{{ detail.auditRecord.listingUpdateDate }}</el-descriptions-item>
      <el-descriptions-item label=" 成交金额">{{ detail.auditRecord.dealAmount || '-' }}元</el-descriptions-item>
      <el-descriptions-item label=" 成交时间">{{ detail.auditRecord.dealDate }}</el-descriptions-item>
      <el-descriptions-item label="浙交汇挂牌地址链接">
        <el-link v-if="detail.auditRecord.listingPcUrl!=''" icon="el-icon-link" @click="openhref(detail.auditRecord.listingPcUrl)">
          {{ detail.auditRecord.listingPcUrl }}
        </el-link>
      </el-descriptions-item>
    </el-descriptions>
  </el-container>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import Title from '@/views/assets/components/Title/index.vue'
import Grid from '@/components/Grid/index.vue'
import AccessoryList from '@/views/assets/components/astFileList/index.vue'
@Component({
  components: {
    Title,
    Grid,
    AccessoryList
  }
})
export default class extends Vue {
  @Prop({
    default: () => ({
      name: 'name',
      auditRecord: {}
    })
  })
  private detail: any
  // private detail: any = {
  //   name: 'name',
  //   auditRecord:{}
  // }
  private openhref(url: string) {
    window.open(url, '__blank')
  }
}
</script>

<style lang="scss" scoped>
::v-deep.el-descriptions {
  margin-bottom: 15px;
}
</style>
