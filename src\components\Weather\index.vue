<template>
  <section class="weather-warp">
    <p>金华市</p>
    <p>{{weatherData.textDay}}</p>
    <i class="icons"
      :class="`qi-${weatherData.iconDay}`" />
    <p class="wd">{{weatherData.tempMin}}℃ - {{weatherData.tempMax}}℃</p>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import axios from 'axios'

@Component({
  name: 'Weather'
})
export default class Header extends Vue {
  private weatherData = {}

  // 组件初始化
  private mounted() {
    this.initData()
  }

  // 获取天气预报时间（天气数据网：https://console.qweather.com）
  private async initData() {
    let params = {
      key: 'f2090f457cfc404bad58d94ef49e4fa8',
      location: '119.649506,29.089524'
    }

    await axios.get(`https://devapi.qweather.com/v7/weather/3d?key=${params.key}&location=${params.location}`).then((res: any) => {
      let data = res.data
      if (data && Array.isArray(data.daily) && data.daily.length) {
        this.weatherData = data.daily[0]
      }
    })
  }
}
</script>

<style scoped lang="scss">
.weather-warp {
  $textColor: #63f1ff;

  p {
    margin: 0;
  }
  p,
  i {
    margin-right: 20px;
  }

  display: flex;
  align-items: end;
  color: $textColor;
  font-size: 40px;
  font-weight: normal;

  .wd {
    color: rgba($color: #63f1ff, $alpha: 0.9);
  }
}
</style>