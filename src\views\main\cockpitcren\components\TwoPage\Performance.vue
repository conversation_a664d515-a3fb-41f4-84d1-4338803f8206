/* 经营状况 */

<template>
  <section ref="wrapper"
    class="cockpit-performance-wrap"
    :class="{'cockpit-wrapperFull-wrap': isWrapperFull}">
    <!-- 标题 -->
    <TitleCom title="经营状况"
      :module="`FinancialIndicator`"
      :isFullScreen="true"
      :orgCode="orgActive.orgCode"
      @fullScreenHandle="fullScreenHandle" />

    <!-- 年度、月度tabs 切换 -->
    <div class="tab-box">
      <span v-for="item of tabList"
        :key="item.value"
        :class="{'active':period===item.value}"
        @click="periodChange(item.value)">{{ item.label }}</span>
    </div>

    <div v-if="getIsExclude" class="exclude-box">
      <el-checkbox v-model="exclude"
        :true-label="0"
        :false-label="1">大合并</el-checkbox>
    </div>

    <!-- 内容区域 -->
    <div v-loading="loading"
      class="content-box">
      <!-- tabs 切换  -->
      <div class="content">
        <div class="total">
          <div v-for="(item, index) of getDataOneList"
            :key="index"
            :class="{'active': item.type === activeTab}"
            class="mode"
            @click="tabChange(item.type)">
            <h4 class="title">
              <p>{{item.name}}</p>
              <p :class="[{'s': +item.tb>0}, {'x': +item.tb<0}]">
                <img v-if="+item.tb>0"
                  src="../../images/thows.png" />
                <img v-if="+item.tb<0"
                  src="../../images/thowx.png" />
                <i>{{item.tb}}%</i>
              </p>
            </h4>
            <CountTo :decimals="2"
              :startVal="0"
              :endVal="+item.value"
              :duration="1500"
              :class="[{'zzc' : !index}, {'zfz' : index}]" />
            <i class="unit">亿元</i>
          </div>
        </div>

        <div class="total total2">
          <div v-for="(item, index) of getDataTwoList"
            :key="index"
            class="mode"
            :class="{'active': item.type === activeTab}"
            @click="tabChange(item.type)">
            <h4 class="title title1">
              <p>{{item.name}}</p>
              <p v-if="+item.tb"
                :class="[{'s': +item.tb>0}, {'x': +item.tb<0}]">
                <img v-if="+item.tb>0"
                  src="../../images/thows.png" />
                <img v-if="+item.tb<0"
                  src="../../images/thowx.png" />
                <i>{{item.tb}}%</i>
              </p>
            </h4>
            <CountTo :decimals="2"
              :startVal="0"
              :endVal="+item.value"
              :duration="1500" />
            <i class="unit">亿元</i>
          </div>
        </div>
      </div>

      <!-- echarts 视图 -->
      <div class="echarts-box">
        <div id="refEcharts"
          ref="refEcharts" />
      </div>
    </div>

    <!-- 数据改变提示层 -->
    <img v-if="visibleFlicker"
      class="cockipt-bg-flicker"
      src="@/views/main/cockpitcren/images/panel_bg1_cg.png" />

    <!-- 详情弹窗 -->
    <CockiptGridDialog v-if="visibleDetail"
      :visible.sync="visibleDetail"
      :year="params.year"
      :closeModal="false"
      :code="orgActive.orgCode"
      :columns="columns"
      :remoteUrl="remoteUrl"
      :searchParams="searchParams"
      title="财务监管" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import { deepClone, bigNumberFormat } from '@/utils'
import { Loading, Throttle } from '@/decorators'
import { echartConfigure } from '@/views/main/cockpitcren/baseData'
import { astHomeSummary, astHomeHistory } from '@/api/cockpit'
import CountTo from 'vue-count-to'
import TitleCom from '@/views/main/cockpitcren/components/Public/TitleCom.vue'
import * as echarts from 'echarts'
import CockiptGridDialog from '@/views/main/cockpitcren/components/Public/CockiptDialog.vue'

type EChartsOption = echarts.EChartsOption

@Component({
  components: {
    CountTo,
    TitleCom,
    CockiptGridDialog
  }
})
export default class extends Vue {
  // 渲染数据
  private loading = false
  private visibleFlicker = true
  private activeTab = ''
  private dataTotalList: any[] = []
  private dataEchartsList: any[] = []

  // 年度、月度切换
  private exclude = 0
  private period = '1'
  private tabList = Object.freeze([
    {
      label: '年度',
      value: '1'
    },
    {
      label: '月度',
      value: '2'
    }
  ])

  // 所属集团+时间切换 选中数据
  private orgActive: {
    orgCode: string
  } = {
    orgCode: '0'
  }
  private params = {
    year: String(new Date().getFullYear()),
    month: ''
  }

  // echarts 视图数据
  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private xAxisData: string[] = []
  private legendData: string[] = []
  private seriesData: any[] = []
  private option: EChartsOption = {}

  // 详情表格数据
  private visibleDetail = false
  private remoteUrl = Object.freeze('/fht-monitor/ds/screen/data/asset')
  private searchParams = {}
  private columns: any[] = [
    {
      prop: '',
      label: '企业名称'
    },
    {
      prop: '',
      label: '所属集团'
    },
    {
      prop: '',
      label: '资产总额'
    },
    {
      prop: '',
      label: '同比'
    },
    {
      prop: '',
      label: '资产净额'
    },
    {
      prop: '',
      label: '同比'
    },
    {
      prop: '',
      label: '营收总额'
    },
    {
      prop: '',
      label: '同比'
    },
    {
      prop: '',
      label: '利润总额'
    },
    {
      prop: '',
      label: '同比'
    },
    {
      prop: '',
      label: '负债总额'
    },
    {
      prop: '',
      label: '同比'
    }
  ]

  // 是否放大展示
  private isWrapperFull = false

  // 大合并按钮是否显示
  get getIsExclude() {
    return this.orgActive.orgCode === 'JH1200' || this.orgActive.orgCode === '0' ? true : false
  }

  // 万元数据转化成亿元
  get getBigNumberFormat() {
    return (num: string | number) => {
      return bigNumberFormat(+num).value || 0
    }
  }

  // 由于样式问题，拆分数据，取前两个
  get getDataOneList() {
    let data = deepClone(this.dataTotalList)
    return data.splice(0, 2)
  }

  // 由于样式问题，拆分数据取后三个
  get getDataTwoList() {
    let data = deepClone(this.dataTotalList)
    return data.splice(2, data.length)
  }

  // 监听 tabs 选中值，然后去匹配数据，在渲染 echarts 视图
  @Watch('activeTab')
  private changeActiveTab() {
    this.filterEcharts()
  }

  // 监听 period 选中值，然后去渲染数据
  @Watch('period')
  private changePeriod() {
    this.initTotalData()
  }

  // 大合并改变，触发数据渲染
  @Watch('exclude', { deep: true })
  private changeExclude() {
    this.initTotalData()
  }

  // 初始化
  private mounted() {
    this.chartDom = this.$refs.refEcharts as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    this.listenerDate()
  }

  // 年度、月度切换
  private periodChange(value: string) {
    this.period = value
  }

  // 头部模块 tabs 切换
  private tabChange(type: string) {
    this.activeTab = type
  }

  // 监听全局事件触发
  private listenerDate() {
    // 集团 切换
    this.$bus.$on('BusTabsChange', (orgActive: any) => {
      this.orgActive = orgActive
      this.initTotalData()
    })

    // 年份 切换
    this.$bus.$on('BusYearTimeChange', (year: string) => {
      this.params.year = year
      this.initTotalData()
    })
  }

  // 获取汇总数据
  @Loading('loading')
  private async initTotalData() {
    this.flickerHandle()

    let exclude = this.orgActive.orgCode === 'JH1200' || this.orgActive.orgCode === '0' ? this.exclude : undefined

    await astHomeSummary({
      period: this.period,
      orgCode: this.orgActive.orgCode,
      year: this.params.year,
      exclude: exclude
    })
      .then((res) => {
        this.dataTotalList = res.data || []
        if (Array.isArray(this.dataTotalList) && this.dataTotalList.length) {
          this.activeTab = this.dataTotalList[0].type
        }
        this.initEchartsData()
      })
      .catch(() => {
        this.dataTotalList = []
        this.activeTab = ''
      })
  }

  // 获取视图数据
  @Loading('loading')
  private async initEchartsData() {
    let exclude = this.orgActive.orgCode === 'JH1200' || this.orgActive.orgCode === '0' ? this.exclude : undefined

    await astHomeHistory({
      period: this.period,
      orgCode: this.orgActive.orgCode,
      year: this.params.year,
      exclude: exclude
    }).then((res) => {
      this.dataEchartsList = res.data || []
      this.filterEcharts()
    }).catch(() => {
      this.dataEchartsList = []
    })
  }

  // 组装 echarts 数据
  private filterEcharts() {
    if (!this.dataEchartsList.length) return

    this.chartDom = this.$refs.refEcharts as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    let dataTotalList: any[] = [] 

    let data: {
      type: string
      data: any[]
    } = this.dataEchartsList.find((item) => {
      return +item.type === +this.activeTab
    })

    if (data.type) dataTotalList = data.data || []

    let xAxisData: string[] = []
    let legendData: string[] = []
    let seriesData: any[] = []

    dataTotalList.forEach((item) => {
      legendData.push(item.name)
      xAxisData = []
      let list: number[] = []
      if (Array.isArray(item.list) && item.list.length) {
        item.list.forEach((itemList: { label: string; value: string; isAnnual: number }) => {
          let mounthStr = +this.period === 2 ? '月' : ''
          // 是年化数据的，需要展示年化两个字
          if (+itemList.isAnnual === 1) {
            xAxisData.push(`${itemList.label}${mounthStr} ：(年化)`)
          } else {
            xAxisData.push(`${itemList.label}${mounthStr}`)
          }
          list.push(+itemList.value)
        })
      }

      seriesData.push({
        name: `${item.name}`,
        type: 'line',
        symbol: 'circle',
        smooth: true,
        lineStyle: { width: 4 },
        areaStyle: {},
        data: list
      })
    })

    this.xAxisData = xAxisData
    this.legendData = legendData
    this.seriesData = seriesData

    this.initEcharts()
  }

  // 渲染 echarts 视图
  private initEcharts() {
    let xAxisData = this.xAxisData
    let legendData = this.legendData
    let seriesData = this.seriesData
    let tooltipData: any = Object.assign(
      {
        trigger: 'axis',
        valueFormatter: function (value: string) {
          return value + ' 亿元'
        }
      },
      echartConfigure.tooltipBody
    )

    this.option = {
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: 'rgba(62, 238, 255, 0.8)'
        },
        {
          offset: 1,
          color: 'rgba(62, 238, 255, 0.1)'
        }
      ]),
      tooltip: tooltipData,
      legend: {
        show: false,
        data: legendData
      },
      grid: {
        left: '0%',
        right: '5%',
        bottom: '2%',
        top: '6%',
        containLabel: true
      },
      xAxis: {
        show: true,
        type: 'category',
        boundaryGap: false,
        data: xAxisData,
        axisLabel: {
          // 是年化数据的，需要展示年化两个字
          formatter: (item) => {
            let xData = item.split('：')
            return xData[0]
          },
          margin: 24,
          fontSize: 30,
          align: 'center',
          fontWeight: 'bold',
          fontFamily: echartConfigure.fontFamilyNumber
        },
        axisLine: {
          lineStyle: {
            color: '#5db0ea'
          }
        }
      },
      yAxis: {
        type: 'value',
        // name: '亿元',
        // splitNumber: 2,
        // nameTextStyle: {
        //   align: 'left',
        //   fontSize: 26
        // },
        axisLabel: {
          margin: 10,
          fontSize: 30,
          fontWeight: 'bold',
          formatter: '{value}',
          fontFamily: echartConfigure.fontFamilyNumber
        },
        axisLine: {
          lineStyle: {
            color: '#5db0ea'
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0, 103, 177, 0.4)'
          }
        }
      },
      series: seriesData,
      animationDuration: 1500,
      animationDurationUpdate: 1500
    }

    
    this.myChart && this.myChart.setOption(this.option, false)
    this.clickEchartsItem()
  }

  // 点击 eacharts 图表
  @Throttle
  private clickEchartsItem() {
    // this.myChart &&
    //   this.myChart.getZr().on('click', (event: any) => {
    //     this.visibleDetail = true
    //   })
  }

  // 触发背景闪烁效果
  private flickerHandle() {
    this.visibleFlicker = true
    setTimeout(() => {
      this.visibleFlicker = false
    }, 3000)
  }

  // 放大/缩小展示
  private fullScreenHandle(isFull: boolean) {
    this.isWrapperFull = isFull
  }
}
</script>

<style scoped lang="scss">
.cockpit-performance-wrap {
  position: relative;
  transition: 1s;
  display: flex;
  flex-flow: column;
  width: 100%;
  height: 880px;
  background: url('../../../cockpitcren/images/panel_bg1.png') no-repeat left top;
  background-size: 100% 100%;

  .echarts-box {
    flex: 1;
    height: 400px;
    #refEcharts {
      width: 100%;
      height: inherit;
    }
  }

  .exclude-box {
    position: absolute;
    right: 0;
    top: 130px;
    z-index: 100;
    font-size: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    span {
      margin-right: 20px;
    }

    ::v-deep .el-checkbox {
      transform: scale(2.1) translateX(-42px);
      .el-checkbox__label {
        color: #ccc;
        padding-left: 5px;
      }
      .el-checkbox__inner {
        background-color: #ccc;
        border-color: #ccc;
      }
      .el-checkbox__input.is-checked .el-checkbox__inner {
        background-color: #2dc4d3;
        border-color: #2dc4d3;
      }
    }

    ::v-deep .is-checked {
      .el-checkbox__label {
        color: #00ffff;
      }
    }
  }

  .tab-box {
    position: absolute;
    top: 52px;
    right: 22%;
    z-index: 2;
    font-size: 36px;
    span {
      display: inline-block;
      padding: 10px 0;
      margin-right: 30px;
      cursor: pointer;
    }
    .active {
      color: #63f1ff;
      border-bottom: 4px solid #63f1ff;
    }
  }

  .content-box {
    position: relative;
    z-index: 2;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 30px;
      p {
        margin: 0;
      }
      img {
        width: 14px;
        margin-right: 3px;
      }
      i {
        font-size: 22px;
      }
      .s {
        color: #ff6267;
      }
      .x {
        color: #00a92b;
      }
    }
    .title1 {
      padding: 0 0 !important;
    }

    .total {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      .mode {
        position: relative;
        flex: 1;
        padding: 10px 10px;
        margin-right: 20px;
        border-radius: 4px;
        text-align: center;
        cursor: pointer;
        border: 1px solid #062675;
        background: url('../../images/mode_bj.png') no-repeat left top;
        background-size: 100% 100%;
        &:hover {
          border: 1px solid #3eeeff;
          box-shadow: 0 0 20px #3eeeff inset;
        }
        &:nth-last-child(1) {
          margin-right: 0;
        }
        // .zzc {
        //   color: #ff368b;
        // }
        // .zfz {
        //   color: #ffcd36;
        // }
        .unit {
          color: #3eefff;
          font-size: 24px;
          margin-left: -17px;
          font-weight: normal;
          font-family: 'PangMenZhengDao';
        }
        h4 {
          font-size: 32px;
          margin: 0;
          margin-bottom: 2px;
        }
        span {
          color: #3eeeff;
          font-size: 44px;
          font-weight: normal;
          font-family: 'PangMenZhengDao';
        }
      }
      .active {
        border: 1px solid #3eeeff;
        box-shadow: 0 0 20px #3eeeff inset;
      }
    }

    .total2 {
      margin-bottom: 10px;
      .mode {
        margin-right: 0;
        border-right: 1px solid #062675;
        &:nth-last-child(1) {
          border-right: none;
        }
      }
    }
  }
}

.cockpit-wrapperFull-wrap {
  z-index: 1000;
  transform-origin: left top;
  transform: scale(1.7);
  border-radius: 20px;
  background: #072979;
  box-shadow: 0 0 40px #000;
}
</style>