/** 双 y 轴图表 样式1 */

<template>
  <div :id="chartId"
    style="width: 100%;height: 100%;" />
</template>

<script lang='ts'>
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import * as echarts from 'echarts'
import { colorSixList, echartConfigure } from '@/views/main/cockpitcren/baseData'
import { deepMerge } from '@/utils'

type EChartsOption = echarts.EChartsOption

@Component
export default class extends Vue {
  @Prop() private chartId!: string
  @Prop() private seriesData!: any
  @Prop() private xData!: any[]
  @Prop({ default: () => ({}) }) private readonly individuationOptions!: EChartsOption // 个性化options

  // 监听 seriesData 数据变化，更新视图
  @Watch('seriesData', { deep: true })
  changeSeriesData() {
    this.initEcharts()
  }

  private timer: any
  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById(this.chartId) as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    this.initEcharts()
  }

  // 初始化数据
  private initEcharts() {
    let textSize = echartConfigure.textSize
    let series = this.seriesData

    this.option = {
      color: colorSixList,
      legend: {
        show: true,
        top: '5%',
        data: ['年度', '同比'],
        textStyle: {
          fontSize: textSize,
          color: '#ffffff88',
          fontWeight: 'bold'
        }
      },
      grid: {
        top: '15%',
        left: '2%',
        right: '0%',
        bottom: '0%',
        containLabel: true
      },
      tooltip: Object.assign(echartConfigure.tooltip),
      xAxis: {
        type: 'category',
        axisLabel: {
          fontSize: textSize * 1,
          fontWeight: 'bold',
          hideOverlap: false,
          interval: 0,
          color: '#FFFFFF',
          opacity: 0.6,
          margin: 30
        },
        axisLine: {
          show: false
        },
        splitLine: {
          show: false
        },
        data: this.xData
      },
      axisPointer: {
        show: false
      },
      yAxis: [
        {
          show: true,
          type: 'value',
          name: '金额(万)',
          nameTextStyle: {
            fontWeight: 'bold',
            fontSize: textSize * 0.8,
            color: '#fff',
            opacity: 0.8,
            align: 'left',
            lineHeight: textSize * 1.6
          },
          axisLabel: {
            fontWeight: 'bold',
            fontSize: textSize * 0.8,
            color: '#FFFFFF',
            opacity: 0.6
          },
          axisLine: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: ['rgba(0, 103, 177, 0.7)']
            }
          }
        },
        {
          show: true,
          type: 'value',
          name: '同比(%)',
          nameTextStyle: {
            fontWeight: 'bold',
            fontSize: textSize * 0.8,
            color: '#fff',
            opacity: 0.8,
            align: 'right',
            lineHeight: textSize * 1.6
          },
          axisLabel: {
            fontWeight: 'bold',
            fontSize: textSize * 0.8,
            color: '#FFFFFF',
            opacity: 0.6
          },
          axisLine: {
            show: false
          },
          splitLine: {
            show: false
          }
        }
      ],
      series: [
        {
          selectedMode: 'single',
          yAxisIndex: 0,
          name: '年度',
          label: {
            show: false,
            position: 'top',
            color: 'RGBA(40, 177, 255, 1)',
            fontSize: textSize * 1.4,
            fontWeight: 'bold',
            fontFamily: echartConfigure.fontFamilyNumber
          },
          itemStyle: {
            borderRadius: [textSize * 0.7, textSize * 0.7, 0, 0],
            shadowColor: '#E6B60766',
            shadowBlur: 30
          },

          showBackground: false,
          backgroundStyle: {
            color: '#081C5866',
            borderRadius: [textSize * 0.7, textSize * 0.7, 0, 0]
          },
          barWidth: textSize * 2,
          data: series.data1,
          type: 'bar'
        },
        {
          type: 'line',
          symbol: 'circle',
          name: '同比',
          smooth: true,
          yAxisIndex: 1,
          lineStyle: {
            cap: 'round',
            width: textSize * 0.4
          },
          markPoint: {
            symbol: 'none',
            data: [
              {
                name: 'd',
                type: 'max',
                // valueDim: 'x',
                symbol: 'circle',
                coord: [this.seriesData.data2.length, this.seriesData.data2[this.seriesData.data2.length - 1]],
                value: `${this.seriesData.data2[this.seriesData.data2.length - 1]}`,
                symbolSize: 15,
                itemStyle: {
                  color: '#fff'
                },
                label: {
                  color: '#fff',
                  fontSize: 40,
                  position: 'insideBottomRight'
                }
              }
            ]
          },
          data: series.data2
        }
      ]
    }

    let resOptions = deepMerge(this.option, this.individuationOptions)

    this.myChart && this.myChart.setOption && this.myChart.setOption(resOptions ? resOptions : this.option)
    this.change()
  }

  private change() {
    if (this.timer) {
      clearInterval(this.timer)
    }

    this.timer = setInterval(() => {
      let index = Math.max(Math.floor(Math.random() * 4), 0)
      this.myChart &&
        this.myChart.dispatchAction({
          type: 'select',
          seriesIndex: 0,
          dataIndex: index
        })
    }, 5000)
  }

  destroyed() {
    clearInterval(this.timer)
  }
}
</script>

<style scoped lang="scss">
#ProjectAmountDistribution {
  width: 100%;
  height: 100%;
}
</style>