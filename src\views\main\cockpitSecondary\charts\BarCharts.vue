/** 通用柱状图 */

<template>
  <div :id="chartId"
    style="width: 100%;height: 100%;" />
</template>

<script lang='ts'>
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import * as echarts from 'echarts'
import { deepMerge } from '@/utils'
import { colorSixList, echartConfigure } from '../baseData'

type EChartsOption = echarts.EChartsOption

@Component
export default class extends Vue {
  @Prop() private chartId!: string
  @Prop() private seriesData!: any[]
  @Prop() private xData!: any[]
  @Prop({ default: () => ({}) }) private readonly individuationOptions!: EChartsOption // 个性化options

  // 监听 seriesData 数据变化，更新视图
  @Watch('seriesData', { deep: true })
  changeSeriesData() {
    this.initEcharts()
  }

  private timer: any
  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById(this.chartId) as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    this.initEcharts()
  }

  // 初始化数据
  private initEcharts() {
    let textSize = echartConfigure.textSize
    this.option = {
      color: colorSixList,
      legend: {
        show: true
      },
      grid: {
        left: '10%',
        right: '5%',
        top: '15%',
        bottom: '3%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 103, 177, 1)',
        borderColor: 'rgba(0, 103, 177, 1)',
        textStyle: {
          color: '#fff',
          fontSize: textSize
        }
      },
      xAxis: {
        type: 'category',
        axisLabel: {
          fontSize: textSize * 1.2,
          fontWeight: 'bold',
          hideOverlap: false,
          interval: 0,
          color: '#5db0ea'
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(67, 154, 255, 1)'
          }
        },
        data: this.xData
      },
      axisPointer: {
        show: false
      },
      yAxis: {
        show: true,
        type: 'value',
        scale: true,
        name: '金额（亿元）',
        nameTextStyle: {
          fontWeight: 'bold',
          color: '#5db0ea',
          fontSize: textSize * 1.2,
          align: 'center'
        },
        axisLabel: {
          fontSize: textSize * 1,
          fontWeight: 'bold',
          color: '#5db0ea'
        },
        axisLine: {
          lineStyle: {
            color: 'RGBA(147, 148, 149, 1)'
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0, 103, 177, 0.4)'
          }
        }
      },
      series: [
        {
          selectedMode: 'single',
          label: {
            show: true,
            position: 'top',
            color: 'RGBA(40, 177, 255, 1)',
            fontSize: textSize * 1.6,
            fontWeight: 'bold',
            fontFamily: echartConfigure.fontFamilyNumber
          },
          itemStyle: {
            borderRadius: [textSize * 1, textSize * 1, 0, 0],
            shadowColor: '#E6B60766',
            shadowBlur: 30
          },
          emphasis: {
            itemStyle: {
              shadowColor: 'RGBA(233, 60, 167, 0.6)',
              shadowBlur: 30
            },
            label: {
              fontSize: textSize * 1.6,
              color: 'RGBA(233, 60, 167, 1)'
            }
          },
          select: {
            itemStyle: {
              shadowColor: 'RGBA(233, 60, 167, 0.6)',
              shadowBlur: 30
            },
            label: {
              fontSize: textSize * 1.6,
              color: 'RGBA(233, 60, 167, 1)'
            }
          },
          showBackground: false,
          backgroundStyle: {
            borderRadius: [textSize * 1, textSize * 1, 0, 0]
          },
          barWidth: textSize * 2,
          data: this.seriesData,
          type: 'bar'
        }
      ]
    }

    let resOptions = deepMerge(this.option, this.individuationOptions)

    this.myChart && this.myChart.setOption && this.myChart.setOption(resOptions ? resOptions : this.option)
    this.change()
  }

  private change() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    this.timer = setInterval(() => {
      let index = Math.max(Math.floor(Math.random() * 4), 0)

      this.myChart &&
        this.myChart.dispatchAction({
          type: 'select',
          seriesIndex: 0,
          dataIndex: index
        })
    }, 5000)
  }

  destroyed() {
    clearInterval(this.timer)
  }
}
</script>

<style scoped lang="scss">
#ProjectAmountDistribution {
  width: 100%;
  height: 100%;
}
</style>