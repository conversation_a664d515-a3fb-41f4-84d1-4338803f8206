<template>
  <section class="assets-map-wrap"
    :class="{'assets-map-full-wrap':isFullScreen}"
    :style="`height: ${height}px;`">
    <!-- 地图组件 -->
    <iframe :src="src"
      ref="refIframe"
      width="100%"
      height="100%"
      frameborder="0"
      allowfullscreen />

    <!-- 退出全景地图 -->
    <div v-if="isPanoramic"
      class="out-panoramic-box">
      <i class="icon"
        @click="outPanoramic">退出全景地图</i>
    </div>

    <!-- 地图大屏、退出按钮 -->
    <div class="assets-full-box"
      :class="{'assets-full-new-box':isFullScreen}"
      @click="isFullScreen = !isFullScreen">
      <i v-if="!isFullScreen"
        class="icon el-icon-full-screen" />
      <i v-else
        class="icon el-icon-s-unfold" />
    </div>

    <!-- 地图表单搜索按钮 -->
    <div v-if="!isPanoramic"
      class="assets-status-box">
      <div v-if="!isShowSearch"
        class="search-box"
        @click="isShowSearch = !isShowSearch">
        <i class="icon el-icon-search" />
      </div>
      <div v-else
        class="form-status-box">
        <el-button icon="el-icon-caret-left"
          class="caret"
          @click="isShowSearch = !isShowSearch" />
        <el-form ref="form"
          :model="assetForm"
          label-width="120px"
          label-position="left"
          class="rule-form">
          <el-form-item label-width="0">
            <el-input v-model.trim="assetForm.keyword"
              placeholder="资产名称"
              class="mode-input">
              <!-- <el-button slot="append"
                icon="search el-icon-search" /> -->
            </el-input>
          </el-form-item>
          <el-form-item label="资产类型">
            <el-checkbox-group v-model="assetForm.assetTypes">
              <el-checkbox :label="1">房产</el-checkbox>
              <el-checkbox :label="2">土地</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="资产用途">
            <el-checkbox-group v-model="assetForm.purposes">
              <el-checkbox v-for="(item, index) of assetPurposeBizData"
                :key="index"
                :label="item.value">{{item.label}}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <div class="footer-btn">
            <el-button type="primary"
              @click="assetSubmit">搜 索</el-button>
            <el-button @click="clearForm">清 空</el-button>
          </div>
        </el-form>
      </div>
    </div>

    <!-- 资产详情弹窗 -->
    <AssetsDetail v-if="visibleDetail"
      :visible.sync="visibleDetail"
      :assetInfo="assetInfo" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { BusinessModule } from '@/store/modules/businessDict'
import { getDictBizData } from '@/api/public'
import AssetsDetail from '@/views/main/map/components/AssetsDetail.vue'

@Component({
  components: {
    AssetsDetail
  }
})
export default class extends Vue {
  @Prop({ default: '400px' }) private height?: number // 地图高度
  @Prop({ default: 0 }) private companyCode?: number // 各集团code

  private visibleDetail = false
  private isShowSearch = false
  private isFullScreen = false
  private isPanoramic = false
  private src = ''
  private assetPurposeBizData: any[] = []
  private assetInfo = {}
  private assetForm = {
    keyword: '',
    assetTypes: [1, 2],
    purposes: []
  }

  // 监听 v-model 绑定值变化，然后重新赋值
  @Watch('companyCode', { immediate: true, deep: true })
  changeCompanyCode() {
    let { keyword, assetTypes, purposes } = this.assetForm

    this.outPanoramic()

    this.src = `${window.location.origin}/monitor/#/assetDistributionMap?companyCode=${this.companyCode}&keyword=${keyword}&assetTypes=${assetTypes}&purposes=${purposes}`
  }

  // 获取字典数据
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }

  // 数据初始化
  private created() {
    this.getAssetPurpose()
  }

  // 组件初始化
  private mounted() {
    window.addEventListener('message', (e: any) => {
      let res = e.data
      switch (res.code) {
        // 点击地图信息弹窗中的“查看详情”
        case 'Ichnography':
          this.visibleDetail = true
          this.assetInfo = res.data
          break
        // 点击地图信息弹窗中的“进入全景地图”
        case 'InPanorama':
          this.isPanoramic = true
          break
        // “退出全景地图”
        case 'OutPanorama':
          this.isPanoramic = false
          break
      }
    })
  }

  // 退出全景地图
  private outPanoramic() {
    let ref: any = this.$refs.refIframe

    if (ref && ref.contentWindow) {
      ref.contentWindow.postMessage({ code: 'IsPanoramicHide' }, '*')
    }
  }

  // 资产用途数据
  private async getAssetPurpose() {
    let { data } = await getDictBizData({
      code: 'asset_purpose'
    })

    let list: any[] = []
    if (Array.isArray(data)) {
      data.forEach((item) => {
        list.push({
          label: item.dictValue,
          value: item.dictKey
        })
      })
    }

    this.assetPurposeBizData = list
  }

  // 表单清空
  private clearForm() {
    this.assetForm = {
      keyword: '',
      assetTypes: [1, 2],
      purposes: []
    }
    this.src = `${window.location.origin}/monitor/#/assetDistributionMap?companyCode=${this.companyCode}`
  }

  // 表单搜索提交
  private assetSubmit() {
    this.isShowSearch = false
    let { keyword, assetTypes, purposes } = this.assetForm
    this.src = `${window.location.origin}/monitor/#/assetDistributionMap?companyCode=${this.companyCode}&keyword=${keyword}&assetTypes=${assetTypes}&purposes=${purposes}`
  }
}
</script>

<style scoped lang="scss">
// 点击地图大屏模式下的样式
.assets-map-full-wrap {
  overflow: initial !important;
  .assets-full-new-box {
    right: -610px !important;
    top: -271px !important;
  }
  .assets-status-box {
    left: -610px !important;
    top: -271px !important;
  }
  .out-panoramic-box {
    left: -610px !important;
    top: -271px !important;
  }
}

// 正常样式
.assets-map-wrap {
  position: relative;
  z-index: 100;
  overflow: hidden;
  background: rgba($color: #eee, $alpha: 0.2);
  iframe {
    transform: scale(1.8);
    box-shadow: 0 0 80px #000;
  }

  .assets-full-box {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 100;
    padding: 20px;
    background: rgba($color: #0f3079, $alpha: 0.4);
    box-shadow: inset 0 0 10px 10px #3b64b8;
    border-radius: 4px;
    cursor: pointer;
    &:hover {
      background: rgba($color: #0f3079, $alpha: 1);
    }
    .icon {
      font-size: 30px;
    }
  }

  .assets-status-box {
    position: absolute;
    left: 10px;
    top: 10px;
    z-index: 100;
    .search-box {
      padding: 20px;
      background: rgba($color: #0f3079, $alpha: 0.4);
      box-shadow: inset 0 0 10px 10px #3b64b8;
      border-radius: 4px;
      cursor: pointer;
      &:hover {
        background: rgba($color: #0f3079, $alpha: 1);
      }
      .icon {
        font-size: 30px;
      }
    }
    .form-status-box {
      position: relative;
      width: 380px;
      padding: 20px 60px 16px 20px;
      background: rgba($color: #0f3079, $alpha: 0.9);
      box-shadow: inset 0 0 10px 10px #3b64b8;
      border-radius: 4px;
      .mode-input {
        width: 338px;
      }
      .caret {
        position: absolute;
        right: 20px;
        top: 21px;
        z-index: 2;
        width: 75px;
        height: 58px;
        color: #fff;
        font-size: 34px;
        background: rgba($color: #021e5d, $alpha: 0.8);
        border: 1px solid #021e5d;
        &:hover {
          background: rgba($color: #021e5d, $alpha: 1);
        }
      }
      .footer-btn {
        display: flex;
        align-items: center;
        button {
          width: 50%;
          font-size: 26px;
        }
      }
    }
    ::v-deep .el-form {
      width: 420px;
      input {
        color: #fff;
        height: 60px;
        font-size: 26px;
        background: rgba($color: #021e5d, $alpha: 0.8);
        border: none;
      }
      .el-form-item__label {
        color: #4a97f8;
        font-size: 26px;
        transform: translateY(-4px);
      }
      .el-radio__label {
        display: inline-block;
        width: 75px;
        color: #4a97f8;
        font-size: 26px;
      }
      .el-radio__inner {
        width: 30px;
        height: 30px;
        transform: translateY(-4px);
      }
      .el-checkbox__label {
        width: 75px;
        color: #4a97f8;
        font-size: 26px;
      }
      .el-checkbox__inner {
        width: 30px;
        height: 30px;
        transform: translateY(-4px);
      }
      .el-input-group__append {
        border: none;
        background: rgba($color: #021e5d, $alpha: 0.8);
        &:hover {
          background: rgba($color: #021e5d, $alpha: 1);
        }
        .search {
          font-size: 30px;
        }
      }
    }
    ::v-deep .el-checkbox {
      margin-bottom: 10px;
    }
  }

  .out-panoramic-box {
    position: absolute;
    left: 10px;
    top: 10px;
    z-index: 100;
    padding: 10px 20px;
    background: rgba($color: #0f3079, $alpha: 0.4);
    box-shadow: inset 0 0 4px 4px #3b64b8;
    border-radius: 4px;
    cursor: pointer;
    &:hover {
      background: rgba($color: #0f3079, $alpha: 1);
    }
    .icon {
      font-size: 26px;
    }
  }
}
</style>