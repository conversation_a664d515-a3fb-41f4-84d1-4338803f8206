/* 负债预警 */

<template>
  <section class="liability-warning-wrap">
    <CommonTitle :title="title"
      :isRotuerThrow="true"
      :routerObj="{
        bizAppCode: 'finance',
        detailsPath: '/wel/index?bizAppCode=finance&path=/business/finance/debtStatistics/search/stateEntriseSum'
      }" />

    <div class="halo" />

    <div class="pie-count-box">
      <h6 class="title">预警总数</h6>
      <p class="count">
        <span>{{totals}}</span>
        <i>个</i>
      </p>
    </div>

    <div v-loading="loading"
      class="content-box"
      ref="refEcharts" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { echartConfigure, colorSixList } from '@/views/main/cockpitcren/baseData'
import { Loading } from '@/decorators'
import { liabilitiesWarn } from '@/api/cockpit'
import * as echarts from 'echarts'
import CountTo from 'vue-count-to'
import CommonTitle from '@/views/main/cockpitSecondary/components/CommonTitle.vue'

type EChartsOption = echarts.EChartsOption

@Component({
  components: {
    CountTo,
    CommonTitle
  }
})
export default class extends Vue {
  @Prop() private title!: string // 标题
  @Prop() private year!: string // 年份
  @Prop() private orgCode!: string // 集团
  @Prop() private moon?: string // 月份

  private loading = false
  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private seriesData: any[] = []
  private option: EChartsOption = {}
  private totals = ''

  // 数据变化，渲染视图
  @Watch('orgCode', { deep: true })
  @Watch('year', { deep: true })
  @Watch('moon', { deep: true })
  private changeEchartsData() {
    this.initData()
  }

  // 组件初始化
  private mounted() {
    this.chartDom = this.$refs.refEcharts as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await liabilitiesWarn({
      year: this.year,
      month: this.moon,
      orgCode: this.orgCode
    })

    // 组装echarts数据
    this.totals = data.value
    this.seriesData = data.list

    this.initEcharts()
  }

  // 初始化数据
  private initEcharts() {
    let textSize = echartConfigure.textSize
    let seriesData = this.seriesData

    this.option = {
      color: colorSixList,
      tooltip: {
        trigger: 'item',
        backgroundColor: '#00000000',
        shadowColor: '#00000000',
        borderWidth: 0,
        formatter: (params: any) => {
          let data = params.data

          return `
            <div style="font-size:40px; line-height: 70px; color: #fff; border-radius:10px;border:2px solid #0C3EB6; padding: 20px; display: flex;flex-direction: column; background: linear-gradient(0deg, rgba(0, 103, 177, 1), rgba(0, 103, 177, 1));">
              <div style="font-weight:bold;">${data.name}</div>
              <div style="display: flex; justify-content: space-between;">
                <span style="padding-right: 40px;">数量</span>
                <span>${data.value}个</span>
              </div>
            </div>
          `
        }
      },
      grid: {
        left: '0%',
        right: '0%',
        top: '0%',
        bottom: '0%',
        containLabel: true
      },
      legend: {
        show: false
      },
      series: [
        {
          name: '',
          type: 'pie',
          radius: ['30%', '50%'],
          label: {
            show: true,
            alignTo: 'edge',
            color: '#fff',
            fontSize: textSize / 1.2,
            minMargin: 30,
            edgeDistance: 10,
            lineHeight: 11,
            formatter: function (params: any) {
              let names = params.data.name
              let values = params.data.value

              let dom = `{name|${names}}\n{value|${values}}{unit|个}`

              return dom
            },
            rich: {
              name: {
                fontSize: 30,
                padding: [0, 0, 50, 0],
                fontWeight: 'bold'
              },
              value: {
                color: '#40EEFF',
                fontSize: 40,
                fontWeight: 'normal',
                fontFamily: 'PangMenZhengDao'
              },
              unit: {
                color: '#40EEFF',
                fontSize: 26,
                padding: [10, 0, 0, 4],
                fontFamily: 'PangMenZhengDao'
              }
            }
          },
          labelLine: {
            lineStyle: {
              width: 3
            }
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          data: seriesData
        },
        {
          name: '',
          type: 'pie',
          radius: ['30%', '50%'],
          data: seriesData,
          label: {
            show: true,
            position: 'inside',
            color: '#fff',
            fontSize: 24,
            fontWeight: 'normal',
            fontFamily: 'PangMenZhengDao',
            formatter: (params) => {
              return params.percent ? `${params.percent}%` : ''
            }
          }
        }
      ]
    }

    this.myChart && this.myChart.setOption(this.option, true)
  }
}
</script>

<style scoped lang="scss">
.liability-warning-wrap {
  position: relative;
  height: 100%;
  @keyframes move {
    0% {
      transform: translate(-50%, -50%) rotateZ(0deg);
    }
    50% {
      transform: translate(-50%, -50%) rotateZ(360deg);
    }
    100% {
      transform: translate(-50%, -50%) rotateZ(0deg);
    }
  }
  .content-box {
    position: relative;
    z-index: 10;
    width: 100%;
    height: 89%;
    box-sizing: border-box;
    transform: translateY(-34px);
  }
  .pie-count-box {
    position: absolute;
    top: 366px;
    width: 100%;
    text-align: center;
    h6,
    p {
      margin: 0;
    }

    .title {
      font-size: 38px;
    }
    .count {
      color: #40eeff;
      span {
        font-size: 56px;
        font-weight: normal;
        font-family: 'PangMenZhengDao';
      }
      i {
        margin-left: 4px;
        font-weight: normal;
        font-family: 'PangMenZhengDao';
      }
    }
  }

  .halo {
    position: absolute;
    z-index: 1;
    top: 402px;
    left: 50%;
    width: 400px;
    height: 400px;
    transform: translate(-50%, -50%);
    background: url('../../../images/halo.png');
    background-size: 100% 100%;
    will-change: width, height, transform;
    //animation: move 20s linear forwards infinite;
  }
}
</style>


