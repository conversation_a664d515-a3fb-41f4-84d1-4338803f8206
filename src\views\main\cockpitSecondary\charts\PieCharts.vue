/** 饼图 */

<template>
  <div class="pie-chart">
    <div :id="chartId"
      style=" width: 100%;height: 100%;" />
    <div class="halo"
      :style="{
      width: `${ringWidth}px`,
      height: `${ringWidth}px`
    }" />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import { companyList, echartConfigure } from '@/views/main/cockpitSecondary/baseData'
import * as echarts from 'echarts'
import { deepMerge } from '@/utils'

type EChartsOption = echarts.EChartsOption
type TitleComponentOption = echarts.TitleComponentOption

@Component
export default class extends Vue {
  @Prop({ required: true }) private readonly chartId!: string

  @Prop({ default: 22 }) private textSize!: number
  @Prop({ default: '' }) private unitStr!: string
  @Prop({ default: '' }) private chartName?: string
  @Prop({ default: 280 }) private ringWidth!: string
  @Prop({ default: () => [] }) private seriesData!: any[]
  @Prop() private titleOptions!: TitleComponentOption | TitleComponentOption[]
  @Prop({ default: () => ({}) }) private labelOptions!: echarts.PieSeriesOption['label']
  @Prop({ default: () => ({}) }) private readonly individuationOptions!: EChartsOption // 个性化options

  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}

  get sum() {
    return this.seriesData.reduce((sum: any, currentValue: any) => {
      return (sum += currentValue.value || 0)
    }, 0)
  }

  // 监听 seriesData 数据变化，更新视图
  @Watch('seriesData', { deep: true })
  changeSeriesData() {
    this.initEcharts()
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById(this.chartId) as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)
    this.initEcharts()
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 初始化数据
  private initEcharts() {
    let textSize = this.textSize
    let tooltipData: any = Object.assign(
      {
        trigger: 'item'
      },
      echartConfigure.tooltip
    )

    this.option = {
      color: ['#E6B607', '#EF702E', '#35AAF6', '#00AEA5', '#EC342F', '#2D5AE5', '#35AAF6'],
      title: this.titleOptions,
      legend: {
        show: false
      },
      tooltip: tooltipData,
      series: [
        {
          type: 'pie',
          radius: ['0%', '60%'],
          avoidLabelOverlap: true,
          // stillShowZeroSum: false,
          name: this.chartName,
          selectedMode: 'single',
          label: Object.assign(
            {
              show: true,
              position: 'outside',
              alignTo: 'labelLine',
              fontSize: 18,
              color: '#5db0ea',
              formatter: ({ data }: { data: any }) => {
                return `{a|${data.value}${this.unitStr}} \n {hr|}\n {b|${data.name}：${+((data.value / this.sum) * 100).toFixed(1)}%}`
              },
              rich: {
                a: {
                  fontSize: 48,
                  color: '#fff',
                  fontWeight: 'bold'
                },
                b: {
                  fontSize: 28,
                  color: '#20DCF9',
                  fontWeight: 'bold'
                },
                hr: {
                  borderColor: '#8C8D8E',
                  width: '100%',
                  borderWidth: 1,
                  height: 0
                }
              }
            },
            this.labelOptions
          ),
          itemStyle: {
            borderRadius: 0,
            borderWidth: 10
          },
          labelLine: {
            length: 20,
            length2: 50,
            maxSurfaceAngle: 80,
            smooth: 0.2,
            lineStyle: {
              width: 3
            }
          },
          data: this.seriesData
        }
      ]
    }

    // 合并传入 options
    let resOptions = deepMerge(this.option, this.individuationOptions)

    this.myChart && this.myChart.setOption(resOptions ? resOptions : this.option)
  }
}
</script>

<style scoped lang="scss">
.pie-chart {
  width: 100%;
  height: 100%;
  .halo {
    position: absolute;
    z-index: -1;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: url('../images/halo.png');
    background-size: 100% 100%;
    will-change: width, height, transform;
    //animation: move 20s linear forwards infinite;
  }

  @keyframes move {
    0% {
      transform: translate(-50%, -50%) rotateZ(0deg);
    }
    50% {
      transform: translate(-50%, -50%) rotateZ(360deg);
    }
    100% {
      transform: translate(-50%, -50%) rotateZ(0deg);
    }
  }
}
</style>