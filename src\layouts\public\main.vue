<template>
  <el-main class="main-wrap"
    :class="getMainBg">

    <transition appear
      name="fade-transform"
      mode="out-in">
      <router-view class="el-main-body" />
    </transition>
  </el-main>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { PermissionModule } from '@/store/modules/permissionDict'

@Component({
  name: 'Main'
})
export default class Main extends Vue {
  // 根据路由单独设置某些模块
  get getMainBg() {
    if (this.$route.path.includes('cockpit') || this.$route.path.includes('moduledetail') || this.$route.path.includes('companydetail')) {
      return 'cockpit'
    } else {
      return ''
    }
  }

  // 数据初始化
  private created() {
    this.getPermissionData()
  }

  // 获取权限字典数据
  private async getPermissionData() {
    await PermissionModule.getPermissionDict()
  }
}
</script>

<style scoped lang="scss">
.main-wrap {
  padding: 10px;
  box-sizing: border-box;
  background: #f0f2f5;
  overflow-y: auto;
}

.cockpit {
  padding: 0 !important;
  background: #010d14 !important;
}
</style>
