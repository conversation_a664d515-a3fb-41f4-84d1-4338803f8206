<template>
  <section v-if="companyData.length"
    class="project-summary-wrap">
    <div class="summary-box">
      <div v-for="(item, index) of companyData"
        :key="index"
        :class="`mode${index+1}`"
        class="mode">
        <i class="icon"
          :class="[
          {'el-icon-files':index==0},
          {'el-icon-document-copy':index==1},
          {'el-icon-office-building':index==2},
          {'el-icon-receiving':index==3}
        ]" />
        <p class="info">
          <strong>{{item.total}}{{item.unit}}</strong>
          <span>{{item.name}}</span>
        </p>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { projectOverview } from '@/api/projectInvestment'

@Component({})
export default class extends Vue {
  private loading = false
  private companyData: any[] = []

  // 监听搜索条件改变
  @Watch('orgCode', { deep: true })
  @Watch('year', { deep: true })
  private watchFormData() {
    this.initData()
  }

  // 组件初始化
  private mounted() {
    this.initData()
  }

  // 获取数据
  @Loading('loading')
  private async initData() {
    let { data } = await projectOverview({})
    this.companyData = data || []
  }
}
</script>

<style scoped lang="scss">
.project-summary-wrap {
  position: relative;
  background: #fff;
  padding: 0 0 10px;
  margin-bottom: 8px;
  h4,
  h6,
  p {
    margin: 0;
  }
  .summary-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .mode1 {
      background: rgba($color: #67c23a, $alpha: 0.8);
    }
    .mode2 {
      background: rgba($color: #e6a23c, $alpha: 0.8);
    }
    .mode3 {
      background: rgba($color: #fb3f3f, $alpha: 0.8);
    }
    .mode4 {
      background: rgba($color: #409eff, $alpha: 0.8);
    }

    .mode {
      flex: 1;
      display: flex;
      align-items: end;
      justify-content: space-between;
      min-width: 183px;
      margin-right: 50px;
      padding: 10px;
      color: #fff;
      border-radius: 4px;
      box-sizing: border-box;
      &:nth-last-child(1) {
        margin-right: 0;
      }
      .icon {
        font-size: 40px;
      }
      .info {
        display: flex;
        align-items: center;
        flex-direction: column;
        font-size: 14px;
        strong {
          font-size: 18px;
          font-weight: 500;
        }
        span {
          font-size: 13px;
        }
      }
    }
  }

  .til {
    display: flex;
    align-items: center;
    font-weight: normal;
    margin: 20px 0;
    transform: translateY(20px);
    color: rgba(245, 108, 108, 0.8);
    i {
      font-size: 20px;
      margin-right: 4px;
    }
    span {
      font-size: 17px;
    }
  }

  .pre-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .mode {
      flex: 1;
      min-width: 183px;
      height: 240px;
      margin-right: 50px;
      box-sizing: border-box;
      &:nth-last-child(1) {
        margin-right: 0;
      }
    }
  }
}
</style>