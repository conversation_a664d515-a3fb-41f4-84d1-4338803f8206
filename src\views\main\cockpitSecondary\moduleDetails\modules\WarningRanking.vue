/* 财务指标 */

<template>
  <section class="financial-indicator">
    <div class="financial-indicator__tabs">
      <!-- <div v-for="(item, index) in tabsList"

        :key="index"
        :class="['financial-indicator__item', index === currentIndex && 'financial-indicator__item--checked']"
        @click="onItemClick(index, item.label)">
        <div class="item__top">
          <CountTo :startVal='0'
            :endVal='+item.value'
            :duration='2000' />
        </div>
        <div class="item__bottom">
          {{ item.label }}
        </div>
        <div
          :class="['item__percent', item.percent > 0 && 'item__percent--up', item.percent < 0 && 'item__percent--down']">
          <img class="icon"
            v-if="item.percent > 0"
            src="../../../images/thows.png" />
          <img class="icon"
            v-if="item.percent < 0"
            src="../../../images/thowx.png" />
          <span>{{ item.percent }}%</span>
        </div>
      </div> -->

      <CommonModuleWrapper title="综合指标"
        :hasTitle="false"
        :indexList="tabsList"
        :height="150"
        @itemClick="onItemClick"
        componentsName="TabsList" />
    </div>
    <div class="financial-indicator__content">
      <el-row :span="24"
        :gutter="24">
        <el-col :span="7">
          <CommonModuleWrapper v-for="company in leftCompanyList"
            :key="company.id"
            :companyId="company.id"
            :title="company.label"
            :height="320"
            :xData="yearList"
            :seriesData="data"
            :loading="loading"
            componentsName="LineChart" />
        </el-col>
        <el-col :span="10">
          <CommonModuleWrapper :title="getRankingTitle"
            :height="600"
            :loading="loading"
            componentsName="RankingChart" />
        </el-col>
        <el-col :span="7">
          <CommonModuleWrapper v-for="company in rightCompanyList"
            :key="company.id"
            :companyId="company.id"
            :title="company.label"
            :height="320"
            :xData="yearList"
            :seriesData="data"
            :loading="loading"
            componentsName="LineChart" />
        </el-col>
      </el-row>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import CountTo from 'vue-count-to'
import CommonModuleWrapper from '@/views/main/cockpitSecondary/components/CommonModuleWrapper.vue'
import { deepClone } from '@/utils'
import { FinancialIndicatorLineData } from '@/views/main/cockpitSecondary/baseData'

export interface TabItem {
  label: string
  value: string | number
  percent: number
  unit: string
}

@Component({
  components: {
    CountTo,
    CommonModuleWrapper
  }
})
export default class extends Vue {
  private loading = false
  private data = deepClone(FinancialIndicatorLineData)

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  private leftCompanyList = [
    {
      label: '城投集团',
      id: 'CT'
    },
    {
      label: '金投集团',
      id: 'KT'
    },
    {
      label: '水务集团',
      id: 'SW'
    }
  ]

  private rightCompanyList = [
    {
      label: '交投集团',
      id: 'JT'
    },
    {
      label: '轨道集团',
      id: 'GD'
    },
    {
      label: '社发集团',
      id: 'SF'
    }
  ]

  get yearList() {
    let year = +this.getMomentTime()
    return [year - 6, year - 5, year - 4, year - 3, year - 2, year - 1, year]
  }

  private companyList = [
    {
      code: '1',
      name: '城投集团'
    },
    {
      code: '4',
      name: '交投集团'
    },
    {
      code: '2',
      name: '金投集团'
    },
    {
      code: '5',
      name: '轨道集团'
    },
    {
      code: '3',
      name: '水务集团'
    },
    {
      code: '6',
      name: '社发集团'
    }
  ]

  private currentIndex = 0
  private currentLabel = '资产总额'
  private tabsList: TabItem[] = [
    {
      label: '资产总额',
      value: '1200000',
      percent: 1.9,
      unit: '元'
    },
    {
      label: '资产净额',
      value: '1220000',
      percent: 1.9,
      unit: '元'
    },
    {
      label: '营业收入',
      value: '12312512',
      percent: 21.7,
      unit: '处'
    },
    {
      label: '利润总额',
      value: '*********',
      percent: 31.6,
      unit: '元'
    },
    {
      label: '有效投资',
      value: '13430000',
      percent: 1.9,
      unit: '元'
    },
    {
      label: '净资产收益率',
      value: '13430000',
      percent: 1.9,
      unit: '元'
    },
    {
      label: '国有资产保值增值率',
      value: '13430000',
      percent: 1.9,
      unit: '元'
    }
  ]

  // 当前年月
  get getCurrentDate() {
    let date = new Date()
    return `截止到${date.getFullYear()}年${date.getMonth() + 1}月`
  }

  get getRankingTitle() {
    return `${this.currentLabel}排行情况`
  }

  created() {
    this.loading = true
    setTimeout(() => {
      this.loading = false
    }, 1000)
  }

  private onItemClick(index: any, data: { label: string }) {
    this.currentIndex = index
    this.currentLabel = data.label
    this.loading = true
    setTimeout(() => {
      this.loading = false
    }, 1000)
  }
}
</script>

<style scoped lang="scss">
</style>




