/* 资产运营 api */

import request from './request'
import { RemoteResponse } from '@/types'

// 资产分布
export const assetsXxxxx = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/xxxxxx', params)
}
// ******************资产地图***********************
// 资产分布
export const assetsInfoMap = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/info/map', params)
}
// 资产概述
export const assetsInfoProfile = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/info/profile', params)
}

// ******************资产运营***********************
/* 资产详情 */
export const assetsDetail = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/info/detail', params,)
}

/* 资产详情：预警过来的 */
export const assetsDetailByCode = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/info/detail-by-code', params,)
}

/* 资产租赁可视化1 */
export const assetsInfoVisual = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/info/visual', params,)
}

/* 资产租赁可视化2 */
export const assetsInfoVisualEwm = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/info/visual-by-code', params,)
}

/* 资产明细分页 */
export const assetsListPage = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/item/page', params,)
}

/* 资产分页列表 */
export const assetsInfoPage = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/info/page', params,)
}

/* 资产明细详情 */
export const assetsItemDetail = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/item/detail', params,)
}

/* 资产导出 */
export const assetsInfoExport = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/info/export', params, {
    responseType: 'blob'
  })
}

//  ******************合同管理***********************
/**
 * 合同详情
 */
export const contractDetail = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/contract/detail', params,)
}

// ***********************资产概况****************************
/**
 * 资产总况
 */
export const assetsGeneral = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/statistics/general', params,)
}

/**
 * 经营性资产（仅包含经营性资产统计数据）
 */
export const assetsOperationalGeneral = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/statistics/operational', params,)
}

/**
 * 资产收缴情况
 */
export const assetsReceivablesOverYear = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/statistics/receivables-over-year', params,)
}

/**
 * 历年资产经营趋势
 */
export const assetsOverYear = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/statistics/trend-over-year', params,)
}

/**
 * 资产挂牌情况
 */
export const assetsTradeListing = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/statistics/trade-listing', params,)
}

/**
 * 资产收益率
 */
export const assetsYieldOverYear = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/statistics/yield-over-year', params,)
}

/**
 * 合同到期
 */
export const assetsContractExpire = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/statistics/contract-expire', params,)
}
// ************************************资产标的交易申报 *********************************
/**
 * 搜索资产
 */
export const searchAst = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/contract/bill-list', params,)
}
/**
 * 搜索资产
 */
export const targetDetail = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/subject/lease/detail', params,)
}
/**
 * 搜索企业列表
 */
export const SearchBasicList = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ent/tyc/basic-list', params,)
}
/**
 * 搜索资产列表
 */
export const SearchAstList = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/item/list', params,)
}
/**
 *  批量新增标的
 */
export const AddTarget = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/subject/lease/savebatch', params,)
}
/**
 *  重新发起之前调用详情
 */
export const ReapplyDetail = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/subject/lease/reapply/detail', params,)
}
/**
 *  重新发起--新增
 */
export const Addreapply = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/subject/lease/reapply', params,)
}
/**
 *  审核
 */
export const LeaseAudit = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/subject/lease/audit', params,)
}
/**
 *  草稿箱
 */
export const getHistoryInfo = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/subject/lease/lessor/info', params,)
}
//********************************************** */
/**
 * 商圈板块列表
 */
export const assetsRegionZoneList = (params: any): Promise<RemoteResponse> => {
  return request('/fht-admin/region/zone-list', params, {
    method: 'GET'
  })
}

/**
 * 资产租金趋势（按商圈统计）
 */
export const assetsRentTrendsZone = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/statistics/rent-trends-zone', params, {
    method: 'POST'
  })
}

/**
 * 资产租金趋势（按年度统计）
 */
export const assetsRentTrendsYear = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/statistics/rent-trends-year', params, {
    method: 'POST'
  })
}

/**
 * 资产租金趋势（按年度统计）
 */
export const mapSuggestion = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/info/suggestion', params, {
    method: 'GET'
  })
}
/**
 * 快捷菜单
 */
export const entryTreekeys = (params: any): Promise<RemoteResponse> => {
  return request('/fht-admin/entrymenu/entry-tree-keys', { ...params, bizTag: "asset" }, {
    method: 'GET'
  })
}
/**
 * 快捷菜单-list
 */
export const entrymenuList = (params: any): Promise<RemoteResponse> => {
  return request('/fht-admin/entrymenu/list', { ...params, bizTag: "asset" }, {
    method: 'GET'
  })
}
/**
 * 快捷菜单 更改
 */
export const updateMenuGrant = (params: any): Promise<RemoteResponse> => {
  return request('/fht-admin/entrymenu/grant', { ...params, bizTag: "asset" }, {
    method: 'POST'
  })
}
/**
 * 快捷菜单 更改
 */
export const getAstSummary = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/summary', {}, {
    method: 'POST'
  })
}

/**
 * 事前备案-详情
 */
export const getFilingLeaseDetail = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/recordation/before/detail', {}, {
    method: 'POST'
  })
}
/* ----------------------------------- 报表 ----------------------------------- */
/**
 * 
 * @title name 超五年
 */
export const ExportOver5year = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/report/over5year/export', params, {
    responseType: 'blob'
  })
}
/**
 * 
 * @title name 超3月
 */
export const ExportFree3Month = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/report/free3Month/export', params, {
    responseType: 'blob'
  })
}
/**
 * 
 * @title name 超1月
 */
export const ExportFree1Month = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/report/unpaid-one-month/export', params, {
    responseType: 'blob'
  })
}
/**
 * 
 * @title name 未进场
 */
export const ExportOuttrans = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/report/out-trans/export', params, {
    responseType: 'blob'
  })
}
/**
 * 
 * @title name 未评估
 */
export const ExportNoteva = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/report/not-eva/export', params, {
    responseType: 'blob'
  })
}
/**
 * 
 * @title 汇总 
 */
export const reportSum = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/report/sum', params, {
    method: 'POST'
  })
}
/**
 * 
 * @title name 汇总导出
 */
export const reportSumExport = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/report/sum/export', params, {
    responseType: 'blob'
  })
}