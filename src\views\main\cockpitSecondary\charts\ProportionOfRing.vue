/** 环形比例图 */

<template>
  <div class="proportion-of-ring">
    <div :id="chartId"
      style=" width: 100%;height: 100%;" />
    <div v-if="ringWidth"
      class="halo"
      :style="{
      width: `${ringWidth}px`,
      height: `${ringWidth}px`
    }" />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import { echartConfigure, colorSixList } from '@/views/main/cockpitcren/baseData'
import { deepMerge } from '@/utils'
import * as echarts from 'echarts'

type EChartsOption = echarts.EChartsOption
type TitleComponentOption = echarts.TitleComponentOption
type PieSeriesOption = echarts.PieSeriesOption

@Component
export default class extends Vue {
  @Prop({ required: true }) private chartId!: string
  @Prop({ default: () => [] }) private seriesData!: any[]
  @Prop() private subtext!: string
  @Prop() private text!: string
  @Prop({ default: () => [] }) private titleList!: TitleComponentOption[]
  @Prop({ default: 22 }) private textSize!: number
  @Prop({ default: '' }) private unitStr!: string
  @Prop({ default: '' }) private chartName?: string
  @Prop({ default: '' }) private unit?: string
  @Prop({ default: 280 }) private ringWidth!: string
  @Prop({ default: () => ['40%', '60%'] }) private radiusList!: string[]
  @Prop({ default: () => ({}) }) private labelOptions!: PieSeriesOption['label']
  @Prop({ default: () => ({}) }) private readonly individuationOptions!: EChartsOption // 个性化options

  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}

  get sum() {
    return this.seriesData.reduce((sum: any, currentValue: any) => {
      return (sum += currentValue.value || 0)
    }, 0)
  }

  // 监听 seriesData 数据变化，更新视图
  @Watch('seriesData', {
    deep: true
  })
  changeSeriesData() {
    this.initEcharts()
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById(this.chartId) as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)
    this.initEcharts()
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 初始化数据
  private initEcharts() {
    let textSize = this.textSize
    let tooltipData: any = Object.assign(
      {
        trigger: 'item',
        valueFormatter: (value: number) => `${value}${this.unit}`
      },
      echartConfigure.tooltip
    )

    this.option = {
      color: colorSixList,
      title:
        this.titleList && Array.isArray(this.titleList)
          ? this.titleList
          : {
              top: '40%',
              left: 'center',
              text: this.text,
              subtext: this.subtext,
              textStyle: {
                color: 'rgba(255, 170, 69, 1)',
                fontSize: 30,
                fontWeight: 'normal',
                fontFamily: 'PangMenZhengDao'
              },
              subtextStyle: {
                color: '#fff',
                fontWeight: 'normal',
                fontFamily: 'PangMenZhengDao',
                fontSize: textSize * 1.4
              }
            },
      legend: {
        show: false
      },
      tooltip: tooltipData,
      series: [
        {
          type: 'pie',
          radius: this.radiusList,
          center: ['50%', '50%'],
          avoidLabelOverlap: true,
          name: this.chartName,
          selectedMode: 'single',
          label: Object.assign(
            {
              show: true,
              position: 'outer',
              alignTo: 'edge',
              minMargin: 40,
              edgeDistance: 10,
              lineHeight: 18,
              fontSize: 18,
              color: '#5db0ea',
              formatter: ({ data }: { data: any }) => {
                return `{a|${data.name}} \n {b|${+data.value}}`
              },
              rich: {
                a: {
                  fontSize: 38,
                  color: '#fff',
                  fontWeight: 'normal',
                  fontFamily: 'PangMenZhengDao'
                },
                b: {
                  fontSize: 36,
                  padding: [30, 0, 0, 0],
                  color: '#20DCF9',
                  fontWeight: 'normal',
                  fontFamily: 'PangMenZhengDao'
                }
              }
            },
            this.labelOptions
          ),
          itemStyle: {
            borderRadius: 0,
            borderWidth: 10
          },
          labelLayout: {
            hideOverlap: false
          },
          labelLine: {
            length: 20,
            length2: 50,
            maxSurfaceAngle: 80,
            smooth: 0.2,
            lineStyle: {
              width: 3
            }
          },
          data: this.seriesData
        },
        {
          type: 'pie',
          radius: this.radiusList,
          center: ['50%', '50%'],
          data: this.seriesData,
          label: {
            show: true,
            position: 'inside',
            color: '#fff',
            fontSize: 28,
            fontWeight: 'normal',
            fontFamily: 'PangMenZhengDao',
            formatter: (params) => {
              return params.percent ? `${params.percent}%` : ''
            }
          }
        }
      ]
    }

    // 合并传入 options
    let resOptions = deepMerge(this.option, this.individuationOptions)

    this.myChart && this.myChart.setOption(resOptions ? resOptions : this.option)
  }
}
</script>

<style scoped lang="scss">
.proportion-of-ring {
  width: 100%;
  height: 100%;
  .halo {
    position: absolute;
    z-index: -1;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: url('../images/halo.png');
    background-size: 100% 100%;
    will-change: width, height, transform;
    //animation: move 20s linear forwards infinite;
  }

  @keyframes move {
    0% {
      transform: translate(-50%, -50%) rotateZ(0deg);
    }
    50% {
      transform: translate(-50%, -50%) rotateZ(360deg);
    }
    100% {
      transform: translate(-50%, -50%) rotateZ(0deg);
    }
  }
}
</style>