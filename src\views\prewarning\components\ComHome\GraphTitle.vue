<template>
  <div class="graph-title">
    <span class="title">{{ title }}</span>
    <span v-if="colorVisible"
      :class="isRect && 'rect'">
      <label class="red"><i></i>红色预警</label>
      <label class="yellow"><i></i>黄色预警</label>
    </span>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

@Component
export default class GraphTitle extends Vue {
  @Prop() private title!: string
  @Prop({ default: true }) private colorVisible!: boolean
  @Prop() private isRect!: boolean
}
</script>

<style lang="scss" scoped>
.graph-title {
  display: flex;
  justify-content: space-between;
  .title {
    line-height: 24px;
    border-left: 6px solid #b43c3c;
    padding-left: 10px;
    font-size: 20px;
    color: #2f3038;
  }
  .red,
  .yellow {
    font-size: 12px;
    color: #333333;
    i {
      border-radius: 50%;
      // width: 4px;
      // height: 4px;
      font-size: 0px;
      display: inline-block;
      margin-right: 5px;
    }
  }
  .red {
    margin-right: 20px;
    i {
      border: 2px solid #ff8888;
    }
  }
  .yellow {
    i {
      border: 2px solid #ffe29b;
    }
  }
  .rect {
    i {
      width: 0;
      height: 0;
      border-width: 4px;
      border-radius: 20%;
    }
  }
}
</style>