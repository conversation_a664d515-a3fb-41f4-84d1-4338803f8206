<template>
  <div id="app">
    <router-view />
  </div>
</template>


<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

export type Theme = 'dazzle' | 'plain'

@Component
export default class extends Vue {
  private theme: Theme = 'dazzle'

  created() {
    this.setTheme(this.theme)
  }

  setTheme(theme: Theme) {
    document.documentElement.dataset.theme = theme
  }
}
</script>

<style lang="scss">
html,
body,
#app {
  height: 100%;
}

// 百度地图，隐藏左下角文案
.BMap_cpyCtrl {
  display: none;
}
.anchorBL {
  display: none;
}

::v-deep .el-select-dropdown {
  background: none;
}

// 滚动条
::-webkit-scrollbar-track-piece {
  background: #d3dce633;
}

::-webkit-scrollbar {
  width: 12px;
  height: 12px;
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  background: #99a9bf44;
  border-radius: 20px;
}

::-webkit-scrollbar-thumb:hover {
  background: #99a9bfaa;
}

// 全局修改 el-tooltip 样式
.el-tooltip__popper.is-dark {
  font-size: 14px;
}

// 驾驶舱自定义弹窗
.custom-cockipt-dialog-wrap {
  @keyframes keyCockiptDialogMove {
    0% {
      transform: rotateY(90deg);
    }
    100% {
      transform: rotateY(0);
    }
  }

  position: relative !important;
  left: 0 !important;
  padding: 0 20px 20px !important;
  background: #072979 !important;
  border-radius: 10px !important;
  border: 1px solid #2eb6f6 !important;
  transform: rotateY(0);
  animation: keyCockiptDialogMove 1s ease;

  h4,
  p {
    margin: 0;
  }

  .el-dialog__header {
    padding: 20px !important;
    background: none !important;
    &::after {
      display: none !important;
    }
  }

  .el-dialog__body {
    padding: 0 0 !important;
  }

  .body-close {
    position: absolute;
    right: 35px;
    top: 26px;
    color: rgb(0, 204, 255);
    font-size: 24px;
    cursor: pointer;
  }

  .header-box {
    display: flex !important;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    padding-right: 0 !important;
    .mode {
      font-weight: normal;
    }
    .title {
      color: #fff;
    }
    .close {
      font-size: 28px;
      margin-left: 10px;
      cursor: pointer;
      &:hover {
        color: #dfdfdf;
      }
    }
    .mode-left {
      flex: 1;
    }
    .mode-right {
      flex: 1;
      color: #2eb6f6;
      font-size: 13px;
      text-align: right;
      display: flex;
      align-items: center;
      justify-content: end;
    }
    .mode-middel {
      flex: 1;
      color: #dfdfdf;
      font-size: 20px;
      text-align: center;
    }
  }

  .content-box {
    position: relative;
  }

  .custom-cockipt-select-wrap {
    $color: #2eb6f6;

    .el-input__inner {
      background: none;
      border: 1px solid #2eb6f6;
      color: $color;
    }
    .el-select__caret {
      color: $color !important;
    }
  }
}

.custom-cockipt-select-down {
  $color: #2eb6f6;
  margin-top: 0 !important;

  $modeHoverBg: linear-gradient(
    to right,
    rgba(5, 186, 253, 0) 0%,
    rgba(5, 186, 253, 0.1) 10%,
    rgba(5, 186, 253, 0.3) 20%,
    rgba(5, 186, 253, 0.5) 30%,
    rgba(5, 186, 253, 0.8) 40%,
    rgba(5, 186, 253, 1) 50%,
    rgba(5, 186, 253, 0.8) 60%,
    rgba(5, 186, 253, 0.5) 70%,
    rgba(5, 186, 253, 0.4) 80%,
    rgba(5, 186, 253, 0.3) 90%,
    rgba(5, 186, 253, 0.1) 100%
  );

  background: rgba($color: #00287e, $alpha: 1) !important;
  border-color: $color !important;

  .el-select-dropdown__list {
    padding: 0 !important;

    .el-select-dropdown__item {
      height: 30px !important;
      line-height: 30px !important;
      color: #dfdfdf !important;
      padding: 0 16px !important;
      text-align: center !important;
    }
    .selected {
      background: $modeHoverBg !important;
    }
    .hover {
      background: $modeHoverBg !important;
    }
  }
  .popper__arrow {
    border: none !important;
    &::after {
      top: 0 !important;
      border-bottom-color: $color !important;
    }
  }
}

// 自定义loading加载样式，加入到 v-loading 的 element-loading-spinner 中
.cockpit-panel-wrapper {
  .el-loading-mask {
    border-radius: 20px !important;
    background: rgba(7, 41, 121, 0.4) !important;
  }
  .el-loading-spinner {
    font-size: 100px !important;
    .circular {
      height: 100px;
      width: 100px;
    }
  }
}

// 数据看板样式
.echarts-overviews-wrap {
  position: relative;
  height: 100%;
  background: #f0f2f5;
  overflow-y: auto;
  box-sizing: border-box;
  h4,
  p {
    margin: 0;
  }
  .header-box {
    text-align: right;
    padding: 10px 10px 10px;
    margin-bottom: 8px;
    background: #fff;
  }

  .conter-box {
    position: relative;
    .modules {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      height: 22vw;
      margin-bottom: 8px;
      &:nth-last-child(1) {
        margin-bottom: 0;
      }
      .mode {
        flex: 1;
        height: 100%;
        margin-right: 8px;
        display: flex;
        padding: 10px;
        border-radius: 4px;
        overflow: hidden;
        box-sizing: border-box;
        justify-content: space-between;
        flex-direction: column;
        background: #fff;
        &:nth-last-child(1) {
          margin-right: 0;
        }
        .til {
          display: flex;
          align-items: center;
          font-weight: normal;
          margin-bottom: 10px;
          color: rgba(245, 108, 108, 0.8);
          i {
            font-size: 20px;
            margin-right: 4px;
          }
          span {
            font-size: 17px;
          }
        }
        .cter {
          flex: 1;
        }
      }
    }
  }

  .echarts-dom-wrap {
    position: relative;
    height: 100%;
    .hide {
      opacity: 0;
    }
    .none {
      display: none !important;
    }
    .empty-none-data {
      position: absolute;
      top: 0;
      width: 100%;
      height: 100%;
      padding: 0;
    }
  }

  .el-form {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .el-form-item {
      margin-bottom: 0;
      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>