<template>
  <div class="grid">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <el-form-item label="单位名称" prop="corpCode">
        <el-cascader v-model="queryParams.corpCode" :options="dwmcOption" clearable :show-all-levels="false"
          :props="{ checkStrictly: true, value: 'deptCode', label: 'deptName', emitPath: false }" filterable
          placeholder="单位名称" />
      </el-form-item>
      <el-form-item label="时间选择" prop="chooseTime">
        <el-date-picker v-if="queryParams.timeFlag == 1" v-model="queryParams.chooseTime" type="month"
          placeholder="时间选择" value-format="yyyy-MM" />
        <el-date-picker v-else v-model="queryParams.chooseTime" type="year" placeholder="时间选择" value-format="yyyy" />
      </el-form-item>
      <el-form-item label="" prop="timeFlag">
        <el-radio-group v-model="queryParams.timeFlag" @input="timeFlagChange">
          <el-radio :label="1">月度</el-radio>
          <el-radio :label="2">年度</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="" prop="mergeFlag" style="margin-left: 20px;">
        <el-checkbox v-model="queryParams.mergeFlag" :true-label="1" :false-label="0">是否为合并表</el-checkbox>
      </el-form-item>
      <el-form-item style="margin-left: 16px;">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div v-show="showTable">
      <div class="breadcrumb">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item v-for="(item, index) in breadList" :key="item.code"
            @click.native="breadClick(item, index)" class="breadClass">{{ item.name }} </el-breadcrumb-item>
        </el-breadcrumb>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleExport">导出</el-button>
      </div>

      <el-table class="riskTableClass" :data="tableList" :span-method="objectSpanMethod" v-loading="loading">
        <el-table-column label="#" align="center" type="index" width="50" />
        <el-table-column v-if="showPage" label="单位名称" align="left" width="300" show-overflow-tooltip>
          <template slot-scope="scope">
            <div class="curP" @click="downToDw(scope.row)">{{ scope.row.corpName }}</div>
          </template>
        </el-table-column>
        <el-table-column v-else label="资金链" align="center" prop="capitalChain" width="100" />
        <el-table-column label="风险预警指标" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <div class="curP" @click="downToFxyjzb(scope.row)">{{ scope.row.indicatorName }}</div>
          </template>
        </el-table-column>
        <el-table-column label="风险提示值" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <div>
              {{ (scope.row.riskWarningValue || scope.row.riskWarningValue == 0) ? scope.row.riskWarningValue : '/' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="横向对比结果" align="center" prop="comparisonResult" show-overflow-tooltip />
        <el-table-column label="预测值" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <div>
              {{ (scope.row.predictiveValue || scope.row.predictiveValue == 0) ? scope.row.predictiveValue : '/' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="实际值" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <div>{{ (scope.row.actualValue || scope.row.actualValue == 0) ? scope.row.actualValue : '/' }}</div>
          </template>
        </el-table-column>
      </el-table>

      <div class="footer pageWrap" v-show="showPage">
        <el-pagination clsss="pagination" layout="total, sizes, prev, pager, next, jumper"
          :current-page="queryForm.current" :page-size="queryForm.size" :total="total" :page-sizes="[10, 30, 50]"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>
  </div>
</template>

<script>
import { getCompanyIndicator, getCompanyPage, companyIndicatorExport, companyPageExport, getCreditGroups } from '@/api/risk'
import { downloadXls } from '@/utils'

export default {
  data () {
    return {
      total: 0,
      tableList: [],
      loading: false,
      queryParams: {
        corpCode: "",
        timeFlag: 1,
        mergeFlag: 0,
        chooseTime: this.$moment(new Date()).add('month', -1).format("YYYY-MM")
      },
      dwmcOption: [],
      showTable: false,
      breadList: [],
      showPage: false,
      queryForm: {
        current: 1,
        size: 10,
        corpCode: "",
        timeFlag: 1,
        mergeFlag: 0,
        indicatorType: null,
        chooseTime: this.$moment(new Date()).add('month', -1).format("YYYY-MM")
      },
      queryExport: {}
    }
  },

  created () {
    this.getOption()
    this.queryParams.corpCode = '913307012549743237'
    this.handleQuery()
  },

  methods: {
    objectSpanMethod ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1 && !this.showPage) {
        if (rowIndex > 0 && row.capitalChain === this.tableList[rowIndex - 1].capitalChain) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let rowspan = 1
          for (let i = rowIndex + 1; i < this.tableList.length; i++) {
            if (this.tableList[i].capitalChain === row.capitalChain) {
              rowspan++
            } else {
              break
            }
          }
          return { rowspan, colspan: 1 }
        }
      }
    },

    timeFlagChange (label) {
      // console.log('变化了', label)
      this.queryParams.chooseTime = this.queryParams.timeFlag == 1 ? this.$moment(new Date()).add('month', -1).format("YYYY-MM") : this.$moment(new Date()).add('year', -1).format("YYYY")
    },

    getOption () {
      getCreditGroups().then(res => {
        // console.log('单位树', res)
        this.dwmcOption = res.data
      })
    },

    getList () {
      this.loading = true
      this.showTable = true
      this.showPage = false
      getCompanyIndicator(this.queryParams).then(res => {
        // console.log('列表', res)
        this.breadList = []
        res.data.corpCode ? this.breadList.push({ name: res.data.corpName, code: res.data.corpCode }) : this.breadList.push({ name: res.data.orgName, code: res.data.orgCode })
        this.tableList = res.data.indicatorList
        this.loading = false
      })
    },

    handleExport () {
      let time = new Date().getTime()
      if (this.showPage) {
        companyPageExport(this.queryExport).then(res => {
          // console.log('分页导出', res)
          downloadXls(res.data, `单个指标_${time}.xlsx`)
          this.$message.success(res.msg || '导出成功')
        })
      } else {
        companyIndicatorExport(this.queryExport).then(res => {
          // console.log('集团单位导出', res)
          downloadXls(res.data, `集团指标_${time}.xlsx`)
          this.$message.success(res.msg || '导出成功')
        })
      }
    },

    breadClick (row, index) {
      console.log('面包屑', row, index)
      if (index == 0) {
        this.tableList = []
        this.loading = true
        this.showPage = false
        let queryParams = {
          corpCode: row.code,
          timeFlag: this.queryParams.timeFlag,
          chooseTime: this.queryParams.chooseTime,
          mergeFlag: this.queryParams.mergeFlag
        }
        this.setQueryExport(queryParams)
        getCompanyIndicator(queryParams).then(res => {
          this.breadList = []
          // console.log('列表', res)
          res.data.corpCode ? this.breadList.push({ name: res.data.corpName, code: res.data.corpCode }) : this.breadList.push({ name: res.data.orgName, code: res.data.orgCode })
          this.tableList = res.data.indicatorList
          this.loading = false
        })
      }
    },

    downToDw (row) {
      this.tableList = []
      console.log('点击单位', row)
      this.loading = true
      this.showPage = false
      let queryParams = {
        corpCode: row.corpCode,
        timeFlag: this.queryParams.timeFlag,
        chooseTime: this.queryParams.chooseTime,
        mergeFlag: this.queryParams.mergeFlag
      }
      this.setQueryExport(queryParams)
      getCompanyIndicator(queryParams).then(res => {
        this.breadList = []
        // console.log('列表', res)
        res.data.corpCode ? this.breadList.push({ name: res.data.corpName, code: res.data.corpCode }) : this.breadList.push({ name: res.data.orgName, code: res.data.orgCode })
        this.tableList = res.data.indicatorList
        this.loading = false
      })
    },

    downToFxyjzb (row) {
      this.tableList = []
      console.log('点击风险指标', row)
      // if (this.showPage) {
      //   return
      // }
      this.loading = true
      if (row.corpCode) {
        this.breadList[0] = { name: row.corpName, code: row.corpCode }
      } else {
        this.breadList[0] = { name: row.orgName, code: row.orgCode }
      }
      this.breadList[1] = { name: row.indicatorName, code: '888888' }
      this.showPage = true
      this.queryForm = {
        current: 1,
        size: 10,
        corpCode: row.corpCode,
        timeFlag: this.queryParams.timeFlag,
        indicatorType: row.indicatorType,
        chooseTime: this.queryParams.chooseTime,
        mergeFlag: this.queryParams.mergeFlag
      }
      this.setQueryExport(this.queryForm)
      getCompanyPage(this.queryForm).then(res => {
        console.log('查询结果', res)
        this.tableList = res.data.records
        this.total = res.data.total
        this.loading = false
      })
    },

    handleSizeChange (size) {
      this.queryForm.size = size
      this.loading = true
      getCompanyPage(this.queryForm).then(res => {
        this.tableList = res.data.records
        this.total = res.data.total
        this.loading = false
      })
    },

    handleCurrentChange (pageNo) {
      this.queryForm.current = pageNo
      this.loading = true
      getCompanyPage(this.queryForm).then(res => {
        this.tableList = res.data.records
        this.total = res.data.total
        this.loading = false
      })
    },

    handleQuery () {
      if (!this.queryParams.corpCode) {
        this.$message({ message: '请选择单位！', type: 'warning' })
        return
      }
      this.setQueryExport(this.queryParams)
      this.getList()
    },

    setQueryExport (obj) {
      this.queryExport = obj
    },

    resetQuery () {
      this.queryParams = {
        corpCode: "",
        timeFlag: 1,
        mergeFlag: 0,
        chooseTime: this.$moment(new Date()).add('month', -1).format("YYYY-MM")
      }
      this.showTable = false
      this.tableList = []
    },
  }
}
</script>

<style scoped lang="scss">
.breadcrumb {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
}

.riskTableClass {
  ::v-deep td.el-table__cell {
    border: 1px solid #EBEEF5 !important;
  }

  ::v-deep th.el-table__cell.is-leaf {
    border: 1px solid #EBEEF5 !important;
  }
}

.pageWrap {
  display: flex;
  flex-direction: row-reverse;
  background-color: #fff;
  box-sizing: border-box;
  padding: 16px 16px 16px 0;
}

.curP {
  cursor: pointer;
}

.breadClass {
  ::v-deep .el-breadcrumb__inner {
    cursor: pointer !important;
  }
}
</style>

<style lang="scss">
.el-radio__original {
  display: none !important;
  /* 隐藏原生 radio 输入，但仍然允许交互 */
}

.el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
  box-shadow: none !important;
}
</style>
