<template>
  <Dialog title="新增标的交易"
    width="1100px"
    :visible="visible"
    @close="handleClose">
    <div slot="body"
      v-loading="loading">
      <el-steps :active="active"
        simple
        class="target-header-step">
        <el-step title="标的信息"
          :icon="active == 1 ? 'el-icon-edit' : 'el-icon-menu'"></el-step>
        <el-step title="出租信息"
          :icon="active == 2 ? 'el-icon-edit' : 'el-icon-tickets'"></el-step>
        <el-step title="承租方资格条件"
          :icon="active == 3 ? 'el-icon-edit' : 'el-icon-document-remove'"></el-step>
      </el-steps>
      <el-tabs class="target_form"
        :value="active.toString()"
        :key="loadform">
        <el-tab-pane label="标的信息"
          name="1">
          <target-info ref="targetinfo"
            :key="loadform"
            v-bind="$attrs"
            :detaildata="formData"></target-info>
        </el-tab-pane>
        <el-tab-pane label="出租信息"
          name="2">
          <LeaseInformationForm ref="lessorInfo"
            :key="loadform"
            v-bind="$attrs"
            :detaildata="formData" />
        </el-tab-pane>
        <el-tab-pane label="承租方资格条件"
          name="3">
          <LesseeInformationForm ref="lesseeInfo"
            :key="loadform"
            v-bind="$attrs"
            :detaildata="formData" />
        </el-tab-pane>
      </el-tabs>
    </div>
    <div slot="footer">
      <div class="m-t-10">
        <el-button type="primary"
          v-show="active > 1"
          @click="goActive(-1)">上一步</el-button>
        <el-button type="primary"
          v-show="active < 3"
          @click="verificationForm">下一步</el-button>
        <el-button type="primary"
          v-show="active == 3"
          @click="verificationForm()">提交</el-button>
      </div>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { Confirm, Throttle } from '@/decorators/index'
import Dialog from '@/components/Dialog/index.vue'
import TargetInfo from './components/TargetInfoForm.vue'
import LeaseInformationForm from './components/LessorInformationForm.vue'
import LesseeInformationForm from './components/LesseeInformationForm.vue'
import { AddTarget, ReapplyDetail, Addreapply } from '@/api/assetsv2'
import { deepClone } from '../../../../utils'

@Component({
  components: {
    Dialog,
    TargetInfo,
    LeaseInformationForm,
    LesseeInformationForm
  }
})
export default class extends Vue {
  @Prop() private visible!: boolean
  @Prop() private detaildata!: any
  private active: any = 1
  private loading = false
  private loadform = true
  private formData: any = {}
  // private detailInfo: any = {}
  @Confirm({
    title: '提示',
    content: `是否关闭？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  mounted() {
    if (this.$attrs.mode == 'add') {
      this.formData = this.detaildata
      try {
        let assetNo = (this.$attrs['assetData'] as any).assetNo
        if (assetNo == undefined || assetNo == '') {
          this.$message.error('没找到对应资产编号')
          setTimeout(() => {
            this.$emit('update:visible', false)
          }, 500)
        }
      } catch (error) {
        this.$message.error('没找到对应资产编号')
        setTimeout(() => {
          this.$emit('update:visible', false)
        }, 500)
      }
    } else if (this.$attrs.mode == 'reset') {
      this.ReapplyDetail()
    }
  }
  //
  private async ReapplyDetail() {
    try {
      this.loading = true
      this.loadform = false
      let res = await ReapplyDetail({
        id: this.detaildata.id
      })
      if (res.success) {
        Object.assign(this.formData, res.data)
        // this.formData = res.data
        this.loading = false
        this.loadform = true
      }
    } catch (e) {
      this.$emit('update:visible', false)
      this.loading = false
    }
  }
  @Confirm({
    title: '提示',
    content: `是否关闭？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private handleClose() {
    this.$emit('update:visible', false)
  }
  @Throttle
  @Confirm({
    title: '提示',
    content: `是否提交信息`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'success'
  })
  //   提交表单信息
  private async submitInfo() {
    let targetInfoRef = this.$refs.targetinfo as any
    let lessorInfoRef = this.$refs.lessorInfo as any
    let lesseeInfoRef = this.$refs.lesseeInfo as any
    // 拼接数据
    let targetInfo = deepClone(targetInfoRef.rentalInfoForm)
    // targetInfo.subjectList = targetInfoRef.rentalInfoForm.subjectList
    targetInfo.lessor = deepClone(lessorInfoRef.lessorInfoForm)
    targetInfo.leaseCond = deepClone(lesseeInfoRef.lesseeQualificationsForm)
    if (this.$attrs.mode == 'add') {
      // 新增
      let res: any = {}
      try {
        res = await AddTarget(targetInfo)
        if (res.success) {
          // this.data = res.data.records
          this.$message.success(res.msg)
          this.$emit('update:visible', false)
        }
      } catch (e) {
        this.$message.error(res.msg)
      }
    } else if (this.$attrs.mode == 'reset') {
      // 重新发起(新增)
      let res: any = {}
      try {
        res = await Addreapply(targetInfo)
        if (res.success) {
          // this.data = res.data.records
          this.$message.success(res.msg)
          this.$emit('update:visible', false)
        }
      } catch (e) {
        this.$message.error(res.msg)
      }
    }
    // this.$emit('update:visible', false)
  }

  private goActive(val: number) {
    this.active = this.active + val
  }
  private verificationForm() {
    // 对表单进行验证
    if (this.active == 1) {
      // 验证标的信息
      let targetInfoRef = this.$refs.targetinfo as any
      // let isPass
      targetInfoRef
        .validate()
        .then((res: any) => {
          this.goActive(1)
        })
        .catch((err: any) => {
          // this.goActive(1)
        })
    } else if (this.active == 2) {
      // 验证出租方信息
      let targetInfoRef = this.$refs.lessorInfo as any
      // let isPass
      targetInfoRef
        .validate()
        .then((res: any) => {
          this.goActive(1)
        })
        .catch((err: any) => {
          // this.goActive(1)
        })
    } else if (this.active == 3) {
      // 验证出租方信息
      let targetInfoRef = this.$refs.lesseeInfo as any
      // let isPass
      targetInfoRef
        .validate()
        .then((res: any) => {
          // this.goActive(1)
          this.submitInfo()
        })
        .catch((err: any) => {
          // this.goActive(1)
          // this.submitInfo()
        })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-tabs__header {
  display: none;
}
::v-deep.common-dailog > .el-dialog > .el-dialog__body {
  padding: 10px;
}
::v-deep.common-dailog > .el-dialog > .el-dialog__body > .dialog-body {
  padding: 0 !important;
  min-height: 500px;
}
// .target-header-step {
//   // transform: translateY(-20px);
// }
::v-deep.el-tabs--border-card {
  border: 0;
  box-shadow: none;
  background: none;
}
.target_form {
  ::v-deep.el-tabs__content {
    padding: 20px !important;
  }
}
</style>
