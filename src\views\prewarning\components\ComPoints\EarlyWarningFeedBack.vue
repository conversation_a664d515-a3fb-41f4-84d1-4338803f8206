<template>
  <Dialog width="1000px"
    :visible="visible"
    :modal-append-to-body="true"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :append-to-body="true"
    @close="handleClose">
    <!-- 标题 -->
    <div slot="header">
      <div>{{title}}</div>
    </div>

    <!-- 内容区域 -->
    <div slot="body">
      <AlertFeedBack :type="type"
        :params="params"
        @handleClose="handleClose"
        @updataHandle="updataHandle" />
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import AlertFeedBack from './AlertFeedBack.vue'

@Component({
  components: {
    Dialog,
    AlertFeedBack
  }
})
export default class extends Vue {
  @Prop({ default: false }) private visible?: boolean // 弹窗显隐
  @Prop() private params!: any // 详情数据
  @Prop({
    validator: (value: string) => {
      return ['dispose', 'see', 'feedback'].indexOf(value) !== -1
    }
  })
  readonly type!: string // 组件类型(处置、查看)

  private title = '处置情况反馈'

  // 触发父组件更新
  private updataHandle() {
    this.$emit('updataHandle')
  }

  // 关闭弹窗
  private handleClose() {
    this.$emit('update:visible', false)
  }
}
</script>