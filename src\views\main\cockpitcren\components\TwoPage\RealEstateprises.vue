/* 不动产、人事信息 */

<template>
  <section class="cockpit-real-estateprises-wrap"
    :class="{'cockpit-wrapperFull-wrap': isWrapperFull}">
    <RealEstate @parentFullScreenHandle="parentFullScreenHandle"
      class="mode-box" />
    <Entrepreneur @parentFullScreenHandle="parentFullScreenHandle"
      class="mode-box" />

    <!-- 数据改变提示层 -->
    <img v-if="visibleFlicker"
      class="cockipt-bg-flicker"
      src="@/views/main/cockpitcren/images/panel_bg2_cg.png" />
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import TitleCom from '@/views/main/cockpitcren/components/Public/TitleCom.vue'
import RealEstate from '@/views/main/cockpitcren/components/TwoPage/RealEstate.vue'
import Entrepreneur from '@/views/main/cockpitcren/components/TwoPage/Entrepreneur.vue'

@Component({
  components: {
    TitleCom,
    RealEstate,
    Entrepreneur
  }
})
export default class extends Vue {
  private isWrapperFull = false
  private visibleFlicker = false

  // 放大/缩小展示
  private parentFullScreenHandle(isFull: boolean) {
    this.isWrapperFull = isFull
  }

  private mounted() {
    this.listenerDate()
  }

  // 监听全局事件触发
  private listenerDate() {
    // 集团 切换
    this.$bus.$on('BusTabsChange', (orgActive: any) => {
      this.flickerHandle()
    })

    // 年份 切换
    this.$bus.$on('BusYearTimeChange', (year: string) => {
      this.flickerHandle()
    })
  }

  // 触发背景闪烁效果
  private flickerHandle() {
    this.visibleFlicker = true
    setTimeout(() => {
      this.visibleFlicker = false
    }, 3000)
  }
}
</script>

<style scoped lang="scss">
.cockpit-real-estateprises-wrap {
  position: relative;
  transition: 1s;
  width: 100%;
  height: 760px;
  background: url('../../images/panel_bg2.png') no-repeat left top;
  background-size: 100% 100%;
}

.cockpit-wrapperFull-wrap {
  z-index: 1000;
  transform-origin: left bottom;
  transform: scale(1.7);
  border-radius: 20px;
  background: #072979;
  box-shadow: 0 0 40px #000;
}

.mode-box {
  position: relative;
  z-index: 2;
}
</style>