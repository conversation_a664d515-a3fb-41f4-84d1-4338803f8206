<template>
  <el-container>
    <div class="asset-rental-left">
      <div id="compositeIndex" style="height: 170px; width: 80px"></div>
      <div class="asset-echart-text">
        <ul>
          <li v-for="(item, index) in itemData" :key="index">
            <p>
              <i :style="[{ backgroundColor: item.itemStyle.color }]" class="asset-rental-listi"></i>
              已出租
              <span :style="[{ color: item.itemStyle.color }]">{{ item.l }}</span>
            </p>
            <p>{{ item.name }}间</p>
          </li>
        </ul>
      </div>
    </div>
  </el-container>
</template>
<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import * as echarts from 'echarts'
@Component({
  components: {}
})
export default class Container extends Vue {
  @Prop() private assetLeasingOverviewVO: any
  @Watch('assetLeasingOverviewVO', { deep: true })
  private changidEvent() {
    // id更改之后的事件
    this.itemData = []
    this.assetLeasingOverviewVO.forEach((item: { houseNum: string; rate: string; status: string }) => {
      this.itemData.push({
        value: item.houseNum,
        name: item.houseNum,
        l: item.rate
      })
    })
    
    this.initChart()
  }
  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private itemData: any = [
    { value: 339, name: '0', l: '100%', itemStyle: { color: '#F57969' } },
  ]
  private async mounted() {
    await this.initChart()
  }
  private initChart() {
    this.chartDom = document.getElementById('compositeIndex') as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)
    let option: any = ''

    option = {
      series: [
        {
          type: 'pie',
          radius: ['100%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center',
            emphasis: {
              show: true
            }
          },
          labelLine: {
            show: true
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '30',
              fontWeight: 'bold'
            }
          },
          data: this.itemData
        }
      ]
    }

    option && this.myChart.setOption(option)&&this.myChart.resize()
  }
}
</script>

<style lang="scss" scoped>
.asset-rental-left {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  div {
  }
}
.asset-echart-text {
  ul {
    padding: 0 20px;
    width: 180px;
    font-size: 12px;
    li {
      i {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 8px;
      }
      font-size: 12px;
      line-height: 10px;
      p:nth-child(1) {
        font-size: 12px;
        color: #909399;
      }
      p:nth-child(2) {
        padding-left: 12px;
        font-size: 12px;
        color: #303133;
        font-weight: 600;
      }
    }
  }
}
</style>
