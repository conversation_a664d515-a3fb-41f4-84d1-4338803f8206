/** 项目投资情况 */
<template>
  <div class="project-implementation">
    <div class="info">
      <div class="info__all">
        <span>项目总数</span>
        <span>{{ infoData.all }}</span>
      </div>
      <div class="info__building">
        <span>在建项目</span>
        <span>{{ infoData.building }}</span>
      </div>
    </div>
    <div id="ProjectImplementation" />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { ProjectImplementationData } from '@/views/main/cockpitSecondary/baseData'
import { capitalProfile } from '@/api/cockpit'
import * as echarts from 'echarts'
import { deepClone } from '@/utils'

type EChartsOption = echarts.EChartsOption

@Component
export default class extends Vue {
  private loading = false
  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}
  private echartsDatas: any[] = []
  private seriesData: any[] = deepClone(ProjectImplementationData)
  private infoData = {
    all: 548,
    building: 324,
    budget: '2605.7',
    executed: '429.43'
  }

  get rate() {
    return +(((+this.infoData.executed || 0) / (+this.infoData.budget || 0)) * 100).toFixed(2)
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('ProjectImplementation') as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    // this.initData()
    this.initEcharts()
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await capitalProfile()
    if (!Array.isArray(data) || !data.length) return
    this.echartsDatas = data
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 初始化数据
  private initEcharts() {
    let textSize = 22
    let series = this.seriesData

    this.option = {
      grid: {
        left: '0%',
        right: '0%',
        top: '0%',
        bottom: '0%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 103, 177, 1)',
        borderColor: 'rgba(0, 103, 177, 1)',
        textStyle: {
          color: '#fff',
          fontSize: textSize
        }
      },
      title: [
        {
          text: '已执行',
          subtext: `${+this.infoData.executed || 0}亿元`,
          left: 0,
          top: 40,
          textStyle: {
            color: '#58D9F9',
            shadowColor: 'rgba(0,138,255,0.45)',
            shadowBlur: 10,
            fontSize: 30
          },
          subtextStyle: {
            color: '#fff',
            opacity: 0.8,
            shadowColor: 'rgba(0,138,255,0.45)',
            shadowBlur: 10,
            fontSize: 36,
            fontWeight: 'bold'
          }
        },
        {
          text: '执行度',
          subtext: `${this.rate}%`,
          textAlign: 'right',
          right: -130,
          top: 40,
          textStyle: {
            color: '#58D9F9',
            shadowColor: 'rgba(0,138,255,0.45)',
            shadowBlur: 10,
            fontSize: 30
          },
          subtextStyle: {
            color: '#fff',
            opacity: 0.8,
            shadowColor: 'rgba(0,138,255,0.45)',
            shadowBlur: 10,
            fontSize: 36,
            fontWeight: 'bold'
          }
        }
      ],
      series: [
        {
          type: 'gauge',
          startAngle: 180,
          name: '项目预算',
          endAngle: 0,
          min: 0,
          radius: '100%',
          max: 100,
          center: ['50%', '65%'],
          splitNumber: 5,
          itemStyle: {
            color: '#58D9F9',
            shadowColor: 'rgba(0,138,255,0.45)',
            shadowBlur: 10,
            shadowOffsetX: 2,
            shadowOffsetY: 2
          },
          progress: {
            show: true,
            roundCap: true,
            width: 36,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: this.rate < 30 ? '#7CB7FF44' : this.rate < 60 ? '#7CB7FF44' : '#7CB7FF44' // 0% 处的颜色
                  },
                  {
                    offset: 0.7,
                    color: this.rate < 30 ? '#4680FF' : this.rate < 60 ? '#4680FF' : '#4680FF' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          },
          pointer: {
            show: false
          },
          axisLine: {
            roundCap: true,
            lineStyle: {
              width: 36,
              color: [[1, '#0E233A']]
            }
          },
          axisTick: {
            splitNumber: 2,
            lineStyle: {
              width: 2,
              color: '#0E233A'
            }
          },
          splitLine: {
            show: false
          },
          axisLabel: {
            distance: 30,
            color: '#5db0ea',
            fontSize: 24
          },
          title: {
            show: false
          },
          detail: {
            width: '100%',
            lineHeight: 40,
            height: 40,
            color: 'RGBA(217, 219, 220, 1)',
            borderRadius: 8,
            offsetCenter: [0, '5%'],
            valueAnimation: true,
            fontSize: 50,
            fontWeight: 'bold',
            formatter: () => {
              return `{a|${this.infoData.budget}}亿元`
            },
            rich: {
              a: {
                fontSize: 68,
                color: '#fff',
                fontWeight: 'bold',
                opacity: 0.8,
                fontFamily: 'digital-7'
              }
            }
          },
          data: [
            {
              value: this.rate,
              name: '项目预算',
              title: {
                offsetCenter: ['0%', '-25%'],
                show: true,
                color: '#5db0ea',
                fontSize: 40,
                fontWeight: 'bold'
              }
            }
          ]
        }
      ]
    }

    this.myChart && this.myChart.setOption && this.myChart && this.myChart.setOption(this.option)
  }
}
</script>

<style scoped lang="scss">
#ProjectImplementation {
  width: 100%;
  height: 100%;
}
.project-implementation {
  height: 100%;
  width: 100%;
}
.info {
  width: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
  span:first-child {
    font-size: 28px;
    font-family: Source Han Sans SC;
    font-weight: 500;
    color: #ffffff;
    line-height: 120px;
    opacity: 0.5;
    margin-right: 20px;
  }
  span:last-child {
    font-size: 68px;
    font-family: DIN Alternate;
    font-weight: bold;
    color: #ffffff;
    line-height: 120px;
    font-family: 'digital-7';
  }
}
</style>