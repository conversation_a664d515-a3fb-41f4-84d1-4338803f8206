import request from './request'
import { RemoteResponse } from '@/types'

// 项目删除
export const deleteProject = (projectCode: string): Promise<RemoteResponse> => {
  return request('/fht-monitor/invest/deleteProject', { projectCode })
}

// 项目计划表导入
export interface InvestImportParams {
  orgPath: '' // 公司名称
  category: string //	分类
  recordNumber: string // 备案编号
  file: File
}
export const investImport = (InvestImportParams: InvestImportParams): Promise<RemoteResponse> => {
  return request('/fht-monitor/invest/import', InvestImportParams)
}

// 项目投资进度表
export interface ImportProjectProgressParams {
  file: File[]
  projectCategory: 'QY' | 'ZF' | 'DJ'
}
export const importProjectProgress = (importProjectProgressParams: ImportProjectProgressParams): Promise<RemoteResponse> => {
  return request('/fht-monitor/invest/importProjectProgress', importProjectProgressParams)
}

// 删除投资项目进度报告
export const deleteProjectProgressReport = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/invest/progress/remove', params)
}

// 获取备案编号
export const createPlanIni = (CompanyCode: number): Promise<RemoteResponse> => {
  return request('/fht-monitor/invest/createPlanIni', { CompanyCode })
}

// 批量新增项目
export const createProject = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/invest/plan/submit', params)
}

// 备案搜索
export interface SelectPlanFilingParams {
  businessSubmissionTime?: string
  companyCode?: string
  companyName?: string
  current: number
  enterpriseSubmitter?: string
  keyword?: string
  month?: string
  projectCategory?: string
  projectCode?: string
  projectName?: string
  recordNumber?: string
  size: number
  year?: string
  planType?: number
}
export const selectPlanFiling = (params: SelectPlanFilingParams): Promise<RemoteResponse> => {
  return request('/fht-monitor/invest/plan/search', params)
}

// 删除备案
export const deletePlanFiling = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/invest/plan/remove', params)
}

// 新增审核
export interface ProjectReview {
  projectName: string //项目名称
  afterInvestmentRatio: number | '' // 本项目投资后预计公司资产负债率
  beforeInvestmentRatio: number | '' // 本项目投资前公司非主业投资比例
  approvalStatus: string // 审核状态
  assetRatio: number | '' // 公司资产负债率
  auditCompletionTime: string // 审核完成时间
  createTime: string // 编制提交时间
  createUser: string // 企业编制人
  expectedInvestPeriod: number | '' //预计投资期限
  expectedRoi: number | '' // 预期投资回报率
  investAmount: number | '' // 预计投入自有资金
  investCategory: string // 投资类别
  investSubject: string // 投资主体
  isMainInvest: string // 是否主体投资
  isOverInvest: string // 是否境外投资
  isPlan: string // 年初是否已列入投资计划
  level: string // 级次
  preparationUnit: string // 编制单位
  projectCode: string // 项目编号
  remark: string // 备注
  reviewFeedback: string // 审核反馈意见
  whetherAmt: number | '' // 预计投资金额
  feasibilityFile: string // 可行性研究报告文件
  investAdviceFile: string // 投资请示
  necessaryFile: string // 必要的材料文件
  legalFile: string // 法律意见文件
  riskFile: string // 风险防控文件
  decisionFile: string // 决策文件
}
export const createProjectReview = (params: ProjectReview): Promise<RemoteResponse> => {
  return request('/fht-monitor/invest/project/submit', params)
}

// 搜索项目
export const selectProjectName = (params: { keyword: string }): Promise<RemoteResponse> => {
  return request('/fht-monitor/invest/plan/detail/search', params)
}

// 资产交易申请
export const applyAssertransaction = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/asset/transaction/apply', params)
}
/*
 *年度计划备案查询详细
 *  传入id
 **/
export const investPLanInfo = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/invest/plan/info', params)
}

// 单个批次查询
export const getTransactionDetailOfbatch = (batchNo: string): Promise<RemoteResponse> => {
  return request('/fht-monitor/asset/transaction/batch/single', { batchNo })
}
// 单个批次查询
export const getAssetbatch = (batchNo: string): Promise<RemoteResponse> => {
  return request('/fht-monitor/asset/transaction/asset/batch', { batchNo })
}
// 单个批次查询
export const reApply = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/asset/transaction/reApply', params)
}
/*
 *查找企业列表信息
 **/
export const getCompTree = (params: any): Promise<RemoteResponse> => {
  return request(
    '/fht-admin/dept/tree?tenantId=000000',
    { ...params, category: 1 },
    {
      method: 'GET'
    }
  )
}
/*
 *查找国资下级企业列表信息
 **/
export const getAstCompTree = (params: any): Promise<RemoteResponse> => {
  return request(
    '/fht-admin/dept/child-list',
    { ...params, parentId: 0, deptCategory: 1, isGroup: 1 },
    {
      method: 'GET'
    }
  )
}
/*
 *查找备案审核详情
 **/
export const GetProjectDetail = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/invest/project/detail', params)
}
/*
* 项目投资总体进展情况
"orgId": 0,
  "year": ""
**/
export const ProjectProgress = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/invest/statistics/project-progress', params)
}
/*
 * 企业项目投资完成情况
 * "orgId": 0,
 * "year": ""
 **/
export const projectCompletion = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/invest/statistics/project-completion', params)
}
/**
 *
 * @param params
 * @returns 用户信息
 */
export const userInfo = (params: any): Promise<RemoteResponse> => {
  return request('/fht-admin/info', params, {
    method: 'GET'
  })
}

/*
 * 项目投资总况
 */
export const projectOverview = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/invest/statistics/overview', params)
}
