// 承租方资格条件
<template>
  <section class="lessee-qualifications">
    <!-- 资格条件信息 -->
    <el-descriptions class="margin-top"
      title=""
      :column="4"
      :labelStyle="{
        width: '140px'
      }"
      border>
      <el-descriptions-item label="标的状态"
        :span="2">
        <span>{{detail.currentAsset.subjectStatusDesc||"无"}}</span>
        <!-- <el-radio-group v-model="detail.currentAsset.subjectStatus" :disabled="true">
          <el-radio :label="1">空置</el-radio>
          <el-radio :label="2">
            使用
            <span v-if="detail.status == 1">
              （原合同已到期/原合同于
              <el-date-picker v-model="detail.currentAsset.expiryDate"
                :disabled="true"
                type="date"
                placeholder="选择日期"></el-date-picker>
              截止）
            </span>
          </el-radio>
          <el-radio :label="99">其他</el-radio>
        </el-radio-group> -->
      </el-descriptions-item>
      <el-descriptions-item label="是否分别计价"
        :span="2">
        {{detail.currentAsset.isPricingSeparatelyDesc}}
      </el-descriptions-item>
      <el-descriptions-item label="原承租人名称"
        :span="2">
        {{ detail.currentAsset.originalLessee }}
      </el-descriptions-item>
      <el-descriptions-item label="是否有优先承租权"
        :span="2">
        {{detail.currentAsset.isPriorityLeaseDesc}}
      </el-descriptions-item>

      <el-descriptions-item :span="2"
        label="租金挂牌价(元)">
        {{ detail.currentAsset.rentListingPrice }}
      </el-descriptions-item>
      <el-descriptions-item :span="2"
        label="租金挂牌价单位">
        {{ detail.currentAsset.listingPriceUnitDesc }}
      </el-descriptions-item>
      <el-descriptions-item :span="2"
        label="房屋坐落">
        {{ detail.currentAsset.location }}
      </el-descriptions-item>
      <el-descriptions-item label="出租面积(可租赁面积)"
        :span="2">
        {{ detail.currentAsset.rentalArea }}
      </el-descriptions-item>

      <el-descriptions-item label="租金支付要求"
        :span="2">
        {{ detail.currentAsset.paymentRequirements }}
      </el-descriptions-item>

      <el-descriptions-item label="房产使用用途要求"
        :span="2">
        {{ detail.currentAsset.useRequirementsDesc }}
      </el-descriptions-item>

      <el-descriptions-item label="租期"
        :span="2">
        {{ detail.currentAsset.leaseTerm }}
      </el-descriptions-item>

      <el-descriptions-item label="免租期"
        :span="2">
        {{ detail.currentAsset.hasRentFreePeriodDesc }}
      </el-descriptions-item>

      <el-descriptions-item label="是否包含在租期内"
        :label-style="{
          width: '120px'
        }"
        :span="2">
        {{ detail.currentAsset.inLeaseTermDesc }}
      </el-descriptions-item>

      <el-descriptions-item label="竞拍保证金(万)"
        :span="2">
        {{ detail.currentAsset.bidSecurity }}
      </el-descriptions-item>

      <el-descriptions-item label="交易方式"
        :span="2">
        {{detail.lessee.tradingMethodDesc}}
        <el-input v-if="detail.currentAsset.tradingMethod === 3"
          disabled
          v-model="detail.currentAsset.otherTradingMethod"
          placeholder="请输入" />
      </el-descriptions-item>

      <el-descriptions-item label="征集到承租方后"
        :span="2">
        {{detail.lessee.tenantSolicitedDesc}}
        <!-- <el-radio-group v-model="detail.currentAsset.tenantSolicited" disabled>
      <el-radio :key="item.value" :label="ChangeN(item.value)" v-for="item in getDictData('tenant_solicited')">{{item.label}}</el-radio>
        </el-radio-group> -->
      </el-descriptions-item>

      <el-descriptions-item label="未征集到承租方后"
        :span="2">
        {{detail.lessee.tenantUnsolicitedDesc}}
        <!-- <el-radio-group v-model="detail.currentAsset.tenantUnsolicited" disabled>
           <el-radio :key="item.value" :label="ChangeN(item.value)" v-for="item in getDictData('tenant_un_solicited')">{{item.label}}</el-radio>

        </el-radio-group> -->
      </el-descriptions-item>

      <el-descriptions-item label="信息公告期(工作日)"
        :span="2">
        {{ detail.lessee.announcementPeriod }}
      </el-descriptions-item>
      <el-descriptions-item label="加价幅度"
        :span="2">
        {{ detail.currentAsset.markupRange }}
      </el-descriptions-item>

      <el-descriptions-item label="是否会同出租方审查"
        :span="2">
        {{ detail.lessee.isReviewedWithLessorDesc }}

      </el-descriptions-item>

      <!-- <el-descriptions-item label="项目名称" :span="4">
        {{ detail.lessee.otherDisclosure }}
      </el-descriptions-item>
         <el-descriptions-item label="项目联系地址" :span="4">
        {{ detail.lessee.otherDisclosure }}
      </el-descriptions-item> -->
      <el-descriptions-item label="约定工作日"
        :span="2">
        {{ detail.lessee.agreedWorkingDay }}
      </el-descriptions-item>
      <el-descriptions-item label="延期周期"
        :span="2">
        {{ detail.lessee.extensionPeriod }}
      </el-descriptions-item>
      <!-- <el-descriptions-item label=""
        :span="2">
        {{ detail.lessee.extensionPeriod }}
      </el-descriptions-item> -->

      <el-descriptions-item label="承租方资格条件"
        :span="4">
        {{ detail.lessee.lesseeEligibilityCriteria }}
      </el-descriptions-item>
      <el-descriptions-item label="公告期限及延牌规则"
        :span="4">
        {{ detail.lessee.rules }}
      </el-descriptions-item>
      <el-descriptions-item label="交易条件"
        :span="4">
        {{ detail.lessee.tradingCondition }}
      </el-descriptions-item>
      <el-descriptions-item label="保证金处理条款"
        :span="4">
        {{ detail.lessee.marginHandlingClause }}
      </el-descriptions-item>
      <el-descriptions-item label="其他披露事项"
        :span="4">
        {{ detail.lessee.otherDisclosure }}
        <!-- 迁移过来的字段 -->
      </el-descriptions-item>
      <el-descriptions-item label="出租方指定账号"
        contentClassName="form-group"
        :span="4">
        <el-descriptions class="margin-top"
          :column="4">
          <el-descriptions-item label="户名"
            :span="4">
            {{ detail.lessor.accountName }}
          </el-descriptions-item>
          <el-descriptions-item label="开户行"
            :span="4">
            {{ detail.lessor.depositBank }}
          </el-descriptions-item>

          <el-descriptions-item label="账户"
            :span="4">
            {{ detail.lessor.bankAccount }}
          </el-descriptions-item>
        </el-descriptions>
      </el-descriptions-item>

      <el-descriptions-item label="发票信息"
        contentClassName="form-group"
        :span="2">
        <el-descriptions class="margin-top"
          :column="4">
          <el-descriptions-item label="发票类型"
            :span="4">
            <el-tag v-if="detail.lessor.isPriorityLease !== ''"
              type="success">
              {{ detail.lessor.isPriorityLease ? '电子普票' : '专票' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="电子发票接受手机号"
            :span="4">
            {{ detail.lessor.receivePhone }}
          </el-descriptions-item>

          <el-descriptions-item label="电子邮件"
            :span="4">
            {{ detail.lessor.receiveEmail }}
          </el-descriptions-item>

          <el-descriptions-item label="开票备注"
            :span="4">
            {{ detail.lessor.invoicingRemark }}
          </el-descriptions-item>
        </el-descriptions>
      </el-descriptions-item>
    </el-descriptions>

    <!-- 附件列表 -->
    <AccessoryList v-model="detail.lessee.attachmentFileDTOList"
      dict="asset_lessee_attach">
      <el-popover slot="otherTip"
        placement="top-start"
        title="可上传"
        width="200"
        trigger="hover">
        报名所需材料清单及说明
        <br />

        竞买和承诺及规则须知
        <br />

        实地勘察证明
        <br />

        特别事项预定
        <br />
        <i slot="reference"
          style="color: #b43c3c; font-size: 16px; cursor: pointer"
          class="el-icon-question"></i>
      </el-popover>
    </AccessoryList>
  </section>
</template>

<script lang="ts">
import { deepClone } from '@/utils'
import { decisionTypeList, economicNatureList, priceUnitList } from '@/views/assets/filterOptions'
import { ElForm } from 'element-ui/types/form'
import { Component, Model, Prop, Vue } from 'vue-property-decorator'
import { TradeDetail } from '../TradeDetailDialog.vue'
import AccessoryList, { Accessory } from './AccessoryFileList.vue'
import { LesseeQualifications } from './LesseeQualificationsForm.vue'
import { BusinessModule } from '@/store/modules/businessDict'

type FileKey = 'attachmentFile1' | 'attachmentFile2' | 'attachmentFile3' | 'attachmentFile4'

@Component({
  components: {
    AccessoryList
  }
})
export default class extends Vue {
  @Prop({ default: () => ({}) }) private initData!: TradeDetail

  private detail: any = {
    assetList: [],
    lessee: {},
    lessor: {},
    basic: {},
    currentAsset: {},
    verify: {}
  }

  private decisionTypeList = decisionTypeList
  private priceUnitList = priceUnitList
  private economicNatureList = economicNatureList

  private accessoryList: Accessory[] = [
    {
      fileName: '委托出租服务协议',
      prop: 'attachmentFile1',
      isRequired: true,

      fileList: []
    },
    {
      fileName: '租赁合同样稿',
      prop: 'attachmentFile2',
      isRequired: true,
      fileList: []
    },
    {
      fileName: '浙交汇意向方报名所需文件',
      prop: 'attachmentFile3',
      isRequired: false,
      fileList: []
    },
    {
      fileName: '其他文件',
      prop: 'attachmentFile4',
      isRequired: false,
      slotName: 'otherTip',
      fileList: []
    }
  ]

  created() {
    this.detail = deepClone(this.initData)
  }
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  private ChangeN(string: any) {
    return Number(string)
  }
  public validate(): Promise<boolean> {
    let form = this.$refs.LesseeQualificationsForm as ElForm
    return form.validate()
  }
}
</script>


<style scoped lang="scss">
::v-deep .el-radio__label {
  line-height: 36px;
}
::v-deep .el-form-item {
  margin-bottom: 0px !important;
}
::v-deep .form-group {
  .el-form-item {
    margin-bottom: 20px !important;
  }
}

::v-deep .el-descriptions-row {
  td {
    min-width: 150px;
  }
}
</style>