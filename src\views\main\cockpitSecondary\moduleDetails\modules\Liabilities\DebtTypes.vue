/* 各个集团带息负债率、负债预警率 */

<template>
  <section class="owners-equity-wrap">
    <CommonTitle :title="title"
      :isRotuerThrow="true"
      :routerObj="{
        bizAppCode: 'finance',
        detailsPath: '/wel/index?bizAppCode=finance&path=/business/finance/debtStatistics/search/stateEntriseSum'
      }" />

    <div class="halo" />

    <!-- 是否排除金华银行 -->
    <div v-if="orgCode === '0' || orgCode === 'JH1200'"
      class="tabs-box">
      <el-checkbox v-model="exclude"
        :true-label="0"
        :false-label="1"
        style="z-index: 2000;"
        >{{getExcludeText}}</el-checkbox>
    </div>

    <!-- 视图 -->
    <div class="pie-pric-box">
      <h6 class="title">总额</h6>
      <p class="pric">
        <CountTo :decimals="2"
          :startVal='0'
          :endVal='+getBigNumberFormat(assetsPric)'
          :duration='1500' />
        <i>亿元</i>
      </p>
    </div>

    <div v-loading="loading"
      class="content-box"
      ref="refEcharts" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { echartConfigure, colorSixList } from '@/views/main/cockpitcren/baseData'
import { simpleSummary } from '@/api/cockpit'
import { Loading } from '@/decorators'
import { bigNumberFormat } from '@/utils'
import * as echarts from 'echarts'
import CountTo from 'vue-count-to'
import CommonTitle from '@/views/main/cockpitSecondary/components/CommonTitle.vue'

type EChartsOption = echarts.EChartsOption

@Component({
  components: {
    CountTo,
    CommonTitle
  }
})
export default class extends Vue {
  @Prop() private title!: string // 标题
  @Prop() private orgCode!: string // 集团
  @Prop() private year!: string // 年份
  @Prop() private moon?: string // 月份

  private exclude = 0
  private loading = false
  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private seriesData: any[] = []
  private option: EChartsOption = {}
  private assetsPric = ''

  // 数据变化，渲染视图
  @Watch('orgCode', { deep: true })
  @Watch('year', { deep: true })
  @Watch('moon', { deep: true })
  @Watch('exclude', { deep: true })
  private changeEchartsData() {
    this.initData()
  }

  // 金华银行tab切换文案改变
  get getExcludeText() {
    let text = '金华银行'

    if (this.orgCode === '0') text = '金华银行'
    if (this.orgCode === 'JH1200') text = '大合并'

    return text
  }

  // 万元数据转化成亿元
  get getBigNumberFormat() {
    return (num: string | number) => {
      return bigNumberFormat(+num).value || 0
    }
  }

  // 组件初始化
  private mounted() {
    this.chartDom = this.$refs.refEcharts as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let exclude = this.orgCode == '0' || this.orgCode == 'JH1200' ? this.exclude : undefined

    let { data } = await simpleSummary({
      year: this.year,
      exclude: exclude,
      orgCode: this.orgCode
    })

    data = data.debtBlockVo

    Array.isArray(data.list) &&
      data.list.forEach((item: any) => {
        item.value = this.getBigNumberFormat(item.value)
      })

    // 组装echarts数据
    this.assetsPric = data.total
    this.seriesData = data.list
    this.initEcharts()
  }

  // 初始化数据
  private initEcharts() {
    let textSize = echartConfigure.textSize
    let seriesData = this.seriesData

    this.option = {
      color: colorSixList,
      tooltip: {
        trigger: 'item',
        backgroundColor: '#00000000',
        shadowColor: '#00000000',
        borderWidth: 0,
        formatter: (params: any) => {
          let data = params.data
          let dom = ''

          if (+data.rote > 0) {
            dom = `
              <div style="display: flex; justify-content: space-between;">
                <span style="padding-right: 40px;">同比</span>
                <span style="color: #ff6267;">
                  <img style="width:24px;" src="${require('../../../images/thows.png')}" />${data.tbRate}%
                </span>
              </div>
            `
          } else {
            dom = `
              <div style="display: flex; justify-content: space-between;">
                <span style="padding-right: 40px;">同比</span>
                <span style="color: #00a92b;">
                  <img style="width:24px;" src="${require('../../../images/thowx.png')}" />${data.tbRate}%
                </span>
              </div>
            `
          }

          return `
            <div style="font-size:40px; line-height: 70px; color: #fff; border-radius:10px;border:2px solid #0C3EB6; padding: 20px; display: flex;flex-direction: column; background: linear-gradient(0deg, rgba(0, 103, 177, 1), rgba(0, 103, 177, 1));">
              <div style="font-weight:bold;">${data.name}</div>
              <div style="display: flex; justify-content: space-between;">
                <span style="padding-right: 40px;">金额</span>
                <span>${data.value}亿元</span>
              </div>
              ${dom}
            </div>
          `
        }
      },
      grid: {
        left: '0%',
        right: '0%',
        top: '0%',
        bottom: '0%',
        containLabel: true
      },
      legend: {
        show: false
      },
      series: [
        {
          type: 'pie',
          radius: ['35%', '55%'],
          label: {
            show: true,
            alignTo: 'edge',
            color: '#fff',
            fontSize: textSize / 1.2,
            minMargin: 30,
            edgeDistance: 10,
            lineHeight: 11,
            formatter: function (params: any) {
              let dom = ''
              let names = params.data.name
              let values = params.data.value
              let tbRate = +params.data.tbRate

              if (tbRate > 0) {
                dom = `{name|${names}}\n{pric|${values}}{unit|亿元}\n{tbs|同比 +${tbRate}%}`
              } else if (tbRate < 0) {
                dom = `{name|${names}}\n{pric|${values}}{unit|亿元}\n{tbx|同比 ${tbRate}%}`
              } else {
                dom = `{name|${names}}\n{pric|${values}}{unit|亿元}`
              }

              return dom
            },
            rich: {
              name: {
                fontSize: 30,
                padding: [0, 0, 50, 0],
                fontWeight: 'bold'
              },
              pric: {
                color: '#40EEFF',
                fontSize: 40,
                fontWeight: 'normal',
                fontFamily: 'PangMenZhengDao'
              },
              unit: {
                color: '#40EEFF',
                fontSize: 26,
                padding: [10, 0, 0, 4],
                fontFamily: 'PangMenZhengDao'
              },
              tbs: {
                color: '#ff6267',
                fontSize: 30,
                padding: [60, 0, 0, 0]
              },
              tbx: {
                color: '#00a92b',
                fontSize: 30,
                padding: [60, 0, 0, 0]
              }
            }
          },
          labelLine: {
            lineStyle: {
              width: 3
            }
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          data: seriesData
        },
        {
          type: 'pie',
          radius: ['35%', '55%'],
          data: seriesData,
          label: {
            show: true,
            position: 'inside',
            color: '#fff',
            fontSize: 24,
            fontWeight: 'normal',
            fontFamily: 'PangMenZhengDao',
            formatter: (params) => {
              return params.percent ? `${params.percent}%` : ''
            }
          }
        }
      ]
    }

    this.myChart && this.myChart.setOption(this.option, true)
  }
}
</script>

<style scoped lang="scss">
.owners-equity-wrap {
  position: relative;
  height: 100%;
  @keyframes move {
    0% {
      transform: translate(-50%, -50%) rotateZ(0deg);
    }
    50% {
      transform: translate(-50%, -50%) rotateZ(360deg);
    }
    100% {
      transform: translate(-50%, -50%) rotateZ(0deg);
    }
  }

  .content-box {
    position: relative;
    z-index: 10;
    width: 100%;
    height: 89%;
    box-sizing: border-box;
    transform: translateY(-34px);
  }

  .pie-pric-box {
    position: absolute;
    top: 343px;
    width: 100%;
    text-align: center;
    h6,
    p {
      margin: 0;
    }

    .title {
      font-size: 42px;
    }
    .pric {
      color: #40eeff;
      span {
        font-size: 40px;
        font-weight: normal;
        font-family: 'PangMenZhengDao';
      }
      i {
        margin-left: -19px;
        font-weight: normal;
        font-family: 'PangMenZhengDao';
      }
    }

    .s {
      color: #ff6267;
    }
    .x {
      color: #00a92b;
    }
  }

  .tabs-box {
    position: absolute;
    right: 0;
    top: 100px;
    font-size: 34px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    span {
      margin-right: 20px;
    }

    ::v-deep .el-checkbox {
      transform: scale(3) translateX(-28px);
      .el-checkbox__label {
        color: #ccc;
        padding-left: 5px;
      }
      .el-checkbox__inner {
        background-color: #ccc;
        border-color: #ccc;
      }
      .el-checkbox__input.is-checked .el-checkbox__inner {
        background-color: #2dc4d3;
        border-color: #2dc4d3;
      }
    }

    ::v-deep .is-checked {
      .el-checkbox__label {
        color: #00ffff;
      }
    }
  }

  .halo {
    position: absolute;
    z-index: 1;
    top: 402px;
    left: 50%;
    width: 430px;
    height: 430px;
    transform: translate(-50%, -50%);
    background: url('../../../images/halo.png');
    background-size: 100% 100%;
    will-change: width, height, transform;
    //animation: move 20s linear forwards infinite;
  }
}
</style>


