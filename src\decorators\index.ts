/*
* 类的装饰器函数
*/
import { createDecorator } from 'vue-class-component'

/*
 * Loading
 * 引入：import { Loading } from '@/decorators'
 * 调用：@Loading('你的 loading')
 */
export const Loading = function (loading: string) {
  return createDecorator((options: any, key: string) => {
    const originalMethod = options.methods[key]
    options.methods[key] = async function (...args: any[]) {
      this[loading] = true
      try {
        await originalMethod.apply(this, args)
        this[loading] = false
      } catch {
        this[loading] = false
      }
    }
  })
}

/*
 * confirm 确认&取消
 * 引入：import { Confirm } from "@/decorators"
 * 调用：@Confirm({
 *  title: "标题",
 *  content: "提示内容",
 *  confirmButtonText: "确认",
 *  cancelButtonText: "取消",
 *  type: "success" | "info" | "warning" | "error"
 *  center: 是否居中布局
 *  showClose: 是否显示右上角关闭按钮
 *  showCancelButton: 是否显示取消按钮
 *  showConfirmButton: 是否显示确定按钮
 *  closeOnClickModal: 是否可通过点击遮罩关闭
 *  roundButton: 是否使用圆角按钮
 * }, function (_this: any) { ...点击取消后的回调函数 })
 */
interface ConfirmParams {
  title?: string
  content: string
  confirmButtonText?: string
  cancelButtonText?: string
  type?: 'success' | 'info' | 'warning' | 'error'
  center?: boolean
  showClose?: boolean
  showCancelButton?: boolean
  showConfirmButton?: boolean
  closeOnClickModal?: boolean
  roundButton?: boolean
}

export const Confirm = function (params: ConfirmParams, catchCallBack?: Function) {
  return createDecorator((options: any, key: string) => {
    const originalMethod = options.methods[key]
    options.methods[key] = function (...args: any[]) {
      this.$confirm(params.content, params.title || '提示', {
        confirmButtonText: params.confirmButtonText,
        cancelButtonText: params.cancelButtonText,
        showCancelButton: params.showCancelButton,
        showConfirmButton: params.showConfirmButton,
        type: params.type || 'warning',
        center: params.center,
        showClose: params.showClose || true,
        closeOnClickModal: params.closeOnClickModal,
        roundButton: params.roundButton,
        beforeClose: async (action: string, instance: any, done: Function) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            try {
              await originalMethod.apply(this, args)
              instance.confirmButtonLoading = false
            } catch (error) {
              instance.confirmButtonLoading = false
            }
            done()
          } else {
            done()
          }
        }
      }).catch(() => {
        catchCallBack && catchCallBack.call(this, this)
      })
    }
  })
}

/*
 * Notification 通知
 * 引入：import { Notice } from "@/decorators"
 * 调用：@Confirm({
 * title: "标题",
 * message: "说明文字",
 * type: "success" | "info" | "warning" | "error"
 * duration: 显示时间, 毫秒。设为 0 则不会自动关闭
 * position: 自定义弹出位置
 * showClose: 是否显示关闭按钮
 * offset: 偏移的距离
 * }, function (_this: any) { ...关闭后的回调函数 })
 */
interface NotificationParams {
  title?: string
  message: string
  type?: "success" | "info" | "warning" | "error"
  duration?: number
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
  showClose?: boolean
  offset?: number
}

export const Notice = function (params: NotificationParams, closeCallBack?: Function) {
  return createDecorator((options: any, key: string) => {
    const originalMethod = options.methods[key]

    options.methods[key] = async function (...args: any[]) {
      await originalMethod.apply(this, args)
      this.$notify({
        title: params.title || '提示',
        message: params.message,
        type: params.type || 'warning',
        duration: params.duration,
        position: params.position,
        showClose: params.showClose,
        customClass: 'custom-notice-wrapper',
        onClose() {
          closeCallBack && closeCallBack.call(this, this)
        }
      })
    }
  })
}

/*
 * Throttle 函数节流（定时触发）
 * 引入：import { Throttle } from "@/decorators";
 * 调用：@Throttle
 */
let throttleFn: any = null
function timeoutThrottleFn(fn: Function, args: any[], context: any) {
  if (throttleFn) clearTimeout(throttleFn)
  throttleFn = setTimeout(function () {
    fn.call(context, ...args)
    throttleFn = null
  }, 300)
}

export const Throttle = createDecorator((options: any, key: string) => {
  const originalMethod = options.methods[key]
  options.methods[key] = function (...args: any[]) {
    timeoutThrottleFn(originalMethod, args, this)
  }
})

/*
 * Debounce 函数防抖（只触发最后一次）
 * 引入：import { Debounce } from "@/decorators";
 * 调用：@Debounce
 */
let debounceFn: any = null
function timeoutDebounceFn(fn: Function, args: any[], context: any) {
  if (debounceFn) return
  debounceFn = setTimeout(function () {
    fn.call(context, ...args)
    debounceFn = null
  }, 300)
}

export const Debounce = createDecorator((options: any, key: string) => {
  const originalMethod = options.methods[key]
  options.methods[key] = function (...args: any[]) {
    timeoutDebounceFn(originalMethod, args, this)
  }
})
