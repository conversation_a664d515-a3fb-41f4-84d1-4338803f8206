<template>
  <div>
    <Dialog
       :title="'对外担保备案-'+getmode"
      width="1100px"
      :visible="visible"
      :v-loading="loading"
      :close-on-click-modal="true"
      :destroy-on-close="true"
      :append-to-body="true"
      @close="handleClose"
    >
      <div slot="body" class="">
        <div>
          <el-row :gutter="30">
            <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="110px" label-position="center">
              <el-col :span="8">
                <el-form-item label="单据编号" prop="documentNo">
                  <el-input
                    disabled
                    v-model="formData.documentNo"
                    placeholder="自动带入单据编号"
                    clearable
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="申请日期" prop="applicationDate">
                  <el-date-picker
                    v-model="formData.applicationDate"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    :style="{ width: '100%' }"
                    placeholder="请输入申请日期"
                    clearable
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="公司名称" prop="companyName">
                  <el-input v-model="formData.companyName" placeholder="请输入公司名称" clearable :style="{ width: '100%' }"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="法定代表人" prop="legalRepresentative">
                  <el-input
                    v-model="formData.legalRepresentative"
                    placeholder="请输入法定代表人"
                    clearable
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="公司地址" prop="companyAddress">
                  <el-input v-model="formData.companyAddress" placeholder="请输入公司地址" clearable :style="{ width: '100%' }"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="董事会决议文号" prop="boardResolutionNum">
                  <el-input
                    v-model="formData.boardResolutionNum"
                    placeholder="请输入董事会决议文号"
                    clearable
                    :style="{ width: '100%' }"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="所有者权益" prop="ownersEquity">
                  <inputNumber type="decimal" v-model="formData.ownersEquity" placeholder="请输入所有者权益" clearable :style="{ width: '100%' }">
                    <template slot="append">元</template>
                  </inputNumber>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="被担保人" prop="guarantor">
                  <el-input v-model="formData.guarantor" placeholder="请输入被担保人" clearable :style="{ width: '100%' }"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-row :gutter="30">
                  <el-col :span="8">
                    <el-form-item label="企业与被担保单位关系" prop="equityRelationship">
                      <!-- <el-input
                        v-model="formData.equityRelationship"
                        placeholder="请输入企业与被担保单位关系"
                        clearable
                        :style="{ width: '100%' }"
                      ></el-input> -->
                      <el-select v-model="formData.equityRelationship" placeholder="请选择企业与被担保单位关系">
                        <el-option
                          v-for="(item, index) in getDictData('equity_relationship')"
                          :key="index"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="持股比例" prop="shareholdingRatio">
                      <inputNumber max="100" type="decimal" v-model="formData.shareholdingRatio" placeholder="请输入持股比例" clearable :style="{ width: '100%' }">
                        <template slot="append">%</template>
                      </inputNumber>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="30">
                  <el-col :span="8">
                    <el-form-item label="担保金额" prop="guaranteeAmount">
                      <inputNumber type="decimal" v-model="formData.guaranteeAmount" placeholder="请输入担保金额" clearable :style="{ width: '100%' }">
                        <template slot="append">元</template>
                      </inputNumber>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="担保期限" prop="guaranteePeriod">
                      <inputNumber  v-model="formData.guaranteePeriod" placeholder="请输入担保期限">
                       <template slot="append">元</template>
                      </inputNumber>
                      <!-- <el-date-picker
                        type="year"
                        v-model="formData.guaranteePeriod"
                        format="yyyy"
                        value-format="yyyy"
                        :style="{ width: '100%' }"
                        placeholder="请输入担保期限"
                        clearable
                      ></el-date-picker> -->
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="累计对外担保金额" prop="accumulatedGuaranteeAmount">
                      <inputNumber type="decimal"
                        v-model="formData.accumulatedGuaranteeAmount"
                        placeholder="请输入累计对外担保金额"
                        clearable
                        :style="{ width: '100%' }"
                      >
                        <template slot="append">元</template>
                      </inputNumber>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="30">
                  <el-col :span="24">
                    <el-form-item label="反担保措施" prop="counterGuaranteeMeasures">
                      <el-input
                        v-model="formData.counterGuaranteeMeasures"
                        type="textarea"
                        placeholder="请输入反担保措施"
                        :autosize="{ minRows: 2, maxRows: 4 }"
                        :style="{ width: '100%' }"
                          maxlength="300"
                        show-word-limit
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="担保项目概况" prop="projectDesc">
                      <el-input
                        v-model="formData.projectDesc"
                        type="textarea"
                        placeholder="请输入担保项目概况"
                        :autosize="{ minRows: 2, maxRows: 4 }"
                        :style="{ width: '100%' }"
                          maxlength="300"
                        show-word-limit
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="备注" prop="remark">
                      <el-input
                        v-model="formData.remark"
                        type="textarea"
                        placeholder="请输入备注"
                        :autosize="{ minRows: 2, maxRows: 4 }"
                        :style="{ width: '100%' }"
                        maxlength="300"
                        show-word-limit
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
            </el-form>
          </el-row>
          <AccessoryList v-model="formData.attachmentFileDTOList" title="附件列表" dict="financial_externalGuarantee_attach" :mode="mode=='see'?'see':'upload'" class="m-20" />
        </div>
      </div>
      <div slot="footer">
            <el-button v-if="mode=='see'" @click="closeDialog">关闭</el-button>
        <el-button v-if="mode!='see'" @click="handleClose">取消</el-button>
        <el-button v-if="mode!='see'" @click="submitForm" type="primary">{{ Diaformdata.id == '' ? '提交' : '确定修改' }}</el-button>
        <!-- <el-button @click="resetForm" class="finance_white_btn">重置</el-button> -->
      </div>
    </Dialog>
    <!-- 上传文件 -->
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import AccessoryList, { Accessory } from './AccessoryFileList.vue'
import { Confirm } from '@/decorators/index'
import Dialog from '@/components/Dialog/index.vue'
import { AddExternalGuarantee, DEtailExternalGuarantee } from '@/api/finance'
import { getYear } from '@/utils/cache'
import { serialNo } from '@/api/finance'
import inputNumber from "@/components/FormComment/inputNumber.vue"
  import { BusinessModule } from '@/store/modules/businessDict'

// import { equityRelationship } from '../filterOptions'

@Component({
  components: {
    Dialog,
    AccessoryList,
    inputNumber
  }
})
export default class Container extends Vue {
  @Prop() private visible!: boolean
  @Prop() private Diaformdata!: any
  @Prop({default:'add'}) private mode!: "see"|'edit'|'add'

  // 文件上传
  private uploaderDlgVisible = false
  private uploaderDlgTitle = '文件上传'
  private uploaderDlgMode = 'upload' //download  upload  see
  private uploadInitList: Array<string> = []
  private currentRow: any = {}
  private uploadtype!: string
  // upload end
  private loading = false
  private equityRelationshipSelect = [
    {
      value: 1,
      label: '全资子公司'
    },
    {
      value: 2,
      label: '控股子公司'
    },
    {
      value: 3,
      label: '参股公司'
    },
    {
      value: 4,
      label: '无股权关系业务关联企业'
    },
    {
      value: 5,
      label: '其他'
    }
  ]
  private rules: object = {
    documentNo: [
      {
        required: true,
        message: '自动带入单据编号',
        trigger: 'blur'
      }
    ],
    applicationDate: [
      {
        required: true,
        message: '请输入申请日期',
        trigger: 'change'
      }
    ],
    companyName: [
      {
        required: true,
        message: '请输入公司名称',
        trigger: 'blur'
      }
    ],
    legalRepresentative: [
      {
        required: true,
        message: '请输入法定代表人',
        trigger: 'blur'
      }
    ],
    companyAddress: [
      {
        required: true,
        message: '请输入公司地址',
        trigger: 'blur'
      }
    ],
    boardResolutionNum: [
      {
        required: true,
        message: '请输入董事决议文号',
        trigger: 'blur'
      }
    ],
    ownersEquity: [
      {
        required: true,
        message: '请输入所有者权益',
        trigger: 'blur'
      }
    ],
    guarantor: [
      {
        required: true,
        message: '请输入被担保人',
        trigger: 'blur'
      }
    ],
    equityRelationship: [
      {
        required: true,
        message: '请输入企业与被担保单位关系',
        trigger: 'change'
      }
    ],
    shareholdingRatio: [
      {
        required: true,
        message: '请输入持股比例',
        trigger: 'blur'
      }
    ],
    guaranteeAmount: [
      {
        required: true,
        message: '请输入担保金额',
        trigger: 'blur'
      },
      {
        pattern: /(^([-]?)[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^([-]?)(0){1}$)|(^([-]?)[0-9]\.[0-9]([0-9])?$)/,
        message: '请输入正确的金额！',
        trigger: 'blur'
      }
    ],
    guaranteePeriod: [
      {
        required: true,
        message: '请输入担保期限',
        trigger: 'change'
      }
    ],
    accumulatedGuaranteeAmount: [
      {
        required: true,
        message: '请输入累计对外担保金额',
        trigger: 'blur'
      }
    ],
    counterGuaranteeMeasures: [
      {
        required: true,
        message: '请输入反担保措施',
        trigger: 'blur'
      }
    ],
    projectDesc: [
      {
        required: true,
        message: '请输入担保项目概况',
        trigger: 'blur'
      }
    ],
    remark: [
      {
        required: false,
        message: '请输入备注',
        trigger: 'blur'
      }
    ]
  }
  //   private formData = this.Diaformdata
  private formData: any = {
    documentNo: '',
    decisionMakingBody: '',
    publisher: '',
    referenceNo: '',
    issuer: '',
    issuanceBondsReason: '',
    issuancePurpose: '',
    issuanceCurrency: '',
    filler: '',
    contactPhone: '',
    creditEnhancementMeasures: '',
    restitutionMeasures: '',
    otherSupplements: '',
    bondsType: '',
    // BigDecimal: '',
    mainUses: '',
    validityStartDate: '',
    expiryDate: '',
    issuanceRate: '',
    issuanceTimeNumber: '',
    issuanceForm: '',
    issuanceTarget: '',
    equityRelationship: '',
    companyName: '',
 
    attachmentFileDTOList: [],
    year: '',
    guaranteePeriod: '',
  }
  private rules1: {} = {}
  private issuanceTimeNumberOptions: object[] = [
    {
      label: '一次',
      value: 1
    },
    {
      label: '多次',
      value: 2
    }
  ]
  private issuanceFormOptions: object[] = [
    {
      label: '境内',
      value: 1
    },
    {
      label: '境外',
      value: 2
    }
  ]
  private issuanceTargetOptions: object[] = [
    {
      label: '机构',
      value: 1
    },
    {
      label: '自然人',
      value: 2
    },
    {
      label: '其他',
      value: 3
    }
  ]
  // 打开弹窗
  private accessoryList: Accessory[] = [
    {
      fileName: '债券发行方案',
      isRequired: true,
      prop: 'attachmentFile1',
      fileList: []
    },
    {
      fileName: '董事会决议文件',
      isRequired: true,
      prop: 'attachmentFile2',
      fileList: []
    },
    {
      fileName: '发债请示文件',
      isRequired: true,
      prop: 'attachmentFile3',
      fileList: []
    }
  ]
  created() {
     if(this.mode=="see"||this.mode=='edit'){
        this.getdetail()
    }
      this.openDiaHandle()
    this.formData = Object.assign(this.formData, this.Diaformdata)
    this.formData.guaranteePeriod=this.formData.guaranteePeriod+""
    let investProjectPlanFiling = this.formData
    // this.accessoryList.forEach((item) => {
    //   let url = this.formData[item.prop as keyof typeof investProjectPlanFiling]
    //   item.fileList = url
    // })
  }
  get getmode(){
    switch(this.mode){
      case "see":
        return "查看"
      case "edit":
        return "编辑"
      default:
        return "新增"
    }
  }
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  private openDiaHandle() {
    try {
      if (this.Diaformdata.id == '' || this.Diaformdata.id == undefined) {
        this.getSerialNo(6)
        ;(this.$refs['elForm'] as any).resetFields()
        return ''
      } else {
        // this.getdetail()
      }
    } catch (error) {
      return ''
    }
  }
  private closeDialog(){
     this.$emit('changshowDialogAdd', false)
  }
  private async getdetail() {
    try {
      let res: any = await DEtailExternalGuarantee({
        id: this.Diaformdata.id + ''
      })
      if (res.success) {
        res.data.year = res.data.year + ''
        res.data.guaranteePeriod = res.data.guaranteePeriod + ''
        this.formData = res.data
      }
    } catch (e) {
      console.error(e)
    }
  }
  private async getSerialNo(id: number) {
    try {
      let res: any = await serialNo({
        index: id
      })
      this.formData.documentNo = res.data
    } catch (e) {
      this.$message.info('获取编号失败')
    }
  }
  //  文件上传事件
  get uploadable() {
    return this.uploaderDlgMode === 'upload'
  }
  get fileList() {
    return this.currentRow.fileList
  }
  private uploadfile(value: string) {
    this.currentRow.fileList = []
    this.uploadtype = value
    this.uploaderDlgVisible = true
  }
  private lookfile(value: string) {
    this.currentRow = {
      fileName: '附件',
      fileList: [value],
      isRequired: true
    }
    this.uploaderDlgVisible = true
  }
  private onFilesChange(fileList: string[]) {
    this.currentRow.fileList = [...fileList]
  }
  // 文件上传成功写入fromdata
  private handleUploadComplete(fileList: Array<any>) {
    // this.formData[this.uploadtype] = fileList[0].url
    // Object.assign(this.formData,{})
    this.$set(this.formData, this.uploadtype + '', fileList[0].url)
    //
  }
  // 新增表单
  // 拉取接口远程数据
  @Confirm({
    title: '提示',
    content: `是否提交？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private async AddExternalGuarantee() {
    try {
      let params: any = { ...this.formData }
      params.year = getYear()
      params.guaranteePeriod = Number(params.guaranteePeriod)
      let res = await AddExternalGuarantee(params)
      if (res.success) {
        this.$message.success('提交成功')
        this.$emit('changshowDialogAdd', false)
      }
    } catch (e) {
      console.error(e)
    }
  }
  private submitForm() {
    if (!this.validateFiles()) return
    ;(this.$refs['elForm'] as any).validate((valid: any) => {
      if (!valid) return
        if(this.formData.attachmentFileDTOList.length==0){
        this.$message.warning("请上传附件")
        return
      }
      this.AddExternalGuarantee()
      // TODO 提交表单
    })
  }
  ///附件校验 转换
  private validateFiles(): boolean {
    // let investProjectPlanFiling = this.formData
    // for (let index in this.accessoryList) {
    //   let accessory = this.accessoryList[index]
    //   let fileUrl = accessory.fileList.map((item: any) => item.id)
    //   this.formData[accessory.prop as keyof typeof investProjectPlanFiling] = fileUrl
    //   if (fileUrl.length == 0 && accessory.isRequired) {
    //     this.$message.warning(`请上传${accessory.fileName}！`)
    //     return false
    //   }
    // }
    return true
  }
  private resetForm() {
    ;(this.$refs['elForm'] as any).resetFields()
  }
  @Confirm({
    title: '提示',
    content: `是否关闭？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private handleClose() {
    this.$emit('changshowDialogAdd', false)
  }
}
</script>

<style lang="scss" scoped>
::v-deep.el-select{
  width: 100%;
}
::v-deep.el-input__inner{
  width: 100% !important;
}
::v-deep.el-form-item--medium .el-form-item__label {
  line-height: 20px;
}
::v-deep.el-form-item__label {
  float: none; // 取消label左浮动
  word-break: break-word; // 支持单词截断换行
}
::v-deep.el-input--medium .el-input__inner {
  height: auto !important;
  line-height: auto;
}
/* 过于长的label分两行展示样式 */
::v-deep.fold_label .el-form-item__label {
  white-space: pre-line;
  text-align-last: justify;
  text-align: justify;
  margin-top: -4px;
  line-height: 25px;
  text-justify: distribute-all-lines;
}
/* 其他label根据宽度自动撑开 */
::v-deep.el-form-item__label {
  text-align-last: justify;
  text-align: justify;
  text-justify: distribute-all-lines;
}
::v-deep .el-input.is-disabled .el-input__inner{
  color: #2e2e2e;
  background-color: #fff;
}
</style>
