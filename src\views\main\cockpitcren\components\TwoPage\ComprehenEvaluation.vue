/* 国企发展指数 */

<template>
  <section ref="wrapper"
    class="cockpit-comprehen-evaluation-wrap"
    :class="{'cockpit-wrapperFull-wrap': isWrapperFull}">
    <!-- 头部区域 -->
    <TitleCom title="国企发展指数"
      module="CompositeIndex"
      :isFullScreen="true"
      :orgCode="orgActive.orgCode"
      @fullScreenHandle="fullScreenHandle" />

    <!-- 指标汇总数字 -->
    <div class="composite-box"
      @click="visibleDetail = true">
      <CountTo v-if="String(dataList.num).split('.').length === 1"
        :decimals="0"
        :startVal="0"
        :endVal="+dataList.num"
        :duration="1500"
        class="num"
        :class="textColor" />

      <CountTo v-else
        :decimals="2"
        :startVal="0"
        :endVal="+dataList.num"
        :duration="1500"
        class="num"
        :class="textColor" />
    </div>

    <!-- 各指标详情数据 -->
    <div v-if="visibleIndexDetail" class="index-list-box">
      <div class="head">
        <span>{{indexDetailTag}}</span>
      </div>

      <ul class="list">
        <li v-for="(item, index) of indexDetailData.list" :key="index">
          <span>{{item.label}}</span>
          <i>{{item.num}}</i>
        </li>
      </ul>
    </div>

    <!-- echarts 视图 -->
    <div v-loading="loading"
      class="refEcharts"
      ref="refEcharts" />

    <!-- 数据改变提示层 -->
    <img v-if="visibleFlicker"
      class="cockipt-bg-flicker"
      src="@/views/main/cockpitcren/images/panel_bg1_cg.png" />

    <!-- 弹窗：指标趋势图 -->
    <DialogCom v-if="visibleDetail"
      :visible.sync="visibleDetail"
      :closeModal="false"
      :year="params.year"
      :code="orgActive.orgCode"
      :title="`${getActiveName(orgActive.orgName)} 发展指数趋势图`"
      width="1000px">
      <IndexRunChart :year="params.year"
        :code="orgActive.orgCode" />
    </DialogCom>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { echartConfigure } from '@/views/main/cockpitcren/baseData'
import { financialIndicator, performanceEvaluation } from '@/api/cockpit'
import CountTo from 'vue-count-to'
import TitleCom from '@/views/main/cockpitcren/components/Public/TitleCom.vue'
import DialogCom from '@/views/main/cockpitcren/components/Public/DialogCom.vue'
import IndexRunChart from '@/views/main/cockpitcren/components/TwoPage/Components/IndexRunChart.vue'
import * as echarts from 'echarts'

type EChartsOption = echarts.EChartsOption

@Component({
  components: {
    TitleCom,
    CountTo,
    DialogCom,
    IndexRunChart
  }
})
export default class extends Vue {
  // 渲染数据
  private loading = false
  private visibleFlicker = false
  private visibleDetail = false
  private visibleIndexDetail = false
  private textColor = ''
  private dataList: {
    name: string
    num: string
    list: any[]
  } = {
    name: '',
    num: '',
    list: []
  }
  private indicatorData: any[] = []
  // 所属集团+时间切换 选中数据
  private orgActive: {
    orgCode: string
  } = {
    orgCode: '0'
  }
  private params = {
    year: String(new Date().getFullYear()),
    month: ''
  }

  // 是否放大展示
  private isWrapperFull = false

  // echarts 视图数据
  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private colorSource = ['']
  private seriesData: number[] = []
  private legendData: string[] = []
  private option: EChartsOption = {}
  private indexCialData = []
  private indexDetailData = []
  private indexDetailTag = '指标名称'
  private sourceList = Object.freeze([
    {
      label: '优秀',
      textColor: 'excellent',
      color: '#00F6FF'
    },
    {
      label: '良好',
      textColor: 'good',
      color: '#FFEA00'
    },
    {
      label: '平均',
      textColor: 'qualified',
      color: '#FF7F18'
    },
    {
      label: '较低',
      textColor: 'lower',
      color: '#903ff9'
    },
    {
      label: '较差',
      textColor: 'range',
      color: '#E72A73'
    }
  ])

  // 动态设置弹窗标题
  get getActiveName() {
    return (orgName: string) => {
      let name = orgName === '国资总况' ? '国企' : orgName
      return name
    }
  }

  // 初始化
  private mounted() {
    this.chartDom = this.$refs.refEcharts as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    this.listenerDate()
  }

  // 监听全局事件触发
  private listenerDate() {
    // 集团切换
    this.$bus.$on('BusTabsChange', (orgActive: any) => {
      this.orgActive = orgActive
      this.initData()
    })

    // 年份切换
    this.$bus.$on('BusYearTimeChange', (year: string) => {
      let yearDeep = ''
      yearDeep = year

      this.params.year = yearDeep
      this.initData()
    })
  }

  // 获取各指标细分数据
  @Loading('loading')
  private async financialInterfaceData() {
    let { data } = await financialIndicator({
      orgCode: this.orgActive.orgCode,
      year: this.params.year,
      type: 1,
    })

    this.indexCialData = data || []
  }

  // 获取渲染视图数据
  @Loading('loading')
  private async initData() {
    this.flickerHandle()
    this.financialInterfaceData()

    await performanceEvaluation({
      orgCode: this.orgActive.orgCode,
      year: this.params.year
    })
    .then((res) => {
      this.dataList = res.data

      // 组装数据
      let dataList = this.dataList.list || []
      let indicatorData: any[] = []
      let seriesData: number[] = []

      Array.isArray(dataList) &&
        dataList.forEach((item) => {
          seriesData.push(+item.num)
          indicatorData.push({
            name: item.label,
            max: 100,
          })
        })

      this.indicatorData = indicatorData
      this.seriesData = seriesData
      this.legendData = this.sourceList.map((item) => {
        return item.label
      })

      // 匹配颜色
      let findItem = this.sourceList.find((item) => {
        return item.label === this.dataList.name
      })

      if (findItem) {
        this.colorSource = [findItem.color]
        this.textColor = findItem.textColor
      }

      this.initEcharts()
    })
    .catch(() => {
        this.dataList = {
          name: '',
          num: '',
          list: []
        }
        this.indicatorData = []
        this.seriesData = []
      })
  }

  // 渲染 echarts 视图
  private initEcharts() {
    let indicatorData = this.indicatorData
    let seriesData = this.seriesData
    let legendData = this.legendData
    let textColor = this.colorSource[0]
    let tooltipData: any = Object.assign(
      {
        trigger: 'item',
      },
      echartConfigure.tooltipBody
    )

    let color = ['#3eeeff', '#FFEA00', '#FF7F18', '#903ff9', '#E72A73'].concat(this.colorSource)

    this.option = {
      color: color,
      tooltip: tooltipData,
      legend: {
        show: true,
        selectedMode: false,
        bottom: '0%',
        itemGap: 50,
        itemWidth: 20,
        itemHeight: 20,
        data: legendData,
        textStyle: {
          color: '#fff',
          fontSize: 34,
          padding: [0, 0, 0, 8]
        }
      },
      grid: {
        left: '0%',
        right: '0%',
        bottom: '0%',
        top: '0%',
        containLabel: true,
      },
      radar: {
        radius: '70%',
        triggerEvent: true,
        axisLabel: {
          fontSize: 14,
          color: '#fff',
          fontWeight: 'bold',
        },
        axisName: {
          color: '#fff',
          fontWeight: 'bold',
          fontSize: 32,
        },
        axisLine: {
          lineStyle: {
            color: '#3eeeff'
          }
        },
        splitLine: {
          lineStyle: {
            color: ['#062675']
          }
        },
        splitArea: {
          areaStyle: {
            color: ['#062675']
          }
        },
        indicator: indicatorData
      },
      animationDelay: function (idx) {
        return idx * 500
      },
      series: [
        {
          type: 'radar',
          data: [
            {
              name: '优秀',
              symbol: 'none',
              value: [100, 100, 100, 100, 100],
            },
            {
              name: '良好',
              symbol: 'none',
              value: [80, 80, 80, 80, 80]
            },
            {
              name: '平均',
              symbol: 'none',
              value: [60, 60, 60, 60, 60]
            },
            {
              name: '较低',
              symbol: 'none',
              value: [40, 40, 40, 40, 40]
            },
            {
              name: '较差',
              symbol: 'none',
              value: [20, 20, 20, 20, 20]
            },
            {
              name: '发展指数',
              symbol: 'rect',
              value: seriesData,
              label: {
                show: true,
                fontSize: 24,
                color: textColor,
                formatter: (data: any) => {
                  let labels = ''

                  if (+data.dimensionIndex === 0) {
                    if (+data.value >= 0 && +data.value <= 20) {
                      labels = `{a1|${data.value}}` // 较差
                    } else if (+data.value > 20 && +data.value <= 40) {
                      labels = `{a2|${data.value}}` // 较低
                    } else if (+data.value > 40 && +data.value <= 60) {
                      labels = `{a3|${data.value}}` // 平均
                    } else if (+data.value > 60 && +data.value <= 80) {
                      labels = `{a4|${data.value}}` // 良好
                    } else if (+data.value > 80 && +data.value <= 100) {
                      labels = `{a5|${data.value}}` // 优秀
                    } else {
                      labels = `{a|${data.value}}`
                    }
                  } else if (+data.dimensionIndex === 1) {
                    if (+data.value >= 0 && +data.value <= 20) {
                      labels = `{b1|${data.value}}` // 较差
                    } else if (+data.value > 20 && +data.value <= 40) {
                      labels = `{b2|${data.value}}` // 较低
                    } else if (+data.value > 40 && +data.value <= 60) {
                      labels = `{b3|${data.value}}` // 平均
                    } else if (+data.value > 60 && +data.value <= 80) {
                      labels = `{b4|${data.value}}` // 良好
                    } else if (+data.value > 80 && +data.value <= 100) {
                      labels = `{b5|${data.value}}` // 优秀
                    } else {
                      labels = `{b|${data.value}}`
                    }
                  } else if (+data.dimensionIndex === 2) {
                    if (+data.value >= 0 && +data.value <= 20) {
                      labels = `{c1|${data.value}}` // 较差
                    } else if (+data.value > 20 && +data.value <= 40) {
                      labels = `{c2|${data.value}}` // 较低
                    } else if (+data.value > 40 && +data.value <= 60) {
                      labels = `{c3|${data.value}}` // 平均
                    } else if (+data.value > 60 && +data.value <= 80) {
                      labels = `{c4|${data.value}}` // 良好
                    } else if (+data.value > 80 && +data.value <= 100) {
                      labels = `{c5|${data.value}}` // 优秀
                    } else {
                      labels = `{c|${data.value}}`
                    }
                  } else if (+data.dimensionIndex === 3) {
                    if (+data.value >= 0 && +data.value <= 20) {
                      labels = `{d1|${data.value}}` // 较差
                    } else if (+data.value > 20 && +data.value <= 40) {
                      labels = `{d2|${data.value}}` // 较低
                    } else if (+data.value > 40 && +data.value <= 60) {
                      labels = `{d3|${data.value}}` // 平均
                    } else if (+data.value > 60 && +data.value <= 80) {
                      labels = `{d4|${data.value}}` // 良好
                    } else if (+data.value > 80 && +data.value <= 100) {
                      labels = `{d5|${data.value}}` // 优秀
                    } else {
                      labels = `{d|${data.value}}`
                    }
                  } else if (+data.dimensionIndex === 4) {
                    if (+data.value >= 0 && +data.value <= 20) {
                      labels = `{e1|${data.value}}` // 较差
                    } else if (+data.value > 20 && +data.value <= 40) {
                      labels = `{e2|${data.value}}` // 较低
                    } else if (+data.value > 40 && +data.value <= 60) {
                      labels = `{e3|${data.value}}` // 平均
                    } else if (+data.value > 60 && +data.value <= 80) {
                      labels = `{e4|${data.value}}` // 良好
                    } else if (+data.value > 80 && +data.value <= 100) {
                      labels = `{e5|${data.value}}` // 优秀
                    } else {
                      labels = `{e|${data.value}}`
                    }
                  }

                  return labels
                },
                rich: {
                  a: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 0]
                  },
                  a1: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 0],
                    color: '#fb3f3f'
                  },
                  a2: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 0],
                    color: '#903ff9'
                  },
                  a3: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 0],
                    color: '#ff7f18'
                  },
                  a4: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 0],
                    color: '#ffea00'
                  },
                  a5: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 0],
                    color: '#00f6ff'
                  },

                  b: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 60, 0, 0]
                  },
                  b1: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 60, 0, 0],
                    color: '#fb3f3f'
                  },
                  b2: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 60, 0, 0],
                    color: '#903ff9'
                  },
                  b3: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 60, 0, 0],
                    color: '#ff7f18'
                  },
                  b4: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 60, 0, 0],
                    color: '#ffea00'
                  },
                  b5: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 60, 0, 0],
                    color: '#00f6ff'
                  },

                  c: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 100, -20, 0]
                  },
                  c1: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 100, -20, 0],
                    color: '#fb3f3f'
                  },
                  c2: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 100, -20, 0],
                    color: '#903ff9'
                  },
                  c3: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 100, -20, 0],
                    color: '#ff7f18'
                  },
                  c4: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 100, -20, 0],
                    color: '#ffea00'
                  },
                  c5: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 100, -20, 0],
                    color: '#00f6ff'
                  },

                  d: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, -20, 100]
                  },
                  d1: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, -20, 100],
                    color: '#fb3f3f'
                  },
                  d2: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, -20, 100],
                    color: '#903ff9'
                  },
                  d3: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, -20, 100],
                    color: '#ff7f18'
                  },
                  d4: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, -20, 100],
                    color: '#ffea00'
                  },
                  d5: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, -20, 100],
                    color: '#00f6ff'
                  },

                  e: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 40]
                  },
                  e1: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 40],
                    color: '#fb3f3f'
                  },
                  e2: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 40],
                    color: '#903ff9'
                  },
                  e3: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 40],
                    color: '#ff7f18'
                  },
                  e4: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 40],
                    color: '#ffea00'
                  },
                  e5: {
                    fontSize: 22,
                    textBorderWidth: 1,
                    textBorderColor: '#062675',
                    fontWeight: 'normal',
                    fontFamily: 'PangMenZhengDao',
                    padding: [0, 0, 0, 40],
                    color: '#00f6ff'
                  }
                }
              },
              lineStyle: {
                width: 6
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(62, 238, 255, 0.6)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(62, 238, 255, 0.2)'
                  }
                ])
              }
            }
          ]
        }
      ]
    }

    this.myChart && this.myChart.setOption(this.option, true)

    // 监听 click 事件
    this.myChart.on('click', (params:any) => {
      // 点击网格内容，弹窗详情
      if (params.componentType === 'series') {
        this.visibleDetail = true
        return
      } 

      // 点击网格name，显示详情
      // if (params.targetType === 'axisName') {
      //   this.getIndexData(params.name)
      //   return
      // } 
    })

    // 监听 mouseover 事件
    this.myChart.on('mouseover', (params:any) => {
      // 移日网格name，显示详情
      if (params.targetType === 'axisName') {
        this.getIndexData(params.name)
        return
      } 
    })

    // 监听 mouseover 事件
    this.myChart.on('mouseout', (params:any) => {
      this.visibleIndexDetail = false
    })
  }

  // 获取各指标详情数据
  private getIndexData(name: string) {
    if(!name) return
    type typeItem = {tag:string, list: any[]}

    this.indexDetailTag = name

    let find: typeItem = this.indexCialData.find((item:typeItem) => {
      return item.tag === name
    }) || {tag: '', list: []}

    if(find && Array.isArray(find.list) && find.list.length) {
      let findList = find.list[0]

      if(Array.isArray(findList.list)) {
        this.indexDetailData = findList
      }

      this.visibleIndexDetail = true
    }
  }

  // 进入全屏/退出全屏
  private fullScreenHandle(isFull: boolean) {
    this.isWrapperFull = isFull
  }

  // 触发背景闪烁效果
  private flickerHandle() {
    this.visibleFlicker = true
    setTimeout(() => {
      this.visibleFlicker = false
    }, 3000)
  }
}
</script>

<style scoped lang="scss">
.cockpit-comprehen-evaluation-wrap {
  position: relative;
  transition: 1s;
  width: 100%;
  height: 880px;
  background: url('../../images/panel_bg1.png') no-repeat left top;
  background-size: 100% 100%;
  .refEcharts {
    position: relative;
    z-index: 2;
    width: 100%;
    height: 680px;
    display: flex;
    align-content: center;
    justify-content: center;
    transform: translateY(-30px);
  }

  .composite-box {
    position: absolute;
    left: 50%;
    top: 445px;
    z-index: 10;
    text-align: center;
    cursor: pointer;
    transform: translate(-50%, 0%);
    h4 {
      margin: 0;
      font-size: 52px;
      text-shadow: 6px 6px 0px #333;
      -webkit-text-stroke: 1px #eee;
    }
    .num {
      font-size: 44px;
      line-height: 40px;
      font-weight: normal;
      font-family: 'PangMenZhengDao';
      text-shadow: 6px 6px 0px #333;
      -webkit-text-stroke: 1px #eee;
    }
    .excellent {
      color: #00f6ff;
    }
    .good {
      color: #ffea00;
    }
    .qualified {
      color: #ff7f18;
    }
    .lower {
      color: #903ff9;
    }
    .range {
      color: #fb3f3f;
    }
  }

  @keyframes indexDetailMove {
    0% {
      transform: translateX(540px);
      opacity: 0;
    }

    100% {
      transform: translateX(0);
      opacity: 1;
    }
  }

  .index-list-box {
    position: absolute;
    z-index: 1;
    bottom: 56px;
    left: -517px;
    width: 540px;
    border-radius: 10px 0 0 10px;
    padding: 20px 20px 16px;
    border: 1px solid #2eb6f6;
    box-sizing: border-box;
    box-shadow: 0 0 10px #333;
    background: rgba($color: #1474be, $alpha: 1);
    animation: indexDetailMove 0.6s ease;
    .head {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 20px;
      span {
        font-size: 34px;
        color: #ffcd36;
      }
      i {
        font-size: 50px;
        color: #ffcd36;
        cursor: pointer;
      }
    }
    .list {
      li {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
        &:nth-last-child(1) {
          margin-bottom: 0;
        }
      }
    }
  }
}

.cockpit-wrapperFull-wrap {
  z-index: 1000;
  transform-origin: top right;
  transform: scale(1.7);
  border-radius: 20px;
  background: #072979;
  box-shadow: 0 0 40px #000;
}
</style>