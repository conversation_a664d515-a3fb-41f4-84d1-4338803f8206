<template>
  <section v-loading="loading" class="assets-overviews-wrap">
    <div class="header-box">
      <el-form ref="form" :inline="true" :model="formData">
        <!-- <el-form-item>
          <el-cascader
            v-model="orgIdList"
            :props="{ label: 'deptName', value: 'deptCode' }"
            :options="compTree"
            :show-all-levels="false"
            clearable
            placeholder="公司名称"
            @change="changeOrgId"
          />
        </el-form-item> -->
        <el-form-item>
          <el-date-picker v-model="formData.year" type="year" placeholder="年份" value-format="yyyy"></el-date-picker>
        </el-form-item>
      </el-form>
    </div>

    <el-divider class="divider-box" />

    <div v-if="isShow" class="conter-box">
      <!-- 财务汇总 -->
      <CompanySummary :year="formData.year" :orgCode="formData.orgCode" />
      <CompanySummary1 :year="formData.year" :orgCode="formData.orgCode" />

      <!-- 企业发债,企业清产核资 -->
      <div class="modules">
        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>企业发债</span>
          </h4>
          <div class="cter">
            <BusinessTrend :year="formData.year" :orgCode="formData.orgCode" />
          </div>
        </div>

        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>企业清产核资</span>
          </h4>
          <div class="cter">
            <TransactionListing :year="formData.year" :orgCode="formData.orgCode" />
          </div>
        </div>
      </div>

      <!-- 企业资产减值,企业对外捐赠 -->
      <div class="modules">
        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>企业资产减值</span>
          </h4>
          <div class="cter">
            <ReceivableOverYears :year="formData.year" :orgCode="formData.orgCode" />
          </div>
        </div>

        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>企业对外捐赠</span>
          </h4>
          <div class="cter">
            <ContractExpires :year="formData.year" :orgCode="formData.orgCode" />
          </div>
        </div>
      </div>
      <!-- 对外担保 -->
      <div class="modules">
        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>大额资金出借</span>
          </h4>
          <div class="cter">
            <largeFunds :year="formData.year" :orgCode="formData.orgCode" />
          </div>
        </div>

        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>对外担保</span>
          </h4>
          <div class="cter">
            <externalGuarantee :year="formData.year" :orgCode="formData.orgCode" />
          </div>
        </div>
      </div>
      <!-- 财务快报 -->
      <div class="modules">
        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>财务快报</span>
          </h4>
          <div class="cter">
            <YearlyRateReturn :year="formData.year" :orgCode="formData.orgCode" @emitChangeArea="emitChangeArea" />
          </div>
        </div>
      </div>

      <!-- 财务年报 -->
      <div class="modules">
        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>财务年报</span>
          </h4>
          <div class="cter">
            <RevenueRateReturn :year="formData.year" :orgCode="formData.orgCode" :areaCode="areaCode" />
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { companyCodeList } from './baseData'
import { getAstCompTree } from '@/api/projectInvestment'
import BusinessTrend from './components/BusinessTrend.vue'
import CompanySummary from './components/CompanySummary.vue'
import CompanySummary1 from './components/CompanySummary1.vue'
import largeFunds from './components/largeFunds.vue'
import externalGuarantee from './components/externalGuarantee.vue'
import ContractExpires from './components/ContractExpires.vue'
import YearlyRateReturn from './components/YearlyRateReturn.vue'
import TransactionListing from './components/TransactionListing.vue'
import ReceivableOverYears from './components/ReceivableOverYears.vue'
import RevenueRateReturn from './components/RevenueRateReturn.vue'

@Component({
  components: {
    BusinessTrend,
    CompanySummary,
    CompanySummary1,
    ContractExpires,
    YearlyRateReturn,
    RevenueRateReturn,
    TransactionListing,
    ReceivableOverYears,
    largeFunds,
    externalGuarantee
  }
})
export default class extends Vue {
  private isShow = true
  private loading = false
  private timer: any = null
  private areaCode = ''
  private compTree = []
  private orgIdList = []
  private formData: {
    orgCode: string | null
    year: string | null
  } = {
    orgCode: '',
    year: ''
  }

  // 获取各集团列表数据
  get getCompanyCodeList() {
    return companyCodeList
  }

  // 数据初始化
  private created() {
    // this.formData.year = (this as any).$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 组件初始化
  private mounted() {
    this.getCompTree()

    window.addEventListener('resize', () => {
      clearTimeout(this.timer)
      this.isShow = false
      this.loading = true
      this.timer = setTimeout(() => {
        this.isShow = true
        this.loading = false
      }, 500)
    })
  }

  // 获取机构数组
  private async getCompTree() {
    let { data } = await getAstCompTree({ parentId: 0, deptCategory: 1 })
    this.compTree = data || []
  }

  // 所属单位改变，赋值 formData.orgCode
  private changeOrgId(val: string) {
    if (Array.isArray(this.orgIdList) && this.orgIdList.length) {
      this.formData.orgCode = this.orgIdList[this.orgIdList.length - 1]
    } else {
      this.formData.orgCode = null
    }
  }

  // 资产营收情况：区域改变时触发
  private emitChangeArea(code: string) {
    this.areaCode = code
  }

  // 组件销毁
  private destroyed() {
    clearTimeout(this.timer)
    this.timer = null
  }
}
</script>

<style scoped lang="scss">
.assets-overviews-wrap {
  position: relative;
  height: 100%;
  background: #fff;
  overflow-y: auto;
  padding: 10px 14px;
  box-sizing: border-box;
  h4,
  p {
    margin: 0;
  }
  .header-box {
    text-align: right;
  }
  .divider-box {
    margin: 0 0 20px;
  }
  .conter-box {
    position: relative;
    .modules {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      height: 22vw;
      margin-bottom: 20px;
      &:nth-last-child(1) {
        margin-bottom: 0;
      }
      .mode {
        flex: 1;
        height: 100%;
        margin-right: 20px;
        display: flex;
        padding: 10px;
        border-radius: 4px;
        box-sizing: border-box;
        justify-content: space-between;
        flex-direction: column;
        background: rgba(172, 196, 221, 0.1);
        &:nth-last-child(1) {
          margin-right: 0;
        }
        .til {
          display: flex;
          align-items: center;
          font-weight: normal;
          margin-bottom: 10px;
          color: rgba(245, 108, 108, 0.8);
          i {
            font-size: 20px;
            margin-right: 4px;
          }
          span {
            font-size: 17px;
          }
        }
        .cter {
          flex: 1;
        }
      }
    }
  }

  ::v-deep .echarts-dom-wrap {
    position: relative;
    height: 100%;
    .hide {
      opacity: 0;
    }
    .none {
      display: none !important;
    }
    .empty-none-data {
      position: absolute;
      top: 0;
      width: 100%;
      height: 100%;
      padding: 0;
    }
  }
}
</style>
