<template>
  <section class="alert-from-wrap">
    <!-- 内容区域 -->
    <div v-loading="loading"
      class="content-box">
      <div v-if="type === 'see'"
        class="readonly-make" />
      <el-form v-loading="loading"
        :model="ruleForm"
        :rules="rules"
        :inline="true"
        label-position="left"
        ref="ruleForm"
        label-width="80px"
        class="form-content">
        <!-- 预警名称、级别、状态等 -->
        <div class="mode-box">
          <el-form-item v-if="type === 'see'"
            prop="ruleNo"
            label="预警编号">
            <el-input v-model.trim="ruleForm.ruleNo"
              readonly
              class="input-default" />
          </el-form-item>
          <el-form-item label="预警名称"
            prop="ruleName">
            <el-input v-model.trim="ruleForm.ruleName"
              :clearable="false"
              :readonly="disabled"
              placeholder="请输入"
              class="input-medium" />
          </el-form-item>
        </div>
        <div class="mode-box">
          <el-form-item label="预警级别"
            prop="level">
            <el-select v-model="ruleForm.level"
              :clearable="false"
              :readonly="disabled"
              placeholder="请选择"
              class="input-default">
              <el-option v-for="item of getDictData('ewmLevel')"
                :key="item.value"
                :label="item.label"
                :value="+item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="预警状态"
            prop="ruleStatus">
            <el-select v-model="ruleForm.ruleStatus"
              :clearable="false"
              :readonly="disabled"
              placeholder="请选择"
              class="input-default">
              <el-option v-for="item of statusDicData"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="预警分类"
            prop="metricsId">
            <el-select v-model="ruleForm.metricsId"
              :clearable="false"
              :readonly="disabled"
              placeholder="请选择"
              class="input-default">
              <el-option v-for="item of tmetricDicData"
                :key="item.id"
                :label="item.name"
                :value="+item.id" />
            </el-select>
          </el-form-item>
        </div>

        <el-divider class="divider-box" />

        <!-- 规则策略 -->
        <div class="mode-box">
          <div class="title">
            <h3>规则策略</h3>
          </div>
          <RulePolicy v-model="ruleForm.ruleItemList"
            ref="refRulePolicy"
            :type="type"
            :disabled="disabled"
            :tmetricDicData="tmetricDicData"
            :metricsId="ruleForm.metricsId" />
        </div>

        <el-divider class="divider-box" />

        <!-- 策略执行 -->
        <!-- <div class="mode-box">
          <div class="title">
            <h3>策略执行</h3>
          </div>
          <el-form-item label-width="0"
            prop="contactType">
            <el-checkbox-group v-model="contactType"
              @change="changeContactType">
              <el-checkbox v-for="item of dictContactTypeData"
                :key="item.value"
                :label="item.value"
                :disabled="item.disabled"
                border>
                {{item.label}}
              </el-checkbox>
            </el-checkbox-group>
            <span v-if="contactType.includes('3')"
              class="info">选择【监管留查】后将屏蔽其他选项，再次点击取消后恢复！</span>
          </el-form-item>

          <br />


          <div v-if="contactType.length && (contactType.includes('1') || contactType.includes('2'))"
            class="excute-policy-contact">
            <h4 class="title">
              联系人信息
              <el-button v-if="type !== 'see'"
                type="text"
                icon="el-icon-circle-plus-outline"
                class="icon"
                @click="visibleAddContact = true">新增联系人</el-button>
            </h4>
            <el-descriptions v-if="ruleForm.ruleContactList.length"
              :column="3">
              <el-descriptions-item v-for="(item,index) of ruleForm.ruleContactList"
                :key="item.id"
                :label="item.name">
                <div class="policy-item">
                  <span>{{item.phone}}</span>
                  <el-button v-if="type !== 'see'"
                    type="text"
                    icon="el-icon-circle-close"
                    class="delete"
                    @click="deletePolicy(index)" />
                </div>
              </el-descriptions-item>
            </el-descriptions>
            <div v-else
              class="noop-data-box">
              {{type==='see'?'暂无数据':'请新增联系人'}}
            </div>
          </div>

     
          <div v-if="false && contactType.length"
            class="excute-policy-info">
            <h4 class="title">通知消息模版</h4>
            <div v-if="contactType.length">
              <el-descriptions v-for="(item,index) of notificationTemplate"
                :key="index"
                :column="1">
                <el-descriptions-item v-if="contactType.includes(item.value)"
                  :label="item.label">{{item.content}}</el-descriptions-item>
              </el-descriptions>
            </div>

            <div v-else
              class="noop-data-box">
              {{type==='see'?'暂无数据':'请勾选消息通知'}}
            </div>
          </div>
        </div>

        <el-divider class="divider-box" /> -->

        <!-- 自动处理设置 -->
        <div class="mode-box">
          <div class="title">
            <h3>自动处理设置</h3>
            <span>（系统自动下发预警通知，无需人员点击查看，预警信息自动下发到业务系统）</span>
          </div>
          <!-- <el-form-item label="自动处理"
            prop="autoDeal">
            <el-radio-group v-model="ruleForm.autoDeal"
              class="input-automatic-group">
              <el-radio v-for="item of dictAutoDealData"
                :key="item.value"
                :label="item.value"
                :readonly="disabled"
                border>{{item.label}}</el-radio>
            </el-radio-group>
          </el-form-item> -->

          <el-form-item v-if="+ruleForm.autoDeal === 1"
            label="处理设置"
            prop="day">
            <el-input-number v-model.trim="ruleForm.day"
              :max="999"
              :min="1"
              :readonly="disabled"
              placeholder="请输入"
              class="input-mini" />
            <span class="day-cycle">天后处理</span>
          </el-form-item>
        </div>

        <div class="mode-box">
          <el-form-item label="反馈时间"
            prop="expireDay">
            <InputNumber v-model="ruleForm.expireDay"
              type="decimalZero"
              placeholder="输入"
              class="input-day">
              <template slot="append">天</template>
            </InputNumber>
          </el-form-item>
        </div>

        <el-divider class="divider-box" />

        <!-- 预警说明、政策说明 -->
        <div class="mode-box">
          <el-form-item label="预警说明"
            prop="ruleDesc">
            <el-input v-model.trim="ruleForm.ruleDesc"
              :rows="3"
              :readonly="disabled"
              clearable
              type="textarea"
              placeholder="最多可输入300个字符..."
              maxlength="300"
              show-word-limit
              class="input-max" />
          </el-form-item>
        </div>
        <div class="mode-box">
          <el-form-item label="政策说明"
            prop="policyDesc">
            <el-input v-model.trim="ruleForm.policyDesc"
              :rows="3"
              :readonly="disabled"
              clearable
              type="textarea"
              placeholder="最多可输入300个字符..."
              maxlength="300"
              show-word-limit
              class="input-max" />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <!-- 底部按钮 -->
    <div class="footer-box">
      <template v-if="type==='add'">
        <el-button :loading="loading"
          type="primary"
          @click="validateForm">新 增</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </template>
      <template v-if="type==='edit'">
        <el-button :loading="loading"
          type="primary"
          @click="validateForm">提 交</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </template>
    </div>

    <!-- 新增联系人 -->
    <AddContact v-if="visibleAddContact"
      :visible.sync="visibleAddContact"
      @selectContactHandle="selectContactHandle" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { BusinessModule } from '@/store/modules/businessDict'
import { getAlertDetail, saveOrUpdate } from '@/api/prewarning'
import { alertTmetricDict } from '@/api/prewarning'
import { deepClone } from '@/utils'
import { Loading } from '@/decorators'
import { ElForm } from 'element-ui/types/form'
import RulePolicy from './RulePolicy.vue'
import AddContact from './AddContact.vue'
import InputNumber from '@/components/FormComment/inputNumber.vue'

@Component({
  components: {
    RulePolicy,
    AddContact,
    InputNumber
  }
})
export default class extends Vue {
  @Prop() private pointId!: string // 详情id
  @Prop({
    validator: (value: string) => {
      return ['add', 'edit', 'see'].indexOf(value) !== -1
    }
  })
  readonly type!: string // 组件类型(新增、编辑、查看)

  // 常量数据
  private statusDicData = Object.freeze([
    {
      label: '启用',
      value: 1
    },
    {
      label: '关闭',
      value: 0
    }
  ])
  private dictAutoDealData = Object.freeze([
    {
      label: '是',
      value: 1
    },
    {
      label: '否',
      value: 2
    }
  ])
  private dictContactTypeData = Object.freeze([
    {
      label: '系统消息通知',
      value: '1'
    },
    {
      label: '短信通知',
      value: '2'
    },
    {
      label: '监管留查',
      value: '3'
    },
    {
      label: '阻断',
      value: '4',
      disabled: true
    }
  ])

  private loading = false
  private disabled = false
  private visibleAddContact = false
  private pointsId = ''
  private contactType: string[] = []
  private tmetricDicData: any[] = []
  private notificationTemplate = [
    {
      label: '系统消息通知',
      value: '1',
      content:
        '模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容。'
    },
    {
      label: '短信消息通知',
      value: '2',
      content:
        '模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容，模版内容。'
    }
  ]
  private rules = {
    ruleName: [{ required: true, message: '请输入', trigger: 'blur' }],
    level: [{ required: true, message: '请选择', trigger: 'change' }],
    metricsId: [{ required: true, message: '请选择', trigger: 'change' }],
    ruleStatus: [{ required: true, message: '请选择', trigger: 'change' }],
    contactType: [{ required: true, message: '请选择', trigger: 'change' }],
    autoDeal: [{ required: true, message: '请选择', trigger: 'change' }],
    expireDay: [{ required: true, message: '请输入', trigger: 'change' }],
    smsTemplate: [{ required: true, message: '请输入', trigger: 'blur' }],
    day: [{ required: true, message: '请输入', trigger: 'blur' }]
  }
  private ruleForm: any = {
    ruleNo: '',
    ruleName: '',
    level: '',
    ruleStatus: '',
    metricsId: '',
    contactType: '',
    ruleDesc: '',
    policyDesc: '',
    autoDeal: '',
    expireDay: '',
    day: '',
    smsTemplate: '',
    ruleItemList: [],
    ruleContactList: []
  }

  // 获取字典项
  get getDictData(): Function {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }

  // hook
  private created() {
    this.initialData()
    this.getTmetricDicData()
  }

  // 初始化数据
  private initialData() {
    switch (this.type) {
      case 'add':
        this.disabled = false
        break
      case 'edit':
        this.disabled = false
        this.initData()
        break
      case 'see':
        this.disabled = true
        this.initData()
        break
    }
  }

  // 获取预警分类数据
  private async getTmetricDicData() {
    let { data } = await alertTmetricDict()

    let list: any[] = []
    if (Array.isArray(data) && data.length) {
      data.forEach((item) => {
        if (Array.isArray(item.children) && item.children.length) {
          list.push(item)
        }
      })
    }

    this.tmetricDicData = list
  }

  // 获取详情数据：编辑、查看模式
  @Loading('loading')
  private async initData() {
    let { data } = await getAlertDetail({
      id: this.pointId
    })
    data = data || {}

    // 处理下特殊数据
    this.contactType = data.contactType.split(',')

    // 赋值详情数据
    for (const key1 in this.ruleForm) {
      for (const key2 in data) {
        if (key1 === key2) {
          this.ruleForm[key1] = data[key2] || ''
          break
        }
      }
    }
  }

  // 策略执行change时，需要组装表单数据
  private changeContactType(val: any[]) {
    if (val.includes('3')) this.contactType = ['3']

    this.ruleForm.contactType = this.contactType.join(',')
    this.$nextTick(() => {
      ;(this.$refs.ruleForm as ElForm).clearValidate()
    })
  }

  // 新增联系人点击确认后
  private selectContactHandle(contactList: any[]) {
    let arr1 = deepClone(this.ruleForm.ruleContactList)
    let arr2 = deepClone(contactList)

    // 添加的联系人信息通过receiveUser值去重
    let newArr: any[] = arr1.concat(arr2)
    let deepNewArr: any[] = []

    for (let item1 of newArr) {
      let flag = true
      for (let item2 of deepNewArr) {
        if (item1.receiveUser === item2.receiveUser) {
          flag = false
        }
      }

      if (flag) deepNewArr.push(item1)
    }
    // end

    this.ruleForm.ruleContactList = deepNewArr
  }

  // 删除联系人
  private deletePolicy(index: number) {
    this.ruleForm.ruleContactList.splice(index, 1)
  }

  // 校验必填数据
  private validateForm() {
    ;(this.$refs.ruleForm as ElForm).validate((valid: boolean) => {
      if (valid) {
        this.submitForm()
      } else {
        this.$message.warning('请输入必填信息')
      }
    })
  }

  // 提交数据
  @Loading('loading')
  private async submitForm() {
    let validateRulePolicy = (this.$refs.refRulePolicy as RulePolicy).validateForm('save')

    if (!validateRulePolicy) {
      this.$message.warning('请输入必填信息')
      return
    }

    let id = this.type === 'add' ? '' : this.pointId
    let objData = Object.assign(
      {
        id: id
      },
      this.ruleForm
    )

    let res = await saveOrUpdate(objData)
    this.$emit('updataHandle')
    this.handleClose()
    this.$message.success(res.msg || '操作成功')
  }

  // 关闭弹窗
  private handleClose() {
    this.$emit('handleClose')
  }
}
</script>

<style scoped lang="scss">
.alert-from-wrap {
  position: relative;

  .content-box {
    position: relative;
    .readonly-make {
      position: absolute;
      left: 0;
      top: 0;
      z-index: 10;
      width: 100%;
      height: 100%;
    }
  }

  ::v-deep .el-form {
    .mode-box {
      .el-form-item:nth-last-child(1) {
        margin-right: 0 !important;
      }
    }
    .el-form-item {
      margin-right: 40px;
    }
  }

  ::v-deep .el-radio-group {
    .el-radio {
      margin-right: 0;
    }
  }

  ::v-deep .el-checkbox-group {
    .el-checkbox {
      margin-right: 0;
    }
  }

  ::v-deep .el-descriptions {
    .el-descriptions__body {
      background: none;
    }
    .el-descriptions-item {
      font-size: 13px;
      padding-bottom: 12px;
    }
  }

  .noop-data-box {
    padding: 0 10px 20px 10px;
    color: #959595;
    font-size: 13px;
    text-align: center;
  }

  .mode-box {
    .info {
      display: inline-block;
      color: #999;
      padding-top: 10px;
    }
    .title {
      display: flex;
      align-items: baseline;
      margin-bottom: 10px;
      h3 {
        font-size: 15px;
        margin: 0;
        margin-right: 4px;
      }
      span {
        color: #999;
        font-size: 12px;
      }
      .icon {
        padding: 0;
        margin-left: 20px;
      }
    }
  }

  .excute-policy-contact {
    margin-top: -10px;
    margin-bottom: 16px;
    padding: 13px 13px 0 13px;
    background: #f5f7fa;
    border-radius: 3px;
    .title {
      margin: 0;
      color: #333;
      font-weight: normal;
      padding-bottom: 14px;
    }
    .policy-item {
      position: relative;
      display: flex;
      align-items: center;
      .delete {
        padding: 0;
        font-size: 16px;
        margin-left: 4px;
      }
    }
  }

  .excute-policy-info {
    margin-bottom: 8px;
    padding: 13px 13px 0 13px;
    background: #f5f7fa;
    border-radius: 3px;
    .title {
      margin: 0;
      color: #333;
      font-weight: normal;
      padding-bottom: 14px;
    }
  }

  .day-cycle {
    margin-left: 14px;
  }

  .divider-box {
    margin: 4px 0 20px 0;
  }

  .footer-box {
    text-align: right;
    margin-top: 20px;
  }

  .input-automatic-group {
    width: 158px;
  }

  .input-smsTemplate {
    width: 478px;
  }

  .input-default {
    width: 170px;
  }

  .input-medium {
    width: 458px;
  }

  .input-mini {
    width: 130px;
  }

  .input-day {
    width: 140px;
  }

  .input-max {
    width: 750px;
  }
}
</style>
