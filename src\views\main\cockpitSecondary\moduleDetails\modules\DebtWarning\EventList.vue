/* 预警事件列表 */

<template>
  <section class="event-list-wrap">
    <CommonTitle :title="title"
      :isRotuerThrow="true"
      :routerObj="{
        bizAppCode: 'alert',
        detailsPath: '/prewarning/events'
      }"
      class="title-box m-b-20" />

    <div class="content-box"
      ref="tableList">
      <div v-for="(item,index) of eventList"
        :key="index"
        class="mode-box"
        :class="[{'mode-active': echartsData.length > 3 && +index === +currentIndex}]"
        @click="detailHandle(item)"
        @mouseenter="mouseenterHandle"
        @mouseleave="mouseleaveHandle">
        <div class="header">
          <div class="left">
            <span>{{item.createTime}}</span>
            <span>{{item.orgName}}</span>
            <span :class="[
              {'h':+item.level === 1},
              {'r':+item.level === 2},
              {'w':+item.level === 3},
              {'l':+item.level === 4}
            ]">{{item.levelDesc}}</span>
          </div>
          <div class="right">
            <span :class="{'h':+!item.hasSupervise}">{{item.hasSupervise?'已督办':'未督办'}}</span>
            <span :class="{'h':+!item.dealStatus}">{{item.dealStatus?'已处置':'未处置'}}</span>
          </div>
        </div>
        <div class="conter text-ellipsis-2">{{item.results}}</div>
      </div>
    </div>

    <!-- 资产弹窗：详情 -->
    <DetailCom v-if="visibleDetail"
      :visible.sync="visibleDetail"
      :itemNo="detailData.serviceNo"
      :orgCode="detailData.orgCode"
      :id="detailData.id"
      :isAssets="false" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { deepClone } from '@/utils'
import CommonTitle from '@/views/main/cockpitSecondary/components/CommonTitle.vue'
import DetailCom from '@/views/assets/page/manage/components/DetailCom.vue'

@Component({
  components: {
    DetailCom,
    CommonTitle
  }
})
export default class extends Vue {
  @Prop() private title!: string // 标题
  @Prop() private echartsData!: any // 渲染数据

  private detailData = {}
  private eventList: any[] = []
  private visibleDetail = false

  // 定时轮播数据
  private timer: any
  private currentIndex = 0
  private middleIndex = 2
  private listHeight = 180
  private listDom: any = {}

  // 数据变化，渲染视图
  @Watch('echartsData', { deep: true })
  private changeEchartsData() {
    this.clearTimer()
    this.initData()
  }

  // 初始化
  private mounted() {
    this.listDom = this.$refs['tableList'] as HTMLDivElement
  }

  // 请求数据接口
  private initData() {
    this.eventList = deepClone(this.echartsData)
    this.scrollTable()
  }

  // 点击查看详情
  private detailHandle(row: any) {
    if (+row.ruleId === 1 || +row.ruleId === 2 || +row.ruleId === 26) {
      this.detailData = row
      this.visibleDetail = true
    }
  }

  // 开启定时器，让页面滚动起来
  private scrollTable() {
    let dataLen = this.echartsData.length
    if (!dataLen || dataLen <= this.middleIndex) return

    this.timer = setInterval(() => {
      this.currentIndex++
      if (this.currentIndex > this.middleIndex && this.currentIndex < dataLen) {
        this.listDom.scrollTo(0, (this.currentIndex - this.middleIndex) * this.listHeight)
      } else if (this.currentIndex === dataLen) {
        this.currentIndex = 0
        this.listDom.scrollTo(0, 0)
      }
    }, 4000)
  }

  // 列表鼠标移入，停止动画
  private mouseenterHandle() {
    clearInterval(this.timer)
    this.timer = null
  }

  // 列表鼠标移出，开始动画
  private mouseleaveHandle() {
    this.scrollTable()
  }

  // 清除定时器，还原相关数据
  private clearTimer() {
    clearInterval(this.timer)
    this.timer = null
    this.currentIndex = 0
    this.listDom.scrollTo(0, 0)
  }

  // 组件销毁
  private destroyed() {
    this.clearTimer()
  }
}
</script>

<style scoped lang="scss">
.event-list-wrap {
  position: relative;
  height: 100%;
  h4,
  p {
    margin: 0;
  }
  .h {
    color: #fb3f3f;
  }
  .w {
    color: #f5b331;
  }
  .l {
    color: #249df7;
  }
  .r {
    color: #ff7500;
  }

  .content-box {
    width: 100%;
    height: 550px;
    overflow-y: auto;
    scroll-behavior: smooth;
    &::-webkit-scrollbar {
      display: none;
    }

    .mode-active {
      box-shadow: 0 0 20px #3eff inset;
    }
  }

  .mode-box {
    height: 140px;
    padding: 20px 30px;
    border-radius: 3px;
    cursor: pointer;
    background: rgba($color: #123c9a, $alpha: 0.6);
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 14px;
      .left {
        display: flex;
        align-items: center;
        color: rgba($color: #fff, $alpha: 0.6);
        span {
          &:nth-child(1) {
            width: 348px;
          }
          &:nth-child(2) {
            min-width: 174px;
          }
        }
      }
      .right {
        span {
          margin-left: 26px;
          &:nth-child(1) {
            margin-left: 0;
          }
        }
      }
    }
    .conter {
      font-size: 34px;
      line-height: 46px;
      .desc {
        flex: 1;
      }
      .bizname {
        width: 300px;
      }
    }
    &:nth-child(2n) {
      background: none;
    }
    &:hover {
      box-shadow: 0 0 20px #3eff inset;
    }
  }
}
</style>


