<!-- 房产监管汇总 propertySummary -->
<template>
  <el-container class="container"
    direction="vertical">
    <el-header height="30px"
      class="summary_header">
      <SearchBar :isReset="false"
        :items="searchItems"
        @onSearch="handleSearch" />

      <el-button type="primary"
        icon="el-icon-refresh-right"
        @click="gettableData">刷 新</el-button>

      <el-button v-loading="loadingExport"
        :disabled="!tableData.length"
        type="primary"
        icon="el-icon-download"
        @click="exportHandle">导 出</el-button>
    </el-header>

    <el-table v-loading="loading"
      border
      :data="tableData"
      style="width: 100%">
      <el-table-column align="center"
        header-align="center"
        prop="orgName"
        label="集团名称"
        min-width="100" />

      <el-table-column align="center"
        header-align="center"
        label="租期超五年">
        <el-table-column align="center"
          header-align="center"
          prop="overYearCount"
          label="处"
          min-width="80" />

        <el-table-column align="center"
          header-align="center"
          prop="overYearArea"
          label="面积"
          min-width="80" />
      </el-table-column>

      <el-table-column align="center"
        header-align="center"
        label="闲置超3个月">
        <el-table-column align="center"
          header-align="center"
          prop="freeMonthCount"
          label="处"
          min-width="80" />
        <el-table-column align="center"
          header-align="center"
          prop="freeMonthArea"
          label="面积"
          min-width="80" />
      </el-table-column>

      <el-table-column align="center"
        header-align="center"
        label="租金欠缴超一个月">
        <el-table-column align="center"
          header-align="center"
          prop="unPaid1MonthCount"
          label="处"
          min-width="80" />
        <el-table-column align="center"
          header-align="center"
          prop="unPaid1MonthArea"
          label="面积"
          min-width="80" />
      </el-table-column>

      <el-table-column align="center"
        header-align="center"
        label="未公开交易">
        <el-table-column align="center"
          header-align="center"
          prop="outTransCount"
          label="处"
          min-width="80" />
        <el-table-column align="center"
          header-align="center"
          prop="outTransArea"
          label="面积"
          min-width="80" />
      </el-table-column>
    </el-table>
  </el-container>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { Loading, Confirm } from '@/decorators'
import { reportSum, reportSumExport } from '@/api/assetsv2'
import { downloadXls } from '@/utils'
import SearchBar from '@/components/SearchBar/index.vue'

@Component({
  components: { SearchBar }
})
export default class HouseCertificate extends Vue {
  // 文件上传
  mounted() {
    this.gettableData()
  }
  private tableData = []
  private searchParams: any = {}
  private loading = false
  private loadingExport = false

  // 表头搜索配置
  private searchItems: any[] = [
    {
      type: 'year',
      key: 'year',
      placeholder: '年份'
    }
  ]

  // 列表搜索
  private handleSearch(condition: any) {
    this.searchParams = condition
    this.gettableData()
  }

  // 刷新
  private gettableData() {
    this.loading = true
    reportSum({
      year: this.searchParams.year
    }).then((res) => {
      this.tableData = res.data
      this.loading = false
    })
  }

  // 导出
  @Loading('loadingExport')
  @Confirm({
    title: '提示',
    content: `是否确认导出数据`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private async exportHandle() {
    let res = await reportSumExport({})
    let time = new Date().getTime()
    downloadXls(res.data, `房产监管汇总表_${time}.xlsx`)
    this.$message.success(res.msg || '导出成功')
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
}
::v-deep.el-table {
  .el-table thead.is-group th.el-table__cell {
    background-color: #fff !important;
  }
  th {
    background: #ffffff !important;
    color: #303133;
    font-size: 14px;

    > .cell {
      padding: 0 5px 0 10px;
    }

    .grid-filter-menu {
      border-bottom: none;

      .el-submenu__title {
        color: #303133;
        line-height: normal;
        height: auto;
        padding: 0;
        border: none;
      }

      .el-submenu__icon-arrow {
        display: none;
      }

      .el-icon-arrow-down {
        position: relative;
        &:before {
          content: '';
          position: absolute;
          border-top: 3px solid #515151;
          border-left: 3px solid transparent;
          border-right: 3px solid transparent;
          height: 0;
          top: 0px;
          bottom: 0;
          left: 0;
          width: 0;
          margin: auto;
        }
      }
    }
  }

  td {
    font-size: 13px;
    padding: 10px 0;
    height: 40px;
    &:first-child {
      padding-left: 10px !important;
    }

    .cell {
      position: relative;
      line-height: normal;
      color: #303133;
      max-height: 32px;
      line-height: 15px;
      .grid-ovflow-popover {
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        white-space: normal;
      }
    }
  }

  .el-table-column--selection .cell {
    padding: 0 5px 0 10px;
  }
}
.summary_header {
  display: flex;
  justify-content: end;
  margin-bottom: 10px;
}
</style>
