// 出租房信息
<template>
  <el-container class="table-wrapper" direction="vertical">
    <el-descriptions
      title="公司信息"
      :column="3"
      border
      :labelStyle="{
        width: '130px'
      }"
      v-if="detail.lessor.lessorType == 1"
    >
      <el-descriptions-item label="公司名称">{{ detail.lessor.entName }}</el-descriptions-item>
      <el-descriptions-item label="统一社会信息代码">{{ detail.lessor.entCreditCode }}</el-descriptions-item>
      <el-descriptions-item label="法定代表人">{{ detail.lessor.entLegalName }}</el-descriptions-item>
      <el-descriptions-item label="注册资本">{{ detail.lessor.entRegCapital || '-' }}万元</el-descriptions-item>
      <el-descriptions-item label="企业类型">{{ detail.lessor.entIndustryTypeDesc }}</el-descriptions-item>
      <el-descriptions-item label="经济性质">{{ detail.lessor.entEconNature }}</el-descriptions-item>
      <el-descriptions-item label="注册地（场所）">{{ detail.lessor.entRegAddress }}</el-descriptions-item>
      <el-descriptions-item label="所属行业类型">{{ detail.lessor.entIndustryTypeDesc }}</el-descriptions-item>
      <el-descriptions-item label="所属行业">{{ detail.lessor.entIndustryDesc }}</el-descriptions-item>
    </el-descriptions>
    <el-descriptions
      title="自然人信息"
      :column="3"
      border
      :labelStyle="{
        width: '130px'
      }"
      v-if="detail.lessor.lessorType == 2"
    >
      <el-descriptions-item label="姓名">{{ detail.lessor.personName }}</el-descriptions-item>
      <el-descriptions-item label="证件类型">{{ detail.lessor.personCardType }}</el-descriptions-item>
      <el-descriptions-item label="证件号">{{ detail.lessor.personCardNo }}</el-descriptions-item>
    </el-descriptions>
    <el-descriptions
      title="联系方式"
      :column="3"
      border
      :labelStyle="{
        width: '130px'
      }"
    >
      <el-descriptions-item label="联系人">{{ detail.lessor.contactName }}</el-descriptions-item>
      <el-descriptions-item label="联系人电话">{{ detail.lessor.contactPhone }}</el-descriptions-item>

      <el-descriptions-item label="邮箱">{{ detail.lessor.contactEmail }}</el-descriptions-item>
      <el-descriptions-item label="联系地址" :span="3">{{ detail.lessor.contactAddress }}</el-descriptions-item>
    </el-descriptions>
    <el-descriptions
      title="开票信息"
      :column="3"
      border
      :labelStyle="{
        width: '130px'
      }"
    >
      <el-descriptions-item label="发票类型">{{ detail.lessor.invoiceTypeDesc }}</el-descriptions-item>
      <el-descriptions-item label="发票接收手机号">{{ detail.lessor.receiveMobile }}</el-descriptions-item>
      <el-descriptions-item label="发票接收邮箱">{{ detail.lessor.receiveEmail }}</el-descriptions-item>
      <!-- <el-descriptions-item label="开票公司名称">{{ detail.lessor.name }}</el-descriptions-item> -->
      <!-- <el-descriptions-item label="纳税人识别号">{{ detail.lessor.name }}</el-descriptions-item> -->
      <el-descriptions-item label="开户行">{{ detail.lessor.bankName }}</el-descriptions-item>
      <el-descriptions-item label="账号">{{ detail.lessor.bankNo }}</el-descriptions-item>
      <el-descriptions-item label="地址">{{ detail.lessor.address }}</el-descriptions-item>
      <el-descriptions-item label="电话">{{ detail.lessor.contactPhone }}</el-descriptions-item>
      <el-descriptions-item label="开票备注" :span="3">{{ detail.lessor.invoiceRemark }}</el-descriptions-item>
      <el-descriptions-item label="其他备注" :span="3">{{ detail.lessor.remark }}</el-descriptions-item>
    </el-descriptions>
    <AccessoryList v-model="detail.lessor.fileList" mode="see" />
    <!-- <Title title="资产明细" /> -->
  </el-container>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import Title from '@/views/assets/components/Title/index.vue'
import Grid from '@/components/Grid/index.vue'
import AccessoryList from '@/views/assets/components/astFileList/index.vue'
@Component({
  components: {
    Title,
    Grid,
    AccessoryList
  }
})
export default class extends Vue {
  @Prop({
    default: () => ({
      name: 'name',
      lessee: {},
      lessor: {
        lessorType:"",
      }
    })
  })
  private detail: any
  // private detail: any = {
  //   name: 'name',
  //   lessor:{}
  // }
}
</script>

<style lang="scss" scoped>
::v-deep.el-descriptions {
  margin-bottom: 15px;
}
</style>
