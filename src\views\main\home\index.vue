<template>
  <section class="home-portal-wrap">
    <!-- <AtlasMind /> -->
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import AtlasMind from './components/AtlasMind.vue'

@Component({
  components: {
    AtlasMind
  }
})
export default class extends Vue {}
</script>

<style scoped lang="scss">
.home-portal-wrap {
  position: relative;
  height: 100%;
  padding: 10px;
  background: #fff;
  box-sizing: border-box;
}
</style>
