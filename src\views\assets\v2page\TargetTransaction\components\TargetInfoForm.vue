// 出租信息
<template>
  <section class="rental-info">
    <el-form ref="RentalInfoForm" :rules="rentalInfoFormRules" :model="rentalInfoForm" label-width="140px">
      <!-- 基本情况 -->
      <el-descriptions
        class="margin-top m-l-10"
        title="基本情况"
        :column="3"
        :labelStyle="{
          width: '120px'
        }"
        border
      >
        <el-descriptions-item label="内部决策情况" :span="24">
          <div class="m-b-10">以下决议已按有关法律法规要求完成，议事规则和决策程序符合规定。</div>
          <el-form-item label="内部决策情况" prop="internalDecision">
            <el-radio-group v-model="rentalInfoForm.internalDecision" class="m-b-1">
              <el-radio v-for="(decision, index) in getDictData('subjectInternalDecision')" :key="index" :label="decision.value">
                {{ decision.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="" :span="24" v-if="rentalInfoForm.internalDecision == '6'">
          <el-form-item label="其它内部决策情况" prop="otherInternalDecision">
            <el-input placeholder="其它内部决策情况" v-model="rentalInfoForm.otherInternalDecision"></el-input>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="" :span="24">
          <el-form-item label="批准单位" prop="approvalOrgan">
            <el-select v-model="rentalInfoForm.approvalOrgan" placeholder @change="changeapprovalOrgan(rentalInfoForm.approvalOrgan)">
              <el-option
                v-for="item in getDictData('subjectApprovalOrgan')"
                :label="item.label"
                :value="Number(item.value)"
                :key="item.value"
              >
                {{ item.label }}
              </el-option>
            </el-select>
            <!-- <el-input v-model="rentalInfoForm.approvalOrgan" placeholder="请输入" /> -->
          </el-form-item>
          <el-form-item label="批准单位名称" v-show="rentalInfoForm.approvalOrgan == 99" prop="approvalOrganName">
            <el-input v-model="rentalInfoForm.approvalOrganName" placeholder="请输入批准单位名称" />
          </el-form-item>
          <el-form-item label="批准文件名称" prop="approvalFileName">
            <el-input v-model="rentalInfoForm.approvalFileName" placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="是否国有资产" contentClassName="department-name-input">
          <el-form-item label="是否国有资产" prop="stateAsset">
            <el-radio-group v-model="rentalInfoForm.stateAsset">
              <el-radio-button :label="1">是</el-radio-button>
              <el-radio-button :label="0">否</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item contentClassName="department-name-input">
          <el-form-item label="国家出租企业或主管部门名称" label-width="200px">
            <el-input v-model="rentalInfoForm.competentDeptName" placeholder="请输入国家出租企业或主管部门名称" />
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
    </el-form>

    <!-- 房产申请表 -->
    <!-- <RealEstateApplicationTable v-model="rentalInfoForm.assetList" 
    />-->
    <!-- 标的列表 -->
    <div style="margin-left:45px;">
      <AccessoryList title="标的附件列表" v-model="rentalInfoForm.fileList" dict="asset_basic_attach" mode="upload" />
    </div>
    <target-list ref="subjectListRef" v-bind="$attrs" @addFormInfo="addFormInfo"  />
    <!-- 附件列表 -->
  </section>
</template>

<script lang="ts">
import { Component, Model, Prop, Vue, Watch } from 'vue-property-decorator'
import AccessoryList, { Accessory } from '@/views/assets/components/astFileList/index.vue'
import { BusinessModule } from '@/store/modules/businessDict'
import TargetList from './TargetList.vue'
export interface RentalInfo {
  internalDecision: number | string
  otherInternalDecision: string
  approvalFileName: string
  approvalOrgan: string | number
  approvalOrganName: string
  stateAsset: number | string
  competentDeptName: string
  fileList: any[]
  subjectList: any[]
  assetNo: string
}

@Component({
  components: {
    AccessoryList,
    TargetList
  }
})
export default class extends Vue {
  @Prop() private initData!: any
  @Watch('initData')
  private changeInitdate() {
    this.setData()
  }
  private rentalInfoForm: RentalInfo = {
    internalDecision: '',
    otherInternalDecision: '',
    approvalFileName: '',
    approvalOrgan: 99,
    approvalOrganName: '',
    stateAsset: 1,
    competentDeptName: '',
    assetNo: '',
    fileList: [],
    subjectList: []
  }
  private rentalInfoFormRules = {
    stateAsset: [{ required: false, trigger: ['blur', 'change'], message: '请选择' }],
    internalDecision: [{ required: true, trigger: ['blur', 'change'], message: '请选择' }],
    otherInternalDecision: [{ required: true, trigger: ['blur', 'change'], message: '请输入其它内容' }],
    approvalOrgan: [{ required: true, trigger: ['blur', 'change'], message: '请选择批准单位名称' }],
    approvalOrganName: [{ required: true, trigger: ['blur', 'change'], message: '请输入批准单位名称' }],
    approvalFileName: [{ required: false, trigger: ['blur', 'change'], message: '请输入批准文件名称' }],
    competentDeptName: [{ required: true, trigger: ['blur', 'change'], message: '请输入国家出租企业或主管部门名称' }]
  }

  private decisionTypeList = this.getDictData('internal_decision')
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}

      return dictionary[key] ? dictionary[key] : []
    }
  }
  private addFormInfo() {
    // this.
  }
  private changeapprovalOrgan(e: any) {
    let dict = this.getDictData('subjectApprovalOrgan')
    let opt = dict.find((res: any) => {
      return res.value == e
    })
    if (e == 99) {
      this.rentalInfoForm.approvalOrganName = ''
    } else {
      this.rentalInfoForm.approvalOrganName = opt.label
    }
  }

  created() {
    this.setData()
  }
  mounted() {
    if (this.$attrs.mode == 'reset') {
      this.rentalInfoForm = Object.assign(this.rentalInfoForm, this.$attrs.detaildata as any)      
        this.$forceUpdate()

      
    }
  }
  private setData() {
    this.rentalInfoForm = Object.assign(this.rentalInfoForm, this.initData)

    // 附件列表赋值
    // this.accessoryList.forEach((item) => {
    //   item.fileList = this.rentalInfoForm[item.prop as FileKey] || []
    // })
  }
  // 获取数据,先执行 validate 再执行 getData
  public getData() {
    // 附件赋值
    return this.rentalInfoForm
  }
  public validate(): Promise<boolean> {
    let form = this.$refs.RentalInfoForm as any
    let subjectList = (this.$refs.subjectListRef as any).targrtList || []
    this.rentalInfoForm.subjectList = subjectList
    if(this.$attrs.mode == 'add'){
    this.rentalInfoForm.assetNo = (this.$attrs['assetData'] as any).assetNo}
    return form.validate().then(
      () => {
        if (this.rentalInfoForm.fileList.length == 0) {
          this.$message.warning(`请上传附件 !`)
          return Promise.reject(false)
        }

        if (this.rentalInfoForm.subjectList.length == 0) {
          this.$message.warning(`请添加标的!`)
          return Promise.reject(false)
        }
        return Promise.resolve(true)
      },
      () => {
        this.$message.warning('请完善出租信息！')
        return Promise.reject(false)
      }
    )
  }
}
</script>

<style scoped lang="scss">
::v-deep .department-name-input {
  .el-form-item {
    margin-bottom: 0px !important;
  }
}
::v-deep .el-descriptions-item__label.is-bordered-label {
  display: none;
}
</style>
