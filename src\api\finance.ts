/**
 * 财务监管api
 */

import request from './request'
import { RemoteResponse } from '@/types'
/**
 * 
 * @param params 财务监管业务序号 \n1:企业发债审批 \n2:企业资产减值准备财务核销备案\n3:企业对外捐赠事项备案\n4:企业清产核资审批\n5:大额资金出借备案\n6:对外担保备案
 * @returns 
 */
// 基础接口 获取业务id
export const serialNo = (params = {index:0}): Promise<RemoteResponse> => {
  return request('/fht-monitor/fin/serialNo', params)
}
// 删除表单数据
export const deleteFormData = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/home/<USER>/deleteFormData', params)
}
//  新增企业发债审批表单数据
export const submitDevFormData = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/fin/bondIssuanceApproval/submit', params)
}
// 查看详情
export const DetailExternalGuarantee = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/fin/bondIssuanceApproval/detail', params)
}
// ***************-对外担保备案-********************
// 新增/修改
export const AddExternalGuarantee = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/fin/externalGuarantee/submit', params)
}
// 查看详情
export const DEtailExternalGuarantee = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/fin/externalGuarantee/detail', params)
}
// 删除
export const DeleteExternalGuarantee = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/fin/externalGuarantee/remove', params)
}
// ***************大额资金出借********************
// 新增/修改
export const AddLargeAmountCapitalLending = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/fin/largeAmountCapitalLending/submit', params)
}
// 查看详情
export const DetailLargeAmountCapitalLending = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/fin/largeAmountCapitalLending/detail', params)
}
// 删除
export const DeleteLargeAmountCapitalLending = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/fin/largeAmountCapitalLending/remove', params)
}
// ***************清产核算********************
// 新增/修改
export const AddExaminationMean = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/fin/examinationMean/submit', params)
}
// 查看详情
export const DetailExaminationMean = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/fin/examinationMean/detail', params)
}
// 删除
export const DeleteExaminationMean = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/fin/examinationMean/remove', params)
}
// ***************对外捐赠********************
// 新增/修改
export const AddForeignDonation = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/fin/foreignDonation/submit', params)
}
// 查看详情
export const DetailForeignDonation = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/fin/foreignDonation/detail', params)
}
// 删除
export const DeleteForeignDonation = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/fin/foreignDonation/remove', params)
}
// ***************对外捐赠********************
// 新增/修改
export const AddAssetsWriteoff = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/fin/assetsWriteoff/submit', params)
}
// 查看详情
export const DetailAssetsWriteoff = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/fin/assetsWriteoff/detail', params)
}
// 删除
export const DeleteAssetsWriteoff = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/fin/assetsWriteoff/remove', params)
}
