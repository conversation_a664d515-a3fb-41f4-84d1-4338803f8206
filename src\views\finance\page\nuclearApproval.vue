// 清产核资审批
<template>
  <el-container class="container"
    direction="vertical">
    <search-bar :items="searchItems"
      @onSearch="handleSearch">
      <div>
        <el-button type="primary"
          @click="AddForm">新增</el-button>
      </div>
    </search-bar>
    <Grid @row-click="loaddetail"
      :columns="cols"
      ref="grid"
      :show-pagination="true"
      :remote-url="remoteUrl"
      :search-params="searchParams">
      <template slot="documentNo"
        slot-scope="scope">
        <span>{{ scope.row.documentNo }}</span>
      </template>
      <template slot="state"
        slot-scope="scope">
        <span v-if="scope.row.state == 1">审批中</span>
        <span v-else-if="scope.row.state == 2">审批通过</span>
        <span v-else-if="scope.row.state == 3"
          style="color: #df7575">审批不通过</span>
      </template>
      <!-- 决策机构 -->
      <!-- <div v-for="(item,index) in cols" :key="index" slot="item.slotName" slot-scope="scope">
            <span v-if="'filterOptions' in item">{{ scope.row[item.prop] }}</span>
      </div>-->
      <template slot="issuanceCurrency"
        slot-scope="scope">
        <span v-if="scope.row.state == 1">人民币</span>
        <span v-else-if="scope.row.state == 2">美元</span>
        <span v-else-if="scope.row.state == 3">其他</span>
      </template>
      <template slot="operatingBar"
        slot-scope="scope">
        <div class="operatingBar_content">
          <el-button type="text"
            @click.stop="loaddetail(scope.row)">查看</el-button>
          <el-button type="text"
            @click.stop="loofinfo(scope.row)"
            v-if="scope.row.approvalStatus!=1">编辑</el-button>
          <!-- <el-button   type="text" slot="reference">删除</el-button> -->
        </div>
      </template>
    </Grid>
    <Dialog-add :visible.sync="showDialogAdd"
      v-if="showDialogAdd"
      :Diaformdata="Diaformdata"
      :mode="DialogMode"
      @changshowDialogAdd="changeShowDialogAdd" />
    <!-- 查看详情 -->
    <DetailAsset :DetailForm="DetailForm"
      :visible.sync="showDetailAsset"
      v-if="showDetailAsset"
      @changeShowDetail="changeShowDetail"></DetailAsset>
  </el-container>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import Grid from '@/components/Grid/index.vue'
import DialogAdd from '../components/DialogNuclear.vue'
import { DataDebissuance } from '../date'
import DetailAsset from '../components/DetailNuclearApproval.vue'
import searchBar from '@/components/SearchBar/index.vue'

@Component({
  components: {
    Grid,
    DialogAdd,
    DetailAsset,
    searchBar
  }
})
export default class Container extends Vue {
  private showDialogAdd = false
  private searchParams = {} //表格搜索条件
  private showDetailAsset = false //显示详情弹窗
  private DetailForm: any = {} //详情列表
  private DialogMode = ''
  private Diaformdata: any = {}
  private remoteUrl = '/fht-monitor/fin/examinationMean/page'
  private data: object[] = DataDebissuance
  private searchItems = [
    {
      type: 'text',
      key: 'keyword',
      placeholder: '请输入关键字进行查询',
      width: '200px'
    }
  ]
  private cols = [
    {
      label: '单据编号',
      prop: 'documentNo',
      slotName: 'documentNo',
      minWidth: 150
    },
    {
      label: '审批状态',
      prop: 'approvalStatusDesc',
      minWidth: 90
    },
    {
      label: '填报单位',
      prop: 'reportDeptName',
      minWidth: 110
    },
    {
      label: '清产核资基准日',
      prop: 'assetVerificationBaseDate',
      minWidth: 110
    },
    {
      label: '账面资产总额(元)',
      prop: 'totalOriginalBookValue',
      minWidth: 140
    },
    {
      label: '所有者权益(元)',
      prop: 'ownersEquity',
      minWidth: 120
    },
    {
      label: '实收资本(元)',
      prop: 'paidinCapital',
      minWidth: 100
    },
    {
      label: '资本公积(元)',
      prop: 'capitalReserve',
      minWidth: 100
    },
    {
      label: '盈余公积(元)',
      prop: 'surplusReserve',
      minWidth: 120
    },
    {
      label: '未分配利润(元)',
      prop: 'undistributedProfit',
      minWidth: 120
    },
    {
      label: '其他利润(元)',
      prop: 'otherProfits',
      minWidth: 100
    },
    {
      label: '少数股东权益（元）',
      prop: 'minorityEquity',
      minWidth: 130
    },
    {
      label: '其他权益(元)',
      prop: 'otherEquity',
      minWidth: 100
    },
    {
      label: '创建人',
      prop: 'createUserName',
      minWidth: 100
    },
    {
      label: '创建日期',
      prop: 'createTime',
      minWidth: 100
    },
    // {
    //   label: '有效期',
    //   prop: 'decisionMakingBody',
    //   minWidth: 80

    {
      label: '操作',
      prop: 'operatingBar',
      slotName: 'operatingBar',
      fixed: 'right',
      minWidth: 80
    }
  ]
  // search搜索
  private handleSearch(condition: any) {
    this.searchParams = condition
    let grid: any = this.$refs.grid
    grid.refresh()
  }
  // 新增按钮
  private AddForm() {
    this.DialogMode = 'add'
    this.Diaformdata = {
      id: ''
    }
    this.showDialogAdd = true
  }
  // 改变显示隐藏
  private changeShowDialogAdd(state: boolean) {
    if (state == false) {
      ;(this.$refs.grid as Grid).refresh(true)
    }
    this.showDialogAdd = state
  }
  // 改变详情显示隐藏
  private changeShowDetail(state: boolean) {
    this.showDetailAsset = state
  }
  //加载查看数据
  private loaddetail(row: any) {
    this.Diaformdata = JSON.parse(JSON.stringify(row))
    this.DialogMode = 'see'

    this.showDialogAdd = true
  }
  //编辑
  private loofinfo(row: object) {
    this.DialogMode = 'edit'
    this.Diaformdata = row
    this.showDialogAdd = true
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
}
</style>
