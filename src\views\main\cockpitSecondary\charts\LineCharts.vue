/** 通用折线图 */

<template>
  <div :id="chartId"
    style="width: 100%; height: 100%;" />
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import { colorSixList, echartConfigure } from '@/views/main/cockpitSecondary/baseData'
import * as echarts from 'echarts'

type EChartsOption = echarts.EChartsOption
type SeriesOption = echarts.SeriesOption

export interface LineOptions {
  color: string // hex 格式
  name: string
  data: number[] | Record<string, any>[]
}

@Component
export default class extends Vue {
  @Prop() private chartId!: string
  @Prop({ default: () => [] }) seriesData!: any[]
  @Prop({ default: () => [] }) legendData!: any[]
  @Prop({ default: () => [] }) xData!: any[]
  @Prop({ default: '' }) private xName?: string
  @Prop({ default: '%' }) private yName?: string
  @Prop({ default: true }) private hasArea!: string
  @Prop() private gridOptions!: echarts.GridComponentOption
  @Prop() private legendOptions!: echarts.LegendComponentOption

  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}

  // 监听 seriesData 数据变化，更新视图
  @Watch('seriesData', { deep: true })
  changeSeriesData() {
    this.initEcharts()
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById(this.chartId) as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    this.change()
    this.initEcharts()
  }

  // 初始化数据
  private initEcharts() {
    let textSize = 22
    let series = this.seriesData
    let tooltipData: any = Object.assign(
      {
        trigger: 'axis',
        valueFormatter: (value: number) => `${value}${this.yName}`
      },
      echartConfigure.tooltip
    )

    this.option = {
      color: colorSixList,
      legend: Object.assign(
        {
          show: !!this.legendData,
          top: '10%',
          right: '5%',
          padding: 0,
          itemHeight: 30,
          itemWidth: 30,
          itemStyle: {
            borderCap: 'butt'
          },
          lineStyle: {
            type: [0, 10],
            dashOffset: 5
          },
          textStyle: {
            color: '#fff',
            fontSize: 30,
            fontWeight: 'bold'
          },
          data: this.legendData
        },
        this.legendOptions
      ),
      grid: Object.assign(
        {
          left: '0%',
          right: '6%',
          bottom: '0%',
          top: '16%',
          containLabel: true
        },
        this.gridOptions
      ),
      tooltip: tooltipData,
      xAxis: {
        type: 'category',
        name: this.xName ? this.xName : '',
        nameTextStyle: {
          color: '#5db0ea',
          fontSize: 30,
          fontWeight: 'bold'
        },
        boundaryGap: false,
        data: this.xData,
        axisLabel: {
          align: 'center',
          margin: 25,
          fontSize: textSize * 1.4,
          fontFamily: echartConfigure.fontFamilyNumber
        },
        axisLine: {
          lineStyle: {
            color: echartConfigure.colorText
          }
        }
      },
      yAxis: {
        type: 'value',
        scale: true,
        name: this.yName ? this.yName : '',
        nameTextStyle: {
          color: echartConfigure.colorText,
          fontSize: 30,
          fontWeight: 'bold'
        },
        max: function (value) {
          return Math.ceil(value.max) + 10
        },
        axisLabel: {
          fontSize: textSize * 1.2,
          fontFamily: echartConfigure.fontFamilyNumber
        },
        axisLine: {
          lineStyle: {
            color: echartConfigure.colorText
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0, 103, 177, 0.4)'
          }
        }
      },
      series: series.map((options, index) => this.getLineOptions(options, index))
    }

    this.myChart && this.myChart.setOption && this.myChart.setOption(this.option)
  }

  private change() {
    this.$emit('change')
  }

  // 获取 line sevice 配置
  private getLineOptions(lineOptions: LineOptions, index: number): SeriesOption {
    return {
      name: lineOptions.name,
      type: 'line',
      symbol: 'circle',
      smooth: true,
      label: {
        formatter: (params) => {
          return params.data + '%'
        }
      },
      lineStyle: {
        width: 6,
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: lineOptions.color || colorSixList[index] // 0% 处的颜色
            },
            {
              offset: 1,
              color: lineOptions.color || colorSixList[index] // 100% 处的颜色
            }
          ]
        }
      },

      areaStyle: {
        origin: 'start',
        shadowColor: `${lineOptions.color || colorSixList[index]}ee`,
        shadowBlur: 10,
        opacity: this.hasArea ? 1 : 0,
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 1,
              color: `${lineOptions.color || colorSixList[index]}55` // 0% 处的颜色
            },
            {
              offset: 0,
              color: `${lineOptions.color || colorSixList[index]}dd` // 100% 处的颜色
            }
          ],
          global: false // 缺省为 false
        }
      },
      data: lineOptions.data
    }
  }
}
</script>

<style scoped lang="scss">
#GeneralAssessment {
  width: 100%;
  height: 100%;
}
</style>
