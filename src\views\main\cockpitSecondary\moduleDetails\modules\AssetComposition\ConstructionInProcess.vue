/** 在建工程 */
<template>
  <section class="construction-inProcess">
    <Ratio :ratioData="constructionBudgetData" />
    <div class="construction-inProcess__execed">
      <div class="execed__item">
        <span>已执行</span>
        <span>
          <span>{{ constructionBudgetData.execed }}</span>
          <span>亿元</span>
        </span>
      </div>
      <div class="execed__item">
        <span>执行率</span>
        <span>
          <span>
            {{ execedRate }}
          </span>
          <span>%</span>
        </span>
      </div>
    </div>
    <RankingChart :seriesData="seriesData.map(item => item.value)"
      :companyList="seriesData.map(item => item.name).reverse()" />

    <div class="construction-inProcess__list">
      <div class="list__label">在建工程列表
        <input type="text"
          @change="onInputChange"
          placeholder="请输入项目名称">
      </div>
      <TableList :cols="cols"
        :source="realList" />
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import Ratio, { RatioData } from './Ratio.vue'
import RankingChart from './RankingList.vue'
import TableList from '@/views/main/cockpitSecondary/components/TableList.vue'

@Component({
  components: {
    Ratio,
    RankingChart,
    TableList
  }
})
export default class extends Vue {
  private constructionBudgetData: any = {
    label: '工程预算',
    // 值
    value: 0,
    // 同比
    YOY: '0',
    // 环比
    QOQ: '0',
    unit: '亿元',
    execed: 0
  }

  private seriesData = [
    {
      name: '项目1',
      value: 333333
    },
    {
      name: '项目2',
      value: 333333
    },
    {
      name: '项目3',
      value: 333333
    },
    {
      name: '项目4',
      value: 333333
    },
    {
      name: '项目5',
      value: 333333
    },
    {
      name: '项目6',
      value: 333333
    },
    {
      name: '项目7',
      value: 333333
    },
    {
      name: '项目8',
      value: 333333
    },
    {
      name: '项目9',
      value: 333333
    },
    {
      name: '项目10',
      value: 333333
    }
  ]

  private cols: any[] = [
    {
      label: '序号',
      span: 1,
      prop: 'id'
    },
    {
      label: '项目名称',
      span: 9,
      prop: 'name'
    },
    {
      label: '项目分类',
      span: 3,
      prop: 'category'
    },
    {
      label: '计划金额/万元',
      span: 4,
      prop: 'planAmout',
      align: 'right'
    },
    {
      label: '实际金额/万元',
      span: 4,
      prop: 'realAmount',
      align: 'right'
    },
    {
      label: '进度',
      span: 3,
      prop: 'rate',
      align: 'center'
    }
  ]
  private listData = [
    {
      id: 1,
      name: '金华市婺城区新华街临4号商铺5年租赁权',
      category: '资产出租',
      planAmout: (1000 + +this.getData(500)).toFixed(2),
      realAmount: this.getData(500),
      rate: this.getData(100) + '%'
    },
    {
      id: 2,
      name: '金华市婺城区新华街临4号商铺5年租赁权',
      category: '资产出租',
      planAmout: (1000 + +this.getData(500)).toFixed(2),
      realAmount: this.getData(500),
      rate: this.getData(100) + '%'
    },
    {
      id: 3,
      name: '金华市婺城区新华街临4号商铺5年租赁权',
      category: '资产出租',
      planAmout: (1000 + +this.getData(500)).toFixed(2),
      realAmount: this.getData(500),
      rate: this.getData(100) + '%'
    },
    {
      id: 4,
      name: '金华市婺城区新华街临4号商铺5年租赁权',
      category: '资产出租',
      planAmout: (1000 + +this.getData(500)).toFixed(2),
      realAmount: this.getData(500),
      rate: this.getData(100) + '%'
    },
    {
      id: 5,
      name: '金华市婺城区新华街临4号商铺5年租赁权',
      category: '资产出租',
      planAmout: (1000 + +this.getData(500)).toFixed(2),
      realAmount: this.getData(500),
      rate: this.getData(100) + '%'
    },
    {
      id: 6,
      name: '金华市婺城区新华街临4号商铺5年租赁权',
      category: '资产出租',
      planAmout: (1000 + +this.getData(500)).toFixed(2),
      realAmount: this.getData(500),
      rate: this.getData(100) + '%'
    },
    {
      id: 7,
      name: '金华市婺城区新华街临4号商铺5年租赁权',
      category: '资产出租',
      planAmout: (1000 + +this.getData(500)).toFixed(2),
      realAmount: this.getData(500),
      rate: this.getData(100) + '%'
    },
    {
      id: 8,
      name: '金华市婺城区新华街临4号商铺5年租赁权',
      category: '资产出租',
      planAmout: (1000 + +this.getData(500)).toFixed(2),
      realAmount: this.getData(500),
      rate: this.getData(100) + '%'
    },
    {
      id: 9,
      name: '金华市婺城区新华街临4号商铺5年租赁权',
      category: '资产出租',
      planAmout: (1000 + +this.getData(500)).toFixed(2),
      realAmount: this.getData(500),
      rate: this.getData(100) + '%'
    },
    {
      id: 10,
      name: '金华市婺城区新华街临4号商铺5年租赁权',
      category: '资产出租',
      planAmout: (1000 + +this.getData(500)).toFixed(2),
      realAmount: this.getData(500),
      rate: this.getData(100) + '%'
    }
  ]

  private realList: any[] = []

  get execedRate() {
    return ((this.constructionBudgetData.execed / this.constructionBudgetData.value) * 100).toFixed(2)
  }

  created() {
    this.onInputChange('')
    let value = +this.getData()
    this.seriesData.reduce((sum: number, current: any) => {
      sum += +this.getData(1000)
      current.value = sum
      return sum
    }, +this.getData(10000))
    this.constructionBudgetData = {
      label: '工程预算',
      // 值
      value: value,
      // 同比
      YOY: +this.getData(20),
      // 环比
      QOQ: +this.getData(20),
      unit: '亿元',
      execed: +this.getData(value)
    }
  }

  private onInputChange(value: string) {
    if (!value) this.realList = [...this.listData]
    let reg = new RegExp(value)
    this.realList = this.listData.filter((item) => {
      return reg.exec(item.name)
    })
  }

  private getData(max = 200) {
    return (Math.random() * max).toFixed(2)
  }
}
</script>


<style scoped lang="scss">
.construction-inProcess {
  padding: 0 10px;
  &__execed {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-bottom: 12px;
    .execed__item {
      font-size: 30px;
      & > span:last-child {
        span {
          color: rgba(0, 204, 255, 1);
        }
        span:first-child {
          font-family: 'digital-7';
          font-size: 58px;
          margin-left: 14px;
        }
      }
    }
  }

  &__list {
    margin-top: 16px;
    .list__label {
      font-size: 40px;
      display: flex;
      align-items: center;
      input {
        margin-left: 16px;
        background: #5db0ea88;
        color: #fff;
        font-size: 22px;
        line-height: 32px;
        height: 40px !important;
        border-color: rgba(14, 41, 118, 1);
        padding-left: 20px;
        &:hover {
          border-color: rgba(14, 41, 118, 1);
        }
        &::placeholder {
          color: #fff !important;
        }
      }
    }
    width: 100%;
    height: 380px;
  }
}
</style>