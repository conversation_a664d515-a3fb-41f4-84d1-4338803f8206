<template>
  <Dialog width="870px"
    :visible="visible"
    :modal-append-to-body="true"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :append-to-body="true"
    class="alert-action-wrap"
    :class="{'alert-action-see-wrap':type==='see'}"
    @close="handleClose">
    <!-- 标题 -->
    <div v-if="type !== 'see'"
      slot="header">
      <div>{{title}}</div>
    </div>

    <!-- 内容区域 -->
    <div slot="body">
      <template v-if="type === 'add' || type === 'edit'">
        <AlertFrom :type="type"
          :pointId="params.id"
          @updataHandle="updataHandle"
          @handleClose="handleClose" />
      </template>

      <template v-if="type === 'see'">
        <el-tabs v-model="activeName">
          <el-tab-pane label="预警详情"
            name="details">
            <AlertFrom :type="type"
              :pointId="params.id" />
          </el-tab-pane>
          <!-- <el-tab-pane label="变更记录"
            name="changes">
            <AlertRecord :type="type"
              :pointId="params.id"
              @handleClose="handleClose" />
          </el-tab-pane> -->
        </el-tabs>
      </template>
    </div>

    <!-- 底部按钮 -->
    <div v-if="activeName !== 'changes'"
      slot="footer">
      <template v-if="type==='see'">
        <el-button @click="handleClose">关 闭</el-button>
      </template>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import AlertFrom from './AlertFrom.vue'
import AlertRecord from './AlertRecord.vue'

@Component({
  components: {
    Dialog,
    AlertFrom,
    AlertRecord
  }
})
export default class extends Vue {
  @Prop({ default: false }) private visible?: boolean // 弹窗显隐
  @Prop() private params!: any // 详情数据
  @Prop({
    validator: (value: string) => {
      return ['add', 'edit', 'see'].indexOf(value) !== -1
    }
  })
  readonly type!: string // 组件类型(新增、编辑、查看)

  private title = '预警操作'
  private activeName: 'details' | 'changes' = 'details'

  // 数据初始化
  private created() {
    this.initialData()
  }

  // 处理初始化数据
  private initialData() {
    switch (this.type) {
      case 'add':
        this.title = '新增预警点'
        break
      case 'edit':
        this.title = '编辑预警点'
        break
      case 'see':
        this.title = '查看预警点'
        break
    }
  }

  // 新增、编辑成功触发父组件更新
  private updataHandle() {
    this.$emit('updataHandle')
  }

  // 关闭弹窗
  private handleClose() {
    this.$emit('update:visible', false)
  }
}
</script>

<style scoped lang='scss'>
.alert-action-wrap {
  ::v-deep .el-dialog {
    .el-dialog__footer {
      padding: 0;
    }
  }
}

.alert-action-see-wrap {
  ::v-deep .el-dialog {
    .el-dialog__header {
      padding: 0;
      .el-dialog__headerbtn {
        top: 20px;
        z-index: 10;
      }
    }
    .el-dialog__body {
      padding-top: 10px;
    }
    .el-dialog__footer {
      padding: 0 20px 20px;
    }
  }
}
</style>