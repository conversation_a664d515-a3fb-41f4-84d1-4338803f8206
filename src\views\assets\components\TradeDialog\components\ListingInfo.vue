// 挂牌信息
<template>
  <section class="rental-info">
    <!-- 基本情况 -->
    <el-descriptions class="margin-top"
      title=""
      :column="6"
      :labelStyle="{
        width: '120px'
      }"
      border>
      <el-descriptions-item label="标的名称"
        :span="6">
        {{ detail.currentAsset.subjectName }}
      </el-descriptions-item>
      <el-descriptions-item label="房屋坐落"
        :span="4">
        {{ detail.currentAsset.location }}
      </el-descriptions-item>

      <el-descriptions-item label="资产类别"
        :span="2">
        {{ getAssetCategory(detail.currentAsset.assetCategory) }}
      </el-descriptions-item>

      <el-descriptions-item label="省"
        :span="2">
        {{ detail.currentAsset.province }}
      </el-descriptions-item>

      <el-descriptions-item label="市"
        :span="2">
        {{ detail.currentAsset.city }}
      </el-descriptions-item>

      <el-descriptions-item label="区(县)"
        :span="2">
        {{ detail.currentAsset.county }}
      </el-descriptions-item>

      <el-descriptions-item label="不动产证号"
        :span="3">
        {{ detail.currentAsset.realEstateCertificateNo }}
      </el-descriptions-item>

      <el-descriptions-item label="房产证"
        :span="3">
        {{ detail.currentAsset.titleDeedNo }}
      </el-descriptions-item>
      <el-descriptions-item label="土地证号"
        :span="3">
        {{ detail.currentAsset.landCertificateNo }}
      </el-descriptions-item>
      <el-descriptions-item label="规划证号"
        :span="3">
        {{ detail.currentAsset.planCertificateNo }}
      </el-descriptions-item>
      <el-descriptions-item label="施工证号"
        :span="3">
        {{ detail.currentAsset.constructionCertificateNo }}
      </el-descriptions-item>
      <el-descriptions-item label="竣工证号"
        :span="3">
        {{ detail.currentAsset.completionCertificateNo }}
      </el-descriptions-item>
      <el-descriptions-item label="出租房屋使用年限(年)"
        :span="3">
        {{ detail.currentAsset.completionCertificateNo }}
      </el-descriptions-item>
      <el-descriptions-item label="房屋使用现状"
        :span="3">
        {{ detail.currentAsset.completionCertificateNo }}
      </el-descriptions-item>
      <el-descriptions-item label="建筑面积"
        :span="3">
        {{ detail.currentAsset.constructionArea }}
      </el-descriptions-item>

      <el-descriptions-item label="建筑面积单位"
        :span="3">
        <el-radio-group v-model="detail.currentAsset.constructionAreaUnit"
          :disabled="true">
          <el-radio label="1">平方米</el-radio>
          <el-radio label="2">亩</el-radio>
          <el-radio label="9">其他</el-radio>
        </el-radio-group>
        <el-input v-if="detail.currentAsset.constructionAreaUnit === 9"
          v-model="detail.currentAsset.otherConstructionAreaUnit"
          :disabled="true"
          placeholder="请输入其他建筑面积单位" />
      </el-descriptions-item>

      <el-descriptions-item label="用途"
        :span="3">
        {{ detail.currentAsset.houseStatus }}
      </el-descriptions-item>

      <el-descriptions-item label="房屋设计用途"
        :span="3">
        {{ detail.currentAsset.houseDesignPurpose }}
      </el-descriptions-item>

      <el-descriptions-item label="装修水平及附属设施"
        :span="3">
        {{ detail.listing.sjpljssj}}
      </el-descriptions-item>

      <el-descriptions-item label="项目行政类别"
        :span="3">
        {{ detail.listing.cwXmxzlb }}
      </el-descriptions-item>

      <el-descriptions-item label="是否重新挂牌"
        :span="3">
        {{ detail.listing.sfcxgp}}
      </el-descriptions-item>

      <el-descriptions-item label="选择交易方式"
        :span="3">
        {{ detail.lessee.tradingMethodDesc }}
        {{detail.lessee.tradingMethod === 9 ?   '-' + detail.lessee.otherTradingMethod : '' }}
      </el-descriptions-item>

      <el-descriptions-item label="实际披露结束时间"
        :span="3">
        {{ detail.listing.sjpljssj }}
      </el-descriptions-item>

      <el-descriptions-item label="最长披露截止日期"
        :span="3">
        {{detail.listing.zcplrq}}
      </el-descriptions-item>

      <el-descriptions-item label="批准单位"
        :span="3">
        {{detail.listing.pzdw }}
      </el-descriptions-item>

      <el-descriptions-item label="挂牌批准单位"
        :span="3">
        {{ detail.listing.qtpzdw }}
      </el-descriptions-item>

      <el-descriptions-item label="出租方名称"
        :span="3">
        {{ detail.listing.czfmc }}
      </el-descriptions-item>

      <el-descriptions-item label="所属集团"
        :span="3">
        {{ detail.listing.ssjt }}
      </el-descriptions-item>

      <el-descriptions-item label="是否国资"
        :span="3">
        {{ detail.listing.sfwgz }}
      </el-descriptions-item>

      <el-descriptions-item label="租金估价"
        :span="3">
        {{ detail.listing.zjgpjbz }}
      </el-descriptions-item>

      <el-descriptions-item label="估价单位"
        :span="3">
        {{ detail.listing.gjdw }}
      </el-descriptions-item>
    </el-descriptions>

    <!-- <el-descriptions class="margin-top m-t-12"
      title="出租信息"
      :column="1"
      :labelStyle="{
        width: '120px'
      }"
      border>
      <el-descriptions-item label="信息1">
        {{ detail.name }}
      </el-descriptions-item>
      <el-descriptions-item label="信息2">
        {{ detail.name }}
      </el-descriptions-item>

    </el-descriptions> -->

    <!-- <el-descriptions class="margin-top m-t-12"
      title="意向承租方信息"
      :column="1"
      :labelStyle="{
        width: '120px'
      }"
      border>
      <el-descriptions-item label="信息1">
        {{ detail.name }}
      </el-descriptions-item>
      <el-descriptions-item label="信息2">
        {{ detail.name }}
      </el-descriptions-item>

    </el-descriptions> -->
  </section>
</template>

<script lang="ts">
import { deepClone } from '@/utils'
import { decisionTypeList } from '@/views/assets/filterOptions'
import { ElForm } from 'element-ui/types/form'
import { Component, Model, Vue } from 'vue-property-decorator'
import { TradeDetail } from '../TradeDetailDialog.vue'
import { BusinessModule } from '@/store/modules/businessDict'

@Component
export default class extends Vue {
  @Model('change') private initData!: TradeDetail

  private detail: Partial<TradeDetail> = {}

  private decisionTypeList = decisionTypeList

  created() {
    this.detail = deepClone(this.initData)
    
  }
  get getDictData() {
  return (key: string) => {
    let dictionary: any = BusinessModule.dictLaodData || {}
    return dictionary[key] ? dictionary[key] : []
  }
}
  get getAssetCategory() {
    return (enterpriseType: number) => {
      return (this.getDictData('asset_category').find((item:any) => item.value === enterpriseType) || { label: '' }).label
    }
  }
}
</script>


<style scoped lang="scss">
::v-deep .el-radio__label {
  line-height: 36px;
}
::v-deep .department-name-input {
  .el-form-item {
    margin-bottom: 0px !important;
  }
}
</style>