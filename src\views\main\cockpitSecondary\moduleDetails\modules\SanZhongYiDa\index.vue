/* 三重一大 */

<template>
  <section class="san-zhong-yi-da">
    <div class="san-zhong-yi-da__content">
      <CommonModuleWrapper v-for="company in companyList"
        :key="company.id"
        :companyId="company.id"
        :height="520"
        :borderGbMode="7"
        :title="company.name"
        :loading="loading"
        wrapperClass="san-zhong-yi-da-common-wrapper"
        componentsName="SanZhongYiDaBarGounp"
        class="cockipt-approach-middel-top" />
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import CountTo from 'vue-count-to'
import CommonModuleWrapper from '@/views/main/cockpitSecondary/components/CommonModuleWrapper.vue'
import { deepClone } from '@/utils'
import { companyList, FinancialIndicatorLineData } from '@/views/main/cockpitSecondary/baseData'

export interface TabItem {
  label: string
  value: string | number
  percent: number
  unit: string
}

@Component({
  components: {
    CountTo,
    CommonModuleWrapper
  }
})
export default class extends Vue {
  private loading = false
  private data = deepClone(FinancialIndicatorLineData)

  private currentYear = new Date().getFullYear() - 1
  private currentCompanyIndex = '0'

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  private companyList = companyList

  get yearList() {
    let year = +this.getMomentTime()
    return [year - 6, year - 5, year - 4, year - 3, year - 2, year - 1, year]
  }

  private currentIndex = 0
  private currentLabel = '资产总额'
  private tabsList: TabItem[] = [
    {
      label: '资产总额',
      value: '1200000',
      percent: 1.9,
      unit: '元'
    },
    {
      label: '资产净额',
      value: '1220000',
      percent: 1.9,
      unit: '元'
    },
    {
      label: '营业收入',
      value: '12312512',
      percent: 21.7,
      unit: '处'
    },
    {
      label: '利润总额',
      value: '*********',
      percent: 31.6,
      unit: '元'
    },
    {
      label: '有效投资',
      value: '13430000',
      percent: 1.9,
      unit: '元'
    },
    {
      label: '净资产收益率',
      value: '13430000',
      percent: 1.9,
      unit: '元'
    },
    {
      label: '国有资产保值增值率',
      value: '13430000',
      percent: 1.9,
      unit: '元'
    }
  ]

  // 当前年月
  get getCurrentDate() {
    let date = new Date()
    return `截止到${date.getFullYear()}年${date.getMonth() + 1}月`
  }

  get getRankingTitle() {
    return `${this.currentLabel}排行情况`
  }

  created() {
    this.$bus.$on('BusCompanyTabs', (currentIndex: number) => {
      this.currentCompanyIndex = currentIndex + ''
      // this.filterData()
      this.loading = true
      setTimeout(() => {
        this.loading = false
      }, 1000)
    })
    // this.onItemClick(0, this.tabsList[0])
    this.loading = true
    setTimeout(() => {
      this.loading = false
    }, 1000)
  }

  private onItemClick(index: any, data: { label: string }) {
    this.currentIndex = index
    this.currentLabel = data.label
    this.loading = true
    setTimeout(() => {
      this.loading = false
    }, 1000)
  }
}
</script>

<style scoped lang="scss">
// .san-zhong-yi-da-common-wrapper {
//   padding: 60px 100px !important;
// }
.san-zhong-yi-da {
  overflow: auto;
  &__content {
    overflow: auto;
    overflow: hidden;
  }
}
</style>




