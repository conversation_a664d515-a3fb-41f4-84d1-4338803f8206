<template>
  <vxe-grid @cell-click="rowClick"
    v-bind="gridOptions"
    ref="xGrid"
    :loading="loading"
    style="overflow:scroll;">
    <!--将表单放在工具栏中-->
    <!-- <template #toolbar>
      <vxe-input v-model="formData.keyword" placeholder="搜索"></vxe-input>
      <vxe-button status="primary" @click="getlist">搜索</vxe-button>
      <vxe-button @click="getlist">刷新</vxe-button>
      <vxe-button @click="$refs.xGrid.exportData()">导出.csv</vxe-button>
    </template> -->
    <template #pager>
      <vxe-pager :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'Sizes', 'FullJump', 'Total']"
        :current-page.sync="tablePage.currentPage"
        :page-size.sync="tablePage.pageSize"
        :total="tablePage.total"
        @page-change="handlePageChange"></vxe-pager>
    </template>

    <template #bizCodeName="{ row }">
      {{ row.bizCodeName || row.bizCode }}
    </template>

    <template #operate="{ row }">
      <template>
        <vxe-button title="查看"
          type="text"
          status="danger"
          content="查看"
          @click="handleClick('see', row)" />
      </template>
      <!-- <vxe-button
        title="资产申报"
        type="text"
        status="danger"
        content="资产申报"
        v-if="getPermission('assets_manage_declare')"
        @click="handleClickAdd(row)"
      ></vxe-button> -->
    </template>
  </vxe-grid>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { assetsInfoPage, assetsInfoExport } from '@/api/assetsv2'
import { PermissionModule } from '@/store/modules/permissionDict'
import XEUtils from 'xe-utils'
import VXETable from 'vxe-table'

@Component({
  components: {}
})
export default class extends Vue {
  @Prop() private assetType!: number // 房产 and 土地
  @Prop({
    default: () => {
      return []
    }
  })
  @Prop()
  private tableData!: any
  private assetTypes!: number
  private loading = false
  private formData: any = {
    keyword: ''
  }
  private tablePage: {
    total: number
    currentPage: number
    pageSize: number
  } = {
    total: 0,
    currentPage: 1,
    pageSize: 20
  }

  // 权限方法
  get getPermission() {
    return (code: string) => {
      return PermissionModule.dictLaodData.includes(code) || false
    }
  }
  private gridOptions: any = {
    align: 'center',
    resizable: true,
    border: true,
    height: '600',
    showHeaderOverflow: true,
    showOverflow: true,
    keepSource: true,
    id: 'full_edit_1',
    rowId: 'id',
    rowConfig: {
      isHover: true
    },
    columnConfig: {
      resizable: true
    },

    customConfig: {
      storage: true,
      checkMethod: this.checkColumnMethod
    },
    printConfig: {
      modes: ['current']
    },
    sortConfig: {
      trigger: 'cell',
      remote: true
    },
    filterConfig: {
      remote: true
    },

    toolbarConfig: {
      refresh: false,
      import: false,
      export: true,
      print: false,
      zoom: true,
      // custom: true,
      custom: {
        icon: 'vxe-icon-menu'
      }
      // slots: {
      //   buttons: 'toolbar_buttons'
      // }
    },
    data: [],
    columns: [
      //   { type: 'checkbox', title: 'ID', width: 120 },
      // 子资产编号、资产名称、建筑面积、土地面积、使用状态、资产原值、
      { field: 'orgName', title: '所属集团', width: 120 },
      { field: 'bizCodeName', slots: { default: 'bizCodeName' }, title: '直属单位', width: 120 },
      { field: 'itemNo', title: '子资产编号', width: 120 },
      { field: 'assetName', title: '资产名称', width: 120 },
      {
        field: 'address',
        title: '资产地址',
        width: 200
      },
      { field: 'assetPurposeDesc', title: '资产用途', width: 120 },
      { field: 'totalBillFee', title: '租金总额', width: 120 },
      { field: 'billFee', title: '应收租金', width: 120 },
      { field: 'totalPaidFee', title: '实收租金', width: 120 },
      { field: 'manageTypeDesc', title: '经营类别', width: 120 },
      { field: 'useStatusDesc', title: '使用状态', width: 120 },
      { field: 'coveredArea', title: '建筑面积(㎡)', width: 120 },
      { field: 'landArea', title: '土地面积(㎡)', width: 120 },
      { field: 'houseCertNo', title: '房产证号', width: 120 },
      { field: 'landCertNo', title: '土地证号', width: 120 },
      { field: 'realEstateCertNo', title: '不动产证号', width: 120 },
      { title: '操作', width: 100, slots: { default: 'operate' }, fixed: 'right' }
    ],
    importConfig: {
      remote: true,
      importMethod: this.importMethod,
      types: ['xlsx'],
      modes: ['insert']
    },
    exportConfig: {
      message: false,
      remote: true,
      exportMethod: this.exportMethod,
      types: ['xlsx'],
      modes: ['current', 'all'],
      isFooter: false,
      isHeader: false
    },
    checkboxConfig: {
      labelField: 'id',
      reserve: true,
      highlight: true,
      range: true
    },
    editRules: {
      //   name: [
      //     { required: true, message: 'app.body.valid.rName' },
      //     { min: 3, max: 50, message: '名称长度在 3 到 50 个字符' }
      //   ],
      //   email: [{ required: true, message: '邮件必须填写' }],
      //   role: [{ required: true, message: '角色必须填写' }]
    },
    editConfig: {
      trigger: 'click',
      mode: 'row',
      showStatus: true
    }
  }
  private findSexList() {
    setTimeout(() => {
      const sexList = [
        { label: '', value: '' },
        { label: '男', value: '1' },
        { label: '女', value: '0' }
      ]
    }, 100)
  }

  // reset page
  private resetPage() {
    this.tablePage = {
      total: 0,
      currentPage: 1,
      pageSize: 20
    }
  }

  private formatAmount({ cellValue }: any) {
    return cellValue ? `${XEUtils.commafy(XEUtils.toNumber(cellValue), { digits: 2 })}` : ''
  }

  private formatDate({ cellValue }: any) {
    return XEUtils.toDateString(cellValue, 'yyyy-MM-dd HH:ss:mm')
  }

  private checkColumnMethod({ column }: any) {
    if (['nickname', 'role'].includes(column.property)) {
      return false
    }
    return true
  }

  // 自定义服务端导入
  private importMethod({ file }: any) {
    // 处理表单
    const formBody = new FormData()
    formBody.append('file', file)
    // 上传文件
  }

  // 自定义服务端导出
  private async exportMethod({ options }: any) {
    const $grid: any = this.$refs.xGrid
    const proxyInfo = $grid.getProxyInfo()
    let objData = Object.assign({
      assetType: this.assetType,
      current: this.tablePage.currentPage,
      size: this.tablePage.pageSize,
      ...this.tableData
    })
    let body = {
      ...objData
    }
    // 处理数据
    delete body.data
    body.fileName = options.filename
    body.selected = options.columns.map((res: any) => {
      return res.field
    })
    body.head = options.columns.map((res: any) => {
      return res.title
    })
    // "-1导入全量数据"
    if (options.mode == 'all') {
      body.size = '-1'
    }
    let res = await assetsInfoExport(body)
    VXETable.saveFile({ filename: options.filename, type: 'xlsx', content: res.data })
    VXETable.modal.message({ content: '导出成功，开始下载', status: 'success' })
  }

  // 请求数据
  private async getlist() {
    this.loading = true
    let objData = Object.assign({
      assetType: this.assetType,
      current: this.tablePage.currentPage,
      size: this.tablePage.pageSize,
      ...this.tableData
    })

    let res = await assetsInfoPage(objData)
    this.gridOptions.data = res.data.records
    this.tablePage = {
      total: res.data.total,
      currentPage: res.data.current,
      pageSize: res.data.size
    }
    this.loading = false
    // 更新统计信息
    this.$emit('updateStatistics', res.data)
  }

  // 分页改变
  private handlePageChange() {
    this.getlist()
  }

  // 单击单元行
  rowClick(row: any) {
    this.handleClick('see', row.row)
  }

  // 操作
  private handleClick(type: string, row: { id: string }) {
    this.$emit('handleClick', type, row)
  }

  // 新增标的
  private handleClickAdd(row: { id: string }) {
    this.$emit('handleClickAdd', row)
  }
}
</script>

<style lang="scss" scoped>
::v-deep .vxe-modal--wrapper .vxe-modal--content tr:last-child {
  display: none;
}

::v-deep .vxe-modal--wrapper .vxe-modal--content tr:nth-child(3) {
  display: none;
}

// // 去掉操作选项
::v-deep .vxe-export--panel .vxe-export--panel-column > ul > li:last-child {
  display: none;
}
</style>
