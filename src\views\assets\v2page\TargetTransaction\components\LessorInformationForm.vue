// 出租方信息
<template>
  <section class="rental-info">
    <el-form ref="LessorInfoForm" :rules="lessorInfoFormRules" :model="lessorInfoForm" labelWidth="150px">
      <!-- 法人及其他组织机构 -->
      <el-alert v-if="isloadHistory" title="已为你导入历史数据" type="success" effect="dark"></el-alert>
      <p
        style="    font-size: 16px;
     font-weight: bold;"
      >
        出租方类型:
        <el-radio-group class="m-l-15" @change="formClear()" v-model="lessorInfoForm.lessorType">
          <el-radio-button label="1">企业</el-radio-button>
          <el-radio-button label="2">自然人</el-radio-button>
        </el-radio-group>
      </p>
      <el-descriptions
        v-if="lessorInfoForm.lessorType == 1"
        class="margin-top"
        title="企业信息"
        :column="24"
        :labelStyle="{
          width: '130px'
        }"
        border
      >
        <el-descriptions-item :span="24">
          <el-form-item label="公司名称" prop="entName">
            <el-autocomplete
              v-model="lessorInfoForm.entName"
              :fetch-suggestions="querySearchAsync"
              placeholder="请输入企业名称"
              @select="handleSelect"
              clearable
            ></el-autocomplete>
            <!-- <el-input v-model="lessorInfoForm.entName" placeholder="请输入" /> -->
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item label="注册地(住所)" :span="24">
          <el-form-item label="注册地(住所)" label-width prop="entRegAddress">
            <el-input v-model="lessorInfoForm.entRegAddress" placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item :span="12">
          <el-form-item label="统一社会信用代码" label-width prop="entCreditCode">
            <el-input v-model="lessorInfoForm.entCreditCode" placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="注册资本" :span="12">
          <el-form-item label="注册资本" label-width prop="entRegCapital">
            <InputNumber max="1000000" v-model="lessorInfoForm.entRegCapital" clearable type="decimal" placeholder="请输入金额">
              <template slot="append">万元</template>
            </InputNumber>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="经济性质" :span="12">
          <el-form-item label="经济性质" label-width prop="entEconNature">
            <el-select v-model="lessorInfoForm.entEconNature" placeholder="请选择">
              <el-option
                v-for="(item, index) in getDictData('entEconType')"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item label="企业类型" :span="12">
          <el-form-item label="企业类型" label-width prop="entType">
            <!-- <el-input v-model="lessorInfoForm.entType" placeholder="请输入企业类型"></el-input> -->
            <el-select v-model="lessorInfoForm.entType" placeholder="请选择企业类型">
              <el-option v-for="(item, index) in economicNatureList" :key="index" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-descriptions-item>

        <!-- <el-descriptions-item label="法定代表人" :span="12">
          <el-form-item label="法定代表人" label-width prop="legalPerson">
            <el-input v-model="lessorInfoForm.legalPerson" placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item> -->

        <el-descriptions-item label="所属行业类型" :span="12">
          <el-form-item label="所属行业类型" prop="entIndustryType">
            <el-select v-model="lessorInfoForm.entIndustryType" placeholder="请选择" @change="changeIndustryType(true)">
              <el-option v-for="(item, index) in industryTypeList" :key="index" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-descriptions-item>

        <el-descriptions-item label="所属行业" :span="12">
          <el-form-item label="所属行业" label-width prop="entIndustry">
            <el-select v-model="lessorInfoForm.entIndustry" placeholder="请选择">
              <el-option v-for="(item, index) in industryList" :key="index" :label="item.label" :value="item.value"></el-option>
            </el-select>
            <!-- <el-input v-model="lessorInfoForm.entIndustry"
            placeholder="请输入" />-->
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 自然人 -->
      <el-descriptions
        v-if="lessorInfoForm.lessorType == 2"
        class="margin-top m-t-12"
        title="自然人"
        :column="3"
        :labelStyle="{
          width: '120px'
        }"
        border
      >
        <el-descriptions-item :span="3">
          <el-form-item label="姓名" prop="personName">
            <el-input v-model="lessorInfoForm.personName" placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item :span="1">
          <el-form-item label="证件类型" prop="personCardType">
            <el-select v-model="lessorInfoForm.personCardType" placeholder="请选择">
              <el-option v-for="(item, index) in getDictData('cardType')" :key="index" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="证件号码" :span="2">
          <el-form-item label="证件号码" prop="personCardNo">
            <el-input v-model="lessorInfoForm.personCardNo" placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 联系方式 -->
      <el-descriptions class="margin-top m-t-12" title="联系方式" :column="2" border>
        <el-descriptions-item label :span="1">
          <el-form-item label="联系人" prop="contactName">
            <el-input v-model="lessorInfoForm.contactName" placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="联系人电话" :span="1">
          <el-form-item label="联系人电话" prop="contactPhone">
            <el-input v-model="lessorInfoForm.contactPhone" placeholder="请输入" type="tel" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="邮箱" :span="3">
          <el-form-item label="邮箱" prop="contactEmail">
            <el-input v-model="lessorInfoForm.contactEmail" placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="联系地址" :span="3">
          <el-form-item label="联系地址" prop="contactAddress">
            <el-input v-model="lessorInfoForm.contactAddress" placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>

      <el-descriptions class="margin-top m-t-12" title="出租方开票信息" :column="2" border>
        <el-descriptions-item label :span="2">
          <el-form-item label="发票类型" prop="invoiceType">
            <el-select style="width:40%" v-model="lessorInfoForm.invoiceType" placeholder="请选择发票类型">
              <el-option
                v-for="(item, index) in getDictData('subjectInvoiceType')"
                :value="item.value"
                :label="item.label"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label :span="1">
          <el-form-item label="发票接收手机号" prop="receiveMobile">
            <el-input v-model="lessorInfoForm.receiveMobile" placeholder="请输入" type="tel" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label :span="1">
          <el-form-item label="发票接收邮箱" prop="receiveEmail">
            <el-input v-model="lessorInfoForm.receiveEmail" placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label :span="1">
          <el-form-item label="开户行" prop="bankName">
            <el-input v-model="lessorInfoForm.bankName" placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label :span="1">
          <el-form-item label="账户" prop="bankNo">
            <inputNumber v-model="lessorInfoForm.bankNo" placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label :span="2">
          <el-form-item label="地址" prop="address">
            <el-input v-model="lessorInfoForm.address" placeholder="请输入" type="tel" />
          </el-form-item>
        </el-descriptions-item>
        <!-- <el-descriptions-item label :span="1">
          <el-form-item label="电话" prop="invoicePhone">
            <el-input v-model="lessorInfoForm.invoicePhone" placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item> -->

        <el-descriptions-item label :span="2">
          <el-form-item label="开票备注">
            <el-input
              type="textarea"
              placeholder="请输入备注"
              v-model="lessorInfoForm.invoiceRemark"
              maxlength="100"
              :autosize="{ minRows: 2, maxRows: 3 }"
              show-word-limit
            ></el-input>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label :span="2">
          <el-form-item label="其他备注">
            <el-input
              type="textarea"
              placeholder="请输入其他备注"
              v-model="lessorInfoForm.remark"
              maxlength="200"
              :autosize="{ minRows: 3, maxRows: 4 }"
              show-word-limit
            ></el-input>
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
    </el-form>

    <!-- 附件列表 -->
    <AccessoryList v-model="lessorInfoForm.fileList" dict="asset_lessor_attach" mode="upload" />
  </section>
</template>

<script lang="ts">
import { decisionTypeList, economicNatureList } from '@/views/assets/filterOptions'
import { ElForm } from 'element-ui/types/form'
import { Component, Model, Vue, Watch } from 'vue-property-decorator'
import AccessoryList, { Accessory } from '@/views/assets/components/astFileList/index.vue'
import InputNumber from '@/components/FormComment/inputNumber.vue'
import { validateEmail, validateLessorSocialCreditCode, validatePhone } from '@/utils/validate'
import { BusinessModule } from '@/store/modules/businessDict'
import { priceUnitList, tenantUnsolicitedTypeList } from '@/views/assets/filterOptions'
import { SearchBasicList, getHistoryInfo } from '@/api/assetsv2'
export interface LessorInfo {
  entName: string // 公司名称
  contactAddress: string // 联系地址
  contactPhone: string // 联系人电话
  contactName: string // 	联系人(取登录人)
  economicNature: number | string // 	经济性质,(1:国资监管机构/政府部门,2:国有独资公司（企业）/国有全资企业,3:国有控股企业,4:国有事业单位，国有社团等,5:国有实际控制企业,6:国有参股企业,7:集体,8:私营,9:外资企业,99:其他)
  contactEmail: string // 邮箱
  entType: string // 企业类型
  entIndustry: string // 所属行业
  entIndustryType: string // 所属行业类型
  legalPerson: string // 法定代表人
  entCreditCode: string // 统一社会信用代码
  personName: string // 自然人
  personCardType: number | '' // 证件类型
  personCardNo: string // 证件号码
  registeredCapital: string //	注册资本( 万元)
  registrationPlace: string //	注册地( 住所)
  depositBank: string // 出租方指定账户开户行
  accountName: string // 出租方指定账户名
  bankAccount: string // 出租方指定账户
  invoiceType: number | '' // 发票类型，1：普票，2：专票
  receiveMobile: string // 电子发票接受手机号
  receiveEmail: string // 电子发票接受邮箱
  invoicingRemark: string // 开票备注
  attachmentFileDTOList: []
}

type FileKey = 'attachmentFile1' | 'attachmentFile2' | 'attachmentFile3'

@Component({
  components: {
    AccessoryList,
    InputNumber
  }
})
export default class extends Vue {
  @Model('change') private initData!: LessorInfo
  private tenantUnsolicitedTypeList = tenantUnsolicitedTypeList
  @Watch('initData')
  private changeInitdate() {
    Object.assign(this.lessorInfoForm, this.initData)
    // 附件列表赋值
    // this.accessoryList.forEach(item => {
    //   item.fileList = this.lessorInfoForm[item.prop as FileKey] || []
    // })
    //   let list = this.getDictData('sshy').find((res: any) => {
    //   if (res.value == this.lessorInfoForm.entIndustryType) {
    //     return res.children
    //   }
    // })
    // this.industryList = list.children||[]
  }

  private isloadHistory = false
  private lessorInfoForm: any = {
    lessorType: '1',
    address:"",
    attachmentFileDTOList: '',
    entName: '',
    contactAddress: '',
    contactPhone: '',
    contactName: '',
    economicNature: '',
    contactEmail: '',
    entType: '',
    entIndustry: '',
    entIndustryType: '',
    legalPerson: '',
    entCreditCode: '',
    personName: '',
    personCardType: '',
    personCardNo: '',
    registeredCapital: '',
    registrationPlace: '',
    depositBank: '',
    accountName: '',
    bankAccount: '',
    invoiceType: '',
    receiveEmail: '',
    receiveMobile: '',
    invoicingRemark: '',
    fileList: [],
    entEconNature: '',
    bankName: '',
    bankNo: '',
    invoicePhone: '',
    entRegAddress: '',
    remark:"",
    invoiceRemark:"",

  }

  private lessorInfoFormRules = {
    bankName: [{ required: true, trigger: ['blur', 'change'], message: '请输入开户行' }],
    bankNo: [{ required: true, trigger: 'blur', message: '请输入' }],
    invoicePhone: [
      { required: true, trigger: ['blur', 'change'], message: '请输入' },
      { required: true, trigger: ['blur', 'change'], validator: validatePhone }
    ],
    entReentNamegCapital: [{ required: true, trigger: ['blur', 'change'], message: '请输入' }],
    entRegAddress: [{ required: true, trigger: ['blur', 'change'], message: '请输入' }],
    entName: [{ required: true, trigger: ['blur', 'change'], message: '请输入公司名称' }],
    address: [{ required: true, trigger: ['blur', 'change'], message: '请输入联系地址' }],
    contactAddress: [{ required: true, trigger: ['blur', 'change'], message: '请输入地址' }],
    // contactPhone: [{ required: true, trigger: ['blur', 'change'], validator: validatePhone }],
    contactPhone: [{ required: true, trigger: ['blur', 'change'], validator: validatePhone }],
    contactName: [{ required: true, trigger: ['blur', 'change'], message: '请输入联系人' }],
    economicNature: [{ required: true, trigger: ['blur', 'change'], message: '请选择经济性质' }],
    contactEmail: [{ required: false, trigger: ['blur', 'change'], validator: validateEmail }],
    entType: [{ required: true, trigger: ['blur', 'change'], message: '请选择企业类型' }],
    entEconNature: [{ required: true, trigger: ['blur', 'change'], message: '请选择经济性质' }],
    entIndustry: [{ required: true, trigger: ['blur', 'change'], message: '请选择所属行业' }],
    entIndustryType: [{ required: true, trigger: ['blur', 'change'], message: '请选择所属行业类型' }],
    legalPerson: [{ required: true, trigger: ['blur', 'change'], message: '请输入法定代表人' }],
    entCreditCode: [
      { required: true, trigger: ['blur', 'change'], validator: validateLessorSocialCreditCode },
      { required: true, trigger: ['blur', 'change'], message: '请输入统一社会信用代码' }
    ],
    personName: [{ required: true, trigger: ['blur', 'change'], message: '请输入自然人姓名' }],
    personCardType: [{ required: true, trigger: ['blur', 'change'], message: '请选择证件类型' }],
    personCardNo: [{ required: true, trigger: ['blur', 'change'], message: '请输入证件号码' }],
    registeredCapital: [{ required: true, trigger: 'change', message: '请输入注册资本' }],
    registrationPlace: [{ required: true, trigger: ['blur', 'change'], message: '请输入注册地(住所)' }],
    lessorType: [{ required: true, trigger: ['blur', 'change'], message: '请选择' }],
    invoiceType: [{ required: true, trigger: ['blur', 'change'], message: '请选择' }],
    receiveMobile: [
      { required: true, trigger: ['blur', 'change'], message: '请选择' },
      { required: true, trigger: ['blur', 'change'], validator: validatePhone }
    ],
    receiveEmail: [
      { required: true, type: 'email', trigger: ['blur', 'change'], message: '请输入邮箱格式' },
      { required: true, trigger: ['blur', 'change'], message: '请输入' }
    ],
    accountName: [{ required: true, trigger: ['blur', 'change'], message: '请输入' }],
    depositBank: [{ required: true, trigger: ['blur', 'change'], message: '请输入' }],
    bankAccount: [
      { required: true, trigger: ['blur', 'change'], message: '请输入' },
      { required: true, trigger: ['blur', 'change'], validator: validateLessorSocialCreditCode }
    ],
    entRegCapital: [{ required: false, trigger: ['blur', 'change'], message: '请输入' }]
  }

  private decisionTypeList: any = decisionTypeList
  private isNo = [
    {
      label: '否',
      value: 0
    },
    {
      label: '是',
      value: 1
    }
  ]
  private economicNatureList = this.getDictData('enterprise_type')
  private industryTypeList = this.getDictData('sshy')
  private naturalPersonCardTypeList = this.getDictData('zjlx')
  private industryList: any = []
  // private economicNatureList = []
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }

  mounted() {
    if (this.$attrs.mode == 'reset') {
      Object.assign(this.lessorInfoForm, (this.$attrs.detaildata as any).lessor)
      this.changeIndustryType(false)
      this.$forceUpdate()
    } else if (this.$attrs.mode == 'add') {
      this.isloadHistoryInfo()
    }
  }
  // 草稿箱功能
  private async isloadHistoryInfo() {
    // isloadHistory
    try {
      let res = await getHistoryInfo({})
      if (res.success) {
        if (res.data.lessorType) {
          Object.assign(this.lessorInfoForm, res.data)
          this.isloadHistory = true
        }
        // this.data= res.data.records
      }
    } finally {
      this.$forceUpdate()
      this.changeIndustryType(false)
    }
  }
  private setData() {
    this.lessorInfoForm = Object.assign(this.lessorInfoForm, this.initData)
  }
  // 更改行业（）
  private changeIndustryType(state: boolean) {
    let list: any = {
      children: []
    }
    list = this.getDictData('sshy').find((res: any) => {
      if (res.value == this.lessorInfoForm.entIndustryType) {
        return res.children
      }
    })
    // 判断需不需要重置
    if (state) {
      this.lessorInfoForm.entIndustry = ''
    }
    if (list) {
      this.industryList = list.children || ''
    }
  }
  // 获取数据,先执行 validate 再执行 getData
  public getData() {
    // 附件赋值
    return this.lessorInfoForm
  }
  private async querySearchAsync(queryString: string, cb: any) {
    try {
      let res: any = await SearchBasicList({
        keyword: queryString
      })
      if (res.success) {
        // this.data= res.data.records
        let list: any = res.data.map((item: any) => {
          return Object.assign(
            {
              label: item.name,
              value: item.name
            },
            item
          )
        })

        cb(list)
      }
    } catch (e) {
      // console.error(e)
    }
  }
  // 选择公司之后，赋值和清空部分值
  private handleSelect(queryString: any) {
    // if()
    this.$set(this.lessorInfoForm, 'entCreditCode', queryString.creditCode)
    this.$set(this.lessorInfoForm, 'entRegCapital', queryString.regCapital)
    this.$set(this.lessorInfoForm, 'entRegAddress', queryString.regLocation)
    this.$set(this.lessorInfoForm, 'entRegCapital', '')
    this.$set(this.lessorInfoForm, 'entEconNature', '')
    this.$set(this.lessorInfoForm, 'entType', '')
    this.$set(this.lessorInfoForm, 'entIndustryType', '')
    this.$set(this.lessorInfoForm, 'entIndustry', '')
  }
  // 清除校验结果
  private formClear() {
    let form = this.$refs.LessorInfoForm as ElForm
    form.clearValidate()
  }
  // 验证数据
  public validate(): Promise<boolean> {
    let form = this.$refs.LessorInfoForm as ElForm
    return form.validate().then(
      () => {
        if (this.lessorInfoForm.fileList.length == 0) {
          this.$message.warning(`请上传附件 !`)
          return Promise.reject(false)
        }
        return Promise.resolve(true)
      },
      () => {
        this.$message.warning('请完善出租方信息！')
        return Promise.reject(false)
      }
    )
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-form-item {
  margin-bottom: 0px !important;
}
::v-deep .department-name-input {
  // .el-form-item {
  //   margin-bottom: 0px !important;
  // }
}
::v-deep .el-descriptions-item__label.is-bordered-label {
  display: none;
}
::v-deep.el-descriptions .is-bordered .el-descriptions-item__cell {
  border: 0;
}
::v-deep.el-form-item--small .el-form-item__label {
  line-height: 25px;
}
::v-deep.el-select,
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
}
</style>
