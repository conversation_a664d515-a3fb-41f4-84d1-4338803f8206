<template>
  <div id="assetDetailChartdom"></div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import * as echarts from 'echarts'
@Component({
  components: {}
})
export default class extends Vue {
  @Prop() private chartData!: any
  private loadchart = false
  private chartDom: any = {}
  private myChart: any = {}

  mounted() {
    try {
      this.initchart()
    } catch (error) {
      //
    }
  }
  initchart() {
    this.loadchart = true
    this.chartDom = document.getElementById('assetDetailChartdom') as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)
    let option = {
      backgroundColor: '#fff',
      grid: {
        top: '25%',
        bottom: '10%',
        left: '10%',
        right: '10%'
      },
      tooltip: {
        trigger: 'axis',
        formatter: function (params: any) {
          let html = params[0].name + '年<br>'
          for (let i = 0; i < params.length; i++) {
            html +=
              '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' +
              params[i].color +
              ';"></span>'
            if (option.series[params[i].seriesIndex].valueType == 'percent') {
              html += params[i].seriesName + ':' + params[i].value + '%<br>'
            } else {
              html += params[i].seriesName + ':' + params[i].value + '万<br>'
            }
          }
          return html
        }
      },
      axisPointer: {
        type: 'shadow',
        label: {
          show: true
        }
      },
      legend: {
        data: this.chartData.series.map((res: any) => {
          return res.name
        }),
        top: '1%',
        textStyle: {
          color: '#333'
        }
      },
      xAxis: {
        data: this.chartData.xAxis,
        axisLine: {
          show: true, //隐藏X轴轴线
          lineStyle: {
            color: '#333'
          }
        },
        axisTick: {
          show: true //隐藏X轴刻度
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#333' //X轴文字颜色
          }
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '价值(万)',
          nameTextStyle: {
            color: '#333'
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: true
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#333'
            }
          },
          axisLabel: {
            show: true,
            formatter: '{value}',
            textStyle: {
              color: '#333'
            }
          }
        },
        {
          type: 'value',
          name: '收缴率(%)',
          nameTextStyle: {
            color: '#333'
          },
          position: 'right',
          splitLine: {
            show: true
          },
          axisTick: {
            show: true
          },
          axisLine: {
            show: true
          },
          axisLabel: {
            show: true,
            formatter: '{value}',
            textStyle: {
              color: '#333'
            }
          }
        },
        {
          type: 'value',
          gridIndex: 0,
          min: 50,
          max: 100,
          splitNumber: 8,
          splitLine: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          },
          splitArea: {
            show: true,
            areaStyle: {
              color: ['rgba(250,250,250,0.0)', 'rgba(250,250,250,0.05)']
            }
          }
        }
      ],
      series: [
        {
          name: this.chartData.series[0].name,
          type: 'bar',
          barWidth: 15,
          itemStyle: {
            normal: {
              color: '#fb3f3f'
            }
          },
          data: this.chartData.series[0].value
        },
        {
          name: this.chartData.series[1].name,
          type: 'bar',
          barWidth: 15,
          itemStyle: {
            normal: {
              color: '#388BFF'
            }
          },
          data: this.chartData.series[1].value
        },

        {
          name: this.chartData.series[2].name,
          type: 'line',
          yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
          smooth: true, //平滑曲线显示
          showAllSymbol: true, //显示所有图形。
          symbol: 'circle', //标记的图形为实心圆
          symbolSize: 5, //标记的大小
          itemStyle: {
            //折线拐点标志的样式
            color: '#FFCC67'
          },
          lineStyle: {
            color: '#FFCC67'
          },
          data: this.chartData.series[2].value,
          valueType: 'percent'
        }
      ]
    }

    this.myChart.setOption(option) && this.myChart.resize()
  }
}
</script>

<style lang="scss" scoped>
#assetDetailChartdom {
  width: 100%;
  height: 200px;
}
</style>