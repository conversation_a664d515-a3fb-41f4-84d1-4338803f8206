/** 项目金额分布 */

<template>
  <div id="ProjectAmountDistribution" />
</template>

<script lang='ts'>
import { Component, Prop, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { ProjectAmountDistributionData } from '@/views/main/cockpitSecondary/baseData'
import { capitalProfile } from '@/api/cockpit'
import * as echarts from 'echarts'
import { deepClone } from '@/utils'

type EChartsOption = echarts.EChartsOption

@Component
export default class extends Vue {
  private loading = false
  private year = ''
  private timer: any
  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}
  private seriesData: any[] = deepClone(ProjectAmountDistributionData)
  private echartsDatas: any[] = []
  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('ProjectAmountDistribution') as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    // this.initData()
    this.initEcharts()
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await capitalProfile()
    if (!Array.isArray(data) || !data.length) return
    this.echartsDatas = data
  }

  // 初始化数据
  private initEcharts() {
    let textSize = 16
    let series = this.seriesData
    this.option = {
      color: [
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: '#835002' // 0% 处的颜色
            },
            {
              offset: 1,
              color: '#E6B607' // 100% 处的颜色
            }
          ]
        },
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: '#7E358B00' // 0% 处的颜色
            },
            {
              offset: 0.2,
              color: '#7E358B' // 0% 处的颜色
            },
            {
              offset: 1,
              color: '#EC342F' // 100% 处的颜色
            }
          ]
        }
      ],
      legend: {
        show: true
      },
      grid: {
        left: '0%',
        right: '0%',
        top: '15%',
        bottom: '4%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 103, 177, 1)',
        borderColor: 'rgba(0, 103, 177, 1)',
        textStyle: {
          color: '#fff',
          fontSize: textSize
        }
      },
      xAxis: {
        type: 'category',

        axisLabel: {
          fontSize: textSize * 1.2,
          fontWeight: 'bold',
          hideOverlap: false,
          interval: 0,
          color: '#5db0ea'
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(67, 154, 255, 1)'
          }
        },
        data: [
          '0~100万',
          '100万~200万',
          '200万~300万',
          '300万~400万',
          '400万~500万',
          '500万~600万',
          '600万~700万',
          '700万~800万',
          '800万~900万',
          '900万~1000万',
          '1000万以上'
        ]
      },
      axisPointer: {
        show: false
      },
      yAxis: {
        show: true,
        type: 'value',
        name: '数量',
        nameTextStyle: {
          fontWeight: 'bold',
          fontSize: 30,
          color: '#5db0ea'
        },
        axisLabel: {
          fontWeight: 'bold',
          color: '#5db0ea',
          fontSize: textSize * 1.2
        },
        axisLine: {
          lineStyle: {
            color: 'RGBA(147, 148, 149, 1)'
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0, 103, 177, 0.4)'
          }
        }
      },
      series: [
        {
          selectedMode: 'single',
          label: {
            show: true,
            position: 'top',
            color: 'RGBA(40, 177, 255, 1)',
            fontSize: textSize * 2.4,
            fontWeight: 'bold'
          },
          itemStyle: {
            borderRadius: [textSize * 2.4, textSize * 2.4, 0, 0],
            shadowColor: '#E6B60766',
            shadowBlur: 30
          },
          emphasis: {
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'RGBA(233, 60, 167, 0)'
                  },
                  {
                    offset: 0.4,
                    color: 'RGBA(233, 60, 167, 0.3)'
                  },
                  {
                    offset: 1,
                    color: 'RGBA(233, 60, 167, 1)'
                  }
                ]
              },
              shadowColor: 'RGBA(233, 60, 167, 0.6)',
              shadowBlur: 30
            },
            label: {
              fontSize: textSize * 2.8,
              color: 'RGBA(233, 60, 167, 1)'
            }
          },
          select: {
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'RGBA(233, 60, 167, 0)'
                  },
                  {
                    offset: 0.4,
                    color: 'RGBA(233, 60, 167, 0.3)'
                  },
                  {
                    offset: 1,
                    color: 'RGBA(233, 60, 167, 1)'
                  }
                ]
              },
              shadowColor: 'RGBA(233, 60, 167, 0.6)',
              shadowBlur: 30
            },
            label: {
              fontSize: textSize * 2.8,
              color: 'RGBA(233, 60, 167, 1)'
            }
          },
          showBackground: false,
          backgroundStyle: {
            color: '#081C5866',
            borderRadius: [textSize * 2, textSize * 2, 0, 0]
          },
          barWidth: textSize * 3.2,
          data: series,
          type: 'bar'
        }
      ]
    }

    this.myChart && this.myChart.setOption && this.myChart && this.myChart.setOption(this.option)
    this.change()
  }

  // 年份切换
  private yearHandler(year: string) {
    this.year = year
    // this.initData()
  }

  private change() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    this.timer = setInterval(() => {
      let index = Math.max(Math.floor(Math.random() * 4), 0)

      this.myChart &&
        this.myChart.dispatchAction({
          type: 'select',
          seriesIndex: 0,
          dataIndex: index
        })
    }, 5000)
  }

  destroyed() {
    clearInterval(this.timer)
  }
}
</script>

<style scoped lang="scss">
#ProjectAmountDistribution {
  width: 100%;
  height: 100%;
}
</style>