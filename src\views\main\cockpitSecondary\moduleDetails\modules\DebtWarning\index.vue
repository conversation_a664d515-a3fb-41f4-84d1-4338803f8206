/* 智慧预警 */

<template>
  <section class="debt-warning-wrap">
    <el-row :span="24"
      :gutter="24"
      class="hegith100">
      <el-col :span="7"
        class="module-box">
        <div class="mode-content pis-relative mode-bg1 cockipt-approach-left-top"
          style="height:710px;">
          <!-- 数据改变提示层 -->
          <img v-if="visibleFlicker"
            class="cockipt-bg-flicker"
            src="@/views/main/cockpitcren/images/panel_bg1_cg.png" />

          <TodayOverview title="今日概况"
            v-loading="loadingTodayOverview"
            :echartsData="todayOverviewData" />
        </div>
        <div class="mode-content pis-relative mode-bg1 cockipt-approach-left-bottom"
          style="height:710px;">
          <!-- 数据改变提示层 -->
          <img v-if="visibleFlicker"
            class="cockipt-bg-flicker"
            src="@/views/main/cockpitcren/images/panel_bg1_cg.png" />

          <DisposeMonth title="本月处置"
            v-loading="loadingDisposeMonth"
            :echartsData="disposeMonthData" />
        </div>
      </el-col>
      <el-col :span="10"
        class="module-box">
        <div class="mode-content mode-bg2 mode-custom-middle cockipt-approach-middel-top">
          <!-- 各集团tabs  -->
          <CommonTabs :orgCode="tabActive"
            module="DebtWarning"
            class="cockipt-approach-middel-top m-b-40"
            @commonTabsHandle="commonTabsHandle" />

          <div class="mode-content pis-relative mode-bg5"
            style="height:1380px;">
            <!-- 数据改变提示层 -->
            <img v-if="visibleFlicker"
              class="cockipt-bg-flicker"
              src="@/views/main/cockpitcren/images/panel_bg5_cg.png" />

            <div style="height:740px;">
              <EarlyWarningTrend title="预警趋势"
                :tabActive="tabActive" />
            </div>

            <div style="height:650px;">
              <EventList title="预警事件列表"
                v-loading="loadingEventList"
                :echartsData="eventListData" />
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="7"
        class="module-box">
        <div class="mode-content pis-relative mode-bg1 cockipt-approach-right-top"
          style="height:710px;">
          <!-- 数据改变提示层 -->
          <img v-if="visibleFlicker"
            class="cockipt-bg-flicker"
            src="@/views/main/cockpitcren/images/panel_bg1_cg.png" />

          <TotalAlerts title="预警总数"
            :tabActive="tabActive" />
        </div>
        <div class="mode-content pis-relative mode-bg1 cockipt-approach-right-bottom"
          style="height:710px;">
          <!-- 数据改变提示层 -->
          <img v-if="visibleFlicker"
            class="cockipt-bg-flicker"
            src="@/views/main/cockpitcren/images/panel_bg1_cg.png" />

          <EarlyWarningFication title="预警分类统计"
            v-loading="loadingEarlyWarningFication"
            :echartsData="earlyWarningFicationData" />
        </div>
      </el-col>
    </el-row>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { realtimeWarning, debtInfaceTodayOverview, debtInfaceCurrentMonthDeal, debtInfaceAlertClassification } from '@/api/cockpit'
import CommonTabs from '@/views/main/cockpitSecondary/components/CommonActiveTabs.vue'
import EventList from './EventList.vue'
import TodayOverview from './TodayOverview.vue'
import DisposeMonth from './DisposeMonth.vue'
import EarlyWarningFication from './EarlyWarningFication.vue'
import EarlyWarningTrend from './EarlyWarningTrend.vue'
import TotalAlerts from './TotalAlerts.vue'

@Component({
  components: {
    CommonTabs,
    EventList,
    TotalAlerts,
    DisposeMonth,
    TodayOverview,
    EarlyWarningTrend,
    EarlyWarningFication
  }
})
export default class extends Vue {
  private tabActive = ''
  private visibleFlicker = false

  private loadingTodayOverview = false
  private todayOverviewData = {}

  private loadingDisposeMonth = false
  private disposeMonthData: any[] = []

  private loadingEarlyWarningFication = false
  private earlyWarningFicationData: any[] = []

  private loadingEventList = false
  private eventListData: any[] = []

  private created () {
    // 如果链接上带有集团code，需要让tabs选中该集团
    let { query} = this.$route
    if(query && query.orgCode) {
      this.tabActive = (query.orgCode as string) || '0'
    }
    // end
  }

  // 各集团模块切换
  private commonTabsHandle(data: string) {
    this.tabActive = data
    this.initData()
    this.infaceTodayOverview()
  }

  // 请求接口，获取各模块数据
  private initData() {
    this.flickerHandle()
    this.infaceDisposeMonth()
    this.infaceEventList()
    this.infaceEarlyWarningFication()
  }

  // 今日概况数据
  @Loading('loadingTodayOverview')
  private async infaceTodayOverview() {
    let { data } = await debtInfaceTodayOverview({
      orgCode: this.tabActive
    })

    this.todayOverviewData = data || {
      warningInfo: [],
      warningType: []
    }
  }

  // 本月处置数据
  @Loading('loadingDisposeMonth')
  private async infaceDisposeMonth() {
    let { data } = await debtInfaceCurrentMonthDeal({
      orgCode: this.tabActive
    })

    this.disposeMonthData = data || []
  }

  // 预警事件列表数据
  @Loading('loadingEventList')
  private async infaceEventList() {
    let { data } = await realtimeWarning({
      current: 1,
      size: 1000,
      orgCode: this.tabActive
    })

    this.eventListData = data.records || []
  }

  // 预警分类统计
  @Loading('loadingEarlyWarningFication')
  private async infaceEarlyWarningFication() {
    let { data } = await debtInfaceAlertClassification({
      orgCode: this.tabActive
    })

    this.earlyWarningFicationData = data || []
  }

  // 触发背景闪烁效果
  private flickerHandle() {
    this.visibleFlicker = true
    setTimeout(() => {
      this.visibleFlicker = false
    }, 3000)
  }
}
</script>

<style scoped lang="scss">
.debt-warning-wrap {
  position: relative;
  height: 100%;

  .hegith100 {
    height: 100%;
  }

  .module-box {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    box-sizing: border-box;
    padding: 0 0 10px 0;
    @extend .hegith100;

    .mode-content {
      font-size: 30px;
      padding: 50px 70px;
    }
    .mode-bg1 {
      background: url('../../../images/panel_bg1.png') no-repeat left top;
      background-size: 100% 100%;
    }
    .mode-bg2 {
      background: url('../../../images/panel_bg2.png') no-repeat left top;
      background-size: 100% 100%;
    }
    .mode-bg3 {
      background: url('../../../images/panel_bg3.png') no-repeat left top;
      background-size: 100% 100%;
    }
    .mode-bg5 {
      background: url('../../../images/panel_bg5.png') no-repeat left top;
      background-size: 100% 100%;
    }
    .mode-custom-middle {
      height: 780px;
      padding: 20px 0;
      background: none;
    }
  }
}
</style>




