<template>
  <section v-loading="loading"
    class="assets-manage-wrap">
    <el-tabs v-model="activeName"
      class="tabs-box"
      @tab-click="initData">
      <el-tab-pane name="house">
        <span slot="label">
          <i class="el-icon-office-building"></i>
          房产
        </span>
        <HouseInfo @pierceThrough="pierceThrough"
          v-show="activeName === 'house'"
          :detailData="updateStatisticsData" />
      </el-tab-pane>
      <el-tab-pane name="land">
        <span slot="label">
          <i class="el-icon-receiving"></i>
          土地
        </span>
        <LandInfo v-show="activeName === 'land'"
          :detailData="updateStatisticsData" />
      </el-tab-pane>
    </el-tabs>

    <!-- 表格 -->
    <SearchTable :tableData="manageData"
      :tableTotal="tableTotal"
      :assetType="activeName === 'house' ? 1 : 2"
      @changeTableData="changeTableData"
      @updateStatistics="updateStatistics"
      ref="searchtable" />
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import HouseInfo from './components/HouseInfo.vue'
import LandInfo from './components/LandInfo.vue'
import SearchTable from './components/SearchTable.vue'

@Component({
  components: {
    HouseInfo,
    LandInfo,
    SearchTable
  }
})
export default class extends Vue {
  private loading = false
  private manageData = {}
  private updateStatisticsData = {} //统计信息
  private searchFormData = {}
  private tableTotal = 0
  private activeName: 'house' | 'land' = 'house'

  // 组件初始化
  private mounted() {
    // this.initData()
  }

  // 获取数据
  private async initData() {
    let assetType = this.activeName === 'house' ? 1 : 2
    let objData = Object.assign(
      {
        assetType: assetType
      },
      this.searchFormData
    )

    this.manageData = objData
    let searchRef: any = this.$refs.searchtable
    searchRef.changeTab()
  }
  // 更新统计信息
  private updateStatistics(data: any) {
    this.updateStatisticsData = data
  }
  // 子组件传递数据，重新搜索数据
  private changeTableData(data = {}) {
    this.searchFormData = Object.assign(this.searchFormData, data)
    this.$nextTick(() => {
      this.initData()
    })
  }
  // 穿透
  private pierceThrough(res: any) {
    ;(this.$refs.searchtable as any).formData = {
      ...res
    }
    setTimeout(() => {
      ;(this.$refs.searchtable as any).searchForm()
    }, 100)
  }
}
</script>

<style scoped lang="scss">
.assets-manage-wrap {
  background: #fff;
  padding: 10px 14px;

  ::v-deep .house-land-info-wrap {
    position: relative;
    background: #fff;
    box-sizing: border-box;
    border: 1px solid rgb(172 196 221 / 20%);
    border-radius: 4px;
    font-size: 14px;
    h4,
    h6,
    p {
      margin: 0;
    }

    .title {
      font-size: 16px;
      font-weight: 500;
      padding: 10px 14px 10px;
      background: rgb(172 196 221 / 10%);
      border-bottom: 1px solid rgb(172 196 221 / 20%);
    }

    .content {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      padding: 10px 14px 10px;
      .mode {
        flex: 1;
        margin-right: 40px;
        &:nth-last-child(1) {
          margin-right: 0;
        }
        h6 {
          color: #333;
          font-size: 14px;
          font-weight: normal;
        }
        strong {
          display: block;
          padding: 6px 0;
          font-weight: 500;
          border-bottom: 1px solid rgb(172 196 221 / 20%);
          span {
            font-size: 20px;
          }
          i {
            font-size: 16px;
            margin-left: 4px;
          }
        }
        p {
          padding: 8px 0 0;
        }
      }
    }
  }

  ::v-deep .tabs-box {
    margin-bottom: 15px;
    .el-tabs__item {
      font-size: 16px;
    }
  }
}
</style>
