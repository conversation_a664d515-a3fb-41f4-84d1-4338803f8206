<template>
  <Dialog title="资产信息详情" width="800px" :visible="visible" @close="handleClose">
    <div slot="body" v-loading="loading" class="dialog_content">
      <el-descriptions border size="" title :colon="false" :column="3">
        <el-descriptions-item label="资产编号">{{detailData.assetNo}}</el-descriptions-item>
        <el-descriptions-item label="坐落" :span="3">{{detailData.location}}</el-descriptions-item>
        <el-descriptions-item label="产权证类型">{{detailData.certTypeDesc}}</el-descriptions-item>
        <el-descriptions-item label="产权证号" :span="3">{{detailData.certNo}}</el-descriptions-item>
        <el-descriptions-item label="档案编号">{{detailData.archiveNo}}</el-descriptions-item>
        <el-descriptions-item label="土地证号">{{detailData.assetNo}}</el-descriptions-item>
        <el-descriptions-item label="产权登记地址">{{detailData.assetRegAddress}}</el-descriptions-item>
        <el-descriptions-item label="所有权人">{{detailData.propertyOwner}}</el-descriptions-item>
        <el-descriptions-item label="产权证书状态">{{detailData.certNoStatus}}</el-descriptions-item>
        <el-descriptions-item label="登记用途">{{detailData.regLandPurposeDesc}}</el-descriptions-item>
        <el-descriptions-item label="土地状态">{{detailData.landStatus}}</el-descriptions-item>
        <el-descriptions-item label="产权证大楼性质 ">{{detailData.certBuildingNature}}</el-descriptions-item>
        <el-descriptions-item label="发证日期">{{detailData.certIssueDate}}</el-descriptions-item>
        <el-descriptions-item label="终止日期">{{detailData.certEndDate}}</el-descriptions-item>
        <el-descriptions-item label="使用年限">{{detailData.useTerm}}</el-descriptions-item>
        <el-descriptions-item label="建筑面积">{{detailData.constructionArea}}</el-descriptions-item>
        <el-descriptions-item label="土地面积">{{detailData.landArea}}</el-descriptions-item>
        <el-descriptions-item label="使用权面积">{{detailData.tenureArea}}</el-descriptions-item>
        <el-descriptions-item label="独有面积">{{detailData.exclusiveUseArea}}</el-descriptions-item>
        <el-descriptions-item label="分摊面积">{{detailData.publicArea}}</el-descriptions-item>
        <el-descriptions-item label="冻结理由">{{detailData.frozenReason}}</el-descriptions-item>
        <el-descriptions-item label="注销原因">{{detailData.cancelReason}}</el-descriptions-item>
        <el-descriptions-item label="管理单位">{{detailData.reasonCancellation0}}</el-descriptions-item>
        <el-descriptions-item label="是否抵押">{{detailData.whetherMortgageDesc}}</el-descriptions-item>
        <el-descriptions-item label="抵押银行">{{detailData.mortgageBank}}</el-descriptions-item>
        <el-descriptions-item label="抵押时间">{{detailData.mortgageDate}}</el-descriptions-item>
        <el-descriptions-item label="贷款项目 ">{{detailData.loanProject}}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{detailData.remark}}</el-descriptions-item>
      </el-descriptions>
    </div>
    <div slot="footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import { PropertyDetail } from '@/api/assets'
export default interface DetailInfo {
  id: string | number
}
@Component({
  components: {
    Dialog
  }
})
export default class extends Vue {
  @Prop() private visible!: boolean
  @Prop() private detailInfo!: any
  private loading = false
  private detailData = {}
  created() {
    if (this.detailInfo) {
      this.getDetail()
    }
  }
  private async getDetail() {
    this.loading = true
    let res: any = {}
    try {
      res = await PropertyDetail({
        id: this.detailInfo.id
      })
      if (res.success) {
        this.detailData = res.data
        // this.$message.success(res.msg)
      }
    } catch (e) {
      this.$message.error(res.msg)
      this.handleClose()
    } finally {
      this.loading = false
    }
  }
  private handleClose() {
    //
    this.$emit('update:visible', false)
  }
}
</script>

<style lang="scss" scoped>
.dialog_content {
    padding: 0 !important;
  min-height: 40vh;
}
</style>