//报表平台
<template>
  <el-container class="container"
    direction="vertical">
    <search-bar :items="searchItems"
      @onSearch="handleSearch">
      <div>
        <el-button type="primary"
          @click="AddForm">新增</el-button>
      </div>
    </search-bar>
    <Grid :columns="cols"
      :remote-url="remoteUrl"
      ref="grid"
      :show-pagination="true"
      :search-params="searchParams">
      <template slot="attachmentFileDTOList"
        slot-scope="scope">
        <div class="operatingBar_content">
          <el-button type="text"
            :key="index"
            @click.stop="loaddetail(scope.row)">
            查看
          </el-button>
        </div>
      </template>

      <template slot="operatingBar"
        slot-scope="scope">
        <div class="operatingBar_content">
          <el-button type="text"
            @click.stop="loaddetail(scope.row)">查看</el-button>
          <el-button type="text"
            @click.stop="loofinfo(scope.row)">编辑</el-button>

          <!-- <el-button type="text" slot="reference">删除</el-button> -->
        </div>
      </template>
    </Grid>
    <Dialog-large :mode="DialogMode"
      :Diaformdata="Diaformdata"
      v-if="showDialogAdd"
      :visible.sync="showDialogAdd"
      @changshowDialogAdd="changeShowDialogAdd" />
    <!-- 查看详情 -->
    <DetailAsset :visible="showDetailAsset"
      :list="Diadetaillist"
      @changeShowDetail="changeShowDetail"
      :fileList="pageData.attachmentFileDTOList"
      dict="financial_largeLending_attach"></DetailAsset>
  </el-container>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import Grid from '@/components/Grid/index.vue'
import DialogLarge from '../components/Dialogjmreport.vue'
import { DataDebissuance } from '../date'
import { equityRelationship } from '../filterOptions'
import DetailAsset from '../components/DetailAsset.vue'
import searchBar from '@/components/SearchBar/index.vue'
import { priceUnitList } from '../../assets/filterOptions'
import { deepClone } from '../../../utils'

@Component({
  name: 'LargeFunds',
  components: {
    Grid,
    DialogLarge,
    DetailAsset,
    searchBar
  }
})
export default class Container extends Vue {
  private pageData = {}
  private showDialogAdd = false
  private searchParams = {} //表格搜索条件
  private DialogMode = ''
  private remoteUrl = '/fht-monitor/fin/largeAmountCapitalLending/page'
  private showDetailAsset = false //显示详情弹窗
  private Diadetaillist: object[] = [] //详情列表
  private Diaformdata = {} //详情列表
  private data: object[] = DataDebissuance
  private searchItems = [
    {
      type: 'text',
      key: 'keyword',
      placeholder: '请输入关键字进行查询',
      width: '200px'
    }
  ]
  private cols = [
    {
      label: '公司名称',
      prop: 'companyName',
      minWidth: 90
    },
    {
      label: '报表类型',
      prop: 'companyAddress',
      minWidth: 90
    },
    {
      label: '报表',
      slotName: 'attachmentFileDTOList',
      prop: 'bbfile',
      minWidth: 90
    },
    {
      label: '上报时间',
      prop: 'legalRepresentative',
      minWidth: 90
    },
    {
      label: ' 上报人',
      prop: 'debtor',
      minWidth: 90
    }
  ]
  // search搜索
  private handleSearch(condition: any) {
    this.searchParams = condition
    let grid: any = this.$refs.grid
    grid.refresh()
  }
  // 新增按钮
  private AddForm() {
    this.DialogMode = 'add'
    this.Diaformdata = {
      id: ''
    }
    this.showDialogAdd = true
  }
  // 改变显示隐藏
  private changeShowDialogAdd(state: boolean) {
    this.showDialogAdd = state
    if (state == false) {
      ;(this.$refs.grid as Grid).refresh(true)
    }
  }
  // 改变详情显示隐藏
  private changeShowDetail(state: boolean) {
    this.showDetailAsset = state
  }
  //编辑
  private loofinfo(row: any) {
    this.DialogMode = 'edit'
    this.Diaformdata = row

    this.showDialogAdd = true
  }
  //查看
  private loaddetail(row: any) {
    this.Diaformdata = deepClone(row)
    this.DialogMode = 'see'
    this.showDialogAdd = true
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
}
</style>
