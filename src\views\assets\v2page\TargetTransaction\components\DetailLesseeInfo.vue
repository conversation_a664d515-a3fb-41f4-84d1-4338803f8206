// 承租方信息
<template>
  <el-container class="table-wrapper" direction="vertical">
    <el-descriptions
      :column="3"
      border
      :labelStyle="{
        width: '130px'
      }"
    >
      <el-descriptions-item label="信息公告期(工作日)">{{ detail.leaseCond.announcementDay || '-' }}个工作日</el-descriptions-item>
      <el-descriptions-item label="约定工作日">{{ detail.leaseCond.agreedWorkingDay || '-' }}个工作日</el-descriptions-item>
      <el-descriptions-item label="延期周期">{{ detail.leaseCond.delayPeriod || '-' }}个工作日</el-descriptions-item>
      <el-descriptions-item label="是否会同出租方审查">{{ detail.leaseCond.isReviewedWithLessorDesc }}</el-descriptions-item>
      <el-descriptions-item label="未征集到承租方后">{{ detail.leaseCond.unIntentionTypeDesc }}</el-descriptions-item>
 
      <el-descriptions-item label="遴选方式">{{ detail.leaseCond.selectTypeDesc }}</el-descriptions-item>
      <el-descriptions-item label="征集到承租后">{{ detail.leaseCond.isReviewedWithLessorDesc }}</el-descriptions-item>
      <el-descriptions-item label="是否会同地方审查">{{ detail.leaseCond.name }}</el-descriptions-item>
    </el-descriptions>
    <el-descriptions
      :column="3"
      border
      :labelStyle="{
        width: '130px'
      }"
      v-if="detail.leaseCond.selectType==5"
    >
      <el-descriptions-item label="价格显示单位">{{ detail.leaseCond.markupRangeDesc }}</el-descriptions-item>
      <el-descriptions-item label="起拍价（万元）">{{ detail.leaseCond.startingPrice }}</el-descriptions-item>
      <el-descriptions-item label="保留价 （万元）">{{ detail.leaseCond.reversePrice }}</el-descriptions-item>
      <el-descriptions-item label="自由报价开始时间">{{ detail.leaseCond.freeQuoteStartTime }}</el-descriptions-item>
      <el-descriptions-item label="自由报价期(工作日)">{{ detail.leaseCond.freeQuoteDay }}</el-descriptions-item>
      <el-descriptions-item label="限时报价开始时间">{{ detail.leaseCond.limitQuoteStartTime }}</el-descriptions-item>
      <el-descriptions-item label="限时报价周期(秒)">{{ detail.leaseCond.limitQuoteSecond }}</el-descriptions-item>
      <el-descriptions-item label="最终报价考虑期(秒)">{{ detail.leaseCond.considerQuoteSecond }}</el-descriptions-item>
      <el-descriptions-item label="行权期考虑时长(秒)">{{ detail.leaseCond.vestingSecond }}</el-descriptions-item>
      <el-descriptions-item label="加价幅度(元)">{{ detail.leaseCond.markupRange }}</el-descriptions-item>
      <el-descriptions-item label="是否允许连续报价">{{ detail.leaseCond.contQuoteDesc }}</el-descriptions-item>
      <el-descriptions-item label="是否允许起始价报价">{{ detail.leaseCond.initialQuoteDesc }}</el-descriptions-item>
      <el-descriptions-item label="动态报价联系人">{{ detail.leaseCond.dynamicContactName }}</el-descriptions-item>
      <el-descriptions-item label="动态报价联系人电话">{{ detail.leaseCond.dynamicContactPhone }}</el-descriptions-item>
      <el-descriptions-item label="竞价场次名称">{{ detail.leaseCond.competeSessionName }}</el-descriptions-item>
      <el-descriptions-item label="竞价图片"><AccessoryList v-model="detail.leaseCond.fileList" mode="see" /></el-descriptions-item>
    </el-descriptions>
    <el-descriptions
      :column="1"
      border
      :labelStyle="{
        width: '130px'
      }"
    >
      <el-descriptions-item label="保证金处理条款">{{ detail.leaseCond.depositClause }}</el-descriptions-item>
      <el-descriptions-item label="交易条件">{{ detail.leaseCond.tradeCond }}</el-descriptions-item>

      <el-descriptions-item label="承租方资格条件">{{ detail.leaseCond.leaseCond}}</el-descriptions-item>
      <el-descriptions-item label="公告期限及延牌规则" :span="3">{{ detail.leaseCond.rules }}</el-descriptions-item>
      <el-descriptions-item label="其他披露事项" :span="3">{{ detail.leaseCond.otherDisclosure }}</el-descriptions-item>
      <!-- <el-descriptions-item label="附 " :span="3">{{ detail.leaseCond.name }}</el-descriptions-item> -->
    </el-descriptions>

    <AccessoryList v-model="detail.leaseCond.fileList" mode="see" />
    <!-- <Title title="资产明细" /> -->
  </el-container>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import Title from '@/views/assets/components/Title/index.vue'
import Grid from '@/components/Grid/index.vue'
import AccessoryList from '@/views/assets/components/astFileList/index.vue'
@Component({
  components: {
    Title,
    Grid,
    AccessoryList
  }
})
export default class extends Vue {
  @Prop({
    default: () => ({
      name: 'name',
        leaseCond:{}
    })
  }) private detail: any

  // private detail: any = {
  //   name: 'name',
  //   leaseCond:{}
  // }
}
</script>

<style lang="scss" scoped>
::v-deep.el-descriptions {
  margin-bottom: 15px;
}
</style>
