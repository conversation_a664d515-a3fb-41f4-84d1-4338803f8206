<template>
  <Dialog width="1300px"
    :visible="visible"
    :close-on-click-modal="true"
    :destroy-on-close="true"
    :append-to-body="true"
    :show-close="false"
    custom-class="custom-architecture-wrap"
    @close="handleClose">
    <div slot="header">
      <i class="close el-icon-circle-close"
        @click="handleClose" />
    </div>

    <div slot="body">
      <div class="content-box p-10">
        <el-carousel :autoplay="false"
          trigger="click"
          height="700px">
          <el-carousel-item v-for="(item, index) of imgList"
            :key="index">
            <img class="img"
              :src="item.src" />
          </el-carousel-item>
        </el-carousel>
      </div>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'

@Component({
  components: {
    Dialog
  }
})
export default class Header extends Vue {
  @Prop() private visible!: boolean

  private imgList = [
    {
      src: require('@/views/main/cockpitcren/images/architecture.jpg')
    },
    {
      src: require('@/views/main/cockpitcren/images/architecture2.png')
    }
  ]

  // 关闭弹窗
  private handleClose() {
    this.$emit('update:visible', false)
  }
}
</script>

<style scoped lang="scss">
.content-box {
  background: #fff;
  border-radius: 10px;
  margin: 10px 0;
}
.img {
  width: 100%;
  height: 100%;
}

::v-deep .el-carousel {
  .el-carousel__container {
    .el-carousel__arrow {
      background-color: rgba(31, 45, 61, 0.7);
    }
  }
  .el-carousel__indicators {
    .is-active {
      .el-carousel__button {
        background-color: rgba(31, 45, 61, 0.7);
      }
    }
  }
}
</style>

<style lang="scss">
// 自定义弹窗
.custom-architecture-wrap {
  @keyframes keyCockiptDialogMove {
    0% {
      transform: rotateY(90deg);
    }
    100% {
      transform: rotateY(0);
    }
  }

  position: relative;
  left: 0;
  padding-bottom: 7px !important;
  background: #072979;
  border-radius: 10px;
  border: 1px solid #2eb6f6;
  transform: rotateY(0);
  animation: keyCockiptDialogMove 1s ease;

  h4,
  p {
    margin: 0;
  }

  .close {
    position: absolute;
    right: 30px;
    top: 30px;
    z-index: 10;
    font-size: 40px;
    color: #2eb6f6;
    cursor: pointer;
    &:hover {
      color: #072979;
    }
  }

  .el-dialog__header {
    background: none !important;
    padding: 4px !important;
    &::after {
      background: none !important;
    }
    .el-dialog__headerbtn {
      top: -18px !important;
      right: 18px !important;
      .el-dialog__close {
        color: #0899fa;
        border: 1px solid #0899fa;
        border-radius: 50%;
      }
    }
  }
  .el-dialog__body {
    padding: 0 0 0 !important;
    min-height: 100px;
  }
}
</style>