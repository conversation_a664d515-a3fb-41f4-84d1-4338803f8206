<template>
  <Dialog :title="title"
    :width="width"
    :visible="visible"
    :close-on-click-modal="closeModal"
    :destroy-on-close="true"
    :append-to-body="true"
    :show-close="false"
    @close="handleClose"
    custom-class="custom-cockipt-dialog-wrap">
    <!-- 公共头部 -->
    <div slot="header"
      class="header-box">
      <div class="mode mode-left">
        <el-select v-if="isYearCode"
          v-model="yearSelect"
          popper-class="cockipt-select-down"
          class="cockipt-select-wrap m-r-10"
          style="width: 56px;"
          size="medium"
          @change="changeYear">
          <el-option v-for="item in yearList"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>

        <el-select v-if="isYearCode"
          v-model="activeCode"
          popper-class="cockipt-select-down"
          class="cockipt-select-wrap m-r-10"
          style="width: 80px;"
          size="medium"
          @change="changeCompanyList">
          <el-option v-for="item in getCompanyList"
            :key="item.orgCode"
            :label="item.orgName"
            :value="item.orgCode">
          </el-option>
        </el-select>
      </div>
      <h4 class="mode mode-middel">{{title}}</h4>
      <div class="mode mode-right">
        <i class="close el-icon-circle-close"
          @click="handleClose" />
      </div>
    </div>

    <!-- 自定义内容 -->
    <div slot="body"
      class="content-box">
      <slot />
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { getGroupList } from '@/api/public'
import Dialog from '@/components/Dialog/index.vue'

@Component({
  components: {
    Dialog
  }
})
export default class Header extends Vue {
  @Prop() private visible!: boolean // 弹窗显隐
  @Prop() private code!: string // 集团code
  @Prop() private year!: string // 年份
  @Prop({ default: false }) private isYearCode?: boolean // 是否展示年份和集团控件
  @Prop({ default: '标题' }) private title?: string // 标题
  @Prop({ default: '1000px' }) private width?: string // 弹窗宽度
  @Prop({ default: true }) private closeModal?: boolean // 弹窗是否可点击mark关闭

  private getCompanyList: any[] = []
  private yearList: string[] = []
  private yearSelect = ''
  private activeCode = ''

  // 组件初始化
  private created() {
    this.yearSelect = this.year
    this.activeCode = this.code

    if (this.isYearCode) {
      this.getYearList()
      this.getOrgList()
    }
  }

  // 集团：获取数据
  private async getOrgList() {
    let { data } = await getGroupList()

    if (Array.isArray(data) && data.length) {
      this.getCompanyList = data || []
    }
  }

  // 年份：获取数据
  private getYearList() {
    let list = []
    let yearNow = (this as any).$moment(new Date()).add('year', 0).format('YYYY')
    for (let i = 0; i < 5; i++) {
      let year = yearNow - i
      list.push(String(year))
    }
    this.yearList = list
  }

  // 年份：切换
  private changeYear() {
    this.$emit('onEmitYear', this.yearSelect)
  }

  // 集团：切换
  private changeCompanyList() {
    this.$emit('onEmitActiveCode', this.activeCode)
  }

  // 弹窗：关闭
  private handleClose() {
    this.$emit('update:visible', false)
  }
}
</script>