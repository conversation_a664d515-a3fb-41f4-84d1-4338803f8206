/** 资金存贷规模 */

<template>
  <div class="deposit-and-loanScale">
    <div id="DepositAndLoanScale" />
    <div class="halo"></div>
  </div>

</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { DepositAndLoanScaleData } from '@/views/main/cockpitSecondary/baseData'
import { capitalProfile } from '@/api/cockpit'
import * as echarts from 'echarts'
import { deepClone } from '@/utils'

type EChartsOption = echarts.EChartsOption

@Component
export default class extends Vue {
  private loading = false
  private maxValue = 100
  private year = ''
  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}
  private tabList = []
  private echartsDatas: any[] = []
  private seriesData: any[] = deepClone(DepositAndLoanScaleData)
  private tabActive: {
    code: string
    name: string
  } = {
    code: '',
    name: ''
  }

  // 当前年月
  get getCurrentDate() {
    let date = new Date()
    return `截止到${date.getFullYear()}年${date.getMonth() + 1}月`
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('DepositAndLoanScale') as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    this.initEcharts()
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await capitalProfile()
    if (!Array.isArray(data) || !data.length) return
    this.echartsDatas = data
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 初始化数据
  private initEcharts() {
    let textSize = 22
    let textColor = '#999'
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    this.option = {
      color: ['#E6B607', '#EF702E', '#35AAF6', '#00AEA5', '#EC342F', '#2D5AE5'],
      legend: {
        show: false
      },
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          type: 'pie',
          center: ['50%', '50%'],
          radius: ['0%', '60%'],
          avoidLabelOverlap: false,
          // stillShowZeroSum: false,
          selectedMode: 'single',
          label: {
            show: true,
            position: 'outside',
            fontSize: textSize * 1.2,
            color: '#5db0ea',
            fontWeight: 'bold',
            formatter: ({ data }: { data: any }) => {
              return ` {a|${data.value}亿元}\n {hr|}\n {b|${data.name}}`
            },
            rich: {
              a: {
                fontSize: '40px',
                color: '#fff',
                fontWeight: 'bold',
                opacity: 0.8
              },
              b: {
                fontSize: '30px',
                color: '#20DCF9',
                fontWeight: 'bold'
              },
              hr: {
                borderColor: '#8C8D8E',
                width: '100%',
                borderWidth: 1,
                height: 0
              }
            }
          },
          labelLine: {
            length: 30,
            length2: 60,
            smooth: 0.2,
            lineStyle: {
              width: 3
            }
          },
          itemStyle: {
            borderRadius: 0,
            borderWidth: 10
          },
          data: this.seriesData
        }
      ]
    }

    this.myChart && this.myChart.setOption(this.option)
  }
  // 年份切换
  private yearHandler(year: string) {
    this.year = year
    // this.initData()

    // this.initEcharts()
  }
}
</script>

<style scoped lang="scss">
#DepositAndLoanScale {
  width: 100%;
  height: 100%;
}
.deposit-and-loanScale {
  position: relative;
  width: 100%;
  height: 100%;
  .halo {
    position: absolute;
    z-index: -1;
    top: 50%;
    left: 50%;
    width: 280px;
    height: 280px;
    transform: translate(-50%, -50%);
    background: url('../../images/halo.png');
    background-size: 100% 100%;
    will-change: width, height, transform;
    animation: move 1000s linear forwards infinite;
  }

  @keyframes move {
    0% {
      transform: translate(-50%, -50%) rotateZ(0deg);
      width: 280px;
      height: 280px;
    }
    50% {
      transform: translate(-50%, -50%) rotateZ(360deg);
      width: 290px;
      height: 290px;
    }
    100% {
      transform: translate(-50%, -50%) rotateZ(0deg);
      width: 280px;
      height: 280px;
    }
  }
}
</style>