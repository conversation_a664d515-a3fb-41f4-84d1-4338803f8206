<template>
  <el-container class="table-wrapper"
    direction="vertical">
    <search-bar :items="searchItems"
      @onSearch="handleSearch">
      <template slot="orgtree">
        <orgTree v-model="orgoptions.bizCodeList"
          class="mode-input"
          width="150"
          :deptCode="orgoptions.orgCode"
          @nodeChange="refresh" />
      </template>
      <!-- <el-button type="primary" @click="OpenAddtarget">新增</el-button>
      <el-button type="primary" @click="SeeDetail">查看</el-button>
      <el-button type="primary" @click="onAllocation">分配</el-button>
      <el-button type="primary" @click="ReviewTarget">审核</el-button>
      <el-button type="primary" @click="Reset">重新发起</el-button> -->
      <!-- <el-button type="primary" @click="refresh">刷新</el-button> -->
    </search-bar>
    <!-- 表格 -->
    <Grid v-loading="loadingTable"
      ref="grid"
      :columns="cols"
      :show-selection="true"
      :show-pagination="true"
      :overflow-tooltip="true"
      :search-params="searchParams"
      :show-index="true"
      show-index-fixed="left"
      :remoteUrl="remoteUrl"
      @row-click='SeeDetail'
      border>
      <template #operationSlot="{ row }">
        <Operationbutton :data="row"
          :items="buttonItems" />
      </template>
    </Grid>
    <!-- 新增 -->
    <add-target :visible.sync="AddVisible"
      v-if="AddVisible"
      :mode="mode"
      :detaildata="detaildata" />
    <!-- 查看 -->
    <detail-target :visible.sync="DetailVisible"
      v-if="DetailVisible"
      :mode="mode"
      :detaildata="detaildata" />
    <!-- 审核 -->
    <review-target :visible.sync="ReviewTargetVisible"
      v-if="ReviewTargetVisible"
      :mode="mode"
      :detaildata="detaildata" />
    <!-- 分配 -->
    <DialogDistribute v-if="visibleDistribute"
      :assetId="assetId"
      :detaildata="detaildata"
      :visible.sync="visibleDistribute" />
  </el-container>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import AddTarget from './AddTargetTransaction.vue'
import SearchBar from '@/components/SearchBar/index.vue'
import Grid from '@/components/Grid/index.vue'
import DetailTarget from './TargetDetail.vue'
import ReviewTarget from './ReviewTarget.vue'
import DialogDistribute from './components/DialogDistribute.vue'
import Operationbutton from '@/components/OperationButton/index.vue'
import { getUserInfo } from '@/api/public'
import { PermissionModule } from '@/store/modules/permissionDict'
import { BusinessModule } from '@/store/modules/businessDict'
import orgTree from '@/components/FormComment/orgTree.vue'
interface ButtonItem {
  label?: string
  permission?: string
  click?: Function
  visible?: Function
  extra?: object
}
@Component({
  components: {
    AddTarget,
    SearchBar,
    Grid,
    DetailTarget,
    Operationbutton,
    ReviewTarget,
    DialogDistribute,
    orgTree
  }
})
export default class extends Vue {
  private mode = 'add'
  public AddVisible = false
  private remoteUrl = '/fht-monitor/ast/subject/lease/page'
  private loadingTable = false
  private DetailVisible = false
  private orgoptions = {
    bizCodeList: [],
    orgCode: ''
  }
  private searchParams = {}
  private ReviewTargetVisible = false //审核
  private visibleDistribute = false //分配显示
  private assetId = '' //资产id
  private orgCode = '' //组织机构码
  private detaildata: any = {}
  @Watch('ReviewTargetVisible')
  private refushtable(state: any) {
    if (!state) {
      this.refresh()
    }
  }
  @Watch('DetailVisible')
  private refushtable1(state: any) {
    if (!state) {
      this.refresh()
    }
  }
  @Watch('visibleDistribute')
  private refushtable2(state: any) {
    if (!state) {
      this.refresh()
    }
  }
  private buttonItems: any = [
    {
      label: '查看',
      permission: 'string',
      click: this.SeeDetail,
      visible: (row: any) => {
        return true
        // return this.getPermission('assets_targetTransaction_view')
      }
      // extra?: {}
    },
    {
      label: '重新发起',
      permission: 'string',
      click: this.Reset,
      visible: (row: any) => {
        return (
          (row.auditStatus == 1000 && this.getPermission('assets_targetTransaction_review')) ||
          (row.auditStatus == 1003 && this.getPermission('assets_targetTransaction_review'))
        )
      }
      // extra?: {}
    },
    {
      label: '分配',
      permission: 'string',
      click: this.onAllocation,
      visible: (row: any) => {
        return row.auditStatus == 1000 && this.getPermission('assets_targetTransaction_distribution')
      }
      // extra?: {}
    },
    {
      label: '审核',
      permission: 'string',
      click: this.ReviewTarget,
      visible: (row: any) => {
        // return true
        return row.auditStatus == 1001 && this.getPermission('assets_targetTransaction_audit')
      }
      // extra?: {}
    }
  ]
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  private searchItems = [
    {
      type: 'text',
      key: 'keyword',
      placeholder: '标的编号/标的名称',
      width: '300px'
    },
    {
      type: 'select',
      key: 'auditStatus',
      placeholder: '状态',
      width: '150px',
      options: this.getDictData('subjectLeaseLifeCycleStatus')
    },
    {
      type: 'orgTree',
      key: 'orgCode',
      placeholder: '机构树',
      width: '150px'
      // options: this.getDictData('subjectLeaseLifeCycleStatus')
    },
    {
      type: 'select',
      key: 'selectType',
      placeholder: '遴选方式',
      width: '150px',
      options: this.getDictData('selectType')
    },
    {
      type: 'daterange',
      key: ['startTime', 'endTime'],
      placeholder: ['开始日期', '结束日期']
    }
  ]
  private cols = [
    {
      label: '标的编号',
      prop: 'subjectNo'
    },
    {
      label: '状态',
      prop: 'auditStatusDesc',
      minWidth: '120'
    },
    {
      label: '标的名称',
      prop: 'subjectName'
    },
    {
      label: '资产数量',
      prop: 'assetNum'
    },
    {
      label: '出租方类型',
      prop: 'lessorTypeDesc',
      minWidth: '100'
    },
    {
      label: '出租方名称',
      prop: 'lessorName',
      minWidth: '100'
    },
    {
      label: '出租面积(㎡)',
      prop: 'rentArea',
      minWidth: '100'
    },
    {
      label: '挂牌金额(元)',
      prop: 'rentPrice',
      minWidth: '100'
    },
    {
      label: '租期',
      prop: 'leaseTermDesc'
    },
    {
      label: '有无免租期',
      prop: 'hasFreeTermDesc',
      minWidth: '100'
    },
    {
      label: '遴选方式',
      prop: 'selectTypeDesc'
    },

    {
      label: '使用用途',
      prop: 'selectTypeDesc'
    },
    {
      label: '申请人',
      prop: 'createUserName'
    },
    {
      label: '申请时间',
      prop: 'createTime',
      minWidth: '150'
    },
    {
      label: '操作',
      slotName: 'operationSlot',
      fixed: 'right'
    }
  ]
  mounted() {
    this.getUserInfo()
  }
  // 获取用户信息
  private async getUserInfo() {
    try {
      let res: any = await getUserInfo({})
      if (res.code == 200) {
        //
        this.orgCode = res.data.orgCode
        this.refresh()
      }
    } catch (e) {
      console.error(e)
    }
  }

  // 打开新增标的
  private OpenAddtarget() {
    this.AddVisible = true
  }
  // 查看详情
  private SeeDetail(data: any) {
    this.mode = 'see'
    this.detaildata = data
    this.DetailVisible = true
  }
  private onAllocation(row: any) {
    //
    this.detaildata = row
    this.assetId = row.id
    this.visibleDistribute = true
    // this.refresh()
  }
  // 重新发起
  private Reset(data: any) {
    this.mode = 'reset'
    this.detaildata = data
    this.AddVisible = true
  }
  // 审核
  private ReviewTarget(data: any) {
    this.mode = 'see'
    this.detaildata = data
    this.ReviewTargetVisible = true
  }
  private handleSearch(condition: any) {
    this.searchParams = condition
    this.refresh()
    // 搜索
  }
  //  更新表格（）
  private refresh() {
    Object.assign(this.searchParams, this.orgoptions)
    let gridRef: any = this.$refs.grid
    gridRef.refresh(true)
  }
  // 权限方法
  get getPermission() {
    return (code: string) => {
      return PermissionModule.dictLaodData.includes(code) || false
    }
  }
}
</script>

<style lang="scss" scoped>
.table-wrapper {
  height: 100%;
}
</style>
