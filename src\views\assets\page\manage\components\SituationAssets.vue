<template>
  <section class="situation-assets-wrap">
    <el-descriptions title="资产基本信息"
      size="medium"
      class="descriptions-box"
      border>
      <el-descriptions-item label="所属集团">{{ detailData.orgName }}</el-descriptions-item>
      <el-descriptions-item label="主资产编号">{{ detailData.assetNo }}</el-descriptions-item>
      <el-descriptions-item label="主资产名称">{{ detailData.mainAssetName }}</el-descriptions-item>
      <el-descriptions-item label=" 资产地址">{{ detailData.address }}</el-descriptions-item>
      <el-descriptions-item label="资产用途">{{ detailData.assetPurposeDesc }}</el-descriptions-item>
      <el-descriptions-item label="资产性质">{{ detailData.manageTypeDesc }}</el-descriptions-item>
      <el-descriptions-item label="直属单位">{{ detailData.bizCode }}</el-descriptions-item>
      <el-descriptions-item label="建筑面积">{{ detailData.coveredArea }}</el-descriptions-item>
      <!--<el-descriptions-item
        label="资产原值">{{ detailData.originalValue || '-' }}万</el-descriptions-item>-->
      <!-- <el-descriptions-item label="资产编号">{{ detailData.certOrgName }}</el-descriptions-item> -->
      <!-- <el-descriptions-item label="资产类型">
        <span v-if="+detailData.assetType === 1">房产</span>
        <span v-if="+detailData.assetType === 2">土地</span>
      </el-descriptions-item> -->
      <!-- <el-descriptions-item label="所在位置">
        {{ detailData.province }}
        {{ detailData.city }}
        {{ detailData.district }}
        {{ detailData.town }}
      </el-descriptions-item>
      <el-descriptions-item label="总楼层">{{ detailData.totalFloor }}</el-descriptions-item>
      <el-descriptions-item label="资产坐落">{{ detailData.location }}</el-descriptions-item>

      <el-descriptions-item label="风险等级">
        {{ getDictLabelData('asset_quality_leve', detailData.safetyLevel) }}
      </el-descriptions-item>
      <el-descriptions-item label="建筑面积(㎡)">{{ detailData.totalArea }}</el-descriptions-item> -->
      <!-- <el-descriptions-item label="土地总面积(㎡)">{{detailData.landArea}}</el-descriptions-item> -->
    </el-descriptions>

    <el-descriptions title="产权信息"
      class="descriptions-box"
      :column="3"
      border
      size="medium">
      <el-descriptions-item label="不动产权证号">{{ detailData.realEstateCertNo }}</el-descriptions-item>
      <el-descriptions-item :labelStyle="{padding: '0'}" :contentStyle="{padding: '0'}">
        <template slot="label">
          <div style="padding: 10px;border-bottom: 1px solid #EBEEF5;">建筑面积（㎡）</div>
          <div style="padding: 10px 10px 10px">土地面积（㎡）</div>
        </template>
          <div style="padding: 10px;border-bottom: 1px solid #EBEEF5;">{{detailData.realEstateCertArea}}</div>
          <div style="padding: 10px">{{detailData.realEstateLandArea}}</div>
      </el-descriptions-item>
      <el-descriptions-item label="产权人">{{ detailData.realEstateOwner }}</el-descriptions-item>
      <el-descriptions-item label="房产证号">{{ detailData.houseCertNo }}</el-descriptions-item>
      <el-descriptions-item label="建筑面积（㎡）">{{ detailData.houseCertArea }}</el-descriptions-item>
      <el-descriptions-item label="产权人">{{ detailData.houseOwner }}</el-descriptions-item>
      <el-descriptions-item label="土地证号">{{ detailData.landCertNo }}</el-descriptions-item>
      <el-descriptions-item label="土地面积（㎡）">{{ detailData.landCertArea }}</el-descriptions-item>
      <el-descriptions-item label="产权人">{{ detailData.landOwner }}</el-descriptions-item>

      <!-- <el-descriptions-item label="资产估值">{{detailData.leaseEvaluateValue || '--'}}万
      </el-descriptions-item>
      <el-descriptions-item label="资产评估价值">{{detailData.assetEvaluateValue || '--'}}万
      </el-descriptions-item>
      <el-descriptions-item label="资产评估价格">{{detailData.leasePrice || '--'}}万
      </el-descriptions-item> -->
    </el-descriptions>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { BusinessModule } from '@/store/modules/businessDict'

@Component({})
export default class extends Vue {
  @Prop({ default: {} }) private detailData!: object

  // 获取字典数据
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }

  // 获取字典项label
  get getDictLabelData() {
    return (type: string, value: string) => {
      if (this.getDictData(type).length) {
        let obj = this.getDictData(type).find((item: any) => {
          return +item.value === +value
        })

        return obj ? obj.label : ''
      } else {
        return ''
      }
    }
  }
}
</script>

<style scoped lang="scss">
.situation-assets-wrap {
  position: relative;

  ::v-deep .descriptions-box {
    margin-bottom: 20px;
    .el-descriptions__header {
      margin-bottom: 10px;
      .el-descriptions__title {
        font-size: 15px;
        font-weight: normal;
        &::before {
          display: inline-block;
          content: ' ';
          width: 3px;
          height: 15px;
          margin-right: 6px;
          background: #ce4c4c;
          transform: translateY(2px);
        }
      }
    }
    .el-descriptions-item {
      font-size: 13px;
    }
  }
}
</style>
