/**
  组件描述: 计划完成情况
*/
<template>
  <section v-loading="loading"
    class="plan-completion">
    <ProgressChart chartId="PlanCompletion"
      :seriesData="seriesData"
      :individuationOptions="individuationOptions"
      :name="chartName" />

    <!-- <div class="info">
      <div class="title">年度已完成投资(亿元)</div>
      <div class="value">{{ seriesData.complete }}</div>
      <div
        :class="['rate', seriesData.completeYoy > 0 && 'up', seriesData.completeYoy < 0 && 'down' ]">
        同比：
        <span><i v-if="seriesData.completeYoy > 0">+</i>{{seriesData.completeYoy}}%</span>
      </div>
    </div> -->
  </section>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import ProgressChart from '@/views/main/cockpitSecondary/charts/ProgressChart.vue'
import { EChartsOption } from 'echarts'
import { getPlanCompleted, ProjectProgressDataItem } from '@/api/cockpit'
import { Loading } from '@/decorators'

@Component({
  components: {
    ProgressChart
  }
})
export default class extends Vue {
  @Prop({ default: '0' }) private activeTabs!: string

  @Watch('activeTabs', { immediate: true })
  private activeTabsChange() {
    this.params.orgCode = this.activeTabs
    this.fetchData()
  }

  private loading = false
  // 参数
  private params: any = {
    orgCode: '0',
    year: String(new Date().getFullYear()),
    projectProperty: ''
  }

  private chartName = ''
  private seriesData = {
    // 计划投资
    plan: 0,
    // 完成投资
    complete: 0,
    // 计划同比
    planYoy: 0,
    // 完成同比
    completeYoy: 0
  }

  get rate() {
    return +(((+this.seriesData.complete || 0) / (+this.seriesData.plan || 0)) * 100).toFixed(2)
  }

  get individuationOptions(): EChartsOption {
    return {
      title: [
        {
          text: `{${this.seriesData.completeYoy > 0 ? 'a' : 'b'}|同比：${this.seriesData.completeYoy > 0 ? '+' : ''}${
            this.seriesData.completeYoy
          }%}`,
          left: 'center',
          top: '58%',
          textStyle: {
            rich: {
              a: {
                fontSize: 38,
                color: '#FF368B',
                fontWeight: 'bold'
              },
              b: {
                fontSize: 38,
                color: '#42FF5B',
                fontWeight: 'bold'
              }
            }
          }
        }
      ],
      series: [
        {
          center: ['50%', '90%'],
          axisLine: {
            roundCap: false,
            lineStyle: {
              width: 50,
              color: [[1, '#093091']]
            }
          },
          axisLabel: {
            distance: 40,
            formatter: '{value} %',
            color: '#fff',
            fontSize: 24
          },
          progress: {
            show: true,
            roundCap: false,
            width: 50,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: '#168BC1' // 0% 处的颜色
                  },
                  {
                    offset: 0.7,
                    color: '#40EEFF' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          },
          splitNumber: 1,
          detail: {
            offsetCenter: ['0', '-20%'],
            fontSize: 40,
            color: '#FFC62C',
            rich: {
              a: {
                color: '#FFC62C',
                opacity: 1
              }
            }
          },
          data: [
            {
              title: {
                color: '#fff',
                fontWeight: 'normal',
                offsetCenter: ['0', '5%']
              }
            }
          ]
        }
      ]
    }
  }

  mounted() {
    this.listenerDate()
    this.$bus.$on('projectInvestmentTypeChange', (projectProperty: string) => {
      this.params.projectProperty = projectProperty
      this.fetchData()
    })
  }

  // 监听时间变化
  private listenerDate() {
    this.$bus.$on('BusYearTimeChange', (year: string) => {
      this.params.year = year
      this.fetchData()
    })

    this.$bus.$on('BusMoonTimeChange', (month: string) => {
      this.params.month = month
      this.fetchData()
    })
  }

  // 获取数据
  @Loading('loading')
  private async fetchData() {
    let res = await getPlanCompleted(this.params)

    if (res.success) {
      this.processingData(res.data)
      // end 
    }

      // this.processingData(res.data)
  }

  // 处理数据
  private processingData(data: Array<ProjectProgressDataItem>) {
    this.chartName = data[1].indicatorName
    this.seriesData = {
      plan: +data[0].indicatorValue,
      planYoy: +data[0].indicatorRate,
      complete: +data[1].indicatorValue,
      completeYoy: +data[1].indicatorRate
    }
  }
}
</script>


<style scoped lang="scss">
.plan-completion {
  position: relative;
  width: 100%;
  height: 120%;
  transform: translateY(-90px);
  .info {
    position: absolute;
    right: 20px;
    top: 0px;
    padding: 4px 10px;
    height: 150px;
    background: linear-gradient(0deg, #0939a0, rgba(8, 45, 133, 0));
    border: 2px solid #0938a2;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    font-size: 28px;
    .value {
      color: #ffc62c;
      font-size: 40px;
      font-weight: normal;
      font-family: 'PangMenZhengDao';
    }
    .rate {
      display: flex;
      align-items: center;
    }
  }
  .up {
    font-size: 30px;
    font-weight: bold;
    color: #ff368b;
    span {
      font-size: 34px;
      font-weight: normal;
      font-family: 'PangMenZhengDao';
      white-space: nowrap;
    }
  }
  .down {
    font-size: 38px;
    font-weight: bold;
    color: #5aff3a;
    span {
      font-size: 48px;
      font-weight: normal;
      font-family: 'PangMenZhengDao';
      white-space: nowrap;
    }
  }
}
</style>