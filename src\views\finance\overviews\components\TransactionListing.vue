<template>
  <section class="echarts-dom-wrap">
    <div v-loading="loading" ref="chartDom" class="chartDom" :class="{ hide: !interfaceData.length }" />

    <el-empty description="暂无数据" class="empty-none-data" :class="{ none: interfaceData.length }" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { assetsOverYear } from '@/api/assetsv2'
import { Loading } from '@/decorators'
import * as echarts from 'echarts'

let chartDom = null
let myChart: any = null

@Component({})
export default class extends Vue {
  @Prop() private orgCode!: string // 集团code
  @Prop() private year!: string // 年份

  private loading = false
  private yAxisData: string[] = []
  private seriesData: any[] = []
  private interfaceData = []

  // 监听搜索条件改变
  @Watch('orgCode', { deep: true })
  @Watch('year', { deep: true })
  private watchFormData() {
    this.initData()
  }

  // 组件初始化
  private mounted() {
    this.$nextTick(() => {
      chartDom = this.$refs.chartDom
      myChart = echarts.init(chartDom as HTMLElement)

      this.initData()
    })
  }

  // 获取数据
  @Loading('loading')
  private async initData() {
    let { data } = await assetsOverYear({
      orgCode: this.orgCode,
      year: this.year
    })

    this.interfaceData = data || []

    // let data = [
    //   {
    //     year: '2019',
    //     occupancyRate: 77,
    //     vacancyRate: 23
    //   },
    // ]

    // 组装数据
    let yAxisData: string[] = []
    let vacancyList: number[] = []
    let occupancyList: number[] = []
    Array.isArray(data) &&
      data.forEach(item => {
        yAxisData.push(item.year)
        vacancyList.push(+item.vacancyRate)
        occupancyList.push(+item.occupancyRate)
      })

    this.yAxisData = yAxisData
    this.seriesData = [
      {
        name: '出租率',
        type: 'bar',
        stack: 'total',
        label: {
          show: true
        },
        data: occupancyList
      },
      {
        name: '空置率',
        type: 'bar',
        stack: 'total',
        label: {
          show: true
        },
        data: vacancyList
      }
    ]
    this.initEcharts()
  }

  // 初始化echarts组件
  private initEcharts() {
    let yAxisData = this.yAxisData
    let seriesData = this.seriesData
    let list: any = [
      {
        name: '城投集团',
        value: Math.ceil(Math.random() * 100)
      },
      {
        name: '国资运营',
        value: Math.ceil(Math.random() * 100)
      },
      {
        name: '交投集团',
        value: Math.ceil(Math.random() * 100)
      },
      {
        name: '金投集团',
        value: Math.ceil(Math.random() * 100)
      },
      {
        name: '轨道集团',
        value: Math.ceil(Math.random() * 100)
      }
    ]
    let option = {
      color: ['#5470c6', '#c3c3c3'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        valueFormatter: function(value: string) {
          return value + ' %'
        }
      },
      legend: {},
      grid: {
        top: '14%',
        left: '1%',
        right: '4%',
        bottom: '0%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      yAxis: {
        type: 'category',
        data: yAxisData
      },
      series: seriesData
    }
    let optiontest = {
      grid: {
        top: '14%',
        left: '1%',
        right: '4%',
        bottom: '0%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: list.map((res: any) => {
          return res.name
        })
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          data: list.map((res: any) => {
            return res.value
          }),
          type: 'bar',
          barWidth: '40%',
          label: {
            show: true,
            position: 'top'
          }
        }
      ]
    }
    myChart && myChart.setOption && myChart.setOption(optiontest)
  }
}
</script>

<style scoped lang="scss">
.chartDom {
  width: 100%;
  height: 100%;
}
</style>
