/* 财务监管 */

<template>
  <section class="assets-comment-wrap">
    <!-- <TitleCom title="财务监管"
      module="FinancialIndicator" /> -->

    <div class="module-pulic-box">
      <Tabs type="assets"
        @tabsHandel="tabsHandel"
        @tabsComsHandel="tabsComsHandel" />

      <!-- 自定义时间轴 -->
      <div class="years-box">
        <span v-for="item of yearList"
          :key="item">{{item}}</span>
      </div>

      <div v-loading="loading"
        id="financialIndicator" />
    </div>

    <!-- 详情弹窗 -->
    <CockiptGridDialog v-if="visibleDetail"
      :visible.sync="visibleDetail"
      :year="year"
      :closeModal="false"
      :code="companyData.code"
      :columns="columns"
      :remoteUrl="remoteUrl"
      :searchParams="searchParams"
      title="财务监管" />
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { capitalProfile } from '@/api/cockpit'
import { echartConfigure, companyList, colorSixList } from '@/views/main/cockpitcren/baseData'
import { Loading, Throttle } from '@/decorators'
import { isWindowFull, bigNumberFormat } from '@/utils'
import * as echarts from 'echarts'
import Tabs from '@/views/main/cockpitcren/components/Public/Tabs.vue'
import TitleCom from '@/views/main/cockpitcren/components/Public/TitleCom.vue'
import CockiptGridDialog from '@/views/main/cockpitcren/components/Public/CockiptDialog.vue'

type EChartsOption = echarts.EChartsOption
type typeSeries = {
  id: ''
  code: string
  name: string
}

@Component({
  components: {
    Tabs,
    TitleCom,
    CockiptGridDialog
  }
})
export default class extends Vue {
  private loading = false
  private year = ''
  private maxValue = 100
  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private option: EChartsOption = {}
  private tabList = []
  private echartsDatas: any[] = []
  private seriesData: any[] = []
  private yearList: number[] = []
  private tabActive: typeSeries = {
    id: '',
    code: '',
    name: ''
  }
  private companyData: typeSeries = {
    id: '',
    code: '',
    name: ''
  }

  // 详情表格数据
  private visibleDetail = false
  private remoteUrl = '/fht-monitor/ds/screen/data/asset'
  private searchParams = {}
  private columns: any[] = [
    {
      prop: '',
      label: '企业名称'
    },
    {
      prop: '',
      label: '所属集团'
    },
    {
      prop: '',
      label: '资产总额'
    },
    {
      prop: '',
      label: '同比'
    },
    {
      prop: '',
      label: '资产净额'
    },
    {
      prop: '',
      label: '同比'
    },
    {
      prop: '',
      label: '营收总额'
    },
    {
      prop: '',
      label: '同比'
    },
    {
      prop: '',
      label: '利润总额'
    },
    {
      prop: '',
      label: '同比'
    },
    {
      prop: '',
      label: '负债总额'
    },
    {
      prop: '',
      label: '同比'
    }
  ]

  // 判断窗口是否处于全屏模式
  get getIsWindowFull() {
    return isWindowFull
  }

  // 万元数据转化成亿元
  get getBigNumberFormat() {
    return (num: string | number) => {
      return bigNumberFormat(+num).value || 0
    }
  }

  // 获取集团列表（文案、对象）数据
  get getCompanyList() {
    return (type = false) => {
      let list: any[] = []

      companyList.forEach((item) => {
        if (+item.code > 0) {
          type ? list.push(item.name) : list.push(item)
        }
      })
      return list
    }
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('financialIndicator') as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)
    this.year = String((this as any).$moment(new Date()).add('year', 0).format('YYYY') - 1)
    this.yearList = [+this.year - 4, +this.year - 3, +this.year - 2, +this.year - 1, +this.year]

    this.initData()
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await capitalProfile({
      year: String(+this.year - 1),
      companyCode: this.tabActive.code
    })

    if (!Array.isArray(data) || !data.length) return
    this.echartsDatas = data
    this.filterEchartsData()
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 返回数组数据按年份大小排序
  private companyListSort(key: string) {
    return function (a: any, b: any) {
      return a[key] - b[key]
    }
  }

  // 筛选相关数据
  private filterEchartsData() {
    let filterSeries = this.echartsDatas.filter((item) => {
      return item.itemCode === this.tabActive.code
    })

    let seriesData: any[] = []
    this.getCompanyList().forEach((itemCompany) => {
      let objList = filterSeries.filter((item) => {
        return +itemCompany.code === +item.companyCode
      })

      objList.sort(this.companyListSort('year'))

      let datas: number[] = []
      objList.forEach((item) => {
        datas.push(this.getBigNumberFormat(item.itemValue))
      })

      // 编写不同的样式
      let areaStyle = {}
      switch (+itemCompany.code) {
        case 1:
          areaStyle = {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(238, 181, 0, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(238, 181, 0, 0)'
              }
            ])
          }
          break
        case 2:
          areaStyle = {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(255, 103, 0, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(255, 103, 0, 0)'
              }
            ])
          }
          break
        case 3:
          areaStyle = {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(0, 171, 253, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(0, 171, 253, 0)'
              }
            ])
          }
          break
        case 4:
          areaStyle = {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(0, 177, 165, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(0, 177, 165, 0)'
              }
            ])
          }
          break
        case 5:
          areaStyle = {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(255, 4, 29, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(255, 4, 29, 0)'
              }
            ])
          }
          break
        case 6:
          areaStyle = {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(35, 87, 237, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(35, 87, 237, 0)'
              }
            ])
          }
          break
      }

      seriesData.push({
        name: itemCompany.name,
        type: 'line',
        symbol: 'circle',
        smooth: true,
        symbolSize: 0,
        lineStyle: {
          width: echartConfigure.borderWidth
        },
        tooltip: {
          valueFormatter: function (value: number) {
            return value + ' 亿元'
          }
        },
        data: datas,
        areaStyle: areaStyle
      })
      // end
    })

    this.seriesData = seriesData
    this.initEcharts()
  }

  // 初始化数据
  private initEcharts() {
    let legendSize = echartConfigure.legendSize
    let textSize = echartConfigure.textSize
    let series = this.seriesData
    let yearList = this.yearList
    let companyData = this.companyData
    let companyList = this.getCompanyList(true)
    let tooltipData: any = Object.assign(
      {
        trigger: 'axis'
      },
      echartConfigure.tooltip
    )

    // 通过地图 tabs 筛选单独集团数据
    if (+companyData.code) {
      series = series.filter((item) => {
        return item.name === companyData.name
      })
    }
    // end

    this.option = {
      color: colorSixList,
      legend: {
        show: false,
        top: 0,
        left: 0,
        right: 0,
        padding: 0,
        itemHeight: 14,
        itemWidth: 14,
        itemStyle: {
          borderCap: 'butt'
        },
        lineStyle: {
          type: [0, 10],
          dashOffset: 5
        },
        textStyle: {
          color: '#999',
          fontSize: legendSize,
          fontWeight: 'bold'
        },
        data: companyList
      },
      grid: {
        left: '4%',
        right: '2%',
        bottom: '-4%',
        top: '13%',
        containLabel: true
      },
      tooltip: tooltipData,
      xAxis: {
        show: false,
        type: 'category',
        boundaryGap: false,
        data: yearList,
        axisLabel: {
          margin: 30,
          rotate: 20,
          align: 'right',
          fontSize: textSize,
          fontWeight: 'bold',
          fontFamily: echartConfigure.fontFamilyNumber
        },
        axisLine: {
          lineStyle: {
            color: '#5db0ea'
          }
        }
      },
      yAxis: {
        name: '亿元',
        type: 'value',
        //interval: 100,
        max: function (value) {
          return Math.ceil(value.max) + 10
        },
        nameTextStyle: {
          align: 'left',
          fontSize: 26
        },
        axisLabel: {
          margin: 10,
          fontSize: textSize,
          fontWeight: 'bold',
          formatter: '{value}',
          fontFamily: echartConfigure.fontFamilyNumber
        },
        axisLine: {
          lineStyle: {
            color: '#5db0ea'
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0, 103, 177, 0.4)'
          }
        }
      },
      series: series,
      animationDuration: echartConfigure.animationDuration,
      animationDurationUpdate: echartConfigure.animationDuration
    }

    this.myChart && this.myChart.setOption && this.myChart && this.myChart.setOption(this.option, true)
    this.clickEchartsItem()
  }

  // 点击 eacharts 图表
  @Throttle
  private clickEchartsItem() {
    this.myChart &&
      this.myChart.getZr().on('click', (event: any) => {
        this.visibleDetail = true
      })
  }

  // tabs 切换
  private tabsHandel(item: typeSeries) {
    this.tabActive = item

    if (Array.isArray(this.echartsDatas) && this.echartsDatas.length) {
      this.filterEchartsData()
    }
  }

  // tabs 切换
  private tabsComsHandel(item: typeSeries) {
    this.companyData = item
    this.filterEchartsData()
  }

  // 销毁相关数据
  private destroyed() {
    this.chartDom = null
    this.myChart = null
  }
}
</script>

<style scoped lang="scss">
#financialIndicator {
  width: 100%;
  height: 560px;
}

.years-box {
  position: absolute;
  bottom: 54px;
  left: 161px;
  width: 624px;
  font-size: 34px;
  color: #5db0ea;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>


