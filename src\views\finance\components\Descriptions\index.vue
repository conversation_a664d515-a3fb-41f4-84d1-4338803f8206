<template>
  <el-descriptions title="" border >
    <el-descriptions-item v-for="(item, index) in list" :key="index" :label="item.title">{{ item.label }}</el-descriptions-item>
  </el-descriptions>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
@Component({
  name: 'Container',
  components: {}
})
export default class Container extends Vue {
  @Prop() private list!: []
  private created(){
    // 
    
  }
}
</script>

<style lang="scss" scoped>
// checkTheDetails
::v-deep.el-descriptions-item__label {
  font-size: 14px;
  color: #909399;
}
</style>
