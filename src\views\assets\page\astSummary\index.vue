<template>
  <section v-loading="loading" class="assets-overviews-wrap">
    <el-table
      :data="tableData"
      style="width: 100%;"
      center
      border
      :header-cell-style="{ 'text-align': 'center', color: '#2f2f2f', background: 'rgba(172, 196, 221, 0.1)' }"
      :cell-style="{ 'text-align': 'center',
      'font-size':'20px',
       }"
    >
      <el-table-column type="index" label="序号" width="50"></el-table-column>
      <el-table-column prop="orgName" label="集团名称" width="80"></el-table-column>
      <el-table-column label="资产总况">
        <el-table-column prop="totalNum" label="资产总数（处）" width="130" sortable></el-table-column>
        <el-table-column prop="totalArea" label="资产总建筑面积（㎡）" width="170" sortable></el-table-column>
      </el-table-column>
      <el-table-column label="资产分类">
        <el-table-column prop="selfUseNum" label="自用资产（处）" width="150" sortable></el-table-column>
        <el-table-column prop="bizNum" label="经营性资产（处）" width="150" sortable></el-table-column>
      </el-table-column>
      <el-table-column label="资产经营情况">
        <el-table-column prop="occupancyNum" label="已出租（处）" width="120" sortable></el-table-column>
        <el-table-column prop="occupancyRate" label="出租率（%）" width="120" sortable></el-table-column>
        <el-table-column prop="vacancyNum" label="空置（处）" width="110" sortable></el-table-column>
        <el-table-column prop="rentPrice" label="租赁均价（元/㎡）" width="150" sortable></el-table-column>
      </el-table-column>
      <el-table-column label="浙交汇">
        <el-table-column prop="listed" label="租赁已挂牌(笔)" width="150" sortable></el-table-column>
        <el-table-column prop="dealed" label="租赁已成交(笔)" width="150" sortable></el-table-column>
        <el-table-column prop="turnover" label="租赁成交总额(万)" width="150" sortable></el-table-column>
      </el-table-column>
    </el-table>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'

import { getAstSummary } from '@/api/assetsv2'

@Component({
  components: {}
})
export default class extends Vue {
  private tableData = []
  private loading = false

  private async getPageList() {
    this.loading = true
    try {
      let res = await getAstSummary({})
      if (res.success) {
        this.tableData = res.data
        
      }
    } catch (e) {
      console.error(e)
    } finally {
      this.loading = false
    }
  }

  // 组件初始化
  private mounted() {
    this.getPageList()
  }
}
</script>

<style scoped lang="scss">
.assets-overviews-wrap {
  position: relative;
  height: 100%;
  background: #fff;
  overflow-y: auto;
  padding: 10px 14px;
  box-sizing: border-box;
  h4,
  p {
    margin: 0;
  }
  .header-box {
    text-align: right;
  }
  .divider-box {
    margin: 0 0 20px;
  }
  .conter-box {
    position: relative;
    .modules {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      height: 22vw;
      margin-bottom: 20px;
      &:nth-last-child(1) {
        margin-bottom: 0;
      }
      .mode {
        flex: 1;
        height: 100%;
        margin-right: 20px;
        display: flex;
        padding: 10px;
        border-radius: 4px;
        box-sizing: border-box;
        justify-content: space-between;
        flex-direction: column;
        background: rgba(172, 196, 221, 0.1);
        &:nth-last-child(1) {
          margin-right: 0;
        }
        .til {
          display: flex;
          align-items: center;
          font-weight: normal;
          margin-bottom: 10px;
          color: rgba(245, 108, 108, 0.8);
          i {
            font-size: 20px;
            margin-right: 4px;
          }
          span {
            font-size: 17px;
          }
        }
        .cter {
          flex: 1;
        }
      }
    }
  }

  ::v-deep .echarts-dom-wrap {
    position: relative;
    height: 100%;
    .hide {
      opacity: 0;
    }
    .none {
      display: none !important;
    }
    .empty-none-data {
      position: absolute;
      top: 0;
      width: 100%;
      height: 100%;
      padding: 0;
    }
  }
}
.el-table__row{
  height: 100px;
}
</style>
