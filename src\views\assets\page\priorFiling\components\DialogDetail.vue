<template>
  <Dialog width="900px"
    :title="title"
    :visible="visible"
    @close="handleClose">
    <!-- 内容 -->
    <div slot="body"
      v-loading="loading"
      class="filing-detail-wrap">
      <el-descriptions title="备案信息">
        <el-descriptions-item label="备案编号">{{detailData.recordNo}}</el-descriptions-item>
        <el-descriptions-item label="备案上报时间">{{detailData.reportTime}}</el-descriptions-item>
        <el-descriptions-item label="备案类别">{{detailData.recordTypeDesc}}
        </el-descriptions-item>
      </el-descriptions>

      <hr class="hr" />

      <el-descriptions title="评估信息">
        <el-descriptions-item label="有无评估">{{detailData.hasEvaluatedDesc}}
        </el-descriptions-item>
        <el-descriptions-item label="评估机构">{{detailData.evaluateOrg}}</el-descriptions-item>
        <el-descriptions-item label="评估价">{{detailData.evaluateValue}}</el-descriptions-item>
      </el-descriptions>

      <hr class="hr" />

      <el-descriptions title="租期信息">
        <el-descriptions-item label="租期">{{detailData.leaseTermDesc}}</el-descriptions-item>
        <el-descriptions-item label="租金是否递增">{{detailData.hasIncreased}}
        </el-descriptions-item>
        <el-descriptions-item label="递增幅度">{{detailData.increaseRange}}</el-descriptions-item>
        <el-descriptions-item label="租期开始时间">{{detailData.startDate}}</el-descriptions-item>
        <el-descriptions-item label="租期结束时间">{{detailData.endDate}}</el-descriptions-item>
      </el-descriptions>

      <hr class="hr" />

      <el-descriptions title="其他信息">
        <el-descriptions-item label="其他权利">{{detailData.otherRightsInfo}}
        </el-descriptions-item>
        <el-descriptions-item label="是否具有招商引资的招租行为">{{detailData.investBehaviorDesc}}
        </el-descriptions-item>
        <el-descriptions-item label="出租方联系人">{{detailData.contactName}}</el-descriptions-item>
        <el-descriptions-item label="出租方联系电话">{{detailData.contactPhone}}
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="1">
        <el-descriptions-item label="交易条件与承租方资格条件">{{detailData.leaseCond}}
        </el-descriptions-item>
      </el-descriptions>

      <hr class="hr" />

      <el-descriptions title="附件"
        :column="1">
        <el-descriptions-item label="附件">
          {{detailData.fileList}}
        </el-descriptions-item>
      </el-descriptions>

      <hr class="hr" />

      <el-descriptions title="长租原因、备注"
        :column="1">
        <el-descriptions-item label="长租原因">{{detailData.reason}}</el-descriptions-item>
        <el-descriptions-item label="备注">{{detailData.remark}}</el-descriptions-item>
      </el-descriptions>

      <hr class="hr" />

      <el-descriptions title="资产基本信息"
        :column="3">
        <el-descriptions-item label="资产类别">{{detailData.assetTypeDesc}}</el-descriptions-item>
        <el-descriptions-item label="处置方式">{{detailData.disposalTypeDesc}}
        </el-descriptions-item>
        <el-descriptions-item label="资产编号">{{detailData.assetNo}}</el-descriptions-item>
        <el-descriptions-item label="资产明细编号">{{detailData.itemNo}}</el-descriptions-item>
        <el-descriptions-item label="资产权利人">{{detailData.rightsOwner}}</el-descriptions-item>
        <el-descriptions-item label="不动产权证号">{{detailData.realEstateCertNo}}
        </el-descriptions-item>
        <el-descriptions-item label="房产证号">{{detailData.houseCertNo}}</el-descriptions-item>
        <el-descriptions-item label="土地证号">{{detailData.landCertNo}}</el-descriptions-item>
        <el-descriptions-item label="建筑面积">{{detailData.coveredArea}}</el-descriptions-item>
        <el-descriptions-item label="土地面积">{{detailData.landArea}}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="1">
        <el-descriptions-item label="坐落地址">{{detailData.location}}</el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 底部 -->
    <div slot="footer">
      <el-button @click="handleClose">关 闭</el-button>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { getFilingLeaseDetail } from '@/api/assetsv2'
import Dialog from '@/components/Dialog/index.vue'

@Component({
  components: {
    Dialog
  }
})
export default class extends Vue {
  @Prop({ default: '备案信息' }) private title?: string
  @Prop() private rowData!: any
  @Prop() private visible!: boolean

  private loading = false
  private detailData = {}

  // 初始化数据
  private mounted() {
    this.initDetail()
  }

  // 获取详情数据
  private async initDetail() {
    let { data } = await getFilingLeaseDetail({
      id: this.rowData.id
    })

    this.detailData = data || {}
  }

  // 关闭弹窗
  private handleClose() {
    this.$emit('update:visible', false)
  }
}
</script>

<style lang="scss" scoped>
::v-deep .filing-detail-wrap {
  position: relative;
  .hr {
    height: 10px;
    border: none;
  }
  .el-descriptions {
    .el-descriptions__header {
      color: #585757;
      margin-bottom: 6px;
      .el-descriptions__title {
        font-size: 14px;
      }
    }
    .el-descriptions__body {
      padding: 10px 14px 2px;
      border-radius: 4px;
      background: #f9f9f9;
    }
    .el-descriptions-row {
      font-size: 14px;
      .el-descriptions-item__label {
        color: #797979;
      }
      .el-descriptions-item__content {
        color: #585757;
      }
    }
  }
}
</style>
