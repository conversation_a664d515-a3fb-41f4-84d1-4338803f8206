<template>
  <section class="echarts-dom-wrap">
    <div v-loading="loading"
      ref="chartDom"
      class="chartDom"
      :class="{'hide': !interfaceData.length}" />

    <el-empty description="暂无数据"
      class="empty-none-data"
      :class="{'none': interfaceData.length}" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { groupByOrg } from '@/api/prewarning'
import { Loading } from '@/decorators'
import * as echarts from 'echarts'

let chartDom = null
let myChart: any = null

@Component({})
export default class extends Vue {
  @Prop() private orgCode!: string // 集团code
  @Prop() private year!: string // 年份
  @Prop() private dealStatus?: string // 类型
  @Prop() private isAssets?: false // 判断是否是资产中的“预警汇总”模块

  private loading = false
  private yAxisData: string[] = []
  private seriesData: any[] = []
  private interfaceData: any[] = []

  // 监听搜索条件改变
  @Watch('orgCode', { deep: true })
  @Watch('year', { deep: true })
  @Watch('dealStatus', { deep: true })
  private watchYear() {
    this.initData()
  }

  // 组件初始化
  private mounted() {
    this.$nextTick(() => {
      chartDom = this.$refs.chartDom
      myChart = echarts.init(chartDom as HTMLElement)

      this.initData()
    })
  }

  // 获取数据
  @Loading('loading')
  private async initData() {
    let { data } = await groupByOrg({
      orgCode: this.orgCode,
      year: this.year,
      dealStatus: this.dealStatus
    })

    // let data: any[] = [
    //   {
    //     name: '红色预警',
    //     list: [
    //       {
    //         name: '城投集团',
    //         value: 3
    //       },
    //     ]
    //   },
    // ]

    this.interfaceData = data || []

    // 组装数据
    let yAxisData = []
    let seriesData: any[] = []
    let list: string[] = []
    Array.isArray(data) &&
      data.forEach((item) => {
        let datas: number[] = []
        if (Array.isArray(item.list) && item.list.length) {
          list = []
          item.list.forEach((itemList: { name: string; value: number }) => {
            list.push(itemList.name)
            datas.push(+itemList.value)
          })
        }

        seriesData.push({
          name: item.name,
          type: 'bar',
          barWidth: 20,
          label: { show: true },
          data: datas
        })
      })

    yAxisData = list

    this.yAxisData = yAxisData
    this.seriesData = seriesData
    this.initEcharts()
  }

  // 初始化echarts组件
  private initEcharts() {
    let yAxisData = this.yAxisData
    let seriesData = this.seriesData

    let option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        valueFormatter: function (value: string) {
          return value + ' 个'
        }
      },
      color: ['#D54941', '#f59806', '#feca02', '#00ACFF'],
      legend: {},
      grid: {
        top: '14%',
        left: '1%',
        right: '4%',
        bottom: '0%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: yAxisData
      },
      yAxis: {
        type: 'value'
      },
      series: seriesData
    }

    myChart && myChart.setOption && myChart.setOption(option)
  }
}
</script>

<style scoped lang="scss">
.chartDom {
  width: 100%;
  height: 100%;
}
</style>
