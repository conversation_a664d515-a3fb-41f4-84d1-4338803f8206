// 审批

<template>
  <Dialog
    class="audit_trade_dialog"
    title="审核挂牌申请资料"
    width="50%"
    :visible="visible"
    :append-to-body="true"
    :modal-append-to-body="false"
    @close="closeDlg"
    v-loading="loading"
  >
    <div slot="body" v-if="detailCom">
      <el-steps :active="stepActive" finish-status="success" simple>
        <el-step @click.native="stepActive = 0" title="标的基本信息"></el-step>
        <el-step @click.native="stepActive = 1" title="出租方信息"></el-step>
        <el-step @click.native="stepActive = 2" title="交易条件与承租方条件"></el-step>
        <el-step @click.native="stepActive = 3" title="审核意见"></el-step>
      </el-steps>

      <div class="content">
        <!-- 出租信息 -->
        <RentalInfoDetail v-show="stepActive === 0" :initData="detail" />

        <!-- 出租方信息 -->
        <LessorInfoDetail v-show="stepActive === 1" :initData="detail.lessor" />

        <LesseeQualificationsDetail
          v-show="stepActive === 2"
          :initData="detail"
          ref="LesseeQualificationsForm"
        />

        <div v-show="stepActive === 3">
          <!-- 审核意见 -->
          <AuditOpinion ref="auditoption" :mode="review" :initData="detail" :resultnum="result" />
        </div>
      </div>
    </div>

    <div slot="footer" class="footer">
      <div class="footer-left">
        <!-- <el-button @click="onPrint">打印</el-button> -->
      </div>
      <div class="footer-right">
        <el-button @click="closeDlg">关闭</el-button>
        <el-button v-if="stepActive < 4 && stepActive > 0" type="primary" @click="toStep(-1)">上一步</el-button>
        <el-button v-if="stepActive < 3" type="primary" @click="toStep(1)">下一步</el-button>
        <el-button v-if="stepActive === 3" type="primary" :loading="loading" @click="submitForm">确认</el-button>
      </div>
      <div class="audit_trade_imgList">
        <div class="audit_trade_header">
          <span>附件</span>
          <!-- {{Img.desc}} -->
          <el-popover placement="top-start" width="" trigger="hover">
            <el-tree
              :data="fileNameTree"
              :props="fileTreeProp"
              accordion
              @node-click="handleNodeClick"
              node-key="id"
              ref="tree"
              :default-expanded-keys="expandedKeys"
            ></el-tree>
            <el-button style="font-size:16px" type="text" slot="reference">{{Img.name}}</el-button>
          </el-popover>

          <!-- <el-dropdown trigger="click" @command="handleCommand">
            <span class="el-dropdown-link">
              {{Img.desc}}
              <i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="(item,index) in ImgLists"
                :command="index"
                :key="index"
              >{{(index+1+",")+(item.desc==""?item.name:item.desc+"")}}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown> -->
        </div>
        <el-divider class="m-0"></el-divider>

        <!--  附件显示 -->
        <ImgSEee :src="Img.link"></ImgSEee>
      </div>
    </div>
  </Dialog>
</template>
<script lang="ts">
import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import RentalInfoDetail from './components/RentalInfoDetail.vue'
import LessorInfoDetail from './components/LessorInfoDetail.vue'
import LesseeQualificationsDetail from './components/LesseeQualificationsDetail.vue'
import { Loading, Throttle } from '@/decorators'
import AuditFlow from '@/views/projectInvestment/components/AuditFlow.vue'
import { TradeDetail } from './TradeDetailDialog.vue'
import AuditOpinion from './components/AuditOpinion.vue'
import { SendTransaction } from '@/api/assets'
import ImgSEee from '../ImgListSee/index.vue'
import { ElCheckboxButton } from 'element-ui/types/checkbox-button'
import { deepClone } from '../../../../utils'
import { DetailAsset } from '@/api/assets'
export interface VerifyType {
  assetId: number | string
  attachmentFile1: any
  attachmentFile2: any
  attachmentFile3: any
  result: number | ''
  verifyOpinions: string
}

@Component({
  name: 'Container',
  components: {
    Dialog,
    RentalInfoDetail,
    LessorInfoDetail,
    LesseeQualificationsDetail,
    AuditOpinion,
    ImgSEee
  }
})
export default class Container extends Vue {
  @Prop({ default: () => ({}) }) private initData!: any
  @Prop() private visible!: boolean
  @Watch('stepActive')
  private changestepActive() {
    this.setfileList()
  }
  @Emit('close')
  private onClose() {
    return
  }
  private detailCom=false
  // 通过/驳回状态判断
  private ImgSrc = ''
  private Img:any = {}
  // 附件列表
  private ImgLists: any = []
  private review = 'review'
  private result = 0
  private loading = false
  private stepActive = 0
  private expandedKeys:number[] =[]
  private detail: any = {}
  private verifyFrom: any = {
    assetId: '',
    result: '',
    verifyOpinions: ''
  }
  // 附件列表树
  private fileTreeProp = {
    children: 'children',
    label: 'label'
  }
  private fileNameTree:any = [
    {
      id:1,
      label: '标的基本信息附件',
      children: [
    
      ]
    },
    {
      id:2,
      label: '出租方信息附件',
      children: []
    },
    {
      id:3,
      label: '交易条件与承租方条件',
      children: []
    }
  ]
 async created() {
    // this.detail = Object.assign({}, this.initData) 
  await  this.getdetail(this.initData.id)
  await  this.setfileList()
  }

  // 1 下一步 -1 上一步
  private toStep(direction: 1 | -1) {
    this.stepActive = this.stepActive + direction
  }

  // 点击关闭
  private closeDlg() {
    this.$emit('update:visible', false)
    this.onClose()
  }
  @Throttle
  private submitForm() {
    //
    let state = (this.$refs.auditoption as any).isupload()
    if (state) {
      this.verifyFrom.verifyOpinions = (this.$refs.auditoption as any).verifyOpinions
      this.verifyFrom.attachmentFileDTOList = (this.$refs.auditoption as any).detail.verify.attachmentFileDTOList
      this.verifyFrom.result = (this.$refs.auditoption as any).result
      this.verifyFrom.assetId = this.initData.id
      this.sendTransactionf()
    }
  }
  private async sendTransactionf() {
    try {
      let res = await SendTransaction(this.verifyFrom)
      if (res.success) {
        this.$message.success(res.msg)
        this.closeDlg()
      }
    } catch (e) {
      console.error(e)
    }
  }
  loadFileTree() {
    // this.fileNameTree[0].children = [...this.detail.basic.attachmentFileDTOList, ...this.detail.currentAsset.attachmentFileDTOList]
    this.fileNameTree[0].children = [...this.detail.basic.attachmentFileDTOList]
    this.fileNameTree[1].children = this.detail.lessor.attachmentFileDTOList
    this.fileNameTree[2].children = this.detail.lessee.attachmentFileDTOList
    this.fileNameTree.forEach((item:any,index:number)=>{
        this.fileNameTree[index].children=deepClone( item.children.map((i:any,n:number)=>{
            i.label=(n+1+",")+(i.desc==""?i.name:i.desc)+""
            return i
        })
        )
    })
   
    
  }
  //
  //  点击数切换目录
 private handleNodeClick(data:any){
   this.Img =data
    
  }
    private async getdetail(id: string | ''|number) {
    this.loading = true
    try {
      let res = await DetailAsset({
        id: id
      })
      if (res.success) {
        this.loading = false
        this.detail = Object.assign({}, res.data)
        this.detailCom = true
      }
    } catch (e) {
         this.loading = false
      // this.closeDialog()
    this.$emit('update:visible', false)
    }
  }
  // 设置附件
  setfileList() {
    this.loadFileTree()
    let index = this.stepActive
    // let treeRef:any=this.$refs.tree
    this.expandedKeys.splice(0,1,index)
  //  let treeDom:any= this.$refs.tree
  //  treeDom.setCurrentKey(index);
    if (index == 0) {
      //   出租信息
      // this.ImgLists = [...this.detail.basic.attachmentFileDTOList, ...this.detail.currentAsset.attachmentFileDTOList]

      this.Img =  this.fileNameTree[0].children[0]
    } else if (index == 1) {
      // 出租房
      // this.ImgLists = this.detail.lessor.attachmentFileDTOList
      this.Img = this.fileNameTree[1].children[0]
    } else if (index == 2) {
      // 承租方
      // this.ImgLists = this.detail.lessee.attachmentFileDTOList
      this.Img = this.fileNameTree[2].children[0]
    } else if (index == 3) {
      // 审核
 
    }
  }
  private handleCommand(index: number) {
    this.ImgSrc = this.ImgLists[index].link
    this.Img = this.ImgLists[index]
  }
  changepdf() {
    this.ImgSrc = 'http://fh-mjgy-test.oss-cn-hangzhou.aliyuncs.com/upload/20220811/a36eb2656e9afb11ad0a6a7e096e432b.pdf'
  }
  private onPrint() {
    //
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0 !important;
  padding-bottom: 20px !important;
  .dialog-body {
    padding: 0 !important;
  }
}
::v-deep .el-step.is-simple:not(:last-of-type) .el-step__title {
  max-width: 70%;
}

::v-deep .el-select {
  width: 100%;
}

::v-deep .el-step__title {
  cursor: pointer;
}

.content {
  padding: 10px 24px;
}

.footer {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}
.audit_trade_box {
  position: relative;
  display: flex;
  justify-content: space-between;
  background-color: pink !important;
  z-index: 5000 !important;
}
::v-deep.common-dailog > .el-dialog {
  position: fixed !important;

  top: 20px;
  right: 20px;
  overflow-y: scroll;
  height: 90vh;
}
.audit_trade_imgList {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  overflow-y: hidden;
  max-width: 48%;

  width: 50%;
  height: 90vh;
  max-height: 90vh;
  text-align: center;
  z-index: 3020;
  box-sizing: border-box;
  position: fixed !important;
  top: 20px;
  left: 20px;
}
.audit_trade_header {
  font-size: 18px !important;
  padding: 8px 20px;
  display: flex;
  justify-content: space-between;
  justify-items: center;
}
::v-deep.el-dropdown-menu--small {
  font-size: 17px !important;
  z-index: 5100 !important;
}
.mzindex {
  z-index: 9000 !important;
}
</style>
