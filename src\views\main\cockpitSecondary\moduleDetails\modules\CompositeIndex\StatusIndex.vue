/* 指数状态 */

<template>
  <section class="composite-index-wrap">
    <CommonTitle :title="title" />

    <div class="status-index-content"
      ref="refEcharts" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { echartConfigure } from '@/views/main/cockpitcren/baseData'
import { deepClone } from '@/utils'
import * as echarts from 'echarts'
import CountTo from 'vue-count-to'
import CommonTitle from '@/views/main/cockpitSecondary/components/CommonTitle.vue'

type EChartsOption = echarts.EChartsOption

@Component({
  components: {
    CountTo,
    CommonTitle
  }
})
export default class extends Vue {
  @Prop() private title!: string // 标题
  @Prop() private echartsData!: any // 渲染数据
  @Prop({ default: '4%' }) private gridBootm?: string

  private loading = false
  private cunot = 0
  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private legendData: any[] = []
  private yAxisData: any[] = []
  private seriesData: any[] = []
  private option: EChartsOption = {}

  // 箭头数据变化，渲染视图
  @Watch('echartsData', { deep: true })
  private changeEchartsData() {
    this.initData()
  }

  // 组件初始化
  private mounted() {
    this.chartDom = this.$refs.refEcharts as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)
  }

  // 请求数据接口
  private async initData() {
    let data: any = deepClone(this.echartsData)

    // 组装echarts格式数据
    if (!Array.isArray(data) || !data.length) return

    let legendData: string[] = []
    let yAxisData: string[] = []
    let seriesData: any[] = []
    let barWidth = 0
    let fontSize = 24
    let barGap = '40%'

    // 设置Y轴、legend数据
    data.forEach((item, index) => {
      legendData.push(item.name)
      if (+index === 0) {
        item.list.forEach((itemChild: { label: string }) => {
          yAxisData.push(itemChild.label)
        })
      }
    })

    // 设置柱状图间距
    barGap = legendData.length > 2 ? '40%' : '70%'

    // 动态设置文字大小和柱状大小
    switch (data[0].list.length) {
      case 1:
        barWidth = 50
        fontSize = 40
        break
      case 2:
        barWidth = 30
        fontSize = 30
        break
      case 3:
        barWidth = 20
        fontSize = 26
        break
      case 4:
        barWidth = 18
        fontSize = 24
        break
      case 5:
        barWidth = 20
        fontSize = 24
        break
      case 6:
        barWidth = 18
        fontSize = 24
        break
      default:
        barWidth = 14
        fontSize = 24
        break
    }

    // 设置每个模块的值
    data.forEach((item) => {
      seriesData.push({
        type: 'bar',
        name: item.name,
        barWidth: barWidth,
        barGap: barGap,
        label: {
          show: true,
          color: '#fff',
          fontSize: fontSize,
          fontWeight: 'bold',
          position: 'right',
          formatter: '{c}'
        },
        data: item.list.map((itemData: { num: number }) => {
          return {
            value: itemData.num,
            itemStyle: {
              normal: {
                borderRadius: +itemData.num > 0 ? [0, 10, 10, 0] : [10, 0, 0, 10]
              }
            }
          }
        })
      })
    })

    this.legendData = legendData
    this.yAxisData = yAxisData
    this.seriesData = seriesData
    this.initEcharts()
  }

  // 初始化数据
  private initEcharts() {
    let gridBootm = this.gridBootm
    let legendSize = echartConfigure.legendSize
    let legendData = this.legendData
    let yAxisData = this.yAxisData
    let seriesData = this.seriesData
    let tooltipData: any = Object.assign(
      {
        trigger: 'axis',
        valueFormatter: (value: number) => `${value}`
      },
      echartConfigure.tooltip
    )

    this.option = {
      color: [
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(156, 63, 234, 0.1)'
            },
            {
              offset: 1,
              color: 'rgba(156, 63, 234, 1)'
            }
          ],
          global: false // 缺省为 false
        },
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(62, 238, 255, 0.1)'
            },
            {
              offset: 1,
              color: 'rgba(62, 238, 255, 1)'
            }
          ],
          global: false // 缺省为 false
        },
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(252, 180, 94, 0.1)'
            },
            {
              offset: 1,
              color: 'rgba(252, 180, 94, 1)'
            }
          ],
          global: false // 缺省为 false
        }
      ],
      tooltip: tooltipData,
      legend: {
        right: 0,
        padding: 0,
        itemGap: 40,
        itemHeight: 20,
        itemWidth: 20,
        textStyle: {
          color: '#fff',
          fontSize: legendSize / 1.2
        },
        data: legendData
      },
      grid: {
        left: '0%',
        right: '10%',
        bottom: gridBootm,
        containLabel: true
      },
      xAxis: [
        {
          type: 'value',
          axisLabel: {
            interval: 1,
            fontSize: 28,
            fontWeight: 'bold',
            fontFamily: echartConfigure.fontFamilyNumber,
            formatter: '{value}'
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#5db0ea'
            }
          },
          splitLine: {
            show: false
          }
        }
      ],
      yAxis: [
        {
          type: 'category',
          axisLabel: {
            interval: 0,
            padding: 0,
            fontSize: 20,
            fontWeight: 'bold',
            fontFamily: echartConfigure.fontFamilyNumber,
            formatter: function (value: string) {
              let ret = '' //拼接加\n返回的类目项

              let valLength = value.length //轴类目项的文字个数
              let maxLength = 20 //每项显示文字个数

              if (valLength >= 7 && valLength <= 8) {
                maxLength = 20
              } else if (valLength >= 9) {
                maxLength = 20
              }

              let rowN = Math.ceil(valLength / maxLength) //类目项需要换行的行数
              if (rowN > 1) {
                for (let i = 0; i < rowN; i++) {
                  let temp = '' //每次截取的字符串
                  let start = i * maxLength //开始截取的位置
                  let end = start + maxLength //结束截取的位置
                  temp = value.substring(start, end) + '\n'
                  ret += temp
                }
                return ret
              } else {
                return value
              }
            }
          },
          axisLine: {
            lineStyle: {
              color: '#5db0ea'
            }
          },
          data: yAxisData
        }
      ],
      series: seriesData,
      animationDelay: function (idx) {
        return idx * 300
      }
    }

    this.myChart && this.myChart.setOption(this.option, true)
  }
}
</script>

<style scoped lang="scss">
.composite-index-wrap {
  .status-index-content {
    width: 100%;
    height: 100%;
    margin-top: -73px;
  }
}
</style>