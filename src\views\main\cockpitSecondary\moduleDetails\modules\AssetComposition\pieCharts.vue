/** 资金存贷规模 */

<template>
  <div class="deposit-and-loanScale">
    <div id="DepositAndLoanScale" />
    <div class="halo"></div>
  </div>

</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { DepositAndLoanScaleData, echartConfigure } from '@/views/main/cockpitSecondary/baseData'
import { capitalProfile } from '@/api/cockpit'
import * as echarts from 'echarts'
import { deepClone } from '@/utils'

type EChartsOption = echarts.EChartsOption

@Component
export default class extends Vue {
  @Prop() private pieData!: any
  private loading = false
  private maxValue = 100
  private year = ''
  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}
  private tabList = []
  private echartsDatas: any[] = []
  private seriesData: any[] = [
    {
      value: 0,
      name: '流动资产',
      itemStyle: { color: 'RGBA(74, 101, 226, 1)' }
    },
    {
      value: 0,
      name: '非流动资产',
      itemStyle: { color: 'RGBA(96, 203, 179, 1)' }
    }
  ]
  private tabActive: {
    code: string
    name: string
  } = {
    code: '',
    name: ''
  }

  get sum() {
    return this.seriesData.reduce((sum: number, current: any) => {
      return (sum += +current.value)
    }, 0)
  }

  created() {
    this.seriesData[0].value = this.pieData[0]
    this.seriesData[1].value = this.pieData[1]
  }

  // 当前年月
  get getCurrentDate() {
    let date = new Date()
    return `截止到${date.getFullYear()}年${date.getMonth() + 1}月`
  }

  private getData() {
    return (Math.random() * 1000).toFixed(2)
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('DepositAndLoanScale') as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    this.initEcharts()
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await capitalProfile()
    if (!Array.isArray(data) || !data.length) return
    this.echartsDatas = data
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 初始化数据
  private initEcharts() {
    let textSize = 22
    let textColor = '#999'
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    this.option = {
      color: ['#E6B607', '#EF702E', '#35AAF6', '#00AEA5', '#EC342F', '#2D5AE5'],
      title: {
        text: '占比情况',
        top: 10,
        left: 10,
        textStyle: {
          color: '#fff',
          fontWeight: 'bold',
          fontSize: 40
        }
      },
      grid: {
        top: '10%'
      },
      legend: {
        show: false
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0, 103, 177, 1)',
        borderColor: 'rgba(0, 103, 177, 1)',
        textStyle: {
          color: '#fff',
          fontSize: textSize * 1.4
        }
      },
      series: [
        {
          type: 'pie',
          center: ['50%', '50%'],
          radius: ['0%', '35%'],
          avoidLabelOverlap: false,
          // stillShowZeroSum: false,
          selectedMode: 'single',
          label: {
            show: true,
            position: 'outside',
            fontSize: textSize * 1.2,
            color: '#5db0ea',
            fontWeight: 'bold',
            formatter: ({ data }: { data: any }) => {
              return ` {a|${data.name}}\n {hr|}\n {b|${data.value}}{c|亿元}\n {hr|}\n {b|${((data.value / this.sum) * 100).toFixed(2)}%}`
            },
            rich: {
              a: {
                fontSize: '40px',
                color: '#fff',
                fontWeight: 'bold',
                opacity: 0.8
              },
              b: {
                fontSize: '36px',
                color: '#20DCF9',
                fontWeight: 'bold',
                fontFamily: echartConfigure.fontFamilyNumber
              },
              c: {
                fontSize: '26px',
                color: '#20DCF9',
                fontWeight: 'bold',
                fontFamily: echartConfigure.fontFamilyNumber
              },
              hr: {
                borderColor: '#8C8D8E',
                width: '100%',
                borderWidth: 1,
                height: 0
              }
            }
          },
          labelLine: {
            length: 20,
            length2: 30,
            smooth: 0.2,
            lineStyle: {
              width: 3
            }
          },
          itemStyle: {
            borderRadius: 0,
            borderWidth: 10
          },
          data: this.seriesData
        }
      ]
    }

    this.myChart && this.myChart.setOption(this.option)
  }
  // 年份切换
  private yearHandler(year: string) {
    this.year = year
    // this.initData()

    // this.initEcharts()
  }
}
</script>

<style scoped lang="scss">
#DepositAndLoanScale {
  width: 100%;
  height: 100%;
}
.deposit-and-loanScale {
  position: relative;
  width: 100%;
  height: 500px;
  .halo {
    position: absolute;
    z-index: -1;
    top: 50%;
    left: 50%;
    width: 220px;
    height: 220px;
    transform: translate(-50%, -50%);
    background: url('../../../images/halo.png');
    background-size: 100% 100%;
    will-change: width, height, transform;
    animation: move 1000s linear forwards infinite;
  }

  @keyframes move {
    0% {
      transform: translate(-50%, -50%) rotateZ(0deg);
    }
    50% {
      transform: translate(-50%, -50%) rotateZ(360deg);
    }
    100% {
      transform: translate(-50%, -50%) rotateZ(0deg);
    }
  }
}
</style>