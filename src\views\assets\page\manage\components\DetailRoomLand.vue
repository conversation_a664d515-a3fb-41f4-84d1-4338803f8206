<template>
  <Dialog width="700px"
    :title="detailData.itemName || '--'"
    :visible="visible"
    :close-on-click-modal="true"
    :destroy-on-close="true"
    :append-to-body="true"
    @close="handleClose">
    <!-- 内容区域 -->
    <div v-loading="loading"
      slot="body"
      class="body-box">
      <el-descriptions :column="2"
        class="descriptions-box">
        <el-descriptions-item label="所属单位名称">{{detailData.orgName || '--'}}</el-descriptions-item>
        <el-descriptions-item label="资产明细编号">{{detailData.itemNo || '--'}}</el-descriptions-item>
        <el-descriptions-item label="使用状态">
          {{getDictLabelData('asset_use_status', detailData.useStatus)}}</el-descriptions-item>
        <el-descriptions-item label="资产用途">{{getDictLabelData('asset_purpose', detailData.purpose)}}
        </el-descriptions-item>
        <el-descriptions-item label="楼层">
          {{detailData.currentFloor}}/{{detailData.totalFloor}}
        </el-descriptions-item>
        <el-descriptions-item label="土地面积(㎡)">{{detailData.landArea || '--'}}</el-descriptions-item>
        <el-descriptions-item label="房屋朝向">
          {{getDictLabelData('asset_room_face', detailData.roomFace)}}</el-descriptions-item>
        <el-descriptions-item label="安全等级">
          {{getDictLabelData('asset_quality_leve', detailData.safetyLevel)}}</el-descriptions-item>
      </el-descriptions>

      <el-descriptions :column="1"
        class="descriptions-box">
        <el-descriptions-item label="坐落地址">{{detailData.location || '--'}}</el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 底部按钮 -->
    <div slot="footer"
      class="footer-box">
      <el-button @click="handleClose">关 闭</el-button>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { BusinessModule } from '@/store/modules/businessDict'
import { assetsItemDetail } from '@/api/assetsv2'
import { Loading } from '@/decorators'
import Dialog from '@/components/Dialog/index.vue'

@Component({
  components: {
    Dialog
  }
})
export default class extends Vue {
  @Prop() private visible!: boolean // 是否显示
  @Prop() private id!: string // 资产明细id

  private loading = false
  private detailData = {}

  // 获取字典数据
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }

  // 获取字典项label
  get getDictLabelData() {
    return (type: string, value: string) => {
      if (this.getDictData(type).length) {
        let obj = this.getDictData(type).find((item: any) => {
          return +item.value === +value
        })

        return obj ? obj.label : '--'
      } else {
        return '--'
      }
    }
  }

  // 组件初始化
  private mounted() {
    this.initDetailData()
  }

  // 资产详情
  @Loading('loading')
  private async initDetailData() {
    let { data } = await assetsItemDetail({
      id: this.id
    })

    this.detailData = data || {}
  }

  // 关闭弹窗
  private handleClose() {
    this.$emit('update:visible', false)
  }
}
</script>

<style scoped lang="scss">
::v-deep .descriptions-box {
  .el-descriptions-row {
    font-size: 14px;
    .el-descriptions-item {
      padding-bottom: 14px;
    }
  }
}
</style>