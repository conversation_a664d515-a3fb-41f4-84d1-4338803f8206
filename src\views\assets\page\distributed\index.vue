<template>
  <el-container direction="vertical"
    class="asset-map">
    <div id="assetmapv2" />

    <!-- <ComTool class="asset-map-tool" @showdetail="changeVisible" /> -->

    <ToolList @loadmark="loadmark"
      ref="tool"
      @addMapLabel="addMapLabel"
      class="asset-map-tool" />

    <AssetDetail v-if="visibleDetail"
      :visible.sync="visibleDetail"
      :markerinfo="markerinfo"
      class="asset-map-detail"
      @loaddetail="loaddetail" />

    <!-- 地图说明 -->
    <el-card v-loading="loading"
      class="asset-map-description">
      <div class="asset-map-description-body">
        <el-link @click="changeType('0')"
          :type="ischeck == 0 ? 'primary' : ''">
          <i class="el-icon-location el-icon--right"
            style="color:#cb5a5a;"></i>
          空置
        </el-link>
        <el-link @click="changeType('1')"
          :type="ischeck == 1 ? 'primary' : ''">
          <i class="el-icon-location el-icon--right"
            style="color:#40c2c6;"></i>
          出租
        </el-link>
        <el-link @click="changeType('2')"
          :type="ischeck == 2 ? 'primary' : ''">
          <i class="el-icon-location el-icon--right"
            style="color:#338fd5;"></i>
          自用
        </el-link>
      </div>
    </el-card>

    <DetailCom v-if="visibleDetailDia"
      :visible.sync="visibleDetailDia"
      :id="detailRow.id" />
  </el-container>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import ComTool from './v2/tool.vue'
import AssetDetail from './v2/AssetDetail.vue'
import ToolList from './v2/ToolList.vue'
import InfoWindows from './v2/components/MarkerInfo.vue'
import DetailCom from '@/views/assets/page/manage/components/DetailCom.vue'

declare const BMapGL: any // 声明 declare
declare const AMap: any // 声明 declare

@Component({
  components: {
    ComTool,
    AssetDetail,
    InfoWindows,
    ToolList,
    DetailCom
  }
})
export default class extends Vue {
  private mapLocation = [119.663154, 29.100489]
  private visibleDetail = false
  private markerinfo = {}

  // 资产详情
  private poilabel: any = {} // poi搜索显示的label
  private loading = false
  private detailRow = {}
  private ischeck: string | number = 4
  private visibleDetailDia = false
  private dist: any = new BMapGL.DistrictLayer({
    name: '(金华)',
    kind: 2,
    strokeWeight: 2,
    strokeColor: '#389BB7',
    fillOpacity: 0.01,
    strokeOpacity: 0.6
  })
  private house = new BMapGL.Icon(require('@/assets/images/assets/house.png'), new BMapGL.Size(24, 31), {})
  private houseUnused = new BMapGL.Icon(require('@/assets/images/assets/houseUnused.png'), new BMapGL.Size(24, 31), {})
  private houseSelf = new BMapGL.Icon(require('@/assets/images/assets/houseSelf.png'), new BMapGL.Size(24, 31), {})
  private land = new BMapGL.Icon(require('@/assets/images/assets/land.png'), new BMapGL.Size(24, 31), {})
  private landUnused = new BMapGL.Icon(require('@/assets/images/assets/landUnused.png'), new BMapGL.Size(24, 31), {})
  private landSelf = new BMapGL.Icon(require('@/assets/images/assets/landSelf.png'), new BMapGL.Size(24, 31), {})

  // 初始化加载
  mounted() {
    this.loading = true
    this.initMap()
  }

  // 初始化地图
  private initMap() {
    ;(window as any).assetmapv2 = new BMapGL.Map('assetmapv2', {
      minZoom: 8
    })
    this.setMap()
  }

  // 对地图元素操作
  private setMap() {
    let map = (window as any).assetmapv2
    map.setDisplayOptions({
      poiText: true,
      poiIcon: false,
      enableAutoResize: false,
      // layer: false,
      building: false
      // overlay: false
    })
    // map.setMapStyleV2({ styleJson: styleJson }) // 设置地图主题(https://lbsyun.baidu.com/customv2/help.html)
    map.centerAndZoom(new BMapGL.Point(this.mapLocation[0], this.mapLocation[1]), 15) // 设置中心点坐标和地图级别、地图3D视角
    map.enableScrollWheelZoom(true) // 开启鼠标滚轮缩放
    // map.setHeading(64.5)
    // 旋转动画
    this.setcity()
  }

  // 创建城市聚合
  private setcity() {
    let map = (window as any).assetmapv2
    setTimeout(() => {
      map.addDistrictLayer(this.dist)
    }, 10)
  }

  // 批量添加mark
  private loadmark(list: Array<any>) {
    let map = (window as any).assetmapv2
    this.visibleDetail = false
    map.closeInfoWindow()
    let len = list.length
    this.removeOverlay()
    let max = 500
    for (let i = 0; i < Math.ceil(len / max); i++) {
      setTimeout(() => {
        this.addMarkers(i * max, (i + 1) * max, list)
      }, i * 10 * 50)
    }
    this.loading = false

    // 如果存在按中心点筛选，移动中心点
    // if (this.poilabel.content!==undefined) {
    //   map.setCenter(this.poilabel, {
    //     noAnimation: true,
    //     callback: () => {
    //       map.setZoom(16)
    //     }
    //   })
    // }

    // map.setCenter(this.poilabel)
  }

  // 创建标注、添加信息窗体
  private addMarkers(start: number, end: number, lists: Array<any>) {
    let map = (window as any).assetmapv2
    let that = this as any
    let assetsList = JSON.parse(JSON.stringify(lists))
    let list = assetsList.slice(start, end)
    list.forEach((item: any) => {
      if (item.longitude == '' || item.latitude == '' || item.longitude == undefined || item.latitude == undefined) {
        return ''
      }
      let point = new BMapGL.Point(item.longitude, item.latitude)
      // 创建标注对象并添加到地图
      let marker: any = {}
      // 添加信息窗体

      // 0-空置 1-出租 2-自用
      if (item.type == 0) {
        if (item.assetType == 1) {
          marker = new BMapGL.Marker(point, { icon: this.house })
        } else {
          marker = new BMapGL.Marker(point, { icon: this.land })
        }
      } else if (item.type == 1) {
        if (item.assetType == 1) {
          marker = new BMapGL.Marker(point, { icon: this.houseUnused })
        } else {
          marker = new BMapGL.Marker(point, { icon: this.landUnused })
        }
      }
      // else if (item.isVacancy == 0) {
      else {
        if (item.assetType == 1) {
          marker = new BMapGL.Marker(point, { icon: this.houseSelf })
        } else {
          marker = new BMapGL.Marker(point, { icon: this.landSelf })
        }
      }
      marker.info = item
      let opts = {
        width: 420, // 信息窗口宽度
        height: 220, // 信息窗口高度
        enableAutoPan: true,
        title: item.location
      }
      map.addOverlay(marker)
      let content: any = `
        <div class="baidu-windows-assetbox">
          <div class="info-header">
            <div><img src="https://img2.baidu.com/it/u=1419977712,1630679449&fm=253&fmt=auto&app=120&f=JPEG?w=902&h=500" alt=""></div>
            <div class="info-header-content">
                <p style="font-size:22px;color: #303133;" class="info-header-title"> <span class="${
                  item.assetType == 1 ? 'info-tag1' : 'info-tag1'
                }">${item.assetType == 1 ? '房产' : '土地'}</span></p>
            <div>
                <p class="font2"><span class="font1">所属单位:</span>${item.orgName || '-'}</p>
                <p class="font2"><span class="font1">经营类别:</span>${item.purposes.join(',') || '-'}</p>
                <p class="font2"><span class="font1">资产用途:</span>${item.mangeTypeStr || '-'}</p>
            </div>
            </div>
          </div>
          <div class="info-table">
            <div>
              <p class="font1">出租率</p>
              <p class="font2">${Number(item.occupancyRate).toFixed(2) || '-'}%</p>
            </div>
            <div>
              <p class="font1">房间数量</p>
              <p class="font2">${item.itemNum || '-'}间</p>
            </div>
            <div>
              <p class="font1">账面价值（已出租）</p>
              <p class="font2">${Number(item.totalOriginalValue).toFixed(2) || '-'}万元</p>
            </div>
        </div>
        <div style="text-align:center;border-top:1px solid #EFEFEF;padding:10px 0">
          <span  class="asset-detail-link" id="${item.id}_assetmap_detail" >资产详情</span>
        </div>
      </div>
      `
      let infoWindow = new BMapGL.InfoWindow(content, opts) // 创建信息窗口对象
      // 信息窗口关闭
      infoWindow.addEventListener('clickclose', function (e: any) {
        that.visibleDetail = false
        that.changeVisible(false)
      })
      // // 添加点击事件
      marker.addEventListener('click', function (e: any) {
        // 点击“查看详情”按钮

        that.markerinfo = e.currentTarget.info
        that.changeVisible(true)
        map.openInfoWindow(infoWindow, point)
        // 跳转到中心点
        // map.setCenter(point, {
        //   noAnimation: true,
        //   callback: () => {
        //     map.setZoom(map.getZoom() > 18 ? map.getZoom() : 18)
        //     that.fltyto(item.longitude, item.latitude)
        //   }
        // })
        // map.flyTo(point, 16)
        // map.panTo(point)
        let detailDom: HTMLElement | null = document.getElementById(`${item.id}_assetmap_detail`)
        detailDom &&
          detailDom.addEventListener('click', function () {
            that.visibleDetailDia = true
            that.detailRow = item
          })
      })
    })
  }

  // 查看详情
  private loaddetail(detailInfo: any) {
    this.visibleDetailDia = true
    this.detailRow = detailInfo
  }

  // 查看详情
  private fltyto(lon: number, lat: number) {
    let map = (window as any).assetmapv2
    let zoom = map.getZoom()
    if (zoom > 15) {
      return ''
    } else {
      let keyFrames = [
        {
          center: new BMapGL.Point(lon, lat),
          zoom: zoom > 18 ? zoom : 20,
          tilt: 70,
          heading: 0,
          percentage: 0
        },
        {
          center: new BMapGL.Point(lon, lat),
          zoom: zoom > 18 ? zoom : 21,
          tilt: 70,
          heading: 120,
          percentage: 0.1
        },
        {
          center: new BMapGL.Point(lon, lat),
          zoom: zoom > 18 ? zoom : 21,
          tilt: 70,
          heading: 240,
          percentage: 0.25
        },
        {
          center: new BMapGL.Point(lon, lat),
          zoom: zoom > 18 ? zoom : 21,
          tilt: 70,
          heading: 360,
          percentage: 1
        }
      ]
      let opts = {
        duration: 1500, // 设置每次迭代动画持续时间
        delay: 300, // 设置动画延迟开始时间
        interation: 1 // 设置动画迭代次数
      }
      let animation = new BMapGL.ViewAnimation(keyFrames, opts) // 初始化动画实例
      map.startViewAnimation(animation) // 开始播放动画
    }
  }

  // 显示详情栏
  private changeVisible(state: boolean) {
    this.visibleDetail = state
  }

  // 清除点
  private removeOverlay() {
    let map = (window as any).assetmapv2
    let allOverlay = map.getOverlays()
    allOverlay.map((item: any) => {
      if (item.info != undefined) {
        // 去掉自定义的marker
        map.removeOverlay(item)
      }
    })
  }

  // 选择标记点跳转
  private addMapLabel(POIinfo: any) {
    let that = this as any
    let map = (window as any).assetmapv2
    let point = {}
    point = new BMapGL.Point(POIinfo.location.lng, POIinfo.location.lat)
    let content = POIinfo.name
    // this.poilabel.zIndex()
    map.removeOverlay(this.poilabel)
    this.poilabel = new BMapGL.Label(content, {
      position: point, // 设置标注的地理位置
      offset: new BMapGL.Size(10, 20) // 设置标注的偏移量
    })

    map.setCenter(point, {
      // callback: () => {
      //   map.setZoom(16)
      //   // that.fltyto(POIinfo.location.lng, POIinfo.location.lat)
      // }
    })
    map.addOverlay(this.poilabel)
  }

  // 改变类型
  private changeType(type: string | number) {
    ;(this.$refs.tool as any).assetsInfoMapbyType(type)
    this.ischeck = type
    this.loading = true
  }

  // 组件销毁
  destroyed() {
    ;(window as any).assetmapv2.destroy()
    ;(window as any).assetmapv2 = null
  }
}
</script>

<style lang="scss" scoped>
.asset-map {
  width: 100%;
  height: 100%;
  position: relative;
}
#assetmapv2 {
  width: 100%;
  height: 100%;
}
.asset-map-tool {
  z-index: 199;
  position: absolute;
  top: 0;
  left: 0;
  // height: 100%;
  width: 420px;
}
.asset-map-detail {
  position: absolute;
  top: 0;
  right: 0;
  height: 96%;
  width: 400px;
}

::v-deep.asset-map-description {
  position: absolute;
  bottom: 0px;
  left: 0px;
  z-index: 500;
  .el-link {
    font-size: 18px;
  }
  .asset-map-description-body {
    display: flex;
    font-size: 25px;
  }
}
</style>

<style lang="scss">
.baidu-windows-assetbox {
  background: #fff;
  width: 100%;
  height: 180px;
  display: flex;
  flex-direction: column;
  .info-header {
    display: flex;
    height: 100px;
  }
  .info-header img {
    width: 100px;
    object-fit: cover;
    height: 100%;
  }
  .info-header-content {
    padding: 0px 0px;
    display: flex;
    flex-direction: row-reverse;
    height: 100px;
    text-align: start;
    font-size: 12px;
    justify-content: start;
    width: 100%;
    span {
      padding: 0 10px;
    }
    div {
      flex: 3;
      padding: 0px 10px;
      display: flex;
      flex-direction: column;
      height: 100px;
      font-size: 12px;
      justify-content: start;
      width: 100%;
    }
  }
  p {
    padding: 0;
    margin: 0;
    font-size: 12px;
  }
  .info-table {
    padding: 10px 10px;
    display: flex;
    justify-content: space-between;
  }
  .font2 {
    color: #303133;
    font-size: 16px;
    height: 33%;
  }
  .font1 {
    color: #909399;
    font-size: 14px;
  }
  .asset-detail-link {
    font-size: 16px;
    color: #b43c3c;
    cursor: pointer;
    line-height: 30px;
  }
  .info-header-title {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    .info-tag1 {
      text-align: center;
      padding: 0 6px;
      border: 1px solid #ce4c4c;
      border-radius: 2px;
      border-radius: 2px;
      font-size: 10px;
      font-weight: 600;
      color: #ce4c4c;
      min-width: 30px;
    }
    .info-tag2 {
      padding: 0 6px;
      border: 1px solid #3dc0c5;
      border-radius: 2px;
      border-radius: 2px;
      font-size: 10px;
      color: #3dc0c5;
      font-weight: 600;
      min-width: 30px;
      text-align: center;
    }
  }
}
.infoshow {
  display: none;
}
</style>
