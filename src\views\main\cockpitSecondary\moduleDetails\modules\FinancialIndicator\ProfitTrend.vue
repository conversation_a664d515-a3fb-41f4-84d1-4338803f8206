/* 历年利润趋势 */

<template>
  <section class="profit-trend-wrap">
    <CommonTitle :title="title"
      :isRotuerThrow="true"
      :routerObj="typeBizObj" />

    <div class="profit-trend-content"
      ref="refEcharts" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { echartConfigure } from '@/views/main/cockpitcren/baseData'
import { Loading } from '@/decorators'
import { deepClone } from '@/utils'
import { bigNumberFormat } from '@/utils'
import * as echarts from 'echarts'
import CommonTitle from '@/views/main/cockpitSecondary/components/CommonTitle.vue'

type EChartsOption = echarts.EChartsOption

@Component({
  components: {
    CommonTitle
  }
})
export default class extends Vue {
  @Prop() private title!: string // 标题
  @Prop() private echartsData!: any // 渲染数据
  @Prop({
    default: () => {
      return {}
    }
  })
  private typeBizObj?: object // 标题跳转

  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private seriesData: any[] = []
  private option: EChartsOption = {}
  private legendData: string[] = []
  private xAxisData: string[] = []

  // 数据变化，渲染视图
  @Watch('echartsData', { deep: true })
  private changeEchartsData() {
    this.initData()
  }

  // 万元数据转化成亿元
  get getBigNumberFormat() {
    return (num: string | number) => {
      return bigNumberFormat(+num).value || 0
    }
  }

  // 组件初始化
  private mounted() {
    this.chartDom = this.$refs.refEcharts as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let data = deepClone(this.echartsData)

    // 组装echarts数据
    if (!Array.isArray(data) || !data.length) return

    let legendList: string[] = []
    let xAxisList: string[] = []
    let seriesList: any[] = []
    data.forEach((item, index) => {
      xAxisList.push(item.year)
      item.list.forEach((itemChild: any) => {
        !index && legendList.push(itemChild.label)
      })
    })

    legendList.forEach((item, index) => {
      let arr: number[] = []
      data.forEach((itemData: any, indexData: number) => {
        if (item === itemData.list[index].label) {
          arr.push(+this.getBigNumberFormat(itemData.list[index].value))
        }
      })

      switch (index) {
        case 0:
          seriesList.push({
            name: item,
            data: arr,
            type: 'bar',
            barWidth: 40,
            tooltip: {
              valueFormatter: function (value: string) {
                return value + ' 亿元'
              }
            },
            itemStyle: {
              borderRadius: [70, 70, 0, 0]
            }
          })
          break
        case 1:
          seriesList.push({
            name: item,
            data: arr,
            type: 'bar',
            barWidth: 40,
            tooltip: {
              valueFormatter: function (value: string) {
                return value + ' 亿元'
              }
            },
            itemStyle: {
              borderRadius: [70, 70, 0, 0]
            }
          })
          break
        case 2:
          seriesList.push({
            name: item,
            data: arr,
            type: 'line',
            symbol: 'circle',
            yAxisIndex: 1,
            lineStyle: {
              width: 4
            },
            tooltip: {
              valueFormatter: function (value: string) {
                return value + ' 亿元'
              }
            }
          })
          break
        case 3:
          seriesList.push({
            name: item,
            data: arr,
            type: 'line',
            symbol: 'circle',
            yAxisIndex: 1,
            lineStyle: {
              width: 4
            },
            tooltip: {
              valueFormatter: function (value: string) {
                return value + ' 亿元'
              }
            }
          })
          break
      }
    })

    this.legendData = legendList
    this.xAxisData = xAxisList
    this.seriesData = seriesList
    this.initEcharts()
  }

  // 初始化数据
  private initEcharts() {
    let textSize = echartConfigure.textSize
    let legendSize = echartConfigure.legendSize
    let legendData = this.legendData
    let xAxisData = this.xAxisData
    let seriesData = this.seriesData

    this.option = {
      color: [
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(252, 180, 94, 0.1)'
            },
            {
              offset: 0.3,
              color: 'rgba(252, 180, 94, 0.7)'
            },
            {
              offset: 1,
              color: 'rgba(252, 180, 94, 1)'
            }
          ],
          global: false // 缺省为 false
        },
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(62, 238, 255, 0.1)'
            },
            {
              offset: 0.3,
              color: 'rgba(62, 238, 255, 0.7)'
            },
            {
              offset: 1,
              color: 'rgba(62, 238, 255, 1)'
            }
          ],
          global: false // 缺省为 false
        },
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(0, 175, 255, 1)'
            },
            {
              offset: 1,
              color: 'rgba(0, 175, 255, 1)'
            }
          ],
          global: false
        },
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(205, 58, 37, 1)'
            },
            {
              offset: 1,
              color: 'rgba(205, 58, 37, 1)'
            }
          ],
          global: false
        }
      ],
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#00000000',
        shadowColor: '#00000000',
        borderWidth: 0,
        formatter: (params: any) => {
          let dom = ''
          let year = params[0].axisValue || ''

          params.forEach((item: any, index: number) => {
            let style = ''
            let unit = '亿元'

            if (+item.value > 0) {
              style = `
                  <span style="padding-right: 40px;">${item.seriesName}</span>
                  <span>
                    ${item.value}${unit}
                  </span>
                `
            } else if (+item.value < 0) {
              style = `
                  <span style="padding-right: 40px;">${item.seriesName}</span>
                  <span>
                    ${item.value}${unit}
                  </span>
                `
            } else {
              style = `
                  <span style="padding-right: 40px;">${item.seriesName}</span>
                  <span style="color: #fff;">
                    ${item.value}${unit}
                  </span>
                `
            }

            dom += `
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  ${style}
                </div>
              `
          })

          return `
            <div style="font-size:40px; line-height: 70px; color: #fff; border-radius:10px;border:2px solid #0C3EB6; padding: 20px; display: flex;flex-direction: column; background: linear-gradient(0deg, rgba(0, 103, 177, 1), rgba(0, 103, 177, 1));">
              <div style="display: flex; justify-content: space-between; font-weight:bold;">
                <span>${year}年</span>
                <span>(年化)</span>
              </div>
              ${dom}
            </div>
          `
        }
      },
      legend: {
        show: false,
        top: 40,
        right: 10,
        padding: 0,
        itemHeight: 24,
        itemWidth: 24,
        itemStyle: {
          borderCap: 'butt'
        },
        lineStyle: {
          type: [0, 10],
          dashOffset: 5
        },
        textStyle: {
          color: '#fff',
          fontSize: 26,
          fontWeight: 'bold'
        },
        data: legendData
      },
      grid: {
        top: '20%',
        left: '1%',
        right: '1%',
        bottom: '8%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          data: xAxisData,
          axisPointer: {
            type: 'shadow'
          },
          axisLabel: {
            fontSize: textSize,
            fontWeight: 'bold'
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#5db0ea'
            }
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: '利润金额(亿元)',
          nameTextStyle: {
            color: '#5db0ea',
            align: 'left',
            fontWeight: 'bold',
            fontSize: 30
          },
          axisLabel: {
            fontSize: textSize,
            fontWeight: 'bold'
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#5db0ea'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0, 103, 177, 0.6)'
            }
          }
        },
        {
          type: 'value',
          name: '营业金额(亿元)',
          nameTextStyle: {
            color: '#5db0ea',
            align: 'right',
            fontWeight: 'bold',
            fontSize: 30
          },
          axisLabel: {
            fontSize: textSize,
            fontWeight: 'bold'
          },
          axisLine: {
            lineStyle: {
              color: '#5db0ea'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0, 103, 177, 0)'
            }
          }
        }
      ],
      series: seriesData,
      animationDelay: function (idx) {
        return idx * 300
      }
    }

    this.myChart && this.myChart.setOption(this.option, true)
  }
}
</script>

<style scoped lang="scss">
.profit-trend-wrap {
  position: relative;
  height: 100%;
  .profit-trend-content {
    width: 100%;
    height: 100%;
    transform: translateY(-80px);
  }
}
</style>


