/*
 * input 数字输入控件：目前支持（最多两位小数｜正整数）
 * 相关参数同 elementUi el-input 组件
 * 引入：import InputNumber from '@/components/FormComment/inputNumber.vue'
 * 调用：<InputNumber v-model="data"
          ...props
          type="integer"
          @updateHandle="updateHandle">
          描述符号 slot
        </InputNumber>
          
 * @updateHandle 是 oninput 事件触发后的回调，用来通知父级组件
 */

<template>
  <el-input v-model.trim="inputValue"
    v-bind="$attrs"
    v-on="$listeners"
    @input="onInput"
    @blur="onBlur">
    <slot name="prefix"
      slot="prefix" />
    <slot name="suffix"
      slot="suffix" />
    <slot name="prepend"
      slot="prepend" />
    <slot name="append"
      slot="append" />
  </el-input>
</template>

<script lang="ts">
import { Throttle } from '@/decorators'
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'

@Component({})
export default class extends Vue {
  @Prop() private value!: string // v-modle 绑定的值
  @Prop() private max!: any // 最大值
  @Prop({
    validator: (value: string) => {
      return ['decimal', 'decimalZero', 'integer', 'integerBurden', 'number'].indexOf(value) !== -1
    },
    default:"decimal"
  })
  readonly type?: string // 类型（小数点后两位(不支持输入0) | 小数点后两位(支持输入0) ｜ 正整数 | 非0的数字）

  private inputValue = ''

  // 监听 v-model 绑定值变化，然后重新赋值
  @Watch('value', { immediate: false, deep: true })
  changeVisible() {
    if (typeof this.max !== 'undefined') {
      this.inputValue = +this.value <= this.max ? this.value : this.max + ''
    } else {
      this.inputValue = this.value
    }
  }

  // 组件初始化
  private mounted() {
    this.inputValue = this.value
  }

  // input 事件
  @Throttle
  private onInput() {
    switch (this.type) {
      case 'decimal': // 最多两位小数(不支持输入0)
      case 'decimalZero': // 最多两位小数(支持输入0)
        this.inputValue = ('' + this.inputValue)
          .replace(/[^\d.]/g, '')
          .replace(/\.{2,}/g, '.')
          .replace('.', '$#$')
          .replace(/\./g, '')
          .replace('$#$', '.')
          .replace(/^(-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
          .replace(/^\./g, '')
        break
      case 'integer': // 正整数
        this.inputValue = this.inputValue.replace(/[^\d+]/g, '').replace(/^0{1,}/g, '')
        break
      case 'integerBurden': // 正负整数
        this.inputValue = this.inputValue.replace(/[^\-?(?!0)\d+$]/g, '').replace(/^(0|--){1,}/g, '')
        break
    }

    this.$emit('input', this.inputValue)
    this.updateHandle()
  }

  // blur 事件
  private onBlur() {
    // 两位小数校验
    if (!this.type || this.type === 'decimal' || this.type === 'decimalZero') {
      if (this.type === 'decimal' && +this.inputValue === 0) this.inputValue = ''
      try{
      this.inputValue.slice(-1) == '.' ? (this.inputValue = this.inputValue.slice(0, this.inputValue.length - 1)) : ''

        
      }
      catch(e){
        // 
      }
    }
    this.$emit('input', this.inputValue)
  }

  // 触发父级回调
  @Throttle
  private updateHandle() {
    this.$emit('updateHandle')
  }
}
</script>
