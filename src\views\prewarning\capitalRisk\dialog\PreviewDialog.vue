<template>
  <el-dialog :visible.sync="open" width="790px" append-to-body>
    <div class="mainContainer">
      <div class="headBar">
        <!-- <el-button type="primary" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button> -->
        <el-button type="primary" icon="el-icon-download" size="mini" v-print="print">导出</el-button>
        <i class="el-icon-close closeClass curP" @click="cancel" />
      </div>

      <div class="paddingWrap" v-loading="loading">

        <div class="reportPage" ref="contentToConvert" id="printArea">
          <!-- <div class="title">{{ pageObj?.corpName }}{{ pageObj?.year }}年<span v-show="pageObj?.month">{{ pageObj?.month
              }}月</span>资金风险预警报告</div> -->
          <div class="title">{{ pageObj?.reportTitle }}</div>
          <div class="sectionOne">
            <div class="secondTitle">一、企业概况</div>
            <div class="content p_1">{{ pageObj?.reportIntro }}</div>
          </div>

          <div class="sectionOne">
            <div class="secondTitle">二、投入链分析</div>
            <div class="flex_1">
              <div class="flex_2 w_1">
                <div class="thirdTitle">（一）偏离度分析</div>
                <div class="content p_1">
                  截至<span v-if="pageObj?.month">{{ pageObj?.month }}月</span><span v-else>{{
                    pageObj?.year
                    }}年</span>末，公司现金流动负债比率{{
                      pageObj?.indicatorList[0].actualValue
                    }}%，预测值{{
                    pageObj?.indicatorList[0].predictiveValue
                  }}%，偏离{{
                    pageObj?.indicatorList[0].predictiveDeviateValue
                  }}%；资产负债率{{
                    pageObj?.indicatorList[1].actualValue
                  }}%，预测值{{
                    pageObj?.indicatorList[1].predictiveValue
                  }}%，偏离{{
                    pageObj?.indicatorList[1].predictiveDeviateValue
                  }}%；带息负债比率{{
                    pageObj?.indicatorList[2].actualValue
                  }}%，预测值{{
                    pageObj?.indicatorList[2].predictiveValue
                  }}%，偏离{{
                    pageObj?.indicatorList[2].predictiveDeviateValue
                  }}%；已获利息倍数{{
                    pageObj?.indicatorList[3].actualValue
                  }}，预测值{{
                    pageObj?.indicatorList[3].predictiveValue
                  }}，偏离{{
                    pageObj?.indicatorList[3].predictiveDeviateValue
                  }}；速动比率{{
                    pageObj?.indicatorList[4].actualValue
                  }}%，预测值{{
                    pageObj?.indicatorList[4].predictiveValue
                  }}%，偏离{{
                    pageObj?.indicatorList[4].predictiveDeviateValue
                  }}%；净资产收益率{{
                    pageObj?.indicatorList[5].actualValue
                  }}%，预测值{{
                    pageObj?.indicatorList[5].predictiveValue
                  }}%，偏离{{
                    pageObj?.indicatorList[5].predictiveDeviateValue
                  }}%；具体如右图：
                </div>
              </div>
              <div class="chartWrapOne w_1">
                <TllfxChart ref="tllfxChart" />
              </div>
            </div>
            <div class="thirdTitle m_1">（二）纵向分析</div>
            <div class="content p_1">
              截至<span v-if="pageObj?.month">{{ pageObj?.month }}月</span><span v-else>{{
                pageObj?.year
                }}年</span>末，公司现金流动负债比率{{
                  pageObj?.indicatorList[0].actualValue
                }}%，较五年内年均值{{
                pageObj?.indicatorList[0].riskWarningValue
              }}%，同比{{
                pageObj?.indicatorList[0].yearIncreaseRate > 0 ? '增加' : '减少'
              }}{{
                Math.abs(pageObj?.indicatorList[0].yearIncreaseRate)
              }}个百分点；资产负债率{{
                pageObj?.indicatorList[1].actualValue
              }}%，较五年内年均值{{
                pageObj?.indicatorList[1].riskWarningValue
              }}%，同比{{
                pageObj?.indicatorList[1].yearIncreaseRate > 0 ? '增加' : '减少'
              }}{{
                Math.abs(pageObj?.indicatorList[1].yearIncreaseRate)
              }}个百分点；带息负债比率{{
                pageObj?.indicatorList[2].actualValue
              }}%，较五年内年均值{{
                pageObj?.indicatorList[2].riskWarningValue
              }}%，同比{{
                pageObj?.indicatorList[2].yearIncreaseRate > 0 ? '增加' : '减少'
              }}{{
                Math.abs(pageObj?.indicatorList[2].yearIncreaseRate)
              }}个百分点；已获利息倍数{{
                pageObj?.indicatorList[3].actualValue
              }}，较五年内年均值{{
                pageObj?.indicatorList[3].riskWarningValue
              }}，同比{{
                pageObj?.indicatorList[3].yearIncreaseRate > 0 ? '增加' : '减少'
              }}{{
                Math.abs(pageObj?.indicatorList[3].yearIncreaseRate)
              }}；速动比率{{
                pageObj?.indicatorList[4].actualValue
              }}%，较五年内年均值{{
                pageObj?.indicatorList[4].riskWarningValue
              }}%，同比{{
                pageObj?.indicatorList[4].yearIncreaseRate > 0 ? '增加' : '减少'
              }}{{
                Math.abs(pageObj?.indicatorList[4].yearIncreaseRate)
              }}个百分点；净资产收益率{{
                pageObj?.indicatorList[5].actualValue
              }}%，较五年内年均值{{
                pageObj?.indicatorList[5].riskWarningValue
              }}%，同比{{
                pageObj?.indicatorList[5].yearIncreaseRate > 0 ? '增加' : '减少'
              }}{{
                Math.abs(pageObj?.indicatorList[5].yearIncreaseRate)
              }}个百分点。具体如下图：
            </div>
            <div class="swipWrap m_1">
              <!-- <div class="blvWrap" v-for="item in 6" :key="item">
                <BlvChart :ref="`blvChart${item}`" />
              </div> -->
              <div class="blvWrap" v-for="item in firstZxfx" :key="item.indicatorName">
                <BlvChart :ref="`blvChartTwo${item.indicatorName}`" />
              </div>
            </div>
            <div class="flex_1 m_1">
              <div class="flex_2 w_1">
                <div class="thirdTitle">（三）横向分析</div>
                <div class="content p_1">
                  截至<span v-if="pageObj?.month">{{ pageObj?.month }}月</span><span v-else>{{
                    pageObj?.year
                  }}年</span>末，公司现金流动负债比率{{
                      pageObj?.indicatorList[0].actualValue
                    }}分<span v-show="pageObj?.indicatorList[0].comparisonResult !== '/'">，{{
                    pageObj?.indicatorList[0].comparisonResult
                  }}</span>；资产负债率{{
                      pageObj?.indicatorList[1].actualValue
                    }}分<span v-show="pageObj?.indicatorList[1].comparisonResult !== '/'">，{{
                    pageObj?.indicatorList[1].comparisonResult
                  }}</span>；带息负债比率{{
                      pageObj?.indicatorList[2].actualValue
                    }}分<span v-show="pageObj?.indicatorList[2].comparisonResult !== '/'">，{{
                    pageObj?.indicatorList[2].comparisonResult
                  }}</span>；已获利息倍数{{
                      pageObj?.indicatorList[3].actualValue
                    }}分<span v-show="pageObj?.indicatorList[3].comparisonResult !== '/'">，{{
                    pageObj?.indicatorList[3].comparisonResult
                  }}</span>；速动比率{{
                      pageObj?.indicatorList[4].actualValue
                    }}分<span v-show="pageObj?.indicatorList[4].comparisonResult !== '/'">，{{
                    pageObj?.indicatorList[4].comparisonResult
                  }}</span>；净资产收益率{{
                      pageObj?.indicatorList[5].actualValue
                    }}分<span v-show="pageObj?.indicatorList[5].comparisonResult !== '/'">，{{
                    pageObj?.indicatorList[5].comparisonResult
                  }}</span>；具体如右图：
                </div>
              </div>
              <div class="chartWrapOne w_1">
                <FxChart ref="fxChart" />
              </div>
            </div>
          </div>

          <div class="sectionOne">
            <div class="secondTitle">三、运营链分析</div>
            <div class="flex_1">
              <div class="flex_2 w_1">
                <div class="thirdTitle">（一）偏离度分析</div>
                <div class="content p_1">
                  截至<span v-if="pageObj?.month">{{ pageObj?.month }}月</span><span v-else>{{
                    pageObj?.year
                    }}年</span>末，公司<span v-for="(item, index) in yylFx" :key="index"><span v-show="item.actualValue">{{
                      item.indicatorDesc }}{{ item.actualValue
                      }}{{ item.indicatorUnit }}<span v-show="item.predictiveValue">，预测值{{
                        item.predictiveValue
                        }}{{ item.indicatorUnit }}</span>，偏离{{
                          item.predictiveDeviateValue
                        }}{{ item.indicatorUnit }}；</span></span>具体如右图：
                </div>
              </div>
              <div class="chartWrapOne w_1">
                <TllfxChart ref="tllfxChartTwo" />
              </div>
            </div>
            <div class="thirdTitle m_1">（二）纵向分析</div>
            <div class="content p_1">
              截至<span v-if="pageObj?.month">{{ pageObj?.month }}月</span><span v-else>{{
                pageObj?.year
                }}年</span>末，公司<span v-for="(item, index) in yylFx" :key="index"><span v-show="item.actualValue">{{
                  item.indicatorDesc }}{{
                    item.actualValue
                  }}{{ item.indicatorUnit }}，较五年内年均值{{
                    item.riskWarningValue
                  }}{{ item.indicatorUnit }}，同比{{
                    item.yearIncreaseRate > 0 ? '增加' : '减少'
                  }}{{
                    Math.abs(item.yearIncreaseRate)
                  }}{{ item.indicatorUnit == '%' ? '个百分点' : '万元' }}；</span></span>具体如下图：
            </div>
            <div class="swipWrap m_1">
              <div class="blvWrap" v-for="item in yylFx" :key="item.indicatorName">
                <BlvChart :ref="`blvChartTwo${item.indicatorName}`" />
              </div>
            </div>
            <div class="flex_1 m_1">
              <div class="flex_2 w_1">
                <div class="thirdTitle">（三）横向分析</div>
                <div class="content p_1">
                  截至<span v-if="pageObj?.month">{{ pageObj?.month }}月</span><span v-else>{{
                    pageObj?.year
                    }}年</span>末，公司<span v-for="(item, index) in yylFx" :key="index"><span v-show="item.actualValue">{{
                      item.indicatorDesc }}{{
                        item.actualValue
                      }}分<span v-show="item.comparisonResult !== '/'">，{{
                        item.comparisonResult
                        }}</span>；</span></span>具体如右图：
                </div>
              </div>
              <div class="chartWrapOne w_1">
                <FxChart ref="fxChartTwo" />
              </div>
            </div>
          </div>

          <div class="sectionOne">
            <div class="secondTitle">四、回笼链分析</div>
            <div class="content p_1">
              截至<span v-if="pageObj?.month">{{ pageObj?.month }}月</span><span v-else>{{
                pageObj?.year
                }}年</span>末。销售回款率{{ hllFx.actualValue }}%<span v-show="hllFx.predictiveValue">，预测值{{
                  hllFx.predictiveValue
                }}</span>%，偏离{{
                  hllFx.predictiveDeviateValue
                }}%，较五年内年均值{{
                hllFx.riskWarningValue
              }}%，同比{{
                hllFx.yearIncreaseRate > 0 ? '增加' : '减少'
              }}{{
                Math.abs(hllFx.yearIncreaseRate)
              }}个百分点<span v-show="pageObj?.indicatorList[5].comparisonResult !== '/'">，{{
                hllFx.comparisonResult
                }}</span>，{{ hllFx.remark }}。
            </div>
          </div>

        </div>

      </div>

    </div>
  </el-dialog>
</template>

<script>
import TllfxChart from '@/views/prewarning/capitalRisk/dialog/TllfxChart.vue'
import BlvChart from '@/views/prewarning/capitalRisk/dialog/BlvChart.vue'
import FxChart from '@/views/prewarning/capitalRisk/dialog/FxChart.vue'
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'
import { getCompanyIndicator } from '@/api/risk'

export default {

  components: {
    TllfxChart,
    BlvChart,
    FxChart
  },

  data () {
    return {
      open: false,
      queryParams: null,
      pageObj: null,
      firstZxfx: [],
      yylFx: [],
      hllFx: [],
      print: {
        id: 'printArea',
        popTitle: '', // 打印配置页上方标题
        extraHead: '', //最上方的头部文字，附加在head标签上的额外标签,使用逗号分隔
        preview: '', // 是否启动预览模式，默认是false（开启预览模式，可以先预览后打印）
        previewTitle: '', // 打印预览的标题（开启预览模式后出现）,
        previewPrintBtnLabel: '', // 打印预览的标题的下方按钮文本，点击可进入打印（开启预览模式后出现）
        zIndex: '', // 预览的窗口的z-index，默认是 20002（此值要高一些，这涉及到预览模式是否显示在最上面）   
        // previewBeforeOpenCallback () { }, //预览窗口打开之前的callback（开启预览模式调用）
        // previewOpenCallback () { }, // 预览窗口打开之后的callback（开启预览模式调用）
        // beforeOpenCallback () { }, // 开启打印前的回调事件
        // openCallback () { }, // 调用打印之后的回调事件
        // closeCallback () { }, //关闭打印的回调事件（无法确定点击的是确认还是取消）
        url: '',
        standard: '',
        extraCss: '',
      },
      loading: true
    }
  },

  methods: {
    showDialog (queryParams) {
      // console.log('回显的数据', row)
      this.open = true
      this.queryParams = queryParams
      this.getList()
    },

    getList () {
      this.loading = true
      getCompanyIndicator(this.queryParams).then(res => {
        this.pageObj = res.data
        this.firstZxfx = this.pageObj.indicatorList.slice(0, 6)
        this.yylFx = this.pageObj.indicatorList.slice(6, -1)
        this.hllFx = this.pageObj.indicatorList[this.pageObj.indicatorList.length - 1]
        console.log('弹窗列表', res, this.yylFx)
        this.$nextTick(() => {
          this.$refs.tllfxChart.option.series[0].data = this.firstZxfx.map(item => item.predictiveDeviateValue)
          // this.$refs.tllfxChart.option.series[0].data = this.firstZxfx.map(item => item.predictiveValue)
          // this.$refs.tllfxChart.option.series[1].data = this.firstZxfx.map(item => item.actualValue)
          this.$refs.tllfxChart.preInitChart()

          this.firstZxfx.forEach(el => {
            this.$refs[`blvChartTwo${el.indicatorName}`][0].option.title.text = el.indicatorName
            this.$refs[`blvChartTwo${el.indicatorName}`][0].option.xAxis.data = el.yearList.map(it => it.year)
            this.$refs[`blvChartTwo${el.indicatorName}`][0].option.series[0].data = el.yearList.map(it => it.valueDecimal)
            this.$refs[`blvChartTwo${el.indicatorName}`][0].option.series[1].data = el.yearList.map(it => el.riskWarningValue)
            this.$refs[`blvChartTwo${el.indicatorName}`][0].preInitChart()
          })
          this.$refs.fxChart.option.series[5].data = this.firstZxfx.map((item, index) => [index, item.comparisonResultDecimal != null ? Number(item.comparisonResultDecimal) : null])
          this.$refs.fxChart.preInitChart()



          this.$refs.tllfxChartTwo.option.series[0].data = this.yylFx.map(item => item.predictiveDeviateValue)
          // this.$refs.tllfxChartTwo.option.series[0].data = this.yylFx.map(item => item.predictiveValue)
          // this.$refs.tllfxChartTwo.option.series[1].data = this.yylFx.map(item => item.actualValue)
          this.$refs.tllfxChartTwo.option.xAxis[0].data = this.yylFx.map(item => item.indicatorDesc)
          this.$refs.tllfxChartTwo.preInitChart()

          this.yylFx.forEach(el => {
            this.$refs[`blvChartTwo${el.indicatorName}`][0].option.title.text = el.indicatorName
            this.$refs[`blvChartTwo${el.indicatorName}`][0].option.xAxis.data = el.yearList.map(it => it.year)
            this.$refs[`blvChartTwo${el.indicatorName}`][0].option.series[0].data = el.yearList.map(it => it.valueDecimal)
            this.$refs[`blvChartTwo${el.indicatorName}`][0].option.series[1].data = el.yearList.map(it => el.riskWarningValue)
            this.$refs[`blvChartTwo${el.indicatorName}`][0].preInitChart()
          })

          let newYylFx = this.yylFx.slice(2, -1)
          if (this.queryParams.timeFlag === 1) { // 月度
            newYylFx = newYylFx.filter(item => item.indicatorDesc !== '投资收益')
          }
          this.$refs.fxChartTwo.option.xAxis.data = newYylFx.map(item => item.indicatorDesc)
          this.$refs.fxChartTwo.option.series[0].data = newYylFx.map(item => 0)
          this.$refs.fxChartTwo.option.series[1].data = newYylFx.map(item => 40)
          this.$refs.fxChartTwo.option.series[2].data = newYylFx.map(item => 20)
          this.$refs.fxChartTwo.option.series[3].data = newYylFx.map(item => 20)
          this.$refs.fxChartTwo.option.series[4].data = newYylFx.map(item => 20)
          this.$refs.fxChartTwo.option.series[5].data = newYylFx.map((item, index) => [index, item.comparisonResultDecimal != null ? Number(item.comparisonResultDecimal) : '-'])
          this.$refs.fxChartTwo.preInitChart()
          this.loading = false

        })


      })
    },

    async handleExport () {
      const dom = this.$refs.contentToConvert
      html2canvas(dom, {
        useCORS: true,//解决网络图片跨域问题
        width: dom.width,
        height: dom.height,
        windowWidth: dom.scrollWidth,
        dpi: window.devicePixelRatio * 4, // 将分辨率提高到特定的DPI 提高四倍
        scale: 4, // 按比例增加分辨率
      }).then((canvas) => {
        const pdf = new jsPDF('p', 'mm', 'a4') // A4纸，纵向
        const ctx = canvas.getContext('2d')
        const a4w = 190
        const a4h = 240 // A4大小，210mm x 297mm，四边各保留20mm的边距，显示区域170x257
        const imgHeight = Math.floor(a4h * canvas.width / a4w) // 按A4显示比例换算一页图像的像素高度
        let renderedHeight = 0

        while (renderedHeight < canvas.height) {
          const page = document.createElement('canvas')
          page.width = canvas.width
          page.height = Math.min(imgHeight, canvas.height - renderedHeight)// 可能内容不足一页

          // 用getImageData剪裁指定区域，并画到前面创建的canvas对象中
          page.getContext('2d').putImageData(ctx.getImageData(0, renderedHeight, canvas.width, Math.min(imgHeight, canvas.height - renderedHeight)), 0, 0)
          pdf.addImage(page.toDataURL('image/jpeg', 1.0), 'JPEG', 10, 10, a4w, Math.min(a4h, a4w * page.height / page.width)) // 添加图像到页面，保留10mm边距

          renderedHeight += imgHeight
          if (renderedHeight < canvas.height) {
            pdf.addPage()// 如果后面还有内容，添加一个空页
          }
        }
        // 保存文件
        pdf.save(`test.pdf`)
      })
    },

    // 可用
    async handleExport2 () {
      // 获取要转换为PDF的DOM元素
      const element = this.$refs.contentToConvert
      // 使用html2canvas将DOM元素转换为canvas
      const canvas = await html2canvas(element, {
        scale: 2, // 提高图像清晰度
        useCORS: true, // 允许跨域资源共享
        logging: false // 禁用日志输出
      })
      // 创建一个新的jsPDF实例，设置为A4纸张大小
      const pdf = new jsPDF('p', 'mm', 'a4')
      const margin = 10 // 设置PDF页面的边距
      const contentWidth = 210 - (margin * 2) // 计算内容区域的宽度

      /********************** 第一页 ***********************/
      // 创建一个临时canvas用于存储当前页的内容
      const tempCanvas = document.createElement('canvas')
      const tempCtx = tempCanvas.getContext('2d')

      // 设置临时canvas的宽度为原始canvas的宽度
      tempCanvas.width = canvas.width
      // 计算并设置临时canvas的高度
      // const sliceHeight = canvas.height / pageCount
      const sliceHeight = 1820
      tempCanvas.height = sliceHeight

      // 计算当前页在原始canvas中的起始y坐标
      const sy = 0  // 1363

      // 将原始canvas的部分内容绘制到临时canvas
      tempCtx.drawImage(
        canvas,
        0, sy, // 源图像的起始x, y坐标
        canvas.width, sliceHeight, // 源图像的宽度和高度
        0, 0, // 目标canvas的起始x, y坐标
        tempCanvas.width, tempCanvas.height // 目标canvas的宽度和高度
      )

      // 将临时canvas转换为JPEG格式的图像数据
      const imgData = tempCanvas.toDataURL('image/jpeg', 1.0)

      // 计算图像在PDF中的高度以保持比例
      const imgHeight = (sliceHeight * contentWidth) / canvas.width
      // 将图像添加到PDF中
      pdf.addImage(
        imgData,
        'JPEG',
        margin, // x坐标
        margin, // y坐标
        contentWidth, // 图像宽度
        imgHeight  // 图像高度
      )


      /********************** 第二页 ***********************/
      pdf.addPage()

      const tempCanvasTwo = document.createElement('canvas')
      const tempCtxTwo = tempCanvasTwo.getContext('2d')

      // 设置实际需要的尺寸（根据第二页内容的实际高度调整）
      const actualHeight = 1000 // 根据实际内容调整这个值
      tempCanvasTwo.width = canvas.width
      tempCanvasTwo.height = actualHeight

      // 设置白色背景
      tempCtxTwo.fillStyle = '#FFFFFF'
      tempCtxTwo.fillRect(0, 0, tempCanvasTwo.width, tempCanvasTwo.height)

      // 计算源图像的起始位置和高度
      const syTwo = 1820 // 第一页内容的高度
      const sourceHeight = Math.min(canvas.height - syTwo, actualHeight)

      // 只复制需要的部分到临时canvas
      tempCtxTwo.drawImage(
        canvas,
        0, syTwo,
        canvas.width, sourceHeight,
        0, 0,
        tempCanvasTwo.width, actualHeight
      )

      // 将临时canvas转换为JPEG
      const imgDataTwo = tempCanvasTwo.toDataURL('image/jpeg', 1.0)

      // 添加到PDF，调整高度以匹配实际内容
      pdf.addImage(
        imgDataTwo,
        'JPEG',
        margin,
        margin,
        contentWidth,
        (contentWidth * actualHeight) / canvas.width // 保持宽高比
      )

      // 保存生成的PDF文件
      pdf.save('资金风险预警报告.pdf')
    },

    cancel () {
      this.open = false
    }
  }

}
</script>

<style scoped lang="scss">
::v-deep .el-dialog__header {
  display: none;
}

::v-deep .el-dialog__body {
  padding: 0;
}


.mainContainer {
  width: 100%;
  height: 100%;
  // background-color: aqua;
  box-sizing: border-box;
}

.headBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  padding: 10px;
  border-bottom: 1px solid #e8e8e8;
}

.closeClass {
  font-size: 15px;
}

.curP {
  cursor: pointer;
}

.paddingWrap {
  width: 100%;
  padding: 24px 36px;
  box-sizing: border-box;
}

.reportPage {
  width: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  .title {
    width: 100%;
    display: flex;
    justify-content: center;
    font-size: 29px;
    text-align: center;
    box-sizing: border-box;
    // border-bottom: 1px solid #e8e8e8;
    // padding-bottom: 16px;
    padding: 0 50px 16px 50px;
    font-family: fzxbsjt;

    span {
      white-space: nowrap; // 添加这一行
    }
  }
}

.sectionOne {
  box-sizing: border-box;
  padding: 16px 0;
  // border-bottom: 1px solid #e8e8e8;
}

.secondTitle {
  font-size: 21px;
  margin-bottom: 16px;
  font-family: ht;
}

.thirdTitle {
  font-size: 21px;
  margin-bottom: 8px;
  font-family: kt;
}

.content {
  text-align: justify;
  text-indent: 2em;
  line-height: 28px;
  font-size: 21px;
  box-sizing: border-box;
  font-family: fsGB2312;
}

.p_1 {
  padding: 0 24px;
}


.flex_1 {
  display: flex;
}

.flex_2 {
  display: flex;
  flex-direction: column;
}

.w_1 {
  width: 50%;
}

.p_1 {
  padding: 0 24px;
}

.m_1 {
  margin-top: 16px;
}

.chartWrapOne {
  height: 370px;
  box-sizing: border-box;
  // background-color: antiquewhite;
}

.swipWrap {
  width: 100%;
  // height: 200px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  grid-column-gap: 20px;
  grid-row-gap: 20px;
  // background-color: rgb(7, 8, 77);
}

.medium {
  background-color: aqua;
  height: 200px;
}

.blvWrap {
  width: 100%;
  height: 200px;
  // background-color: aqua;

}
</style>
