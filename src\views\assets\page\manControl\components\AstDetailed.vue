<template>
  <section>
    <!-- <el-form :inline="true"
      :model="formData"
      ref="ruleForm"
      label-width="40px"
      class="form-box">
      <el-form-item label="日期"
        prop="dateList">
        <el-date-picker v-model="formData.dateList"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="截止日期"
          @change="changeDateHandle" />
      </el-form-item>
    </el-form> -->

    <Grid ref="grid"
      :show-pagination="false"
      :overflow-tooltip="true"
      :data="gridData"
      :columns="columns"
      :search-params="searchParams">
      <template slot="operationSlot"
        slot-scope="scope">
        <Operation :list="operationItems"
          :row="scope.row" />
      </template>
    </Grid>

    <!-- 弹窗：详情 -->
    <DetailCom v-if="visibleDetailDia"
      :visible.sync="visibleDetailDia"
      :id="detailRow.id"
      :itemNo="detailRow.itemNo"
      :itemId="detailRow.itemId" />
  </section>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { astItemList } from '@/api/assets'
import Grid from '@/components/Grid/index.vue'
import DetailCom from '@/views/assets/page/manage/components/DetailCom.vue'

@Component({
  components: {
    Grid,
    DetailCom
  }
})
export default class extends Vue {
  @Prop() private rowData!: {
    id: number | string
  }

  private visibleDetailDia = false
  private detailRow = {}
  private gridData: any[] = []
  private formData = {
    dateList: []
  }
  private searchParams: any = {
    id: this.rowData.id
  }
  private operationItems: any[] = [
    {
      label: '查看',
      params: { type: 'see' },
      click: this.operateHandel
    }
  ]
  private columns = [
    {
      label: '资产名称',
      prop: 'assetName'
    },
    {
      label: '子资产编号',
      prop: 'itemNo'
    },
    {
      label: '资产地址',
      prop: 'address'
    },
    {
      label: '资产用途',
      prop: 'assetPurposeDesc'
    },
    {
      label: '租金总额',
      prop: 'totalBillFee'
    },
    {
      label: '应收租金',
      prop: 'billFee'
    },
    {
      label: '实收租金',
      prop: 'totalPaidFee'
    },
    {
      label: '经营类别',
      prop: 'manageTypeDesc'
    },
    {
      label: '使用状态',
      prop: 'useStatusDesc'
    },
    {
      label: '建筑面积(㎡)',
      prop: 'coveredArea'
    },
    {
      label: '土地面积(㎡)',
      prop: 'landArea'
    },
    {
      label: '操作',
      fixed: 'right',
      width: 60,
      slotName: 'operationSlot'
    }
  ]

  // 初始化
  private created() {
    this.getGridData()
  }

  // 日期改变触发表格更新
  private changeDateHandle(date: any[string] | null) {
    if (!date) this.formData.dateList = []
    this.getGridData()
  }

  // 列表搜索
  private async getGridData() {
    let objData = Object.assign(
      {
        id: this.rowData.id
      },
      this.formData
    )

    let data = await astItemList(objData)

    if (Array.isArray(data.data) && data.data.length) {
      this.gridData = data.data
    }
  }

  // 列表操作
  private operateHandel(row: any = {}, index: number, params: any) {
    this.detailRow = row

    switch (params.type) {
      case 'see':
        this.visibleDetailDia = true
        break
    }
  }
}
</script>

<style scoped lang="scss">
.form-box {
  margin-bottom: 4px;
  padding: 10px 0 0;
  background: #fff;
  .el-form-item {
    margin-bottom: 10px;
  }
  .mode-input {
    width: 260px;
  }
}
</style>