/*
 * 正则校验规则 & 表单自定义校验
 */

/* ---------------------------- 校验规则 ---------------------------- */
// 手机号码
export const isValidPhone = (path: string) => {
  const reg = /^(13[0-9]|14[579]|15[0-3,5-9]|16[6]|17[0135678]|18[0-9]|19[89])\d{8}$/
  return reg.test(path)
}

// 身份证
export function isID(path: string) {
  return /^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X)$/.test(path)
}

// 纳税人识别号
export function isTaxpayer(path: string) {
  return /^[^_IOZSVa-z\W]{2}\d{6}[^_IOZSVa-z\W]{10}$/.test(path)
}

// 统一社会信用代码
export function isLessorSocialCreditCode(path: string) {
  // /[^_IOZSVa-z\W]{2}\d{6}[^_IOZSVa-z\W]{10}$/g
  return /[^_IOZSVa-z\W]{2}\d{6}[^_IOZSVa-z\W]{10}$/.test(path)
}

/**
 * email
 * @param {*} email
 */
export const isValidEmail = (email: string) => {
  const reg = /^[A-Za-z0-9._%-]+@([A-Za-z0-9-]+.)+[A-Za-z]{2,4}$/
  return reg.test(email)
}

/* ------------------------- 表单自定义校验 -------------------------- */
// 表单自定义校验（手机号码）
export const validatePhone = (rule: any, value: string, callback: Function) => {
  if (value === '') {
    callback(new Error('请输入手机号码'))
  } else {
    if (!isValidPhone(value)) {
      callback(new Error('手机号码格式错误'))
      return
    }
    callback()
  }
}

// 表单自定义校验（身份证）
export const validateIdCard = (rule: any, value: string, callback: Function) => {
  if (value === '') {
    callback(new Error('请输入身份证号码'))
  } else {
    if (!isID(value)) {
      callback(new Error('身份证号码格式错误'))
      return
    }
    callback()
  }
}

// 表单自定义校验（纳税人识别号）
export const validateTaxpayerId = (rule: any, value: string, callback: Function) => {
  if (value === '') {
    callback(new Error('请输入纳税人识别号'))
  } else {
    if (!isTaxpayer(value)) {
      callback(new Error('纳税人识别号格式错误'))
      return
    }
    callback()
  }
}

// 自定义校验（email）
export const validateEmail = (rule: any, value: string, callback: Function) => {
  if (value === '') {
    callback()
  } else {
    if (!isValidEmail(value)) {
      callback(new Error('邮箱账号格式错误'))
      return
    }
    callback()
  }
}

// 自定义校验（统一社会信用代码）
export const validateLessorSocialCreditCode = (rule: any, value: string, callback: Function) => {
  if (value === '') {
    callback()
  } else {
    if (!isLessorSocialCreditCode(value)) {
      callback(new Error('统一社会信用代码格式错误'))
      return
    }
    callback()
  }
}
