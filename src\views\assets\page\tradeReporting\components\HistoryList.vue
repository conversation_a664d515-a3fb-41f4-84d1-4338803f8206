<template>
  <Grid ref="grid"
    :show-index="true"
    :show-pagination="true"
    :columns="cols"
    :remote-url="remoteUrl"
    :search-params="params">

    <template #operationSlot="{row}">
      <Operation :list="operationList"
        :row="row" />
    </template>
  </Grid>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import Uploader from '@/components/Uploader/index.vue'
import Grid from '@/components/Grid/index.vue'
import { deleteAssetLedger, transactionBack } from '@/api/assets'
import { Confirm } from '@/decorators/index'
import searchBar from '@/components/SearchBar/index.vue'
import Operation, { OperationItem } from '@/components/Operation/index.vue'

export interface TransactionBatch {
  applicant: string // 申请人
  applicationTime: string // 申请时间
  batchNo: string // 申请批次号
  status: number // 有效状态，1-正常，0-作废
  assignUser:number,//-1为未分配
}

@Component({
  components: {
    Grid,
    searchBar,
    Uploader,
    Operation
  }
})
export default class extends Vue {
  @Prop({ default: () => ({}) }) private params!: { input: string }

  private remoteUrl = '/fht-monitor/asset/transaction/batch/page'

  private searchItems = [
    {
      type: 'text',
      key: 'input',
      placeholder: '单位名称/坐落',
      width: '330px'
    }
  ]

  // 操作按钮
  private operationList: OperationItem[] = [
    {
      label: '撤回',
      click: this.onRevocation,
      style: 2,
      visible: (row: any) => {
        return row.status == "1"
      }
    },
    {
      label: '重新发起',
      click: this.onReset,
      style: 1,
      visible: (row: any) => {
        return row.status == "0"
      }
    }
  ]

  private cols = [
    {
      prop: 'batchNo',
      label: '申请批次号',
      minWidth: 580
    },
    {
      prop: 'applicant',
      label: '申请人',
      minWidth: 210
    },
    {
      prop: 'applicationTime',
      label: '申请时间',
      minWidth: 280
    },
    {
      label: '操作',
      slotName: 'operationSlot',
      fixed: 'right',
      minWidth: 80,
      labelAlign: 'center'
    }
  ]

  // 撤销
  @Confirm({
    title: '提示',
    content: `是否确认撤销该交易申报？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private async onRevocation(row: TransactionBatch) {
    //
    let res = await transactionBack({ batchNo: row.batchNo })

    if (res.success) {
      this.$message.success(res.msg || '撤回成功！')
      this.refresh()
    }
  }

  // 重新发起, 与新增逻辑一样，只是有老数据
  @Confirm({
    title: '提示',
    content: `是否确认重新发起该交易申报？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private onReset(row: TransactionBatch) {
    this.$emit('reset', row.batchNo)
  }

  // 刷新表格
  public refresh() {
    // let grid = this.$refs['grid'] as Grid
    // grid.refresh(true)
  }
}
</script>

<style lang="scss" scoped>
</style>
