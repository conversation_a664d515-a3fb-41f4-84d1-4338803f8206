/** 资产概况 */
<template>
  <section class="asset-overview">
    <Ratio :ratioData="currentAssets" />
    <Ratio :ratioData="nonCurrentAssets" />
    <PieCharts :pieData="[currentAssets.value, nonCurrentAssets.value]" />
    <BarCharts />
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import PieCharts from './pieCharts.vue'
import BarCharts from './barCharts.vue'
import Ratio, { RatioData } from './Ratio.vue'

@Component({
  components: {
    PieCharts,
    BarCharts,
    Ratio
  }
})
export default class extends Vue {
  private currentAssets: RatioData = {
    label: '流动资产',
    // 值
    value: this.getData(),
    // 同比
    YOY: 21.56,
    // 环比
    QOQ: 19.54,
    unit: '亿元'
  }
  private nonCurrentAssets: RatioData = {
    label: '非流动资产',
    // 值
    value: this.getData(),
    // 同比
    YOY: 21.56,
    // 环比
    QOQ: 19.54,
    unit: '亿元'
  }

  private getData() {
    return (Math.random() * 100).toFixed(2)
  }
}
</script>


<style scoped lang="scss">
.asset-overview {
  box-sizing: border-box;
  width: 100%;
}
</style>