<!-- 业务预警占比 -->
<template>
  <div class="graph-proportion">
    <graph-title title="业务预警占比(Top 6)"
      :color-visible="false" />
    <div ref="graph"
      class="graph"></div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import * as echarts from 'echarts'
import GraphTitle from './GraphTitle.vue'

@Component({
  components: {
    GraphTitle
  }
})
export default class PrewarningProportion extends Vue {
  private mounted() {
    let option = {
      legend: {
        bottom: '0',
        orient: 'horizontal',
        itemWidth: 8,
        itemHeight: 8
      },
      toolbox: {
        show: true
      },
      color: ['#FFD879', '#fb3f3f', '#FF9F9F', '#FF8C07', '#77ADFF', '#9BBAFF'],
      series: [
        {
          type: 'pie',
          radius: [30, 100],
          center: ['50%', '50%'],
          label: {
            normal: {
              show: true,
              formatter: '{b}({d}%)'
            }
          },
          roseType: 'area',
          itemStyle: {
            borderRadius: 0
          },
          data: [
            { value: 40, name: '资产' },
            { value: 38, name: '负债' },
            { value: 32, name: '欠租' },
            { value: 30, name: '合同' },
            { value: 28, name: '收款' },
            { value: 26, name: '租金' }
          ]
        }
      ]
    }

    let chart = echarts.init(this.$refs.graph as any)
    chart.setOption(option)
  }
}
</script>

<style lang="scss" scoped>
.graph-proportion {
  background: #fff;
  border-radius: 4px;
  margin-left: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  .graph {
    flex: 1;
  }
}
</style>