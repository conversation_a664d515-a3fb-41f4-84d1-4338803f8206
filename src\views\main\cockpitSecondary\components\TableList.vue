/** 自动滚动表格 */

<template>
  <div :style="{ fontSize: `${fontSize || 22}px` }"
    class="project-implementation-overview">
    <i v-if="type === 'ZJH'"
      class="rank el-icon-rank"
      title="查看详情"
      @click="onClickRank" />

    <el-row class="project-implementation-overview__header"
      :span="24">
      <el-col
        :class="[item.align === 'right' && 'text-right', item.align === 'center' && 'text-center']"
        v-for="(item, index) in cols"
        :key="index"
        :span="item.span">{{ item.label }}</el-col>
    </el-row>

    <div class="project-implementation-overview__list"
      ref="list">
      <el-row v-for="(data, index) in source"
        :key="`data${index}`"
        :span="24"
        :class="['project-implementation-overview__item', currentIndex === index && 'project-implementation-overview__item--checked']"
        @mouseenter.native="mouseenterRow"
        @mouseleave.native="mouseleaveRow"
        @click.native="onItemClick(index, data, $event)">
        <el-col v-for="(item, i) in cols"
          :key="`item${i}`"
          :class="[item.align === 'right' && 'text-right', item.align === 'center' && 'text-center']"
          :span="item.span"
          :title="data[item.prop]">
          <div v-if="item.type === 'earlyWarningLevel'"
            :style="{color: data[item.prop] === 1 ? 'rgba(230, 182, 7, 1)' : 'rgba(236, 52, 47, 1)'}">
            {{ data[item.prop] ? '黄色预警' : '红色预警' }}
          </div>
          <template v-else>
            {{ data[item.prop] }}
          </template>
        </el-col>
      </el-row>
    </div>

    <!-- 浙交会（type = ZJH）-->
    <ViewIframe v-if="visibleViewIframe"
      :visible.sync="visibleViewIframe"
      :src="iframeSrc"
      :title="iframeTitle"
      :isHeader="isHeader"
      width="1260px" />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { capitalProfile } from '@/api/cockpit'
import ViewIframe from '@/components/ViewIframe/index.vue'

export interface ColItem {
  label: string
  span: number
  prop: string
  type?: string
  align?: 'right' | 'center' | 'left'
}

@Component({
  components: {
    ViewIframe
  }
})
export default class extends Vue {
  @Prop({ default: () => [] }) cols!: ColItem[]
  @Prop({ default: () => [] }) source!: Record<string, any>[]
  @Prop({ default: 5 }) private middleIndex!: number
  @Prop() private fontSize!: number
  @Prop({ default: '' }) private type?: string // 模块类型(如果模块有交互事件)

  private currentIndex = 0
  private loading = false
  private timer: any

  // 浙交会相关数据
  private iframeSrc = ''
  private iframeTitle = ''
  private isHeader = false
  private visibleViewIframe = false

  // 组件初始化
  private mounted() {
    this.setTimer()
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await capitalProfile()
  }

  // 浙交会（type = ZJH）
  private onClickRank() {
    this.isHeader = true
    this.iframeTitle = '浙交会'
    this.iframeSrc = 'https://www.zjpse.com/page/s/prjs/lease/index'
    this.visibleViewIframe = true
  }

  // 点击列表
  private onItemClick(index: number, data: any) {
    this.currentIndex = index

    if (this.type === 'ZJH') {
      this.isHeader = true
      this.iframeTitle = '浙交会'
      this.iframeSrc = data.link
      this.visibleViewIframe = true
    }
  }

  private setTimer() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    let listDom = this.$refs['list'] as HTMLDivElement
    this.timer = setInterval(() => {
      this.currentIndex++
      if (this.currentIndex > this.middleIndex && this.currentIndex < this.source.length) {
        listDom.scrollTo(0, (this.currentIndex - this.middleIndex) * 48)
      } else if (this.currentIndex === this.source.length) {
        this.currentIndex = 0
        listDom.scrollTo(0, 0)
      }
    }, 6 * 1000)
  }

  mouseenterRow() {
    clearInterval(this.timer)
  }

  mouseleaveRow() {
    this.setTimer()
  }

  destroyed() {
    clearInterval(this.timer)
  }
}
</script>

<style scoped lang="scss">
.project-implementation-overview {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .rank {
    position: absolute;
    right: 10px;
    top: -75px;
    z-index: 10;
    color: #00e8fa;
    font-size: 52px;
    cursor: pointer;
  }

  .el-col {
    text-align: left;
    font-weight: bold;
    padding: 0 10px;
    font-size: 28px;
    height: 64px;
    line-height: 64px;
    white-space: nowrap;
  }
  .text-right {
    text-align: right !important;
  }
  .text-center {
    text-align: center !important;
  }
  &__header {
    .el-col {
      font-size: 28px;
      color: #00e8fa;
    }
  }
  &__list {
    height: calc(100% - 48px);
    overflow-x: hidden;
    overflow-y: auto;
    scroll-behavior: smooth;
    &::-webkit-scrollbar {
      display: none;
    }
  }

  &__item {
    position: relative;
    z-index: 10;
    transition: all 0.5s;
    will-change: background;
    cursor: pointer;
    .el-col {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      font-size: 30px;
    }

    &:nth-child(odd) {
      border-radius: 4px;
    }
    &--checked,
    &:hover {
      .el-col {
        height: 64px;
        line-height: 64px;
        color: #ffcd36;
      }
      background: rgba($color: #387aff, $alpha: 0.5) !important;
    }
  }
}
</style>