// 对外捐赠事项
<template>
  <el-container class="container"
    direction="vertical">
    <search-bar :items="searchItems"
      @onSearch="handleSearch">
      <div>
        <el-button type="primary"
          @click="AddForm">新增</el-button>
        <!-- <el-button type="primary">批量导入</el-button> -->
      </div>
    </search-bar>
    <Grid @row-click="loaddetail"
      :columns="cols"
      ref="grid"
      :remote-url="remoteUrl"
      :show-pagination="true"
      :search-params="searchParams">
      <template slot="documentNo"
        slot-scope="scope">
        <span>{{ scope.row.documentNo }}</span>
      </template>
      <template slot="state"
        slot-scope="scope">
        <span v-if="scope.row.state == 1">审批中</span>
        <span v-else-if="scope.row.state == 2">审批通过</span>
        <span v-else-if="scope.row.state == 3"
          style="color: #df7575">审批不通过</span>
      </template>
      <!-- 决策机构 -->
      <!-- <div v-for="(item,index) in cols" :key="index" slot="item.slotName" slot-scope="scope">
            <span v-if="'filterOptions' in item">{{ scope.row[item.prop] }}</span>
          </div> -->
      <template slot="issuanceCurrency"
        slot-scope="scope">
        <span v-if="scope.row.state == 1">人民币</span>
        <span v-else-if="scope.row.state == 2">美元</span>
        <span v-else-if="scope.row.state == 3">其他</span>
      </template>
      <template slot="operatingBar"
        slot-scope="scope">
        <div class="operatingBar_content">
          <el-button type="text"
            @click.stop="loaddetail(scope.row)">查看</el-button>
          <el-button type="text"
            @click.stop="loofinfo(scope.row)">编辑</el-button>

        </div>
      </template>
    </Grid>
    <Dialog-add :mode="DialogMode"
      :visible.sync="showDialogAbroad"
      v-if="showDialogAbroad"
      :Diaformdata="Diaformdata"
      @changshowDialogAdd="changeShowDialogAdd" />
    <DetailAsset :fileList="pageData.attachmentFileDTOList"
      dict="financial_foreignDonation_attach"
      :visible="showDetailAsset"
      :list="Diadetaillist"
      @changeShowDetail="changeShowDetail"></DetailAsset>
  </el-container>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import Grid from '@/components/Grid/index.vue'
import DialogAdd from '../components/DialogAbroad.vue'
import DetailAsset from '../components/DetailAsset.vue'
import { DataDebissuance } from '../date'
import { issuanceCurrency, donationTimesType, disbursementWayType } from '../filterOptions'
import searchBar from '@/components/SearchBar/index.vue'
import { deepClone } from '../../../utils'

@Component({
  components: {
    Grid,
    DialogAdd,
    DetailAsset,
    searchBar
  }
})
export default class Container extends Vue {
  private pageData = {}
  private showDialogAbroad = false
  private searchParams = {} //表格搜索条件
  private DialogMode = ''
  private Diaformdata = {}
  private showDetailAsset = false //显示详情弹窗
  private Diadetaillist: object[] = [] //详情列表
  private remoteUrl = '/fht-monitor/fin/foreignDonation/page'
  private data: object[] = DataDebissuance
  private searchItems = [
    {
      type: 'text',
      key: 'keyword',
      placeholder: '请输入关键字进行查询',
      width: '200px'
    }
  ]
  private cols = [
    {
      label: '单据编号',
      prop: 'documentNo',
      slotName: 'documentNo',
      minWidth: 150
    },
    // {
    //   label: '审批状态',
    //   prop: 'state',
    //   slotName: 'state',
    //   minWidth: 90,
    //   searchKey: 'state',
    //   filterOptions: [
    //     {
    //       label: '审批中',
    //       value: 1
    //     },
    //     {
    //       label: '审批通过',
    //       value: 2
    //     },
    //     {
    //       label: '审批不通过',
    //       value: 3
    //     }
    //   ]
    // },
    {
      label: '填报单位',
      prop: 'reportDeptName',
      minWidth: 110
    },
    {
      label: '文号',
      prop: 'referenceNo',
      minWidth: 120
    },
    {
      label: '拟捐赠对象',
      prop: 'issuer',
      minWidth: 100
    },
    {
      label: '捐赠依据',
      prop: 'preDonationBasis',
      minWidth: 100
    },
    {
      label: '捐赠理由',
      prop: 'donationReason',
      minWidth: 80
    },
    {
      label: '捐赠项目',
      prop: 'donationProject',
      minWidth: 80
    },
    {
      label: '捐赠金额（元）',
      prop: 'donationAmount',
      minWidth: 110
    },
    {
      label: '拟赠时间',
      prop: 'preDonationDate',
      minWidth: 100
    },
    {
      label: '拟捐数量',
      prop: 'preDonationNum',
      minWidth: 80
    },
    {
      label: '捐赠形式',
      prop: 'donationFormDesc',
      minWidth: 80
      // filterOptions: issuanceCurrency
    },
    {
      label: '其他捐赠形式',
      prop: 'currency',
      minWidth: 100
    },
    {
      label: '捐赠次数',
      prop: 'donationTimesDesc',
      minWidth: 80
      // filterOptions: donationTimesType
    },
    {
      label: '捐赠用途',
      prop: 'donationPurpose',
      minWidth: 120
    },
    // {
    //   label: '有效期',
    //   prop: 'decisionMakingBody',
    //   minWidth: 80
    // },
    {
      label: '列支途径',
      prop: 'disbursementWayDesc',
      minWidth: 100
    },
    {
      label: '联系人',
      prop: 'contact',
      minWidth: 80
    },
    {
      label: '联系电话',
      prop: 'contactPhone',
      minWidth: 80
    },
    // {
    //   label: '备案日期',
    //   prop: 'issuanceTimeNumber',
    //   minWidth: 80
    // },
    // {
    //   label: '备案人',
    //   prop: 'issuanceForm',
    //   minWidth: 80
    // },
    {
      label: '操作',
      prop: 'operatingBar',
      slotName: 'operatingBar',
      fixed: 'right',
      minWidth: 80
    }
  ]

  // search搜索
  private handleSearch(condition: any) {
    this.Diaformdata = {
      id: ''
    }
    this.searchParams = condition
    let grid: any = this.$refs.grid
    grid.refresh()
  }
  // 新增按钮
  private AddForm() {
    this.DialogMode = 'add'
    this.Diaformdata = {
      id: ''
    }
    this.showDialogAbroad = true
  }
  // 改变显示隐藏
  private changeShowDialogAdd(state: boolean) {
    if (state == false) {
      ;(this.$refs.grid as Grid).refresh(true)
    }

    this.showDialogAbroad = state
  }
  // 改变详情显示隐藏
  private changeShowDetail(state: boolean) {
    this.showDetailAsset = state
    let grid: any = this.$refs.grid
    grid.refresh()
  }
  //加载查看数据
  private loaddetail(row: any) {
    this.Diaformdata = deepClone(row)
    this.DialogMode = 'see'
    this.showDialogAbroad = true
  }
  //编辑
  private loofinfo(row: object) {
    // await
    this.DialogMode = 'edit'
    this.Diaformdata = row
    this.showDialogAbroad = true
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
}
</style>

