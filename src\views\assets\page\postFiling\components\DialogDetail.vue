<template>
  <Dialog width="900px"
    :title="title"
    :visible="visible"
    @close="handleClose">
    <!-- 内容 -->
    <div slot="body"
      v-loading="loading">
      <section class="filing-detail-wrap">
        <el-tabs v-model="activeName">
          <el-tab-pane label="备案核定信息"
            name="verification">
            <el-descriptions title="备案信息">
              <el-descriptions-item label="备案编号">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="备案上报时间">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="备案类别">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="处置方式">{{detailData.xxxx}}</el-descriptions-item>
            </el-descriptions>

            <hr class="hr" />

            <el-descriptions title="评估信息">
              <el-descriptions-item label="有无评估">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="评估机构">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="评估价">{{detailData.xxxx}}</el-descriptions-item>
            </el-descriptions>

            <hr class="hr" />

            <el-descriptions title="交易信息">
              <el-descriptions-item label="账面净值">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="挂牌价">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="成交价">{{detailData.xxxx}}</el-descriptions-item>
            </el-descriptions>

            <hr class="hr" />

            <el-descriptions title="租期信息">
              <el-descriptions-item label="租期">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="有无免租期">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="免租期是否在租期内">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="免租时间">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="租金是否递增">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="递增幅度">{{detailData.xxxx}}</el-descriptions-item>
            </el-descriptions>

            <hr class="hr" />

            <el-descriptions title="其他信息">
              <el-descriptions-item label="其他权利">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="付款方式">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="出让方联系人">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="出让方联系电话">{{detailData.xxxx}}</el-descriptions-item>
            </el-descriptions>
            <el-descriptions :column="1">
              <el-descriptions-item label="交易条件与受让方资格条件">{{detailData.xxxx}}</el-descriptions-item>
            </el-descriptions>

            <hr class="hr" />

            <el-descriptions :column="1"
              title="附件">
              <el-descriptions-item label="附件">{{detailData.xxxx}}</el-descriptions-item>
            </el-descriptions>

            <hr class="hr" />

            <el-descriptions :column="1"
              title="长租原因、备注">
              <el-descriptions-item label="长租原因">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="备注">{{detailData.xxxx}}</el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>

          <el-tab-pane label="资产基本信息"
            name="assetBase">
            <el-descriptions :column="3">
              <el-descriptions-item label="资产编号">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="资产权利人">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="出让方名称">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="受让方名称">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="资产类别">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="不动产权证号">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="房产证编号">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="土地证编号">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="房产性质">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="建筑面积">{{detailData.xxxx}}</el-descriptions-item>
              <el-descriptions-item label="计量单位">{{detailData.xxxx}}</el-descriptions-item>
            </el-descriptions>
            <el-descriptions :column="1">
              <el-descriptions-item label="坐落地址">{{detailData.xxxx}}</el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>
        </el-tabs>
      </section>
    </div>

    <!-- 底部 -->
    <div slot="footer">
      <el-button @click="handleClose">关 闭</el-button>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'

@Component({
  components: {
    Dialog
  }
})
export default class extends Vue {
  @Prop({ default: '备案信息' }) private title?: string
  @Prop() private rowData!: object
  @Prop() private visible!: boolean

  private loading = false
  private detailData = {}
  private activeName: 'verification' | 'assetBase' = 'verification'

  // 初始化数据
  private mounted() {
    this.initDetail()
  }

  // 获取详情数据
  private initDetail() {
    //this.$message.warning('初始化数据')
  }

  // 关闭弹窗
  private handleClose() {
    this.$emit('update:visible', false)
  }
}
</script>

<style lang="scss" scoped>
::v-deep .filing-detail-wrap {
  position: relative;
  .hr {
    height: 10px;
    border: none;
  }
  .el-descriptions {
    .el-descriptions__header {
      color: #585757;
      margin-bottom: 6px;
      .el-descriptions__title {
        font-size: 14px;
      }
    }
    .el-descriptions__body {
      padding: 10px 14px 2px;
      border-radius: 4px;
      background: #f9f9f9;
    }
    .el-descriptions-row {
      font-size: 14px;
      .el-descriptions-item__label {
        color: #797979;
      }
      .el-descriptions-item__content {
        color: #585757;
      }
    }
  }
}
</style>
