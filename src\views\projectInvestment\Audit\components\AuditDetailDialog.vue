/**
  组件描述:  新增年度计划投资弹框
*/
// Schedule
// Record
<template>
  <Dialog title="项目投资审批-详情"
    width="980px"
    :visible="visible"
    :close-on-click-modal="true"
    :destroy-on-close="false"
    :append-to-body="true"
    @close="closeDlg"
    :modal="false"
    v-loading="loading">
    <div slot="body"
      style="padding:0;">
      <el-tabs type="border-card"
        style="height:550px;">
        <el-tab-pane>
          <span slot="label">
            基本信息
          </span>
          <el-descriptions class="margin-top p-l-20 p-r-20"
            :column="2"
            border>
            <el-descriptions-item label="项目名称">{{ auditDetail.projectName }}</el-descriptions-item>
            <el-descriptions-item label="项目编号">{{auditDetail.projectCode}}</el-descriptions-item>
            <el-descriptions-item label="投资主体">{{auditDetail.orgName}}</el-descriptions-item>
            <el-descriptions-item
              label="开工情况">{{getStateValue("invest_project",auditDetail.workStatus)}}</el-descriptions-item>
            <el-descriptions-item
              label="项目性质">{{getStateValue("invest_project_property",auditDetail.projectProperty)}}</el-descriptions-item>
            <el-descriptions-item
              label="项目类型">{{getStateValue("invest_project_type",auditDetail.projectCategory)}}</el-descriptions-item>
            <el-descriptions-item
              label="项目分类">{{getStateValue("invest_project_category",auditDetail.projectCategory)}}</el-descriptions-item>
          </el-descriptions>
          <el-descriptions class="margin-top p-l-20 p-r-20"
            :column="2"
            border>
            <el-descriptions-item
              label="年初是否已列入投资计划">{{auditDetail.isPlan==1?"是":"否"}}</el-descriptions-item>
            <el-descriptions-item
              label="是否主体投资">{{auditDetail.isMainInvest==1?"是":"否"}}</el-descriptions-item>
            <el-descriptions-item
              label="是否境外投资">{{auditDetail.isOverInvest==1?"是":"否"}}</el-descriptions-item>
          </el-descriptions>
          <el-descriptions class="margin-top p-l-20 p-r-20"
            :column="2"
            border>
            <el-descriptions-item label="总投资额">{{auditDetail.whetherAmt}}万元</el-descriptions-item>
            <el-descriptions-item
              label="预计投入自有资金">{{auditDetail.investAmount}}万元</el-descriptions-item>
            <el-descriptions-item label="建设起止日期">{{auditDetail.projectDate}}</el-descriptions-item>
            <el-descriptions-item
              label="预期投资回报率">{{auditDetail.expectedRoi}}%</el-descriptions-item>
            <el-descriptions-item
              label="本项目投资前公司资产负债率">{{auditDetail.beforeInvestmentRatio}}%</el-descriptions-item>
            <el-descriptions-item
              label="本项目投资后预计公司资产负债率">{{auditDetail.afterInvestmentRatio}}%</el-descriptions-item>
            <el-descriptions-item
              label="本项目投资前公司非主业投资比例">{{auditDetail.beforeInvestmentRatio}}%</el-descriptions-item>
            <el-descriptions-item
              label="本项目投资后预计公司非主业投资比例">{{auditDetail.afterInvestmentRatio}}%</el-descriptions-item>
            <el-descriptions-item class="el-row-24"
              label="项目地址"
              :span="2">{{auditDetail.projectAddress}}</el-descriptions-item>
            <el-descriptions-item class="el-row-24"
              label="备注"
              :span="2">{{auditDetail.remark}}</el-descriptions-item>
          </el-descriptions>
          <AccessoryList v-model="auditDetail.fileList"
            mode="see"
            class="m-20" />
        </el-tab-pane>
        <el-tab-pane label="进度列表">
          <AuditPropress v-if="!loading"
            style="height:550px;"
            :option="options"
            mode="audit"></AuditPropress>
        </el-tab-pane>
        <el-tab-pane label="备案列表">
          <AuditSchedule v-if="!loading"
            style="height:550px;"
            :option="options"
            mode="audit"></AuditSchedule>
        </el-tab-pane>
      </el-tabs>

      <!-- 审批流 -->
      <!-- <AuditFlow :mode="mode" /> -->
    </div>
    <div slot="footer">
      <el-button v-if="mode === 'see'"
        @click="closeDlg">关闭</el-button>
      <el-button v-if="mode === 'audit'"
        @click="closeDlg">取消</el-button>
      <el-button v-if="mode === 'audit'"
        class="primary-buttom"
        type="primary"
        :loading="loading"
        @click="submitForm">确认</el-button>
    </div>
  </Dialog>
</template>
<script lang="ts">
import { Component, Emit, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import { Loading } from '@/decorators'
import { ElForm } from 'node_modules/element-ui/types/form'
import Uploader from '../../components/Uploader/index.vue'
import AuditFlow from '../../components/AuditFlow.vue'
import { ProjectReview } from '@/api/projectInvestment'
import AccessoryList, { Accessory } from '@/views/assets/components/TradeDialog/components/AccessoryFileList.vue'
import { GetProjectDetail } from '@/api/projectInvestment'
import AuditSchedule from '@/views/projectInvestment/RecordsAdjustment/index.vue'
import AuditPropress from '@/views/projectInvestment/Propress/index.vue'
import { BusinessModule } from '@/store/modules/businessDict'
@Component({
  components: {
    Dialog,
    Uploader,
    AuditFlow,
    AccessoryList,
    AuditSchedule,
    AuditPropress
  }
})
export default class extends Vue {
  @Prop() private visible!: boolean
  @Prop() private title!: string
  @Prop() private mode!: 'audit' | 'see'
  @Prop() private detail!: any

  // 配置
  private options = {
    mode: 'audit',
    planType: '',
    projectCode: this.detail.projectCode
  }

  private loading = false

  private auditDetail: any = {
    projectName: '',
    afterInvestmentRatio: 0,
    approvalStatus: '',
    assetRatio: 0,
    auditCompletionTime: '',
    beforeInvestmentRatio: 0,
    createTime: '',
    createUser: '',
    decisionFile: '',
    expectedInvestPeriod: 0,
    expectedRoi: 0,
    feasibilityFile: '',
    investAdviceFile: '',
    investAmount: 0,
    investCategory: '',
    investSubject: '',
    isMainInvest: '',
    isOverInvest: '',
    isPlan: '',
    legalFile: '',
    level: '',
    necessaryFile: '',
    preparationUnit: '',
    projectCode: '',
    remark: '',
    reviewFeedback: '',
    riskFile: '',
    whetherAmt: 0,
    fileList: []
  }

  // 弹窗关闭
  @Emit('close')
  private onClose() {
    //
  }

  created() {
    // this.auditDetail = Object.assign({}, this.detail)

    // let auditDetail = this.auditDetail
    this.getdetail()
  }
  private async getdetail() {
    this.loading = true
    try {
      let res = await GetProjectDetail({
        id: this.detail.id || ''
      })
      if (res.success) {
        this.auditDetail = Object.assign({}, res.data)
        this.$forceUpdate()

        this.loading = false
      }
    } catch (e) {
      this.loading = false
    }
  }
  // 关闭弹窗
  private closeDlg() {
    this.$emit('update:visible', false)
    this.onClose()
  }
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  // 筛选字典项
  private getStateValue(dictname: string, values: any = '') {
    let list = this.getDictData(dictname).find((res: any) => {
      return res.value == values
    }) || { label: '', value: '' }
    return list.label
  }
}
</script>

<style lang="scss" scoped>
::v-deep.el-tabs--border-card > .el-tabs__content,
.common-dailog > .el-dialog > .el-dialog__body {
  padding: 0 !important;
}
::v-deep.el-tabs--border-card {
  border: 0;
  box-shadow: none;
}
.uploader-list {
  display: flex;
  flex-wrap: wrap;
  ::v-deep .uploader-wrapper {
    margin: 12px 24px;
  }
}
::v-deep.el-radio-button__orig-radio:disabled:checked {
  background-color: aqua !important;
}
::v-deep .el-descriptions-row {
  th {
    width: 230px;
  }
  td {
    height: 34px;
    min-width: 200px;
  }
}
</style>

