/* 通用页面 */

<template>
  <section class="cockpit-panel-wrapper"
    ref="wrapper">
    <div class="cockpit-panel"
      :style="{
      width: `${designWidth}px`,
      height: `${designHeight}px`,
      transform: `scale(${rate}, ${rate}) translate(-50%, -50%) !important` 
    }">
      <!-- <StarrySky /> -->
      <TimeSelection v-if="isTimeSelection" />

      <div class="content">
        <CommonHeader v-model="switchValue"
          :switchList="switchList"
          @change="onModuleChange"
          @enterFull="inWindowFullScreen"
          @exitFull="outWindowExitFullScreen()"
          class="cockipt-approach-header" />

        <div class="cockpit-panel-content">
          <slot name="content" />
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator'
import CommonHeader, { SwitchItem } from './CommonHeader.vue'
import { debounce, isWindowFull, windowExitFullScreen, windowFullScreen } from '@/utils'
import StarrySky from './StarrySky.vue'
import TimeSelection from '@/views/main/cockpitcren/components/Public/TimeSelection.vue'

@Component({
  components: {
    CommonHeader,
    StarrySky,
    TimeSelection
  }
})
export default class extends Vue {
  @Prop() private switchList!: SwitchItem<any>[]
  @Prop() private currentModule!: any

  // 比例
  private rate = 1
  private loading = false
  private switchValue: any = ''

  // 设计图大屏宽高
  private designWidth = 3240
  private designHeight = 1823

  @Watch('currentModule', { immediate: true })
  private onCurrentModuleChange() {
    this.switchValue = this.currentModule
  }

  @Emit('moduleChange')
  private onModuleChange(name: string) {
    return name
  }

  // 销毁掉公共组件里面绑定的 $bus 避免重复触发
  @Watch('switchValue', { immediate: true })
  private onChangeSwitchValue() {
    this.$bus.$off('BusYearTimeChange')
    this.$bus.$off('BusMoonTimeChange')
  }

  // 判断窗口是否处于全屏模式
  get getIsWindowFull() {
    return isWindowFull
  }

  // 左侧时间模块是否显示
  get isTimeSelection() {
    let isShow = true
    let query: any = this.$route.query

    switch (query.module) {
      case 'DebtWarning':
      case 'CompanyDetail':
        isShow = false
        break
      default:
        isShow = true
        break
    }

    return isShow
  }

  // 进入全屏
  private inWindowFullScreen() {
    return windowFullScreen(this.$refs['wrapper'] as HTMLElement)
  }

  // 退出全屏
  private outWindowExitFullScreen() {
    return windowExitFullScreen()
  }

  // 组件初始化
  private mounted() {
    this.getContentWidth()

    // 页面大小改变
    window.onresize = debounce(() => {
      this.$bus.$emit('BusWindowFullScreen', this.getIsWindowFull())
      this.$nextTick(() => {
        this.getContentWidth()
      })
    })
  }

  // 获取内容宽度, 保证宽高比相等, 并设置
  private getContentWidth() {
    let contentDom = this.$refs['wrapper'] as HTMLDivElement
    let contentHeight = contentDom.clientHeight || 0
    let contentWidth = contentDom.clientWidth || 0
    // 背景图的宽高，默认为高100%
    let panelWidth = contentHeight * (this.designWidth / this.designHeight)

    if (panelWidth > contentWidth) {
      // 如果大于, 以宽为标准
      this.rate = contentWidth / this.designWidth
    } else {
      // 否则以高为标准
      this.rate = panelWidth / this.designWidth
    }

    this.$bus.$emit('cockpitRateChange', this.rate)
  }
}
</script>

<style scoped lang="scss">
$sliderWidth: 605px;
$textColor: #5db0ea;

.module-switch {
  position: absolute;
  top: 170px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 40px;
}
.cockpit-panel-wrapper {
  background: var(--cockpit-wrapper-bg);
  position: relative;
  color: #fff;
  width: 100%;
  height: 100%;
  overflow: hidden;
  -webkit-print-color-adjust: exact;
  font-weight: bold !important;
}
.content {
  position: relative;
  z-index: 1;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
}

.cockpit-panel {
  position: relative;
  top: 50%;
  left: 50%;
  transition: all 0.3s;
  overflow-y: hidden;
  overflow-x: auto;
  transform-origin: 0 0;
  user-select: none;
  // padding: 0 90px 0;
  box-sizing: border-box;
  background: #021e5d;
  // background: url('../images/detail-bg.png') no-repeat center center;
  // background-size: 100% 100%;

  div {
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }

  &-content {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: calc(100% - 180px);
    padding: 0 20px 0 45px;
    // padding-top: 50px;
    box-sizing: border-box;
    .panel-mode {
      margin-bottom: 70px;
      &:nth-last-child(1) {
        margin-bottom: 0;
      }
    }
    .content {
      &-left,
      &-right {
        width: $sliderWidth;
        height: 100%;
      }

      &-middle {
        flex: 1;
        height: 100%;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        box-sizing: border-box;
      }
    }
  }
}
</style>