/** 指数总览 样式2 */

<template>
  <div :class="['index-overview']">
    <div v-for="(item, index) in indexList"
      :key="index"
      :style="{
        width: `${itemWidth || 240}px`,
        height: `${itemHeight || 240}px`
      }"
      @click="onItemClick(index, item)"
      :class="['index-overview__item', currentIndex === index && 'index-overview__item--checked']">
      <div class="item__top">
        <div :style="{ fontSize: `${fontSize * 0.6}px` }">{{ item[realPropName['label']] }}</div>
      </div>

      <div class="item__middle">
        <CountTo :startVal="0"
          :decimals="item.decimals !== undefined ? decimals : 2"
          :endVal="+item[realPropName['value']]"
          :duration="2000" />
        <span class="unit">{{ item[realPropName['unit']] }}</span>
      </div>

      <div v-if="!item.isPercent"
        :class="[
          'item__percent',
          +item[realPropName['percent']] > 0 && 'item__percent--up',
          +item[realPropName['percent']] < 0 && 'item__percent--down'
        ]">
        <img :style="{ width: `${fontSize * 0.5}px` }"
          class="icon"
          v-if="+item[realPropName['percent']] > 0"
          src="../images/thows.png" />
        <img :style="{ width: `${fontSize * 0.5}px` }"
          class="icon"
          v-if="+item[realPropName['percent']] < 0"
          src="../images/thowx.png" />
        <span>{{ item[realPropName['percent']] }}%</span>
      </div>

      <!-- 高亮闪光点 -->
      <div ref="Lighlight"
        :style="{
          animationDelay: `${Math.random()}s`
        }"
        class="item__highlight"></div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import CountTo from 'vue-count-to'

export interface IndexItem {
  label: string
  value: string | number
  percent: string | number
  unit: string
}

@Component({
  components: {
    CountTo
  }
})
export default class extends Vue {
  @Prop({ default: () => [] }) indexList!: IndexItem[]
  @Prop({ default: () => ({}) }) propMap!: IndexItem
  @Prop({ default: 50 }) private fontSize!: number
  @Prop() private itemWidth!: number
  @Prop() private itemHeight!: number
  @Prop() private currentIndex!: number
  @Prop() private decimals!: number

  @Watch('indexList', { deep: true })
  private onIndexListChange() {
    this.realList = [...this.indexList]
    this.realList.forEach((item) => {
      this.bigNumberFormat(item)
    })
  }
  private realPropName: IndexItem = {
    label: 'label',
    value: 'value',
    percent: 'percent',
    unit: 'unit'
  }

  private realList: any[] = []

  created() {
    this.realPropName = Object.assign(this.realPropName, this.propMap)
    this.realList = [...this.indexList]
    this.realList.forEach((item) => {
      !item.noFormat && this.bigNumberFormat(item)
    })
  }

  // 万元转化成 亿元、万亿元
  bigNumberFormat(item: any) {
    let k = 10000
    let value = +item[this.realPropName['value']]
    if (value < k) {
      return
    }

    let newValue = Math.abs(value) * k
    let sizes = ['', '万', '亿元', '万亿元']
    let i

    if (newValue < k) {
      item.value = newValue + ''
      item.unit = ''
    } else {
      i = Math.floor(Math.log(newValue) / Math.log(k))
      item.value = (newValue / Math.pow(k, i)).toFixed(2)
      item.unit = sizes[i]
    }

    let zhValue = String(item.value).split('.')
    if (zhValue && zhValue[1] === '00') item.value = zhValue[0]
    if (+value < 0) item.value = `-${item.value}`

    if (String(newValue).length < 9) item.value = String((+item.value / 10000).toFixed(2))

    if (!item.value) item.value = '0'
    if (!item.unit) item.unit = ''
  }

  // 点击模块
  private onItemClick(index: any, item: IndexItem) {
    this.$emit('itemClick', index, item)
  }
}
</script>

<style scoped lang="scss">
.index-overview {
  $length: 10;

  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  margin: 10px 0;
  &__item {
    position: relative;
    width: 380px;
    height: 250px;
    background: url('../images/index_border1.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    box-sizing: border-box;
    padding: 20px 0;
    color: #fff;
    cursor: pointer;
    &--checked,
    &:hover {
      background: url('../images/index_border2.png') no-repeat;
      background-size: 100% 100%;
      .item__bottom {
        color: #fff;
      }
    }
    .item {
      &__top,
      &__middle {
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
      }

      &__top {
        display: flex;
        align-items: flex-end;
        font-size: 30px;
        font-family: Alibaba PuHuiTi;
        font-weight: normal;
        color: #ffffff;
      }
      &__middle {
        font-size: 40px;
        white-space: nowrap;
        color: #3eeeff;
        display: flex;
        align-items: flex-end;
        font-weight: normal;
        font-family: 'PangMenZhengDao';
        .unit {
          font-size: 30px;
          margin-left: 2px;
        }
      }
      &__percent {
        font-size: 40px;
        display: flex;
        align-items: center;
        img {
          width: 14px;
          margin-right: 6px;
        }
        &--up {
          color: #fb3f3f;
        }
        &--down {
          color: #86ff66;
        }
      }

      &__highlight {
        position: absolute;
        left: -44px;
        top: -7px;

        width: 88px;
        height: 14px;
        background-image: url('../images/index_highlight.png');
        background-size: 100% 100%;

        animation: highlightMove 4s linear infinite;
      }
    }
  }
}

@keyframes highlightMove {
  0% {
    left: -44px;
    top: -7px;
    opacity: 0;
  }

  24% {
    left: calc(100% + -44px);
    top: -7px;
    opacity: 1;
    transform: rotateZ(0deg);
  }

  25% {
    left: calc(100% + -44px);
    top: -7px;
    opacity: 1;
    transform: rotateZ(45deg);
  }
  26% {
    left: calc(100% + -44px);
    top: -7px;
    opacity: 1;
    transform: rotateZ(90deg);
  }

  49% {
    left: calc(100% + -44px);
    top: calc(100% + -7px);
    transform: rotateZ(90deg);
  }
  50% {
    left: calc(100% + -44px);
    top: calc(100% + -7px);
    opacity: 0;
    transform: rotateZ(135deg);
  }
  51% {
    left: calc(100% + -44px);
    top: calc(100% + -7px);
    opacity: 0;
    transform: rotateZ(180deg);
  }

  74% {
    left: -44px;
    top: calc(100% + -7px);
    opacity: 1;
    transform: rotateZ(180deg);
  }
  75% {
    left: -44px;
    top: calc(100% + -7px);
    opacity: 1;
    transform: rotateZ(225deg);
  }
  76% {
    left: -44px;
    top: calc(100% + -7px);
    opacity: 1;
    transform: rotateZ(270deg);
  }

  99% {
    left: -44px;
    top: -7px;
    transform: rotateZ(270deg);
  }
  100% {
    left: -44px;
    top: -7px;
    opacity: 0;
    transform: rotateZ(360deg);
  }
}
</style>
