<template>
  <Dialog
    title="企业发债审批-详情"
    width="980px"
    :visible="visible"
    :close-on-click-modal="true"
    :destroy-on-close="true"
    :append-to-body="true"
    @close="handleClose"
    @open="openDiaHandle"
  >
<div slot="body" class="descriptions-body">
 <el-descriptions title="" border :column="4" >
    <el-descriptions-item  label="单据编号" span="4">{{ DetailForm.documentNo }}</el-descriptions-item>
    <el-descriptions-item  label="填报单位" span="2">{{ DetailForm.reportDeptName }}</el-descriptions-item>
    <el-descriptions-item  label="清产核资基准日" span="2">{{ DetailForm.assetVerificationBaseDate }}</el-descriptions-item>
    <el-descriptions-item  label="账面资产总额" span="2">{{ DetailForm.totalOriginalBookValue }}</el-descriptions-item>
    <el-descriptions-item  label="所有者权益" span="2">{{ DetailForm.ownersEquity }}</el-descriptions-item>
    <el-descriptions-item  label="实收资本" span="2">{{ DetailForm.paidinCapital }}</el-descriptions-item>
    <el-descriptions-item  label="资本公积" span="2">{{ DetailForm.capitalReserve }}</el-descriptions-item>
    <el-descriptions-item  label="盈余公积" span="2">{{ DetailForm.surplusReserve }}</el-descriptions-item>
    <el-descriptions-item  label="未分配利润" span="2">{{ DetailForm.undistributedProfit }}</el-descriptions-item>
    <el-descriptions-item  label="其他利润" span="2">{{ DetailForm.otherProfits }}</el-descriptions-item>
    <el-descriptions-item  label="少数股东权益" span="2">{{ DetailForm.minorityEquity }}</el-descriptions-item>
    <el-descriptions-item  label="其他权益" span="2">{{ DetailForm.otherEquity }}</el-descriptions-item>
  </el-descriptions>
      <AccessoryFileList v-model="DetailForm.attachmentFileDTOList" dict="financial_examinationMean_attach" mode="see" />
    </div>
    <div slot="footer">
        <el-button @click="closeDetail" >关闭</el-button>
        <!-- <el-button class="finance_red_btn">确认审批</el-button> -->
    </div>
  </Dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import Descriptions from './Descriptions/index.vue'
import Dialog from '@/components/Dialog/index.vue'
import Stel from './Stel/index.vue'
import AccessoryFileList from "./AccessoryFileList.vue"
@Component({
  components: {
    Descriptions,
    Dialog,
    Stel,
    AccessoryFileList
  }
})
export default class Container extends Vue {
  @Prop() private visible!: boolean
  @Prop() private DetailForm!: any
  
  private handleClose() {
    // this.visible = false
    this.$emit("changeShowDetail",false)
  }
  private closeDetail(){
    this.handleClose()
    this.$emit("visible:update",false)
  }
  openDiaHandle() {
    // this.visible = true
    
  }
}
</script>

<style lang="scss" scoped>
.descriptions-body {
  ::v-deep.el-descriptions__body .el-descriptions__table .el-descriptions-item__cell {
    font-size: 14px;
    color: #303133;
  }
}
</style>
