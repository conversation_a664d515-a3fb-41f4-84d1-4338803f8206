/** 资产构成情况 */

<template>
  <div class="asset-composition">
    <div id="AssetComposition" />
    <div class="halo"></div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { AssetCompositionData, companyList } from '@/views/main/cockpitSecondary/baseData'
import { capitalProfile } from '@/api/cockpit'
import * as echarts from 'echarts'
import { deepClone } from '@/utils'

type EChartsOption = echarts.EChartsOption

@Component
export default class extends Vue {
  @Prop({ default: '2022' }) private currentYear!: string

  @Watch('currentCompanyId', { immediate: true })
  private onCurrentCompanyId() {
    this.filterData()
  }

  private loading = false
  private maxValue = 100
  private year = ''
  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}
  private tabList = []
  private realData: any = {
    ALL: [
      { value: this.randomValue(), name: '流动资产净额' },
      { value: this.randomValue(), name: '固定资产净额' },
      { value: this.randomValue(), name: '流动资产' }
    ],
    CT: [
      { value: this.randomValue(), name: '流动资产净额' },
      { value: this.randomValue(), name: '固定资产净额' },
      { value: this.randomValue(), name: '流动资产' }
    ],
    KT: [
      { value: this.randomValue(), name: '流动资产净额' },
      { value: this.randomValue(), name: '固定资产净额' },
      { value: this.randomValue(), name: '流动资产' }
    ],
    SW: [
      { value: this.randomValue(), name: '流动资产净额' },
      { value: this.randomValue(), name: '固定资产净额' },
      { value: this.randomValue(), name: '流动资产' }
    ],
    JT: [
      { value: this.randomValue(), name: '流动资产净额' },
      { value: this.randomValue(), name: '固定资产净额' },
      { value: this.randomValue(), name: '流动资产' }
    ],
    GD: [
      { value: this.randomValue(), name: '流动资产净额' },
      { value: this.randomValue(), name: '固定资产净额' },
      { value: this.randomValue(), name: '流动资产' }
    ],
    SF: [
      { value: this.randomValue(), name: '流动资产净额' },
      { value: this.randomValue(), name: '固定资产净额' },
      { value: this.randomValue(), name: '流动资产' }
    ]
  }
  private echartsDatas: any[] = []
  private seriesData: any[] = [
    { value: 0, name: '流动资产净额' },
    { value: 0, name: '固定资产净额' },
    { value: 0, name: '流动资产' }
  ]
  private tabActive: {
    code: string
    name: string
  } = {
    code: '',
    name: ''
  }

  get currentCompanyId(): any {
    return this.$route.query ? this.$route.query.id || '' : ''
  }

  get sum() {
    return this.seriesData
      .reduce((sum: any, currentValue: any) => {
        return (sum += +currentValue.value || 0)
      }, 0)
      .toFixed(2)
  }

  // 获取集团列表
  get getCompanyList() {
    let list: string[] = []
    companyList.forEach((item) => {
      list.push(item.name)
    })
    return list
  }
  created() {
    this.$bus.$on('BusChangeCascader', (data: string[]) => {
      this.currentYear = data[0]
      this.filterData()
    })
    // this.filterData()
  }

  private filterData() {
    this.seriesData = !this.realData[this.currentCompanyId]
      ? [
          { value: 0, name: '流动资产净额' },
          { value: 0, name: '固定资产净额' },
          { value: 0, name: '流动资产' }
        ]
      : this.realData[this.currentCompanyId] || [
          { value: 0, name: '流动资产净额' },
          { value: 0, name: '固定资产净额' },
          { value: 0, name: '流动资产' }
        ]
  }

  private randomValue(max = 1000) {
    return (Math.random() * max).toFixed(2)
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('AssetComposition') as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    // this.initData()
    this.initEcharts()
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await capitalProfile()
    if (!Array.isArray(data) || !data.length) return
    this.echartsDatas = data
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 初始化数据
  private initEcharts() {
    let textSize = 22
    let textColor = '#999'
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    this.option = {
      color: ['#E6B607', '#EF702E', '#35AAF6', '#00AEA5', '#EC342F', '#2D5AE5'],
      title: {
        top: '42%',
        left: 'center',
        text: `${this.sum}`,
        subtext: '总资产',
        textStyle: {
          fontWeight: 'bold',
          fontSize: textSize * 1.6,
          color: 'rgba(255, 170, 69, 1)',
          fontFamily: 'PangMenZhengDao'
        },
        subtextStyle: {
          color: '#fff',
          fontSize: textSize * 1.4
        }
      },
      legend: {
        show: false
      },
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '60%'],
          avoidLabelOverlap: false,
          // stillShowZeroSum: false,
          name: '项目预选',
          selectedMode: 'single',
          label: {
            show: true,
            position: 'outside',
            fontSize: 24,
            color: '#5db0ea',
            formatter: ({ data }: { data: any }) => {
              // ${
              //   +(data.value / this.sum).toFixed(1) * 100
              // }%
              let rate = +((data.value / this.sum) * 100).toFixed(1)
              return `{a|${data.value}}{c|亿元} \n {hr|}\n {b|${data.name}${rate ? rate + '%' : ''}}`
            },
            rich: {
              a: {
                fontSize: 40,
                color: '#fff',
                fontWeight: 'bold',
                opacity: 0.8,
                fontFamily: 'PangMenZhengDao'
              },
              b: {
                fontSize: '22px',
                color: '#20DCF9',
                fontWeight: 'bold'
              },
              c: {
                fontSize: 36,
                color: '#fff',
                fontWeight: 'bold',
                opacity: 0.8
              },
              hr: {
                borderColor: '#8C8D8E',
                width: '100%',
                borderWidth: 1,
                height: 0
              }
            }
          },
          itemStyle: {
            borderRadius: 0,
            borderWidth: 10
          },
          labelLine: {
            length: 15,
            length2: 20,
            maxSurfaceAngle: 80,
            smooth: 0.2,
            lineStyle: {
              width: 3
            }
          },
          data: this.seriesData
        }
      ]
    }

    this.myChart && this.myChart.setOption(this.option)
  }
  // 年份切换
  private yearHandler(year: string) {
    this.year = year
    // this.initData()
  }
}
</script>

<style scoped lang="scss">
#AssetComposition {
  position: relative;
  z-index: 1000;
  width: 100%;
  height: 100%;
}
.asset-composition {
  width: 100%;
  height: 100%;
}
.halo {
  width: 320px;
  height: 320px;
  background: #00000078;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: url('../../images/halo.png');
  background-size: 100% 100%;
  z-index: 1;
  will-change: width, height, transform;
  animation: move 1000s linear forwards infinite;
}

@keyframes move {
  0% {
    transform: translate(-50%, -50%) rotateZ(0deg);
    width: 300px;
    height: 300px;
  }
  50% {
    transform: translate(-50%, -50%) rotateZ(360deg);
    width: 300px;
    height: 300px;
  }
  100% {
    transform: translate(-50%, -50%) rotateZ(0deg);
    width: 300px;
    height: 300px;
  }
}
</style>


