/*
 * prop[type:string]: tabs 切换数据类型
 * @inWindowFullScreen: 点击全屏触发
 * @changeCompanyTabs: 点击tabs触发
*/

<template>
  <section class="cockpit-title-wrap">
    <!-- 标题文案 -->
    <div class="text-box"
      @click="companyTabs(0)">
      <CockpitTitle />
    </div>

    <!-- 头部时间+天气等内容 -->
    <div class="head-box">
      <div class="mode mode-left">
        <div class="cascader">
          <el-cascader v-model="cascaderMode"
            :options="optionTimes"
            :clearable="false"
            :props="{ checkStrictly: true }"
            placeholder=""
            popper-class="cockpit-cascader-wrap"
            @change="changeCascaderMode" />
        </div>
        <span class="fs-40 m-r-20">{{getMomentTime()}}</span>
        <div class="fs-34">
          <span class="m-r-20">{{getMomentWeek()}} {{momentHms}}</span>
          <el-tooltip effect="dark"
            content="架构图"
            placement="top-start">
            <i class="pointer el-icon-s-operation m-r-20"
              @click="visibleArchitecture = true" />
          </el-tooltip>
          <el-tooltip effect="dark"
            content="PPT"
            placement="top-start">
            <i class="pointer el-icon-document-copy m-r-20"
              @click="viewPPT('https://fh-ka.oss-cn-hangzhou.aliyuncs.com/金华市国资委监管系统20220802.pptx')" />
          </el-tooltip>
        </div>
      </div>

      <div class="mode mode-right">
        <Weather />
        <el-button v-if="!getIsWindowFull()"
          type="primary"
          icon="el-icon-rank"
          class="full"
          title="全屏"
          @click="inWindowFullScreen" />
        <el-button v-else
          type="primary"
          icon="el-icon-s-unfold"
          class="full"
          title="退出全屏"
          @click="outWindowExitFullScreen()" />
      </div>
    </div>

    <!-- type=company：集团tabs -->
    <div v-if="type==='company'"
      class="tabs-box">
      <div class="mode mode-left">
        <div v-for="item of getCompanyList(0, 3)"
          class="skey"
          :key="item.code"
          :class="{'acitve-skey': +item.code === +tabIndex}"
          @click="companyTabs(+item.code, $event)">
          <span>{{item.name}}</span>
        </div>
      </div>
      <div class="mode mode-right">
        <div v-for="item of getCompanyList(3, 6)"
          class="skey"
          :key="item.code"
          :class="{'acitve-skey': +item.code === +tabIndex}"
          @click="companyTabs(+item.code, $event)">
          <span>{{item.name}}</span>
        </div>
      </div>
    </div>

    <!-- 查看架构图 -->
    <Architecture v-if="visibleArchitecture"
      :visible.sync="visibleArchitecture" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { companyList } from '@/views/main/cockpitSecondary/baseData'
import { viewOffice, windowExitFullScreen, isWindowFull } from '@/utils'
import Weather from '@/components/Weather/index.vue'
import Architecture from '@/views/main/cockpitSecondary/components/Architecture.vue'
import CockpitTitle from '@/views/main/cockpitSecondary/FrontPageCom/cockpitTitle.vue'

@Component({
  components: {
    Weather,
    CockpitTitle,
    Architecture
  }
})
export default class extends Vue {
  @Prop({
    validator: (value: string) => {
      return ['company'].indexOf(value) !== -1
    }
  })
  readonly type!: string // tabs 数据类型（company：集团数据）

  private momentHms = ''
  private tabIndex = 0
  private visibleArchitecture = false
  private cascaderMode: string[] = []
  private children = Object.freeze([
    {
      value: '1',
      label: '1季度',
      children: [
        {
          value: '1',
          label: '1月'
        },
        {
          value: '2',
          label: '2月'
        },
        {
          value: '3',
          label: '3月'
        }
      ]
    },
    {
      value: '2',
      label: '2季度',
      children: [
        {
          value: '4',
          label: '4月'
        },
        {
          value: '5',
          label: '5月'
        },
        {
          value: '6',
          label: '6月'
        }
      ]
    },
    {
      value: '3',
      label: '3季度',
      children: [
        {
          value: '7',
          label: '7月'
        },
        {
          value: '8',
          label: '8月'
        },
        {
          value: '9',
          label: '9月'
        }
      ]
    },
    {
      value: '4',
      label: '4季度',
      children: [
        {
          value: '10',
          label: '10月'
        },
        {
          value: '11',
          label: '11月'
        },
        {
          value: '12',
          label: '12月'
        }
      ]
    }
  ])
  private optionTimes: any[] = []

  // 判断窗口是否处于全屏模式
  get getIsWindowFull() {
    return isWindowFull
  }

  // 获取集团tabs内容
  get getCompanyList() {
    return (start: number, end: number) => {
      let list = []
      for (let i = start; i < end; i++) {
        list.push(companyList[i])
      }

      return list
    }
  }

  // 组件初始化
  private mounted() {
    this.initTimes()
    this.setInterHms()
  }

  // 设置年份、季度、月份数据
  private initTimes() {
    let list = []
    let that = this as any
    let year = that.$moment(new Date()).add('year', 0).format('YYYY')

    for (let i = 0; i < 5; i++) {
      list.push({
        value: String(+year - i),
        label: String(+year - i),
        children: this.children
      })
    }

    this.optionTimes = list
  }

  // 获取当前星期
  private getMomentWeek() {
    let that = this as any
    return that.$moment().format('dddd')
  }

  // 设置倒计时
  private setInterHms() {
    setInterval(() => {
      let that = this as any
      this.momentHms = that.$moment().format('HH:mm:ss')
    }, 1000)
  }

  // tabs 切换选中
  private companyTabs(index: number) {
    this.tabIndex = +index
    this.$emit('changeCompanyTabs', index)
  }

  // 获取当前年月日，或者选中的年、季、月数据
  private getMomentTime() {
    let that = this as any
    let cascaderMode = this.cascaderMode
    let time = that.$moment(new Date()).add('year', 0).format('YYYY/MM/DD')

    if (!cascaderMode.length) {
      return time
    } else {
      if (Array.isArray(cascaderMode) && cascaderMode.length) {
        let str = ''
        switch (+cascaderMode.length) {
          case 1:
            str = `${cascaderMode[0]}年`
            break
          case 2:
            str = `${cascaderMode[0]}年${cascaderMode[1]}季度`
            break
          case 3:
            str = `${cascaderMode[0]}年${cascaderMode[1]}季度${cascaderMode[2]}月`
            break
          default:
            break
        }
        return str
      } else {
        return time
      }
    }
  }

  // 选择年份、季度、月份时，全局触发 $bus
  private changeCascaderMode(val: string[]) {
    this.$bus.$emit('BusChangeCascader', val)
  }

  // 查看ppt文件
  private viewPPT(src: string) {
    viewOffice(src)
  }

  // 进入全屏
  private inWindowFullScreen() {
    this.$emit('inWindowFullScreen')
  }

  // 退出全屏
  private outWindowExitFullScreen() {
    return windowExitFullScreen()
  }
}
</script>

<style scoped lang="scss">
.cockpit-title-wrap {
  position: relative;
  width: 100%;
  height: 200px;
  background: url('../images/title.png') no-repeat center center;
  background-size: 100% 100%;

  .pointer {
    cursor: pointer;
  }

  .text-box {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
    width: 100%;
    display: flex;
    justify-content: center;
    cursor: pointer;
  }

  .head-box {
    display: flex;
    align-items: center;
    height: 62px;
    padding: 0 30px;
    justify-content: space-between;
    .mode {
      position: relative;
      z-index: 10;
      display: flex;
      align-items: center;
      .full {
        font-size: 30px;
        padding: 14px 35px;
        background: none;
        color: #fff;
        cursor: pointer;
      }
    }
  }

  .cascader {
    position: absolute;
    left: 0;
    z-index: 10;
    opacity: 0;
    ::v-deep .el-cascader {
      .el-input {
        font-size: 46px;
        input {
          padding: 22px 18px;
          background: none;
          color: #fff;
          border-width: 2px;
          border-color: #004964;
          border: none;
          text-align: left;
        }
        .el-input__icon {
          display: none;
          font-size: 30px;
          color: #fff;
        }
      }
    }
  }

  .tabs-box {
    position: absolute;
    left: 0;
    top: 74px;
    width: 100%;
    height: 58px;
    font-size: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .mode {
      position: relative;
      z-index: 10;
      width: 930px;
      height: 100%;
      .skey {
        display: inline-block;
        width: 200px;
        height: 100%;
        text-align: center;
        cursor: pointer;
        box-sizing: border-box;
        font-size: 33px;
        letter-spacing: 4px;
        font-weight: normal;
        font-family: 'Alibaba-PuHuiTi-Bold';
        color: #fff;
        background: #0e1989;
        &:hover {
          box-shadow: 0 0 16px #0090ff;
        }
        span {
          line-height: 58px;
          display: inline-block;
        }
      }
      .acitve-skey {
        box-shadow: 0 0 16px #0090ff;
        background: #0090ff;
      }
    }
    .mode-left {
      text-align: right;
      .skey {
        margin-right: 20px;
        transform: skewX(45deg);
        span {
          transform: skewX(-45deg);
        }
      }
    }
    .mode-right {
      .skey {
        margin-left: 20px;
        transform: skewX(-45deg);
        span {
          transform: skewX(45deg);
        }
      }
    }
  }
}
</style>
