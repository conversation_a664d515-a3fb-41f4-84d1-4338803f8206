/** 成员职称情况 */
<template>
  <div id="TitlesOfMembers" />
</template>

<script lang='ts'>
import { Component, Prop, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { TitlesOfMembersData, echartConfigure } from '@/views/main/cockpitSecondary/baseData'
import { capitalProfile } from '@/api/cockpit'
import * as echarts from 'echarts'
import { deepClone } from '@/utils'

type EChartsOption = echarts.EChartsOption

@Component
export default class extends Vue {
  private loading = false
  private year = ''
  private timer: any
  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}
  private seriesData1: any[] = []
  private seriesData2: any[] = []
  private echartsDatas: any[] = []
  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('TitlesOfMembers') as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    // this.initData()
    this.initEcharts()
  }

  created() {
    this.seriesData1 = this.getData()
    this.seriesData2 = this.seriesData1.map((item) => +(100 - item).toFixed(2))
  }

  private getData() {
    return [
      +(Math.random() * 100).toFixed(2),
      +(Math.random() * 100).toFixed(2),
      +(Math.random() * 100).toFixed(2),
      +(Math.random() * 100).toFixed(2)
    ]
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await capitalProfile()
    if (!Array.isArray(data) || !data.length) return
    this.echartsDatas = data
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 初始化数据
  private initEcharts() {
    let textSize = 22
    // let series = this.seriesData
    this.option = {
      color: [
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'RGBA(40, 177, 255, 1)'
            },
            {
              offset: 1,
              color: 'RGBA(40, 177, 255, 1)'
            }
          ]
        },
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: '#835002' // 0% 处的颜色
            },
            {
              offset: 1,
              color: '#E6B607' // 100% 处的颜色
            }
          ]
        }
      ],
      title: {
        text: '近年概况',
        top: 20,
        left: 10,
        textStyle: {
          color: '#fff',
          fontWeight: 'bold',
          fontSize: 40
        }
      },
      legend: {
        show: true,
        top: 30,
        right: '5%',
        textStyle: {
          fontSize: 30,
          fontWeight: 'bold',
          color: '#fff'
        }
      },
      grid: {
        left: '0%',
        right: '0%',
        top: '35%',
        bottom: '0%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 103, 177, 1)',
        borderColor: 'rgba(0, 103, 177, 1)',
        textStyle: {
          color: '#fff',
          fontSize: textSize
        }
      },
      xAxis: {
        type: 'category',

        axisLabel: {
          fontSize: textSize * 1.4,
          fontFamily: echartConfigure.fontFamilyNumber,
          fontWeight: 'bold',
          color: '#5db0ea',
          interval: 0,
          margin: 25,
          hideOverlap: false
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(67, 154, 255, 1)'
          }
        },
        data: [2019, 2020, 2021, 2022]
      },
      axisPointer: {
        show: false
      },
      yAxis: {
        show: true,
        type: 'value',
        name: '亿元',
        nameTextStyle: {
          fontWeight: 'bold',
          color: '#5db0ea',
          lineHeight: 50,
          fontSize: textSize * 1.2
        },
        axisLabel: {
          fontWeight: 'bold',
          fontFamily: echartConfigure.fontFamilyNumber,
          color: '#5db0ea',
          fontSize: textSize * 1.2
        },
        axisLine: {
          lineStyle: {
            color: 'RGBA(147, 148, 149, 1)'
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0, 103, 177, 0.4)'
          }
        }
      },
      series: [
        {
          selectedMode: 'single',
          label: {
            show: true,
            position: 'top',
            color: 'RGBA(40, 177, 255, 1)',
            fontSize: textSize * 2.6,
            fontWeight: 'bold',
            fontFamily: 'digital-7',
            formatter: ({ data }: any) => {
              return `{a|${data}}{b|亿元}`
            },
            rich: {
              a: {
                fontSize: 38,
                color: '#fff',
                fontWeight: 'bold',
                opacity: 0.8,
                fontFamily: 'digital-7'
              },
              b: {
                fontSize: '22px',
                color: '#fff',
                fontWeight: 'bold'
              }
            }
          },
          itemStyle: {
            borderRadius: [textSize * 2, textSize * 2, 0, 0],
            shadowColor: 'RGBA(40, 177, 255, 0.6)',
            shadowBlur: 30
          },
          emphasis: {
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'RGBA(233, 60, 167, 1)'
                  },
                  {
                    offset: 1,
                    color: 'RGBA(233, 60, 167, 1)'
                  }
                ]
              },
              shadowColor: 'RGBA(233, 60, 167, 0.6)',
              shadowBlur: 30
            },
            label: {
              fontSize: textSize * 2.2,
              color: 'RGBA(233, 60, 167, 1)'
            }
          },
          select: {
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'RGBA(233, 60, 167, 1)'
                  },
                  {
                    offset: 1,
                    color: 'RGBA(233, 60, 167, 1)'
                  }
                ]
              },
              shadowColor: 'RGBA(233, 60, 167, 0.6)',
              shadowBlur: 30
            },
            label: {
              fontSize: textSize * 2.2,
              color: 'RGBA(233, 60, 167, 1)'
            }
          },
          showBackground: false,
          backgroundStyle: {
            color: '#081C5866',
            borderRadius: [textSize * 2, textSize * 2, 0, 0]
          },
          barWidth: textSize * 2,
          data: this.seriesData1,
          name: '流动资产',
          type: 'bar'
        },
        {
          selectedMode: 'single',
          label: {
            show: true,
            position: 'top',
            color: 'RGBA(40, 177, 255, 1)',
            fontSize: textSize * 2.6,
            fontWeight: 'bold',
            fontFamily: 'digital-7',
            formatter: ({ data }: any) => {
              return `{a|${data}}{b|亿元}`
            },
            rich: {
              a: {
                fontSize: 38,
                color: 'fff',
                fontWeight: 'bold',
                opacity: 0.8,
                fontFamily: 'digital-7'
              },
              b: {
                fontSize: '22px',
                color: 'fff',
                fontWeight: 'bold'
              }
            }
          },
          itemStyle: {
            borderRadius: [textSize * 2, textSize * 2, 0, 0],
            shadowColor: 'fff',
            shadowBlur: 30
          },
          emphasis: {
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'RGBA(233, 60, 167, 1)'
                  },
                  {
                    offset: 1,
                    color: 'RGBA(233, 60, 167, 1)'
                  }
                ]
              },
              shadowColor: 'RGBA(233, 60, 167, 0.6)',
              shadowBlur: 30
            },
            label: {
              fontSize: textSize * 2.2,
              color: 'RGBA(233, 60, 167, 1)'
            }
          },
          select: {
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'RGBA(233, 60, 167, 1)'
                  },
                  {
                    offset: 1,
                    color: 'RGBA(233, 60, 167, 1)'
                  }
                ]
              },
              shadowColor: 'RGBA(233, 60, 167, 0.6)',
              shadowBlur: 30
            },
            label: {
              fontSize: textSize * 2.2,
              color: 'RGBA(233, 60, 167, 1)'
            }
          },
          showBackground: false,
          backgroundStyle: {
            color: '#081C5866',
            borderRadius: [textSize * 2, textSize * 2, 0, 0]
          },
          barWidth: textSize * 2,
          data: this.seriesData2,
          name: '非流动资产',
          type: 'bar'
        }
      ]
    }

    this.myChart && this.myChart.setOption && this.myChart && this.myChart.setOption(this.option)
    this.change()
  }

  // 年份切换
  private yearHandler(year: string) {
    this.year = year
    // this.initData()
  }

  private change() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    this.timer = setInterval(() => {
      let index = Math.max(Math.floor(Math.random() * 4), 0)

      this.myChart &&
        this.myChart.dispatchAction({
          type: 'select',
          seriesIndex: 0,
          dataIndex: index
        })
    }, 5000)
  }

  destroyed() {
    clearInterval(this.timer)
  }
}
</script>

<style scoped lang="scss">
#TitlesOfMembers {
  width: 100%;
  height: 420px;
}
</style>