<template>
  <el-button class="operation-button"
    v-if="items.length === 1 && (!items[0].visible || items[0].visible(data, items[0].extra))"
    type="text"
    size="small"
    @click="items[0].click && items[0].click(data, items[0].extra)">
    {{ typeof items[0].label === 'function' ? items[0].label(data) : items[0].label }}</el-button>
  <el-button v-else-if="findVisibleItems(items).length === 1"
    class="operation-button"
    type="text"
    size="small"
    @click="findVisibleItems(items)[0].click && findVisibleItems(items)[0].click(data, findVisibleItems(items)[0].extra)">
    {{ findVisibleItems(items)[0].label }}</el-button>
  <el-dropdown v-else-if="items.length > 1 && isDropdownVisible(items)"
    class="operation-dropdown">
    <span class="el-dropdown-link">
      {{label}}<i class="el-icon-arrow-down el-icon--right"></i>
    </span>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item v-for="(item, index) of items"
        :key="index"
        :class="{'hide':  item.visible && !item.visible(data, item.extra)}"
        :disabled="item.disabled"
        @click.native="item.click && item.click(data, item.extra)">{{ item.label }}
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
  <span v-else></span>
</template>

<script lang="ts">
interface ButtonItem {
  label: string
  permission?: string
  click?: Function
  visible?: Function
  extra?: object
}

import { Component, Vue, Prop } from 'vue-property-decorator'

@Component
export default class OperationButton extends Vue {
  @Prop({ default: [] }) private items!: Array<ButtonItem>
  @Prop({ default: '操作' }) private label?: string
  @Prop() private data?: any

  private isDropdownVisible(
    items: Array<{
      visible: Function
      extra: object
    }>
  ) {
    let filterItems = items.filter((item) => {
      return !item.visible || item.visible(this.data, item.extra)
    })
    return filterItems.length > 0
  }

  private findVisibleItems(items: Array<ButtonItem>) {
    return items.filter((item) => {
      if (!item.visible) {
        return true
      }
      return item.visible(this.data, item.extra)
    })
  }
}
</script>

<style lang="scss" scoped>
.operation-dropdown {
  $color: #ce4c4c;

  color: $color;
  font-size: 12px;
  font-weight: normal;
  cursor: pointer;

  .el-icon-arrow-down:before {
    content: '';
    position: absolute;
    border-top: 3px solid $color;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    height: 0;
    top: 0;
    bottom: 0;
    right: -3px;
    width: 0;
    margin: auto;
  }
}
</style>

<style lang="scss" scoped>
.operation-button {
  $color: #ce4c4c;
  padding: 0;
  color: $color;
  font-weight: normal !important;
}
</style>

<style lang="scss">
.el-dropdown-menu__item.hide {
  display: none;
}
</style>