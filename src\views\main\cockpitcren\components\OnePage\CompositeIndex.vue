/* 绩效评价 */

<template>
  <section class="assets-comment-wrap">
    <TitleCom title="绩效评价"
      module="CompositeIndex" />

    <div class="composite-content module-pulic-box">
      <CountTo class="cunot"
        :startVal='0'
        :endVal='cunot'
        :duration='8000' />

      <div v-loading="loading"
        id="compositeIndex" />
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { comprehensiveIndex } from '@/api/cockpit'
import { echartConfigure, companyList, colorSixList, indicatorList } from '@/views/main/cockpitcren/baseData'
import { Loading } from '@/decorators'
import { isWindowFull } from '@/utils'
import * as echarts from 'echarts'
import CountTo from 'vue-count-to'
import TitleCom from '@/views/main/cockpitcren/components/Public/TitleCom.vue'

type EChartsOption = echarts.EChartsOption
type typeTabItem = {
  id: string
  code: string
  name: string
}

@Component({
  components: {
    CountTo,
    TitleCom
  }
})
export default class extends Vue {
  private year = ''
  private loading = false
  private cunot = 0
  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private seriesData: any[] = []
  private option: EChartsOption = {}
  private echartsDatas: any[] = []
  private tabActive: typeTabItem = {
    id: '',
    code: '',
    name: ''
  }

  // 获取集团列表（文案、对象）数据
  get getCompanyList() {
    return (type = false) => {
      let list: any[] = []

      companyList.forEach((item) => {
        type ? list.push(item.name) : list.push(item)
      })
      return list
    }
  }

  // 判断窗口是否处于全屏模式
  get getIsWindowFull() {
    return isWindowFull
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('compositeIndex') as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)
    this.year = String((this as any).$moment(new Date()).add('year', 0).format('YYYY') - 1)

    this.initData()

    // 中间各tabs切换触发数据更新
    this.$bus.$on('BusTabsChange', (data: typeTabItem) => {
      this.tabActive = data
      this.filterEchartsData()
    })

    // 时间改变时触发数据更新
    this.$bus.$on('BusYearTimeChange', (data: string) => {
      this.yearHandel(data)
    })
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await comprehensiveIndex({
      year: String(+this.year - 1),
      companyCode: +this.tabActive.code
    })

    if (!Array.isArray(data) || !data.length) {
      this.echartsDatas = []
    }
    this.echartsDatas = data as any[]
    this.filterEchartsData()
  }

  // 筛选相关数据
  private filterEchartsData() {
    let datas: any[] = []
    let filterList = this.getCompanyList(false).filter((item) => {
      return +item.code === +this.tabActive.code
    })

    filterList.forEach((itemCompany) => {
      let list = this.echartsDatas.filter((item) => {
        return +itemCompany.code === +item.companyCode
      })

      let values: number[] = []
      list.forEach((item) => {
        values.push(+item.itemValue)
      })

      // 编写不同的样式
      let areaStyle = {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: 'rgba(156, 63, 234, 0.8)'
          },
          {
            offset: 1,
            color: 'rgba(156, 63, 234, 0)'
          }
        ])
      }
      let obj = {
        value: values,
        name: itemCompany.name,
        symbolSize: 0,
        areaStyle: areaStyle,
        lineStyle: {
          color: '#03F1FF',
          width: 3
        }
      }

      datas.push(obj)
    })

    this.seriesData = datas
    this.initCunot()
    this.initEcharts()
  }

  // 计算绩效评价
  private initCunot() {
    if (!Array.isArray(this.seriesData) || !this.seriesData.length) return

    let values = this.seriesData[0].value
    let num = 0
    values.forEach((item: number) => {
      num += +item
    })

    let int = num / 5
    this.cunot = Number(int.toFixed(1))
  }

  // 初始化数据
  private initEcharts() {
    let legendSize = echartConfigure.legendSize
    let textSize = echartConfigure.textSize
    let data = this.seriesData
    let indicator = indicatorList
    let companyList = this.getCompanyList(true)
    let tooltipData: any = Object.assign(
      {
        trigger: 'item'
      },
      echartConfigure.tooltip
    )

    this.option = {
      color: colorSixList,
      grid: {
        left: '0%',
        right: '0%',
        bottom: '0%',
        top: '0%',
        containLabel: true
      },
      legend: {
        show: false,
        top: 0,
        padding: 0,
        itemHeight: textSize / 1.4,
        itemWidth: textSize / 1.4,
        itemStyle: {
          borderCap: 'butt'
        },
        lineStyle: {
          type: [0, 10],
          dashOffset: 5
        },
        textStyle: {
          color: '#fff',
          fontSize: legendSize,
          fontWeight: 'bold'
        },
        data: companyList
      },
      tooltip: tooltipData,
      radar: {
        radius: '80%',
        center: ['50%', '59%'],
        axisLabel: {
          show: false,
          fontSize: 14,
          color: '#fff',
          fontWeight: 'bold'
        },
        axisName: {
          color: '#63F1FF',
          fontWeight: 'bold',
          fontSize: textSize
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(0, 103, 177, 1)'
          }
        },
        splitLine: {
          lineStyle: {
            color: ['rgba(0, 103, 177, 1)']
          }
        },
        splitArea: {
          areaStyle: {
            color: ['rgba(0, 103, 177, 0.6)']
          }
        },
        indicator: indicator
      },
      animationDelay: function (idx) {
        return idx * 1000
      },
      series: [
        {
          type: 'radar',
          data: data,
          lineStyle: {
            width: 2
          }
        }
      ]
    }

    this.myChart && this.myChart.setOption(this.option)
  }

  // 年份切换
  private yearHandel(year: string) {
    this.year = year
    this.initData()
  }

  // tabs切换
  private tabsHandel(item: typeTabItem) {
    this.tabActive = item

    if (Array.isArray(this.echartsDatas) && this.echartsDatas.length) {
      this.filterEchartsData()
    }
  }

  // 销毁相关数据
  private destroyed() {
    this.chartDom = null
    this.myChart = null
  }
}
</script>

<style scoped lang="scss">
.composite-content {
  position: relative;
  .cunot {
    position: absolute;
    left: 50%;
    top: 50%;
    z-index: 10;
    color: #63f1ff;
    transform: translate(-50%, -50%);
    font-size: 60px;
    font-weight: normal;
    font-family: 'PangMenZhengDao';
    margin-top: 38px;
  }
}

#compositeIndex {
  width: 100%;
  height: 400px;
  margin-top: -52px;
}
</style>


