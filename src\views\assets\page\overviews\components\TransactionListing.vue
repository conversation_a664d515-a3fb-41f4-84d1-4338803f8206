<template>
  <section class="echarts-dom-wrap">
    <div v-loading="loading"
      ref="chartDom"
      class="chartDom"
      :class="{'hide': !seriesData.length}" />

    <el-empty description="暂无数据"
      class="empty-none-data"
      :class="{'none': seriesData.length}" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { annualRevenueGrowth } from '@/api/cockpit'
import { Loading } from '@/decorators'
import * as echarts from 'echarts'

let chartDom = null
let myChart: any = null

@Component({})
export default class extends Vue {
  @Prop() private orgCode!: string // 集团code
  @Prop() private year!: string // 年份

  private loading = false
  private legendData: string[] = []
  private seriesData: any[] = []
  private xAxisData: string[] = []

  // 监听搜索条件改变
  @Watch('orgCode', { deep: true })
  @Watch('year', { deep: true })
  private watchFormData() {
    this.initData()
  }

  // 组件初始化
  private mounted() {
    this.$nextTick(() => {
      chartDom = this.$refs.chartDom
      myChart = echarts.init(chartDom as HTMLElement)

      this.initData()
    })
  }

  // 获取数据
  @Loading('loading')
  private async initData() {
    let { data } = await annualRevenueGrowth({
      orgCode: this.orgCode,
      year: this.year
    })

    // 组装数据
    if (!Array.isArray(data) || !data.length) return

    let legendData: string[] = []
    let xAxisData: string[] = []
    let seriesData: any[] = []
    let xAxisDataList: string[] = []

    data.forEach((item, index) => {
      legendData.push(item.name)
      xAxisDataList = []
      if (Array.isArray(item.list) && item.list.length) {
        item.list.forEach((itemList: { year: string }) => {
          xAxisDataList.push(itemList.year)
        })
      }

      if (!index) {
        let arr: number[] = []
        Array.isArray(item.list) &&
          item.list.forEach((itemList: { revenueAmount: number }) => {
            let num = itemList.revenueAmount ? +itemList.revenueAmount.toFixed(2) : 0
            arr.push(num)
          })

        seriesData.push({
          name: item.name,
          type: 'bar',
          barWidth: 40,
          label: {
            show: true,
            position: 'inside'
          },
          tooltip: {
            valueFormatter: function (value: number) {
              return value + ' 万元'
            }
          },
          data: arr
        })
      } else {
        let arr: number[] = []
        Array.isArray(item.list) &&
          item.list.forEach((itemList: { averagePrice: number }) => {
            let num = itemList.averagePrice ? +itemList.averagePrice.toFixed(2) : 0
            arr.push(num)
          })

        seriesData.push({
          name: item.name,
          type: 'line',
          symbol: 'circle',
          yAxisIndex: 1,
          lineStyle: {
            width: 2
          },
          label: {
            show: true,
            color: '#91cc75',
            position: 'top'
          },
          tooltip: {
            valueFormatter: function (value: number) {
              return value + ' 元'
            }
          },
          data: arr
        })
      }
    })

    xAxisData = xAxisDataList

    this.legendData = legendData
    this.xAxisData = xAxisData
    this.seriesData = seriesData
    this.initEcharts()
  }

  // 初始化echarts组件
  private initEcharts() {
    let legendData = this.legendData
    let xAxisData = this.xAxisData
    let seriesData = this.seriesData
    let tooltipData: any = Object.assign(
      {
        trigger: 'axis'
      },
    )

    let option = {
      tooltip: tooltipData,
      legend: {
        top: 0,
        right: 'center',
        data: legendData
      },
      grid: {
        top: '20%',
        left: '1%',
        right: '1%',
        bottom: '0%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          data: xAxisData,
          axisLabel: {
            fontSize: 14,
            fontWeight: 'normal'
          },
        }
      ],
      yAxis: [
        {
          type: 'value',
          min: 0,
          name: `(万元)`,
          nameTextStyle: {
            align: 'left',
            fontSize: 12
          },
          axisLine: {
            show: false,
          }
        },
        {
          type: 'value',
          name: `(元)`,
          nameTextStyle: {
            align: 'right',
            fontSize: 12
          },
          axisLine: {
            show: false,
          },
          splitLine: {
            show: false,
            lineStyle: {
              color: 'rgba(0, 103, 177, 0)'
            }
          }
        }
      ],
      series: seriesData,
      animationDelay: function (idx:number) {
        return idx * 300
      }
    }

    myChart && myChart.setOption && myChart.setOption(option)
  }
}
</script>

<style scoped lang="scss">
.chartDom {
  width: 100%;
  height: 100%;
}
</style>