<template>
  <Dialog width="1000px"
    :visible="visible"
    :modal-append-to-body="true"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :append-to-body="true"
    class="early-warning-detail-wrap"
    @close="handleClose">
    <!-- 标题 -->
    <div slot="header">
      <div>{{ title }}</div>
    </div>

    <!-- 内容区域 -->
    <div v-loading="loading"
      slot="body">
      <el-tabs v-model="activeName">
        <el-tab-pane label="事件详情"
          name="details">
          <AlertDetail :params="params" />
        </el-tab-pane>
        <!-- <el-tab-pane label="短信发送记录" name="sendingRecord">
          <SendingRecord :params="params" />
        </el-tab-pane>
        <el-tab-pane label="处置情况" name="disposalSituation">
          <AlertDispose :type="type" :params="params" />
        </el-tab-pane> -->
      </el-tabs>
    </div>

    <!-- 底部按钮 -->
    <!-- <div slot="footer"
      class="footer-box">
      <el-button :loading="loading"
        :type="~~params.isClick !== 1?'primary':''"
        :disabled="~~params.isClick === 1"
        @click="handleRead">已 阅</el-button>
      <el-button :loading="loading"
        :disabled="+params.eventStatus !== 0"
        @click="handleRectification">下发整改通知</el-button>
    </div> -->
  </Dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { updateClickStatus, issuedRectification } from '@/api/prewarning'
import { Loading } from '@/decorators'
import Dialog from '@/components/Dialog/index.vue'
import AlertDetail from './AlertDetail.vue'
import AlertDispose from './AlertDispose.vue'
import SendingRecord from './SendingRecord.vue'

@Component({
  components: {
    Dialog,
    AlertDetail,
    AlertDispose,
    SendingRecord
  }
})
export default class extends Vue {
  @Prop({ default: false }) private visible?: boolean // 弹窗显隐
  @Prop() private params!: any // 详情数据
  @Prop({
    validator: (value: string) => {
      return ['dispose', 'see'].indexOf(value) !== -1
    }
  })
  readonly type!: string // 组件类型(处置、查看)

  private loading = false
  private title = ''
  private activeName: 'details' | 'sendingRecord' | 'disposalSituation' = 'details'

  // 点击已阅
  @Loading('loading')
  private async handleRead() {
    let res = await updateClickStatus({
      id: this.params.id
    })

    this.$message.success(res.msg || '操作成功')
    this.updataHandle()
    this.handleClose()
  }

  // 下发整改通知
  @Loading('loading')
  private async handleRectification() {
    let res = await issuedRectification({
      id: this.params.id
    })

    this.$message.success(res.msg || '操作成功')
    this.updataHandle()
    this.handleClose()
  }

  // 触发父组件更新
  private updataHandle() {
    this.$emit('updataHandle')
  }

  // 关闭弹窗
  private handleClose() {
    this.$emit('update:visible', false)
  }
}
</script>

<style scoped lang="scss">
.early-warning-detail-wrap {
  ::v-deep .el-dialog {
    .el-dialog__header {
      padding: 0;
      .el-dialog__headerbtn {
        top: 20px;
        z-index: 10;
      }
    }
    .el-dialog__body {
      padding-top: 10px;
    }
    .el-dialog__footer {
      padding: 0 20px 20px;
    }
  }
}
</style>
