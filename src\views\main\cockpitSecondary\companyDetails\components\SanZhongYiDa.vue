/** 三重一大 */
<template>
  <div class="san-zhong-yi-da">
    <ProjectNumber />
    <div class="line-between"></div>
    <AppointAndDismiss />
    <div class="line-between"></div>
    <MatterNumber />
    <div class="line-between"></div>
    <ItemAmount />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import CommonModuleWrapper from '@/views/main/cockpitSecondary/components/CommonModuleWrapper.vue'
import ProjectNumber from './SanZhongYiDaCom/ProjectNumber.vue'
import AppointAndDismiss from './SanZhongYiDaCom/AppointAndDismiss.vue'
import ItemAmount from './SanZhongYiDaCom/ItemAmount.vue'
import MatterNumber from './SanZhongYiDaCom/MatterNumber.vue'

@Component({
  components: {
    CommonModuleWrapper,
    ProjectNumber,
    AppointAndDismiss,
    ItemAmount,
    MatterNumber
  }
})
export default class extends Vue {
  @Prop({ default: false }) private globalLoading!: boolean

  private loading = false
  private year = ''
  // 年份切换
  private yearHandler(year: string) {
    this.year = year
    // this.initData()
    this.loading = true
    this.$nextTick(() => {
      this.loading = false
    })
  }
}
</script>

<style scoped lang="scss">
.san-zhong-yi-da {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  .line-between {
    width: 3px;
    height: 100%;
    // background: linear-gradient(
    //   to right,
    //   #2589fe00 0%,
    //   #2589fe 50%,
    //   #2589fe00 100%
    // );
    background: linear-gradient(
      to bottom,
      rgba(37, 137, 254, 0) 0%,
      rgba(37, 137, 254, 0) 20%,
      rgba(37, 137, 254, 0.5) 50%,
      rgba(37, 137, 254, 0) 80%,
      rgba(37, 137, 254, 0) 100%
    );
  }
}
</style>