/*
* BusEmit[BusYearTimeChange]: 年份选择时触发全局 bus, 传递选中年份值
* BusEmit[BusMoonTimeChange]: 月份选择时触发全局 bus, 传递选中月份值
*/

<template>
  <!-- 只显示年份 -->
  <section v-if="isYearCom"
    class="time-selection-wrap">
    <i class="chassis" />
    <i v-if="!getTimeText"
      class="thow" />
    <span class="time">
      <span v-for="(item, index) of getTimeText"
        :key="index">{{ item }}</span>
    </span>
    <div class="time-year-box">
      <div class="time-box">
        <span v-for="item of years"
          :key="item"
          :class="{ 'active': +yearCount === +item }"
          @click="changeYear(item)">{{
          item }}</span>
      </div>
    </div>
  </section>

  <!-- 显示年份+月份 -->
  <section v-else
    class="time-selection-wrap">
    <i class="chassis" />
    <i v-if="!getTimeText"
      class="thow" />
    <span class="time">
      <span v-for="(item, index) of getTimeText"
        :key="index">{{ item }}</span>
    </span>
    <div class="time-year-moon-box">
      <div class="time-box">
        <div class="years"
          :class="{ 'active': yearCount }">
          <p class="year">{{ yearCount ? yearCount : '年份' }}</p>
          <div class="list">
            <span v-for="item of years"
              :key="item"
              :class="{ 'active': +yearCount === +item }"
              @click="changeYear(item)">{{ item }}年</span>
          </div>
        </div>
        <span v-for="item of 12"
          :key="item"
          :class="{ 'active': +moonCount === +item }"
          @click="changeMoon(item)">{{
          item }}月</span>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { setLocalStorage } from '@/utils/cache'

@Component({})
export default class extends Vue {
  @Prop({ default: true }) private isYearCom?: boolean // 是否只显示年份

  private yearCount = String(new Date().getFullYear())
  private moonCount = ''
  private years: number[] = []

  // 获取时间数据
  get getTimeText() {
    let text = ''

    if (this.yearCount && !this.moonCount) {
      text = `${this.yearCount}年`
    }

    if (!this.yearCount && this.moonCount) {
      text = `${this.moonCount}月`
    }

    if (this.yearCount && this.moonCount) {
      text = `${this.yearCount}年${this.moonCount}月`
    }

    return text
  }

  @Watch('$route')
  private changeRoute() {
    this.resetData()
  }

  // 组件初始化
  private mounted() {
    this.getYears()
  }

  // 获取年份内容
  private getYears() {
    let list: number[] = []
    let nowYear = (this as any).$moment(new Date()).add('year', 0).format('YYYY')
    for (let i = 0; i < 5; i++) {
      list.push(+nowYear - i)
    }
    setLocalStorage('yearFull', nowYear)
    this.years = list
  }

  // 年份改变
  private changeYear(year: string) {
    this.yearCount = year + ''
    setLocalStorage('yearFull', year)
    this.$bus.$emit('BusYearTimeChange', this.yearCount)
  }

  // 月份改变
  private changeMoon(month: string) {
    this.moonCount = month + ''
    this.$bus.$emit('BusMoonTimeChange', this.moonCount)
  }

  // 重置数据
  private resetData() {
    this.yearCount = String(new Date().getFullYear())
    this.moonCount = ''
  }

  // 组件销毁
  private destroyed() {
    this.$bus.$off('BusYearTimeChange')
    this.$bus.$off('BusMoonTimeChange')
  }
}
</script>

<style scoped lang="scss">
.time-selection-wrap {
  $borderImage: linear-gradient(
    90deg,
    rgba(5, 186, 253, 0) 0%,
    rgba(5, 186, 253, 0.1) 10%,
    rgba(5, 186, 253, 0.3) 20%,
    rgba(5, 186, 253, 0.5) 30%,
    rgba(5, 186, 253, 0.8) 40%,
    rgba(5, 186, 253, 1) 50%,
    rgba(5, 186, 253, 0.8) 60%,
    rgba(5, 186, 253, 0.5) 70%,
    rgba(5, 186, 253, 0.3) 80%,
    rgba(5, 186, 253, 0.1) 90%,
    rgba(5, 186, 253, 0) 100%
  );
  $modeHoverBg: linear-gradient(
    to right,
    rgba(5, 186, 253, 0) 0%,
    rgba(5, 186, 253, 0.1) 10%,
    rgba(5, 186, 253, 0.3) 20%,
    rgba(5, 186, 253, 0.5) 30%,
    rgba(5, 186, 253, 0.8) 40%,
    rgba(5, 186, 253, 1) 50%,
    rgba(5, 186, 253, 0.8) 60%,
    rgba(5, 186, 253, 0.5) 70%,
    rgba(5, 186, 253, 0.4) 80%,
    rgba(5, 186, 253, 0.3) 90%,
    rgba(5, 186, 253, 0.1) 100%
  );

  p {
    margin: 0;
  }

  &:hover .time-year-moon-box {
    opacity: 1;
    transform: translateX(0);
  }

  &:hover .time-year-box {
    opacity: 1;
    transform: translateX(0);
  }

  @keyframes keySliderMove {
    0% {
      transform: translateX(-80px);
    }

    100% {
      transform: translateX(0);
    }
  }

  @keyframes keyThowMove {
    0% {
      transform: translateX(-50px) rotateY(180deg);
    }

    50% {
      transform: translateX(-30px) rotateY(180deg);
    }

    100% {
      transform: translateX(-50px) rotateY(180deg);
    }
  }

  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 20;
  width: 111px;
  height: 816px;
  font-size: 30px;
  cursor: pointer;
  margin: auto;
  animation: keySliderMove 5s ease;

  .chassis {
    position: relative;
    z-index: 4;
    display: block;
    height: 100%;
    background: url('../../images/slider1.png') no-repeat left center;
    background-size: 100% 100%;
  }

  &:hover {
    .thow {
      display: none;
    }
  }

  .thow {
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 5;
    margin: auto;
    width: 110%;
    height: 50px;
    transform: translateX(-50px) rotateY(180deg);
    background: url('../../images/thow3.png') no-repeat left center;
    background-size: 100% 100%;
    animation: keyThowMove 5s ease infinite;
  }

  .time {
    position: absolute;
    left: 0;
    top: 50%;
    width: 46px;
    margin: auto;
    z-index: 10;
    transform: translateY(-50%);
    font-size: 36px;
    line-height: 48px;
    font-weight: normal;
    font-family: 'FZZZHONGHJW';
    display: flex;
    flex-flow: column;
    justify-content: center;
    align-items: center;
  }

  .time-year-box {
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 5;
    width: 185px;
    height: 800px;
    margin: auto;

    opacity: 0;
    transform: translateX(-200px);

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: end;
    transition: 0.5s ease;
    background: url('../../images/slider2.png') no-repeat left center;
    background-size: 100% 100%;

    .time-box {
      position: relative;
      right: 30px;
      z-index: 4;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: end;
      font-size: 30px;
      box-sizing: border-box;

      span {
        position: relative;
        z-index: 4;
        width: 100%;
        text-align: right;
        padding: 36px 17px;
        box-sizing: border-box;
        font-weight: normal;
        font-family: 'FZZZHONGHJW';
        border-bottom: 4px solid;
        border-image: $borderImage 1;

        &:hover {
          background: $modeHoverBg;
        }

        &:nth-last-child(1) {
          border-bottom: none;
        }
      }

      .active {
        background: $modeHoverBg;
      }
    }
  }

  .time-year-moon-box {
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 5;
    width: 180px;
    height: 1332px;
    margin: auto;
    opacity: 0;
    transform: translateX(-200px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: end;
    transition: 0.5s ease;
    background: url('../../images/slider2.png') no-repeat left center;
    background-size: 100% 100%;

    .time-box {
      position: relative;
      right: 30px;
      z-index: 4;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: end;
      font-size: 38px;
      box-sizing: border-box;

      span {
        position: relative;
        z-index: 4;
        width: 100%;
        text-align: right;
        padding: 14px 20px;
        box-sizing: border-box;
        font-weight: normal;
        font-family: 'FZZZHONGHJW';
        border-bottom: 4px solid;
        border-image: $borderImage 1;

        &:hover {
          background: $modeHoverBg;
        }

        &:nth-last-child(1) {
          border-bottom: none;
        }
      }

      .active {
        background: $modeHoverBg;
      }
    }

    .years {
      width: 100%;
      text-align: right;
      padding: 14px 20px;
      box-sizing: border-box;
      font-weight: normal;
      font-family: 'FZZZHONGHJW';
      border-bottom: 4px solid;
      border-image: $borderImage 1;

      &:hover {
        background: $modeHoverBg;

        .list {
          opacity: 1;
          transform: translateX(0);
        }
      }
    }

    .list {
      position: absolute;
      z-index: 3;
      right: -210px;
      top: 0;
      width: 200px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: end;
      font-size: 38px;
      font-weight: normal;
      font-family: 'FZZZHONGHJW';
      opacity: 0;
      transform: translateX(-210px);
      transition: 0.5s ease;
      background: url('../../images/slider_year_bg.png') no-repeat left center;
      background-size: 100% 100%;

      span {
        width: 100%;
        box-sizing: border-box;
        padding: 14px 20px;
        text-align: center;
        font-weight: normal;
        font-family: 'FZZZHONGHJW';
        border-bottom: 4px solid;
        border-image: $borderImage 1;

        &:hover {
          background: $modeHoverBg;
        }

        &:nth-last-child(1) {
          border-bottom: none;
        }
      }
    }
  }
}
</style>
