/* axios 请求封装 */

import axios from 'axios'
import qs from 'qs'
import { getLocalStorage, setLocalStorage, removeLocalStorage, removeToken } from '@/utils/cache'
import { RemoteResponse } from '@/types'
import { Message } from 'element-ui'
import { MessageType } from 'element-ui/types/message'

/* 防止重复提交，利用axios的cancelToken */
let cancelPromise: any
const requestPath: any = {}
const CancelToken = axios.CancelToken

let VUE_APP_URL = process.env.VUE_APP_URL
let VUE_APP_BASE_URL = ''

if (process.env.NODE_ENV === 'production') {
  if (window.location.origin.includes('dpm1-jh-fht-admin.mdguanjia.com')) {
    VUE_APP_URL = 'http://dpm1-jh-fgw.mdguanjia.com/'
    VUE_APP_BASE_URL = 'http://dpm1-jh-fht-admin.mdguanjia.com/'
  } else if (window.location.origin.includes('szjg.gzw.jinhua.gov.cn')) {
    VUE_APP_URL = 'https://szjg.gzw.jinhua.gov.cn:7443/'
    VUE_APP_BASE_URL = 'https://szjg.gzw.jinhua.gov.cn:6443/'
  } else if (window.location.origin.includes('10.24.161.131')) {
    VUE_APP_URL = 'http://10.24.161.131:9230/'
    VUE_APP_BASE_URL = 'http://10.24.161.131:9990/'
  }
}

const defaultConfig = {
  baseURL: VUE_APP_URL,
  timeout: 30 * 1000
}
const service = axios.create(defaultConfig)

/* 调用退出系统 */
const logOutMethod = () => {
  removeToken()
  removeLocalStorage('saber-token')
  if (process.env.NODE_ENV === 'development') {
    window.location.href = `${window.location.origin}/#/login`
  } else {
    window.location.href = VUE_APP_BASE_URL + '#/login'
  }
}

/* request拦截器 */
service.interceptors.request.use(
  config => {
    /* 发起请求时，取消掉当前正在进行的相同请求 */
    config.headers['Authorization'] = `Basic c2FiZXI6c2FiZXJfc2VjcmV0`
    config.headers['Tenant-Id'] = '000000'
    if (String(config.url).indexOf('/oauth/token') > -1) {
      config.headers['Captcha-Ignore'] = 'true'
    } else {
      let tokenJson = getLocalStorage('saber-token')
      if (tokenJson) {
        let token = JSON.parse(tokenJson).content
        config.headers['Fht-Auth'] = `bearer ${token}`
      }
    }
    return config
  },
  error => {
    Promise.reject(error)
  }
)

/* respone拦截器 */
service.interceptors.response.use(
  response => {
    return response
  },
  error => {
    // 服务器getUser偶尔会报500,导致页面无法进入,做了特殊判断,待服务器解决问题再移除代码
    let response = error.response || {}
    try {
      if (response.status === 406) {
        if (!response.config || !response.config.url.endsWith('/area/listAll')) {
          window.location.href = '/#/'
          window.location.reload()
        }
      } else if (response.status === 403) {
        // 接口 403
      } else if (response.status === 401) {
        // 接口 401 
        logOutMethod()
      }
    } catch (e) {
      console.error(e, response)
    }

    let errorMsg = response && response.data && response.data.msg ? response.data.msg : '网络链接失败，请刷新重试！'

    if (response.status !== 402) {
      let type: MessageType = 'warning'
      let code = +response.status.toString().substring(0, 1)

      if (code === 2) {
        type = 'success'
      } else if (code === 4) {
        type = 'error'
      } else if (code === 5) {
        type = 'error'
      }

      Message({
        type: type,
        message: errorMsg,
        duration: 3 * 1000
      })
    }

    return {
      data: {
        success: false,
        msg: errorMsg
      }
    }
  }
)

/* axios请求体包装 */
const responseMehod = (response: any, resolve: any, reject: any, options: any = {}) => {
  if (!response) return

  // 是否返回原生响应头, 如果是，直接返回 response
  if (options.isReturnNativeResponse) {
    return resolve(response)
  }

  const res = response.data

  // 判断是否返回文件流格式
  if (res instanceof Blob) {
    return resolve(response)
  }

  if (res.code === 401) {
    logOutMethod()
  } else if (res.access_token || (res.code === 200 && res.success)) {
    return resolve(res)
  } else {
    {
      if (res.code && res.msg) {
        let type: MessageType = 'warning'
        let code = +res.code.toString().substring(0, 1)

        if (code === 2) {
          type = 'success'
        } else if (code === 4) {
          type = 'warning'
        } else if (code === 5) {
          type = 'error'
        }

        Message({
          type: type,
          message: res.msg || '未知错误',
          duration: 3 * 1000
        })
      }
    }

    return reject(res || 'error')
  }
}

const judgeMethod = (url: string, params?: object, options?: any): Promise<RemoteResponse> => {
  const requestBody: any = {
    method: options ? options.method || 'POST' : 'POST',
    url,
    ...options,
    cancelToken: new CancelToken(c => {
      cancelPromise = c
    })
  }
  if (options && options.method && options.method.toUpperCase() === 'GET') {
    requestBody.params = params
  } else {
    requestBody.data = url.indexOf('/oauth/token') > -1 ? qs.stringify(params) : params
  }
  return new Promise((resolve, reject) => {
    service(requestBody).then(response => {
      responseMehod(response, resolve, reject, options)
    })
  })
}

export default judgeMethod
