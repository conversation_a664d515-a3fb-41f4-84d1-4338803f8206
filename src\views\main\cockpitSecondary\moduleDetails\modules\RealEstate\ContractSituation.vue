/** 合同情况 */

<template>
  <div class="contract-situation-wrap">
    <div class="contract-box">
      <div class="head">
        <span class="m-r-20">有效合同数</span>
        <CountTo v-if="listData.all"
          :startVal='0'
          :endVal='+listData.all'
          :duration='1500'
          class="num" />
      </div>
      <div class="content">
        <div v-for="(item, index) of listData.list"
          :key="index"
          class="mode">
          <CountTo :startVal='0'
            :endVal='item.total'
            :duration='1500'
            class="count" />
          <p class="tb">
            <template v-if="+item.growth > 0">
              <img src="@/views/main/cockpitSecondary/images/thows.png" />
              <span class="s">{{Math.ceil(item.growth)}}%</span>
            </template>
            <template v-else>
              <img src="@/views/main/cockpitSecondary/images/thowx.png" />
              <span class="x">{{Math.floor(item.growth)}}%</span>
            </template>
          </p>
          <p class="text">{{item.name}}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import CountTo from 'vue-count-to'

@Component({
  components: {
    CountTo
  }
})
export default class extends Vue {
  @Prop({ default: () => [] }) seriesData!: any
  @Prop({ default: () => [] }) source!: Record<string, any>[]

  private listData = {}

  // 监听 seriesData 数据变化，更新视图
  @Watch('seriesData', { immediate: true })
  changeSeriesData() {
    this.initData()
  }

  // 数据初始化
  private initData() {
    this.listData = this.seriesData
  }
}
</script>

<style scoped lang="scss">
.contract-situation-wrap {
  height: 100%;
  $color: #333;
  $bg: rgba(
    $color: #4662ea,
    $alpha: 0.5
  );

  position: relative;
  p {
    margin: 0;
  }

  .contract-box {
    height: 100%;
    display: flex;

    flex-direction: column;
    justify-content: space-around;
    .head {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 40px;
      padding: 10px 0;
      .num {
        color: #00c091;
        font-size: 70px;
        font-weight: normal;
        font-family: 'PangMenZhengDao';
      }
    }

    .content {
      display: flex;
      justify-content: space-between;
      .mode {
        background: $bg;
        padding: 20px 10px 20px;
        flex: 1;
        text-align: center;
        border-radius: 4px;
        margin-right: 10px;
        &:nth-last-child(1) {
          margin-right: 0;
        }

        .count {
          font-size: 64px;
          font-weight: normal;
          font-family: 'PangMenZhengDao';
        }
        .tb {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 38px;
          padding: 14px 0;
          img {
            width: 26px;
            margin-right: 4px;
          }
        }
        .text {
          font-size: 32px;
        }
      }
    }
  }

  .s {
    color: #fb3f3f;
  }
  .x {
    color: #00c091;
  }
}
</style>