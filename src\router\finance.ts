/* 财务系统 */

import Layout from '@/layouts/public/index.vue'

export const routes = [
  {
    path: '/finance',
    name: '财务监管系统',
    component: Layout,
    redirect: '/finance/home',
    children: [
      {
        path: 'overview',
        name: '财务概况',
        component: () => import('@/views/finance/overviews/index.vue')
      },
      // {
      //   path: 'home',
      //   name: '企业发债审批',
      //   component: () => import('@/views/finance/index.vue')
      // },
      {
        path: 'debtIssuanceApproval',
        name: '企业发债审批',
        component: () => import('@/views/finance/page/debtIssuanceApproval.vue')
      },
      {
        path: 'assetImpairment',
        name: '资产减值备案',
        component: () => import('@/views/finance/page/assetImpairment.vue')
      },
      {
        path: 'abroad',
        name: '对外捐赠事项',
        component: () => import('@/views/finance/page/abroad.vue')
      },
      {
        path: 'nuclearApproval',
        name: '清产核资审批',
        component: () => import('@/views/finance/page/nuclearApproval.vue')
      },
      {
        path: 'largeFunds',
        name: '大额资金出借',
        component: () => import('@/views/finance/page/largeFunds.vue')
      },
      {
        path: 'externalGuarantee',
        name: '对外担保备案',
        component: () => import('@/views/finance/page/externalGuarantee.vue')
      },
      {
        path: 'jmreport',
        name: '财务报表',
        component: () => import('@/views/finance/page/jmreport.vue')
      }
      // {
      //   path: 'setting',
      //   name: '预警设置',
      //   component: () => import('@/views/finance/setting.vue')
      // }
    ]
  }
]

export default routes
