<template >
  <div>
    <!-- 动态增加 -->
    <el-row v-for="(item,index) in badAssetTypeFrom" :key="index">
      <el-col :span="24">
        <el-divider></el-divider>

        <el-col :span="8">
          <el-form-item label-width="140px"  :style="{ fontWeight: 600,lineHeight:'50px' }" class="f-w-600" label="核销不良资产类型">
            <el-select
            :disabled="mode=='see'?true:false"
              v-model="item.assetType"
              placeholder="货币单位"
              @change="changeAssetType"
              :style="{ width: '100%' }"
              size="small"
            >
              <el-option
                v-for="(item, index) in badAssetType"
                :key="index"
                :label="item.label"
                :value="item.value"
                :disabled="item.disabled"
              ></el-option>
            </el-select>
            <!-- <el-input v-model="formData.content" placeholder="请输入核销不良资产内容" clearable :style="{ width: '100%' }"></el-input> -->
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-button
            @click="addBadAssetTypeFrom"
            v-if="index == 0"
            class="m-l-10"
            style="height: 36px"
            icon="el-icon-plus"
            type="text"
          >新增一类</el-button>
          <el-button
            @click="deleteBadAssetTypeFrom(index)"
            class="m-l-10"
            v-else
            style="height: 36px;color:#9E9E9E;"
            icon="el-icon-plus"
            type="text"
          >删除该类</el-button>
        </el-col>
      </el-col>
      <!-- 动态表格 -->

      <el-col :span="24" class="p-l-50">
        <!-- 不良资产类型 -->
        <!-- badAssetTypeFrom -->

        <el-row
          :gutter="0"
          v-for="(tableItem, tableIndex) in item.badAssetList"
          :key="tableIndex"
          class="row_form"
        >
          <el-form
            class="row_form"
            label-position="top"
            :model="tableItem"
            ref="dynamicValidateForm"
            label-width="250px"
            :disabled="mode=='see'?true:false"
          >
            <el-form-item label="序号">{{ tableIndex + 1 }}</el-form-item>
            <el-form-item
              :rules="[
      { required: true, message: '请输入资产名称', trigger: 'blur' },
    ]"
              label="资产名称"
              prop="assetName"
            >
              <el-input v-model="tableItem.assetName"></el-input>
            </el-form-item>
            <el-form-item
              :rules="[
      { required: true, message: '请输入账面原值总值', trigger: 'blur' },
    ]"
              label="账面原值总值"
              prop="totalOriginalBookValue"
            >
              <inputNumber type="decimal" v-model="tableItem.totalOriginalBookValue"></inputNumber>
            </el-form-item>
            <el-form-item
              :rules="[
      { required: true, message: '请输入累计折旧总额', trigger: 'blur' },
    ]"
              label="累计折旧总额"
              v-if="item.assetType==2||item.assetType==3?true:false"
               prop="totalAccumulatedDepreciation"
            >
              <inputNumber type="decimal" v-model="tableItem.totalAccumulatedDepreciation"></inputNumber>
            </el-form-item>
            <el-form-item
              :rules="[
      { required: true, message: '请输入账面净值总额', trigger: 'blur' },
    ]"
              label="账面净值总额"
              prop="totalNetBookValue"
            >
              <inputNumber type="decimal" v-model="tableItem.totalNetBookValue"></inputNumber>
            </el-form-item>
            <el-form-item label="操作">
              <el-col :span="24" style="text-align: start">
                <el-button type="primary" @click="badAssetTypeFromAdd(index)">新增</el-button>
                <el-button
                  type="danger"
                  v-if="tableIndex > 0"
                  @click="badAssetTypeFromdelete(index, tableIndex)"
                >删除</el-button>
              </el-col>
            </el-form-item>
          </el-form>
        </el-row>

        <!-- <el-divider></el-divider> -->
      </el-col>
    </el-row>
  </div>
  <!-- end -->
</template>
<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import * as filterOptions from '@/views/finance/filterOptions'
import { number } from 'echarts'
import { Confirm } from '@/decorators/index'
import inputNumber from '@/components/FormComment/inputNumber.vue'
// import Uploader from "@/components/Uploader/index.vue"
@Component({
  components: {
    inputNumber
  }
})

export default class Container extends Vue {
  @Watch("badAssetTypeFrom",{deep:true})
private setbadAssetTypeFrom() {
  // 统计账面净值总额
    let Amountn=0
    if(this.mode!="see"&&this.badAssetTypeFrom.length>0){

    this.badAssetTypeFrom.forEach((item:any)=>{
        item.badAssetList.forEach((n:any)=>{
          Amountn=Amountn+Number(n.totalNetBookValue)
        })
    })
    }
    
    this.$emit("setAmount",Amountn)
    
}
  @Prop({ default: () => [] }) private badList: any
  @Prop({ default: "add" }) private mode: any
  
  private badAssetTypeFrom: any = [
    {
      assetType: "1",
      badAssetList: [
        {
          assetName: '',
          totalAccumulatedDepreciation: '',
          totalNetBookValue: '',
          totalOriginalBookValue: ''
        }
      ]
    }
  ]
  private badAssetType = [
    {
      value: "1",
      label: '实物资产类',
      disabled: true
    },
    {
      value: "2",
      label: '往来款项类',
      disabled: false
    },
    {
      value: "3",
      label: '对外投资类',
      disabled: false
    },
    {
      value: "4",
      label: '资产核销类',
      disabled: false
    }
  ]
  // 新增不良资产大类
  private addBadAssetTypeFrom() {
    let list = this.badAssetType.find((item: any) => {
      return item.disabled == false
    })
    if (list == undefined) {
      this.$message('类型已全部选择')
    } else {
      this.badAssetTypeFrom.push({
        assetType: list.value,
        badAssetList: [
          {
            assetName: '',
            totalAccumulatedDepreciation: '',
            totalNetBookValue: '',
            totalOriginalBookValue: ''
          }
        ]
      })
      this.changeAssetType(list.value)
    }
  }
  // 删除不良资产大类
  @Confirm({
    title: '提示',
    content: `是否确认删除？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private deleteBadAssetTypeFrom(index: number) {
    this.badAssetTypeFrom.splice(index, 1)
    this.changeAssetType("1")
  }
  //  切换不良资产类型
  private changeAssetType(val: any) {
    let isdisablelist: any = this.badAssetTypeFrom.map((item: any) => {
      return item.assetType
    })
    this.badAssetType.forEach((i: any, n: number) => {

      if (isdisablelist.indexOf(i.value) > -1) {
        this.badAssetType[n].disabled = true
      } else {
        this.badAssetType[n].disabled = false
      }
    })
  }

   
    // let list: any = []
    // this.badAssetTypeFrom.forEach((item: any) => {
    //   item.badAssetList.forEach((i: any) => {
    //     i.assetType = item.assetType
    //     list.push(i)
    //   })
    // })
    // return list
  // assetName: "333333"
  // assetType: 1
  // recordId: "1546382415425413125"
  // totalAccumulatedDepreciation: -1
  // totalNetBookValue: "333333.00"
  // totalOriginalBookValue: "333333.00"
  created() {
    if (this.badList.length > 0) {
      this.setData(this.badList)
    }
  }
  private setData(data: any) {    
    this.badAssetTypeFrom=data
    // data.forEach((item: any) => {
    //   if (this.badAssetTypeFrom[item.assetType] == undefined) {
    //     this.badAssetTypeFrom[item.assetType] = JSON.parse(
    //       JSON.stringify({
    //         assetType: '',
    //         badAssetList: []
    //       })
    //     )
    //     this.$set(this.badAssetTypeFrom[item.assetType], 'assetType', item.assetType)
    //     this.$set(this.badAssetTypeFrom[item.assetType], 'badAssetList', [])
    //   }
    //   this.badAssetTypeFrom[item.assetType].badAssetList.push(item)
    // })
    // this.badAssetTypeFrom.splice(0, 1)
    this.changeAssetType("1")
  }
  // 新增不良资产表格
  private validateBadForm() {
    let isvalidate=true
    if ((this.$refs.dynamicValidateForm as any).length > 0) {
      for (let i = 0; i < (this.$refs.dynamicValidateForm as any).length; i++) {        
        ;(this.$refs.dynamicValidateForm as any)[i].validate((valid: boolean) => {
          if (valid) {
            // 
          } else {
            isvalidate=false
            return false
          }
        })
      }
    }
    return isvalidate
  }
  private badAssetTypeFromAdd(index: number) {
    this.badAssetTypeFrom[index].badAssetList.push({
      assetName: '',
      totalAccumulatedDepreciation: '',
      totalNetBookValue: '',
      totalOriginalBookValue: ''
    })
  }
  // 删除不良资产表格项目
  private badAssetTypeFromdelete(index: number, tableIndex: number) {
    this.badAssetTypeFrom[index].badAssetList.splice(tableIndex, 1)
  }
}
</script>
<style scoped>
.row_form {
  display: flex;
  /* justify-content: space-evenly; */
}
.el-form-item {
  margin-left: 10px;
}
::v-deep .el-form-item__label {
  line-height: 35px !important;
}
</style>