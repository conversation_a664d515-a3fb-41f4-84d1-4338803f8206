/* 每日运营动态 */

<template>
  <section ref="wrapper"
    class="cockpit-enter-operations-wrap"
    :class="{'cockpit-wrapperFull-wrap': isWrapperFull}">
    <TitleCom title="每日运营动态"
      :isThrow="false"
      :isFullScreen="true"
      module="FinancialIndicator"
      class="m-t-10"
      @fullScreenHandle="fullScreenHandle" />

    <!-- tabs -->
    <!-- <div class="tab-box">
      <span v-for="item of tabList"
        :key="item.value"
        :class="{'active':activeTab===item.value}"
        @click="tabChange(item.value)">{{ item.label }}</span>
    </div> -->

    <div class="time-box">
      <el-date-picker v-model="yesterday"
        type="date"
        size="medium"
        align="right"
        :clearable="false"
        format="yyyy年MM月dd日"
        value-format="yyyy-MM-dd"
        placeholder="选择日期"
        @change="changeYesterday" />
    </div>

    <!-- 内容区域 -->
    <div v-loading="loading"
      class="content-box"
      ref="tableList">
      <div v-for="(item, index) of dataList"
        :key="index"
        class="mode"
        :class="[
          {'active': +index === +currentIndex},
          {'mode-max':item.list.length <= 2},
          {'mode-defalut':item.list.length === 3},
          {'mode-min':item.list.length > 3},
        ]"
        @mouseenter="mouseenterHandle"
        @mouseleave="mouseleaveHandle"
        @click="detailHandle(item)">
        <div class="title">{{item.name}}</div>
        <div class="info">
          <div v-for="(itemInfo, indexInfo) of item.list"
            :key="indexInfo"
            class="md">
            <p>{{itemInfo.label}}</p>
            <span>{{itemInfo.value}}</span>
            <i>{{itemInfo.unit}}</i>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据改变提示层 -->
    <img v-if="visibleFlicker"
      class="cockipt-bg-flicker"
      src="@/views/main/cockpitcren/images/panel_bg2_cg.png" />

    <!-- 弹窗：查看详情 -->
    <DialogCom v-if="visibleDetail"
      :visible.sync="visibleDetail"
      :closeModal="false"
      :year="params.year"
      :code="orgActive.orgCode"
      :title="titleDetail"
      width="1200px">
      <EnterOptionCom :type="typeDetail"
        :yesterday="yesterday" />
    </DialogCom>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { entextHomeSummary } from '@/api/cockpit'
import CountTo from 'vue-count-to'
import TitleCom from '@/views/main/cockpitcren/components/Public/TitleCom.vue'
import DialogCom from '@/views/main/cockpitcren/components/Public/DialogCom.vue'
import EnterOptionCom from './Components/EnterOptionCom.vue'

@Component({
  components: {
    CountTo,
    TitleCom,
    DialogCom,
    EnterOptionCom
  }
})
export default class extends Vue {
  private loading = false
  private visibleFlicker = false
  private timer: any
  private listHeight = 180
  private currentIndex = 0
  private middleIndex = 2
  private listDom: any = {}
  private activeTab = '1'
  private yesterday = ''
  private typeDetail = ''
  private tabList = Object.freeze([
    {
      label: '昨日',
      value: '1'
    },
    {
      label: '本年',
      value: '2'
    }
  ])
  private dataList: any[] = []

  // 弹窗详情数据
  private visibleDetail = false
  private detailData: any[] = []
  private titleDetail = ''

  // 是否放大展示
  private isWrapperFull = false

  // 所属集团+时间切换 选中数据
  private orgActive: {
    orgCode: string
  } = {
    orgCode: '0'
  }
  private params = {
    year: String(new Date().getFullYear()),
    month: ''
  }

  // 初始化
  private mounted() {
    let that = this as any
    this.yesterday = that.$moment().subtract(1, 'day').format('yyyy-MM-DD')

    this.listDom = this.$refs['tableList'] as HTMLDivElement
    this.initData()
    // this.listenerDate()
  }

  // 获取渲染视图数据
  @Loading('loading')
  private async initData() {
    this.flickerHandle()
    await entextHomeSummary({
      type: this.activeTab,
      // orgCode: this.orgActive.orgCode,
      // year: this.params.year,
      date: this.yesterday
    })
      .then((res) => {
        if (res.data) {
          this.dataList = res.data
          this.scrollTable()
        }
      })
      .catch(() => {
        this.dataList = []
      })
  }

  // 监听全局事件触发
  private listenerDate() {
    // 集团 切换
    this.$bus.$on('BusTabsChange', (orgActive: any) => {
      this.orgActive = orgActive
      this.clearTimer()
      this.initData()
    })

    // 年份 切换
    this.$bus.$on('BusYearTimeChange', (year: string) => {
      this.params.year = year
      this.clearTimer()
      this.initData()
    })
  }

  // 昨日、本年切换
  private tabChange(value: string) {
    this.activeTab = value
    this.clearTimer()
    this.initData()
  }

  // 时间日期改变
  private changeYesterday(val: string) {
    this.clearTimer()
    this.initData()
  }

  // 开启定时器，让页面滚动起来
  private scrollTable() {
    let dataLen = this.dataList.length
    if (!dataLen || dataLen <= this.middleIndex) return

    this.timer = setInterval(() => {
      this.currentIndex++
      if (this.currentIndex > this.middleIndex && this.currentIndex < dataLen) {
        this.listDom.scrollTo(0, (this.currentIndex - this.middleIndex) * this.listHeight)
      } else if (this.currentIndex === dataLen) {
        this.currentIndex = 0
        this.listDom.scrollTo(0, 0)
      }
    }, 6000)
  }

  // 列表鼠标移入，停止动画
  private mouseenterHandle() {
    clearInterval(this.timer)
    this.timer = null
  }

  // 列表鼠标移出，开始动画
  private mouseleaveHandle() {
    this.scrollTable()
  }

  // 清除定时器，还原相关数据
  private clearTimer() {
    clearInterval(this.timer)
    this.timer = null
    this.currentIndex = 0
    this.listDom.scrollTo(0, 0)
  }

  // 放大/缩小展示
  private fullScreenHandle(isFull: boolean) {
    this.isWrapperFull = isFull
  }

  // 查看详情
  @Loading('loading')
  private async detailHandle(item: any) {
    this.typeDetail = item.type
    this.titleDetail = item.name
    this.visibleDetail = true
  }

  // 触发闪烁效果
  private flickerHandle() {
    this.visibleFlicker = true
    setTimeout(() => {
      this.visibleFlicker = false
    }, 3000)
  }

  // 组件销毁
  private destroyed() {
    this.clearTimer()
  }
}
</script>

<style scoped lang="scss">
.cockpit-enter-operations-wrap {
  position: relative;
  transition: 1s;
  width: 100%;
  height: 760px;
  background: url('../../images/panel_bg2.png') no-repeat left top;
  background-size: 100% 100%;

  .tab-box {
    position: absolute;
    top: 52px;
    right: 30px;
    z-index: 2;
    font-size: 36px;
    span {
      display: inline-block;
      padding: 10px 0;
      margin-right: 30px;
      cursor: pointer;
    }
    .active {
      color: #63f1ff;
      border-bottom: 4px solid #63f1ff;
    }
  }

  .time-box {
    position: absolute;
    top: 90px;
    right: 62px;
    z-index: 2;
    font-size: 36px;
    font-weight: normal;
    font-family: 'FZZZHONGHJW';

    ::v-deep .el-input {
      width: 280px;
      input {
        color: #69f2ff;
        font-size: 28px;
        height: 50px;
        line-height: 50px;
        padding: 0 0 0 40px;
        border-color: #69f2ff;
        background: none;
      }
      .el-input__prefix {
        left: 10px;
        top: 1px;
        color: #69f2ff;
        .el-input__icon {
          font-size: 24px;
        }
      }
    }
  }

  .content-box {
    position: relative;
    z-index: 2;
    height: 536px;
    overflow-y: auto;
    scroll-behavior: smooth;
    &::-webkit-scrollbar {
      display: none;
    }

    .mode:nth-last-child(2) {
      margin-bottom: 15px;
    }

    .active.mode {
      box-shadow: 0 0 30px #3eeeff inset;
    }

    .mode {
      display: flex;
      align-items: center;
      box-sizing: border-box;
      cursor: pointer;
      padding: 26px 20px;
      background: url('../../images/mode_bj.png') no-repeat left top;
      background-size: 100% 100%;
      margin-bottom: 25px;
      border-radius: 7px;

      &:hover {
        box-shadow: 0 0 30px #3eeeff inset;
      }

      .title {
        width: 94px;
        font-size: 38px;
        line-height: 46px;
        padding-right: 6px;
        color: #8ec6ff;
        text-align: center;
        font-weight: normal;
        font-family: 'FZZZHONGHJW';
        border-right: 1px solid #1d8c96;
      }
      .info {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .md {
          flex: 1;
          padding-left: 20px;
          p {
            font-size: 32px;
            margin: 0;
            margin-bottom: 6px;
          }
          span {
            color: #3eeeff;
            font-size: 36px;
            font-weight: normal;
            font-family: 'PangMenZhengDao';
          }
          i {
            color: #3eeeff;
            font-size: 28px;
            margin-left: 8px;
            font-weight: normal;
            font-family: 'FZZZHONGHJW';
          }
        }
      }
    }

    .mode-max {
      .info .md span {
        font-size: 44px;
      }
    }

    .mode-defalut {
      .info .md span {
        font-size: 40px;
      }
    }

    .mode-min {
      .info .md span {
        font-size: 36px;
      }
    }
  }
}

.cockpit-wrapperFull-wrap {
  z-index: 1000;
  transform-origin: right bottom;
  transform: scale(1.7);
  border-radius: 20px;
  background: #072979;
  box-shadow: 0 0 40px #000;
}
</style>