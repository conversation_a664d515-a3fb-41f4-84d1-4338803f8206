/* 资产构成 */

<template>
  <section class="asset-composition">
    <div class="asset-composition__content">
      <el-row :span="24"
        :gutter="24">
        <el-col :span="7">
          <CommonModuleWrapper :hasTabs="false"
            :height="1320"
            :loading="loading"
            :borderGbMode="1"
            title="资产概况"
            wrapperClass="asset-composition-common-wrapper"
            componentsName="AssetOverview"
            class="cockipt-approach-left-top" />
        </el-col>
        <el-col :span="10">
          <!-- 各集团tabs  -->
          <CommonTabs class="cockipt-approach-middel-top"
            @commonTabsHandle="onTabsChange" />

          <CommonModuleWrapper title="在建工程"
            :height="1190"
            :borderGbMode="1"
            :loading="loading"
            wrapperClass="asset-composition-common-wrapper"
            componentsName="ConstructionInProcess"
            class="cockipt-approach-middel-top" />
        </el-col>
        <el-col :span="7">
          <CommonModuleWrapper title="固定资产"
            :height="1320"
            :borderGbMode="1"
            :loading="loading"
            @jumpTo="jumpToRealEstate"
            wrapperClass="asset-composition-common-wrapper"
            componentsName="FixedAssets"
            class="cockipt-approach-right-top" />
        </el-col>
      </el-row>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import CountTo from 'vue-count-to'
import CommonModuleWrapper from '@/views/main/cockpitSecondary/components/CommonModuleWrapper.vue'
import { deepClone } from '@/utils'
import { companyList, FinancialIndicatorLineData } from '@/views/main/cockpitSecondary/baseData'
import { capitalProfile } from '@/api/cockpit'
import { Loading } from '@/decorators'
import CommonTabs from '@/views/main/cockpitSecondary/components/CommonTabs.vue'

export interface TabItem {
  label: string
  value: string | number
  percent: number
  unit: string
}

@Component({
  components: {
    CountTo,
    CommonModuleWrapper,
    CommonTabs
  }
})
export default class extends Vue {
  private loading = false
  private data = deepClone(FinancialIndicatorLineData)
  private currentYear = new Date().getFullYear() - 1
  private currentCompanyIndex = '0'
  private companyList = companyList
  private tabsIndex = 0
  private currentCompany: {
    code: string
    name: string
    id: string
  } = {
    code: '0',
    name: '国资总况',
    id: 'ALL'
  }
  private currentItem: any = {
    label: '资产总额',
    value: '',
    percent: 0,
    unit: '亿元',
    prop: 'asset_amount'
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  private leftCompanyList = [
    {
      label: '城投集团',
      id: 'CT'
    },
    {
      label: '金投集团',
      id: 'KT'
    },
    {
      label: '水务集团',
      id: 'SW'
    }
  ]

  private rightCompanyList = [
    {
      label: '交投集团',
      id: 'JT'
    },
    {
      label: '轨道集团',
      id: 'GD'
    },
    {
      label: '社发集团',
      id: 'SF'
    }
  ]

  get yearList() {
    let year = +this.getMomentTime()
    return [year - 6, year - 5, year - 4, year - 3, year - 2, year - 1, year]
  }

  private useData = [
    {
      value: 1048,
      name: '预留',
      itemStyle: { color: 'RGBA(74, 101, 226, 1)' }
    },
    {
      value: 735,
      name: '出租',
      itemStyle: { color: 'RGBA(96, 203, 179, 1)' }
    },
    {
      value: 735,
      name: '空置',
      itemStyle: { color: 'RGBA(46, 102, 162, 1)' }
    }
  ]

  get useDataSum() {
    return this.useData.reduce((sum, currentItem) => (sum += +currentItem.value), 0)
  }

  private currentIndex = 0
  private tabsList: any[] = [
    {
      label: '资产总额',
      value: '',
      percent: 0,
      unit: '亿元',
      prop: 'asset_amount'
    },
    {
      label: '资产净额',
      value: '',
      percent: 0,
      unit: '亿元',
      prop: 'asset_net'
    },
    {
      label: '营业收入',
      value: '',
      percent: 0,
      unit: '亿元',
      prop: 'turnover'
    },

    {
      label: '利润总额',
      value: '',
      percent: 0,
      unit: '万元',
      prop: 'profit_amount'
    },

    {
      label: '有效投资',
      value: '',
      percent: 0,
      unit: '万元',
      prop: 'effective_invest'
    },
    {
      label: '净资产收益率',
      value: '',
      percent: 0,
      unit: '%',
      prop: 'asset_net_rate'
    },
    {
      label: '净资产负债率',
      value: '',
      percent: 0,
      unit: '%',
      prop: 'asset_debt_rate'
    },
    {
      label: '保值增值率',
      value: '',
      percent: 0,
      unit: '%',
      prop: 'asset_rise_date'
    }
  ]
  private summarizeData: any = {}
  private reverseRankingList: any[] = []
  private rankingList: any[] = [
    {
      code: '1',
      id: 'CT',
      name: '城投集团',
      value: 0
    },
    {
      code: '4',
      id: 'JT',
      name: '交投集团',
      value: 0
    },
    {
      code: '2',
      id: 'KT',
      name: '金投集团',
      value: 0
    },
    {
      code: '5',
      id: 'GD',
      name: '轨道集团',
      value: 0
    },
    {
      code: '3',
      id: 'SW',
      name: '水务集团',
      value: 0
    },
    {
      code: '6',
      id: 'SF',
      name: '社发集团',
      value: 0
    }
  ]
  private dataList: any[] = []

  // 当前年月
  get getCurrentDate() {
    let date = new Date()
    return `截止到${date.getFullYear()}年${date.getMonth() + 1}月`
  }

  get getRankingTitle() {
    return `${this.currentItem.label}排行情况`
  }

  created() {
    this.$bus.$on('BusCompanyTabs', (currentIndex: number) => {
      this.currentCompanyIndex = currentIndex + ''
      this.filterData()
    })
    // this.onItemClick(0, this.tabsList[0])
    this.capitalProfile()
  }

  // 获取财务指标
  @Loading('loading')
  private async capitalProfile() {
    let res = await capitalProfile()
    if (res.success) {
      this.dataList = res.data || []
      this.filterData()
    }
  }

  private onTabsChange(item: any) {
    this.currentCompany = item
    this.loading = true
    setTimeout(() => {
      this.loading = false
    }, 1000)
  }

  private jumpToRealEstate() {
    this.$router.push({
      name: 'moduleDetail',
      query: { module: 'RealEstate', companyId: this.currentCompany.code }
    })
  }

  // 筛选数据
  private filterData() {
    // 筛选出当前年份数据
    let currentYearList = this.dataList.filter((item) => item.year === +this.currentYear)

    // 筛选出 tabsList 数据
    this.tabsList.forEach((tab) => {
      // 筛选出国资总况数据
      let currentCompanyData = currentYearList.find(
        (item) => item.itemCode === tab.prop && item.companyCode === this.currentCompanyIndex
      ) || {
        itemDesc: '0.00',
        itemName: '',
        itemValue: ''
      }
      let allData = currentYearList.find((item) => item.itemCode === tab.prop && item.companyCode === '0') || {
        itemDesc: '0.00',
        itemName: '',
        itemValue: ''
      }
      tab.allData = allData
      tab.value = tab.unit === '%' ? (+currentCompanyData.itemValue).toFixed(2) : currentCompanyData.itemValue
      tab.percent = +(+currentCompanyData.itemDesc * 100).toFixed()
    })

    // 计算六大集团各指数排名
    this.rankingList.forEach((company) => {
      let indexData = currentYearList.find((item) => item.itemCode === this.currentItem.prop && item.companyCode === company.code) || {
        itemDesc: '0.00',
        itemName: '',
        itemValue: ''
      }
      company.value = this.currentItem.unit === '%' ? +(+indexData.itemValue * 100).toFixed(2) : +(+indexData.itemValue).toFixed(2)
      company.rate = +(+indexData.itemDesc * 100).toFixed(2)
      company.sum = (
        currentYearList.find((item) => item.itemCode === 'asset_count' && item.companyCode === company.code) || {
          itemDesc: '0.00',
          itemName: '',
          itemValue: ''
        }
      ).itemValue
    })

    this.rankingList = this.rankingList.sort((a: any, b: any) => a.value - b.value)
    this.reverseRankingList = [...this.rankingList].reverse()
    // 筛选出 summarizeData
    this.summarizeData = {
      rate: +this.tabsList[this.currentIndex].allData.itemDesc * 100,
      name: this.rankingList[this.rankingList.length - 1].name,
      companyRate: this.rankingList[this.rankingList.length - 1].rate || 6.9,
      sum: +this.tabsList[this.currentIndex].allData.itemValue,
      companySum: this.rankingList[this.rankingList.length - 1].value
    }

    // 筛选出各集团数据
    // this.companyList.forEach((company) => {
    //   // 筛选出各集团信息
    //   let currentCompanyDataList = this.dataList.filter((item) => company.code === item.companyCode)
    //   // 筛选出各集团信息后按年度分类 按指数
    //   let indexList: Record<string, any[]> = {}
    //   currentCompanyDataList.forEach((item) => {
    //     if (indexList[item.itemCode]) {
    //       indexList[item.itemCode].push(item)
    //     } else {
    //       indexList[item.itemCode] = []
    //     }
    //   })
    //   for (let index in indexList) {
    //     indexList[index] = indexList[index]
    //       .sort((a: any, b: any) => b.year - a.year)
    //       .map((item) => {
    //         return (+item.itemValue).toFixed(2)
    //       })
    //   }
    //   company.data = indexList
    // })
  }

  private onItemClick(index: any, data: { label: string }) {
    this.currentIndex = index
    this.currentItem = data
    this.loading = true
    setTimeout(() => {
      this.loading = false
    }, 1000)
  }
}
</script>

<style scoped lang="scss">
::v-deep .common-tabs-default-wrap {
  margin: 50px 0 30px;
}

.asset-composition {
  &__tabs {
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 60px;
    margin: 14px 0px;
    overflow: hidden;
    padding: 4px;
    border-radius: 8px;
  }
  &__tabs-item {
    font-size: 32px;
    cursor: pointer;
    width: 200px;
    line-height: 60px;
    text-align: center;
    border: 3px solid rgba(40, 124, 246, 1);
    box-shadow: 0 0 30px 0px inset rgba(40, 124, 246, 0.6);
    border-radius: 10px;
    &--checked,
    &:hover {
      border: 2px solid rgba(187, 164, 38, 1);
      box-shadow: 0 0 30px 0px inset rgba(187, 164, 38, 0.6);
    }
  }
}

.asset-composition-common-wrapper {
  padding-bottom: 110px !important;
}
</style>




