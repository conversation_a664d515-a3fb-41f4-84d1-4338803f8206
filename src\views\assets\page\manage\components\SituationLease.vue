<template>
  <section v-loading="loading"
    class="situation-lease-wrap">
    <div class="header-box">
      <div class="mode">
        <div class="til">
          <span>{{ +detailData.assetType === 1 ? '建筑面积' : '土地面积' }}</span>
          <el-tooltip effect="dark"
            :content="`资产的${ +detailData.assetType === 1 ? '建筑总面积' : '土地面积' }`"
            placement="top-start">
            <i class="el-icon-question" />
          </el-tooltip>
        </div>
        <div class="num">
          <span>{{ +detailData.assetType === 1 ? detailData.coveredArea : detailData.landArea || '--' }}</span>
          <i>㎡</i>
        </div>
        <div class="total">
          <span>总房间数量：</span>
          <span>{{ detailData.itemNum }}间</span>
        </div>
      </div>
      <div class="mode">
        <div class="til">
          <span>出租率</span>
          <el-tooltip effect="dark"
            content="当前出租数量/总数量 * 100"
            placement="top-start">
            <i class="el-icon-question" />
          </el-tooltip>
        </div>
        <div class="num">
          <span>{{ detailData.occupancyRate || '--' }}</span>
          <i>%</i>
        </div>
        <div class="total">
          <span>出租面积：</span>
          <span>{{ detailData.occupancyCoveredArea }}㎡</span>
        </div>
      </div>
   
      <div class="mode">
        <div class="til">
          <span>空置面积</span>
          <el-tooltip effect="dark"
            :content="`当前空置的${ +detailData.assetType === 1 ? '建筑面积' : '土地面积' }`"
            placement="top-start">
            <i class="el-icon-question" />
          </el-tooltip>
        </div>
        <div class="num">
          <span>{{ detailData.vacancyCoveredArea || '--' }}</span>
          <i>㎡</i>
          <i class="lv">空置率：{{ detailData.vacancyRate || '--' }}%</i>
        </div>
        <div class="total">
          <span>空置房间数量：</span>
          <span>{{ detailData.vacancyItemNum }}间</span>
        </div>
      </div>
    </div>

    <div class="rooms-box">
      <div class="illustrate-box">
        <!-- <div class="mode">
          <i class="zz" />
          <span>招租中</span>
        </div> -->
        <div class="mode">
          <i class="cz" />
          <span>承租中</span>
        </div>
        <div class="mode">
          <i class="dq" />
          <span>承租中即将到期</span>
        </div>
        <div class="mode">
          <i class="kz" />
          <span>空置中</span>
        </div>
      </div>

      <div v-if="Array.isArray(detailData.floorList) && detailData.floorList.length"
        class="floor-box">
        <div v-for="item of detailData.floorList"
          :key="item.floor"
          class="mode">
          <div class="til">
            <strong>{{ item.floor }}F</strong>
            <span
              v-if="item.totalFloorArea !== undefined">{{ (+item.totalFloorArea).toFixed(2) || '--' }}
              ㎡</span>
          </div>

          <div class="floor">
            <div v-for="(rooms, index) of item.roomInfo"
              :key="index"
              class="room"
              :class="[{ zz: +rooms.status === 2 }, { cz: +rooms.status === 1 }, { dq: +rooms.status === 4 }, { kz: +rooms.status === 3 }]">
              <div class="loom">
                <!-- 招租中、承租中、将到期 -->
                <template v-if="+rooms.status !== 3">
                  <div @click="loaddetail(rooms)"
                    style="cursor: pointer">
                    <strong>{{ rooms.renterName || '--' }}</strong>
                    <br />
                    <span>{{ rooms.endDate || '--' }} 到期</span>
                    <i v-if="rooms.current==1"
                      style="color: red;"
                      class="el-icon-star-on"></i>
                    <!-- <i>{{ rooms.roomNo || '--' }}</i> -->
                  </div>
                  <div v-if="rooms.useStatus === 5 || rooms.useStatus === 10 || rooms.useStatus === 11 || rooms.useStatus === 12">（{{ rooms.useStatusStr }}）</div>
                </template>

                <!-- 空置 -->
                <template v-else>
                  <strong></strong>
                  <span>{{ rooms.roomName || '--' }}</span>
                  <i
                    v-if="rooms.roomArea !== undefined">{{ (+rooms.roomArea).toFixed(2) || '--' }}㎡</i>
                  <span v-if="rooms.useStatus === 5 || rooms.useStatus === 10 || rooms.useStatus === 11 || rooms.useStatus === 12">（{{ rooms.useStatusStr }}）</span>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>

      <el-empty v-else
        description="暂无数据" />
    </div>

    <!-- 房源详情 -->
    <DetailAssetInfo v-if="visvileDetailif"
      :detailInfo="Diadetaillist"
      :visibleif.sync="visvileDetailif"
      :visible.sync="visvileDetail" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { assetsInfoVisual, assetsInfoVisualEwm } from '@/api/assetsv2'
import { Loading } from '@/decorators'
import DetailAssetInfo from '@/views/assets/v2page/contractManagement/DetailAssetinfo.vue'

@Component({
  components: {
    DetailAssetInfo
  }
})
export default class extends Vue {
  @Prop() private id!: string
  @Prop({ default: true }) private isAssets?: boolean // 判断是资产还是预警

  // 从【预警】模块过来的，传这两个参数
  @Prop({ default: '' }) private itemNo?: string
  @Prop({ default: '' }) private orgCode?: string

  private loading = false
  private visvileDetail = false
  private visvileDetailif = false
  private detailData = {}
  private Diadetaillist: object = {}

  // 组件初始化
  private mounted() {
    this.initData()
  }

  //  查看合同详情
  private loaddetail(row: { id: number | string }) {
    if (row.id) {
      this.Diadetaillist = row
      this.visvileDetailif = true
      this.$nextTick(() => {
        this.visvileDetail = true
      })
    } else {
      console.error('房源id不能为空')
    }
  }
  // 获取数据
  @Loading('loading')
  private async initData() {
    let data = {}

    // 判断是从资产还是预警过来的（详情接口不同）
    if (this.isAssets) {
      let res = await assetsInfoVisual({
        id: this.id,
        itemNo: this.itemNo
      })
      data = res.data || {}
    } else {
      let res = await assetsInfoVisualEwm({
        itemNo: this.itemNo,
        orgCode: this.orgCode
      })
      data = res.data || {}
    }

    this.detailData = data || {}
  }
}
</script>

<style scoped lang="scss">
.situation-lease-wrap {
  $zz: #e6a23c;
  $cz: #409eff;
  $dq: #fb3f3f;
  $kz: #a6a6a6;

  .zz {
    background: rgba($color: $zz, $alpha: 0.3);
  }
  .cz {
    background: rgba($color: $cz, $alpha: 0.3);
  }
  .dq {
    background: rgba($color: $dq, $alpha: 0.3);
  }
  .kz {
    background: rgba($color: $kz, $alpha: 0.3);
  }

  position: relative;
  .header-box {
    display: flex;
    align-content: flex-start;
    justify-content: space-between;
    padding: 14px;
    border-radius: 4px;
    margin-bottom: 16px;
    border: 1px solid #e9e9e9;
    .mode {
      flex: 1;
      margin-right: 20px;
      &:nth-last-child(1) {
        margin-right: 0;
      }
    }
    .til {
      display: flex;
      align-items: center;
      margin-bottom: 6px;
      span {
        margin-right: 4px;
      }
      i {
        font-size: 16px;
      }
    }
    .num {
      padding-bottom: 6px;
      margin-bottom: 10px;
      border-bottom: 1px solid #e9e9e9;
      span {
        font-size: 24px;
        margin-right: 4px;
      }
      i {
        font-size: 16px;
      }
      .lv {
        font-size: 12px;
        margin-left: 10px;
      }
    }
    .total {
      font-size: 13px;
    }
  }

  .rooms-box {
    .illustrate-box {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      .mode {
        display: flex;
        align-items: center;
        margin-right: 30px;

        i {
          width: 14px;
          height: 14px;
          border-radius: 50%;
          margin-right: 4px;
        }
      }
    }
    .floor-box {
      max-height: 400px;
      overflow-y: auto;
      position: relative;
      .mode {
        $pd: 100px;
        display: flex;
        align-items: flex-start;
        position: relative;
        margin-bottom: 4px;
        .til {
          display: flex;
          flex-direction: column;
          justify-content: center;
          width: $pd;
          height: 56px;
          margin-right: 8px;
          text-align: center;
          box-sizing: border-box;
          background: rgba($color: #d3d3d3, $alpha: 0.2);
          border: 1px solid rgba($color: #d3d3d3, $alpha: 0.6);
          strong {
            font-size: 16px;
            font-weight: 500;
          }
          span {
            color: #999;
          }
        }
        .floor {
          flex: 1;
          display: flex;
          flex-wrap: wrap;
          .room {
            display: inline-block;
            vertical-align: middle;
            min-width: 200px;
            height: 56px;
            margin-right: -1px;
            margin-bottom: 4px;
            box-sizing: border-box;
            &:nth-child(6n) {
              margin-right: 0;
            }
            .loom {
              text-align: center;
              width: 100%;
              height: 100%;
              padding: 4px 4px 2px;
              line-height: 16px;
              box-sizing: border-box;
              display: flex;
              align-items: center;
              flex-direction: column;
              justify-content: space-between;
            }
            strong {
              width: 100%;
              height: 16px;
              font-size: 13px;
              font-weight: normal;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
            span,
            i {
              color: #999;
              font-size: 12px;
            }
          }

          .zz {
            background: rgba($color: $zz, $alpha: 0.2);
            border: 1px solid rgba($color: $zz, $alpha: 0.4);
            span,
            i {
              color: rgba($color: $zz, $alpha: 0.7);
            }
          }
          .cz {
            background: rgba($color: $cz, $alpha: 0.2);
            border: 1px solid rgba($color: $cz, $alpha: 0.4);
            span,
            i {
              color: rgba($color: $cz, $alpha: 0.7);
            }
          }
          .dq {
            background: rgba($color: $dq, $alpha: 0.2);
            border: 1px solid rgba($color: $dq, $alpha: 0.4);
            span,
            i {
              color: rgba($color: $dq, $alpha: 0.7);
            }
          }
          .kz {
            background: rgba($color: $kz, $alpha: 0.2);
            border: 1px solid rgba($color: $kz, $alpha: 0.4);
            span,
            i {
              color: rgba($color: $kz, $alpha: 0.7);
            }
          }
        }
      }
    }
  }
}
</style>
