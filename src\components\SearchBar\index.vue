<template>
  <div class="search-bar">
    <div class="search-item"
      v-for="(item, index) of items"
      :key="index">
      <div v-if="item.label"
        class="search-item-label">
        {{ item.label }}
      </div>

      <div v-if="item.type === 'tab'"
        class="tab">
        <span v-for="(tabItem, index) of item.options"
          :key="index"
          :class="{ active: tabItem.selected }"
          @click="handleTabClick(item, tabItem)">
          <el-badge :is-dot="tabItem.isDot"
            class="badge">{{ tabItem.label }}</el-badge>
          <i v-if="tabItem.dot" />
        </span>
      </div>

      <el-select v-else-if="item.type === 'select' && !isHidden(item)"
        v-model="item.value"
        :label="item.label || 'label'"
        filterable
        :style="`width: ${item.width}`"
        :multiple="item.multiple"
        :collapse-tags="item.collapseTags"
        :clearable="typeof item.clearable === 'boolean' ? item.clearable : true"
        class="search-select"
        :class="item.cls"
        :has-all="typeof item.hasAll === 'boolean' ? item.hasAll : true"
        :no-data-text="item.noDataText"
        :placeholder="item.placeholder"
        :disabled="item.disabled"
        @change="handleSelectChange(item)">
        <el-option v-for="item in item.options"
          :key="item.value"
          :label="item.label"
          :value="item.value" />
      </el-select>

      <el-date-picker v-else-if="item.type === 'daterange'"
        v-model="item.value"
        class="search-date-picker"
        type="daterange"
        unlink-panels
        range-separator="至"
        value-format="yyyy-MM-dd"
        :clearable="item.clearable === undefined ? true : item.clearable"
        :start-placeholder="item.placeholder[0]"
        :end-placeholder="item.placeholder[1]"
        @change="handleDateRangeChange(item)" />

      <el-date-picker v-else-if="item.type === 'year'"
        v-model="item.value"
        class="search-date-picker year"
        type="year"
        :disabled="item.disabled"
        value-format="yyyy"
        :clearable="item.clearable === undefined ? true : item.clearable"
        :placeholder="item.placeholder"
        @change="handleDateRangeChange(item)" />

      <el-date-picker v-else-if="item.type === 'date'"
        v-model="item.value"
        class="search-date-picker year"
        type="date"
        value-format="yyyy-MM-dd"
        :clearable="item.clearable === undefined ? true : item.clearable"
        :placeholder="item.placeholder"
        @change="handleDateRangeChange(item)" />

      <el-menu v-else-if="item.type === 'menu'"
        menu-trigger="hover"
        class="search-menu"
        mode="horizontal"
        :collapse-transition="false">
        <el-submenu popper-class="search-menu-popper"
          index="1"
          :popper-append-to-body="true">
          <template slot="title">
            <span>
              {{ item.default ? getMenuDefaultLabel(item) : item.label || item.placeholder }}
            </span>
          </template>
          <el-menu-item @click.native="
              handleMenuChange({
                label: item.placeholder,
                key: item.key,
                childKeys: item.childKeys,
                value: -1,
                item
              })
            ">
            全部
          </el-menu-item>
          <template v-for="(menuItem, menuIndex) of item.options">
            <el-menu-item v-if="menuItem.children"
              :key="menuItem.value"
              :index="'index-' + menuIndex"
              @click.native="
                handleMenuChange({
                  label: menuItem.label,
                  key: item.key,
                  value: menuItem.value,
                  item
                })
              ">
              <el-submenu :index="'sub' + menuIndex"
                @click.native="
                  handleMenuChange({
                    label: menuItem.label,
                    key: item.key,
                    childKeys: item.childKeys,
                    value: menuItem.value,
                    item
                  })
                ">
                <template slot="title">{{ menuItem.label }}</template>
                <el-menu-item v-for="(subItem, subIndex) of menuItem.children"
                  :key="subItem.value"
                  :index="'index-' + index + '-' + subIndex"
                  @click.native="
                    handleMenuChange({
                      label: subItem.label,
                      key: menuItem.key,
                      parentKey: item.key,
                      value: subItem.value,
                      parentValue: menuItem.value,
                      item,
                      childKeys: item.childKeys
                    })
                  ">
                  {{ subItem.label }}
                </el-menu-item>
              </el-submenu>
            </el-menu-item>
            <el-menu-item v-else
              :key="menuItem.value"
              :index="'index-' + menuIndex"
              @click.native="
                handleMenuChange({
                  label: menuItem.label,
                  key: item.key,
                  value: menuItem.value,
                  item,
                  childKeys: item.childKeys
                })
              ">
              {{ menuItem.label }}
            </el-menu-item>
          </template>
        </el-submenu>
      </el-menu>

      <el-cascader v-else-if="item.type === 'cascader'"
        ref="commonCascader"
        clearable
        v-model="commonCascaderValue"
        class="search-cascader search-select search-common-cascader"
        popper-class="search-popper-cascader"
        :show-all-levels="item.showAllLevels"
        :placeholder="item.placeholder"
        :props="item.props || { expandTrigger: 'hover', label: 'text' }"
        :options="item.options"
        @mouseenter.native="handleCascaderShow('commonCascader', '.search-popper-cascader')"
        @mouseleave.native="handleCascaderHide('commonCascader', '.search-popper-cascader')"
        @change="handleCascaderChange(item.key)" />

      <el-input v-else-if="item.type === 'text'"
        v-model.trim="item.value"
        clearable
        class="search-ipt"
        :class="{ 'is-focus': isTextSearchFocus, 'search-ipt-option': item.options }"
        :placeholder="item.placeholder"
        :style="{ width: item.width ? item.width : '' }"
        :maxlength="item.maxlength || 200"
        @change="handleTextSearch(item.key, item)"
        @focus="isTextSearchFocus = true"
        @blur="isTextSearchFocus = false">
        <el-select v-if="item.options"
          class="search-select search-input-select"
          v-model="item.optionValue"
          filterable
          slot="prepend"
          :placeholder="item.selectPlaceholder ? item.selectPlaceholder : '不限'"
          @change="handleTextSelectChange(item)">
          <el-option v-if="!item.selectPlaceholder"
            key="0"
            label="不限"
            :value="0" />
          <el-option v-for="optionItem of item.options"
            :key="optionItem.key"
            :label="optionItem.label || optionItem.text"
            :value="optionItem.value" />
        </el-select>
        <el-button slot="append"
          icon="el-icon-search" />
      </el-input>
    </div>

    <slot name="orgtree" />

    <div v-if="isReset"
      class="search-btn">
      <!-- <el-button type="primary"
        icon="el-icon-search"
        @click="onSearch">搜索</el-button> -->
      <el-button icon="el-icon-delete"
        @click="reset">清空</el-button>
    </div>

    <div class="search-slot">
      <slot />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Emit } from 'vue-property-decorator'

interface SearchItem {
  key: string | Array<string>
  placeholder: string
  oldPlaceHolder?: string
  default: string | number
  label: string
  value?: string | number
  optionValue: string | number
  optionKey: string
  options: Array<{
    value: number | string
    key: string
    label: string
  }>
}

@Component
export default class extends Vue {
  @Prop() private items!: Array<SearchItem>
  @Prop({ default: true }) private isReset?: boolean

  @Emit('onSearch') // 触发搜索
  private onSearch() {
    return this.condition
  }

  private condition: Record<string, any> = {}
  private searchText = ''
  private namephone = ''
  private isTextSearchFocus = false
  private communityName = ''
  private timer: any = null
  private commonCascaderValue = []

  mounted() {
    this.items &&
      this.items.forEach((item) => {
        if (item.default !== undefined) {
          this.condition[`${item.key}`] = item.default
        }
      })
  }

  private handleTabClick(item: { key: string; type: string; options: Array<{ selected: boolean }> }, tabItem: { value: string }) {
    let curSelItem = item.options.find((o) => {
      return o.selected
    })

    this.$set(curSelItem as object, 'selected', false)
    this.$set(tabItem, 'selected', true)
    Object.assign(this.condition, { [item.key]: tabItem.value })
    if (this.$attrs['tabChange']) {
      this.searchText = ''
      this.items &&
        this.items.forEach((item: any) => {
          if (item.type === 'text') {
            this.condition[`${item.key}`] = item.default || ''
          }
        })

      if (item.type === 'tab') {
        setTimeout(() => {
          this.$bus.$emit('onSearchBarTab', tabItem)
        }, 30)
      }
    }

    this.onSearch()
  }

  private handlePopperMouseLeave(refName: string, popperCls: string) {
    this.timer && clearTimeout(this.timer)
    try {
      this.$refs[refName] && (this.$refs[refName] as any[])[0].toggleDropDownVisible(false)
    } catch (e) {
      console.error(e)
    }

    this.handleRemovePopperEvent(refName, popperCls)
  }

  private handlePopperMouseEnter() {
    this.timer && clearTimeout(this.timer)
  }

  private handleRemovePopperEvent(refName: string, popperCls: string) {
    let popper = document.querySelector(popperCls) as HTMLElement
    popper.removeEventListener('mouseleave', this.handlePopperMouseLeave.bind(this, refName, popperCls))
    popper.removeEventListener('mouseenter', this.handlePopperMouseEnter)
  }

  private handleCascaderShow(refName: string, popperCls: string) {
    this.timer && clearTimeout(this.timer)
    try {
      this.$refs[refName] && (this.$refs[refName] as any[])[0].toggleDropDownVisible(true)
    } catch (e) {
      // console.error(e)
    }
    let popper = document.querySelector(popperCls) as HTMLElement
    popper.addEventListener('mouseleave', this.handlePopperMouseLeave.bind(this, refName, popperCls))
    popper.addEventListener('mouseenter', this.handlePopperMouseEnter)
  }

  private handleCascaderHide(refName: string, popperCls: string, timeout = 300) {
    this.timer = setTimeout(() => {
      try {
        this.$refs[refName] && (this.$refs[refName] as any[])[0].toggleDropDownVisible(false)
      } catch (e) {
        console.error(e)
      }
      this.handleRemovePopperEvent(refName, popperCls)
    }, timeout)
  }

  private handleSelectChange(item: { key: string; value: number | string }) {
    if (+item.value === 9999) {
      delete this.condition[item.key]
      this.$set(item, 'value', '')
    } else {
      Object.assign(this.condition, { [item.key]: item.value })
    }
    this.onSearch()
  }

  private handleDateRangeChange(item: { value: Array<string> | string; key: Array<string> | string }) {
    if (item.value) {
      if (typeof item.value === 'object') {
        if (Object.prototype.toString.call(item.key) === '[object String]') {
          Object.assign(this.condition, {
            [item.key as string]: item.value
          })
        } else {
          for (let index in item.value) {
            Object.assign(this.condition, {
              [item.key[index]]: item.value[index]
            })
          }
        }
      } else {
        Object.assign(this.condition, {
          [item.key as string]: item.value
        })
      }
    } else {
      if (typeof item.key === 'object') {
        for (let index in item.key) {
          delete this.condition[item.key[index]]
        }
      } else {
        delete this.condition[item.key]
      }
    }
    this.onSearch()
  }

  private handleCommunityChange(key: string) {
    Object.assign(this.condition, { [key]: this.communityName })
    this.onSearch()
  }

  private handleTextSearch(key: string, { value }: any) {
    Object.assign(this.condition, { [key]: value })
    this.onSearch()
  }

  private handleInputTextSearch(key: string) {
    Object.assign(this.condition, { [key]: this.namephone })
    this.onSearch()
  }

  // 相应搜索框筛选条件变化
  private handleTextSelectChange(item: SearchItem) {
    if (!item.oldPlaceHolder) {
      item.oldPlaceHolder = item.placeholder
    }
    let placeholder = ''
    if (item.optionValue) {
      let option = item.options.find((oItem) => {
        return item.optionValue === oItem.value
      })
      placeholder = option ? `搜索${option.label}` : ''
    } else if (item.oldPlaceHolder) {
      placeholder = item.oldPlaceHolder
    }
    item.placeholder = placeholder
    this.searchText = ''
    Object.assign(this.condition, {
      [item.optionKey]: item.optionValue,
      [item.key as string]: ''
    })
    if (!item.optionValue) {
      this.onSearch()
    }
  }

  private handleMenuChange(menu: {
    key: string
    parentKey: string
    label: string
    value: number
    parentValue: number
    item: SearchItem
    childKeys: Array<string>
    default: any
  }) {
    // 用户一旦更改筛选条件，则清空默认值
    delete menu.default
    for (let childKey of menu.childKeys) {
      delete this.condition[childKey]
    }
    if (menu.value === -1) {
      delete this.condition[menu.key]
    } else {
      Object.assign(this.condition, {
        [menu.key]: menu.value
      })
    }
    if (menu.parentKey) {
      Object.assign(this.condition, {
        [menu.parentKey]: menu.parentValue
      })
    }
    this.$set(menu.item, 'label', menu.label)
    this.onSearch()
  }

  private getMenuDefaultLabel(item: { label: string; placeholder: string; default: any; options: Array<{ value: any; label: string }> }) {
    if (!item.options) {
      return item.label || item.placeholder
    }
    let target = item.options.find((optionItem) => {
      return optionItem.value === item.default
    })
    return target ? target.label : item.label || item.placeholder
  }

  private handleCascaderChange(keys: Array<string> | string) {
    if (typeof keys === 'string') {
      this.condition[keys] = this.commonCascaderValue[this.commonCascaderValue.length - 1]
      this.handleCascaderHide('commonCascader', '.search-popper-cascader', 0)
    } else {
      keys.map((item, index) => {
        this.condition[item] = this.commonCascaderValue[index]
      })
    }
    this.onSearch()
  }

  // 重置数据，并通知父组件
  public reset() {
    this.resetData()
    this.onSearch()
  }

  // 重置数据
  public resetData() {
    let items = this.items
    for (let item of this.items) {
      item.value = ''
      delete item.value
    }
    this.searchText = ''
    this.condition = {}
  }

  private isHidden(item: any): boolean {
    if (typeof item.hidden === 'function') {
      return item.hidden()
    }
    return !!item.hidden
  }
}
</script>

<style lang="scss" scoped>
.search-bar {
  background: #f0f2f5;
  display: flex;
  margin-bottom: 10px;
  height: 32px;

  .badge {
    width: 100%;
    ::v-deep .is-dot {
      top: 6px;
    }
  }

  .search-item {
    height: 100%;
    margin-right: 10px;
    display: flex;
    align-items: center;
    .search-item-label {
      margin-right: 8px;
      margin-left: 12px;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      color: #909399;
    }
    .tab {
      background: #fff;
      border-radius: 2px;

      span {
        display: inline-block;
        font-size: 14px;
        min-width: 44px;
        padding: 0 10px;
        text-align: center;
        line-height: 32px;
        cursor: pointer;
        position: relative;
        border-right: 1px solid #eff1f4;
        font-size: 12px;

        &.active {
          color: #b43c3c;
        }

        &:hover {
          background-color: #e3ecff;
          color: #b43c3c;
        }

        &:last-child {
          border: none;
        }

        i {
          display: inline-block;
          width: 6px;
          height: 6px;
          position: absolute;
          top: 4px;
          right: 5px;
          background-color: #fb3f3f;
          border-radius: 50%;
        }
      }
    }

    .el-select {
      width: 100px;
      position: relative;
    }

    .search-ipt {
      min-width: 160px;
      max-width: 480px;
      box-sizing: border-box;
      .el-input-group__append {
        border: none;
      }
    }

    .el-autocomplete {
      width: 100px;
    }
  }

  .search-btn {
    margin-left: 12px;
  }

  .search-slot {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
  ::v-deep .el-input-group__append {
    .el-button {
      background: #fff;
      color: #4680ff;
      border: none;
      padding: 9px 12px;
      margin-left: 10px;
    }
  }

  .search-menu {
    width: 100px;
  }

  .search-cascader {
    width: 100px;
    height: 32px;
  }
}
</style>

<style lang="scss">
.search-bar {
  .el-input__inner {
    border: none !important;
    height: 32px;
    color: #909399;
    font-size: 12px;
    padding-left: 15px;
    padding-right: 15px;
  }

  .el-input__icon {
    width: 14px;
  }

  input::-webkit-input-placeholder {
    color: #909399;
  }

  .search-ipt {
    .el-input__inner {
      height: 32px;
    }
    .el-input-group__append {
      border: none;
      background: #fff;
      padding-left: 0;
      overflow: hidden;

      .el-button {
        padding: 8px 15px 8px 0;
      }

      .el-icon-search {
        color: #909399;
      }
    }

    .el-input-group__prepend {
      border-radius: 2px;
      background: #fff;
      color: #8c8ea4;
      border: none;
      width: 70px;
      padding: 0;

      .el-select {
        margin: 0;
      }

      .el-input__inner {
        padding-right: 10px;
      }
    }

    &.is-focus {
      // border: 1px solid #4680ff;
      .el-icon-search {
        color: #4680ff;
      }
    }

    .search-input-select {
      width: 90px !important;
    }

    .el-input__suffix {
      display: flex;
      align-items: center;
    }
  }

  .search-ipt-option {
    .el-input__inner {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }

    .el-select {
      .el-input__inner {
        padding-left: 15px !important;
        padding-right: 15px !important;
      }
    }
  }

  .search-slot {
    img {
      width: 12px;
      height: 12px;
      margin-right: 6px;
      vertical-align: middle;
    }
  }

  .search-date-picker {
    width: 190px;
    &.year {
      width: 100px;
    }
    .el-icon-date {
      display: none;
    }

    .el-range__close-icon {
      width: 20px;
      position: absolute;
      right: 0px;
      top: 2px;
      color: #909399;
    }

    .el-range-separator {
      color: #909399;
      line-height: 26px;
      font-size: 12px;
      margin: 0 2px;
      padding: 0;
      width: 10px;
    }

    .el-range-input {
      width: 50%;
      text-align: left;
      font-size: 12px;

      &::-webkit-input-placeholder {
        color: #909399;
      }
    }

    .el-range-input:last-of-type {
      text-align: right;
      padding-right: 5px;
    }
  }

  .el-popper {
    min-width: 100px;
    width: auto !important;
  }

  .search-menu {
    .el-submenu {
      width: 100%;
      &:hover {
        .el-submenu__title {
          color: #909399;
        }
      }
    }
    .el-submenu__title {
      height: 32px;
      line-height: 32px;
      font-size: 12px;
      padding: 0 15px;
      position: relative;
      border-bottom: none;
      span {
        display: inline-block;
        width: 52px;
      }
    }

    .el-submenu__icon-arrow {
      margin-top: 0px;
      // margin-left: -2px;
      &::before {
        content: '\e790';
      }
    }
  }
  .el-submenu.is-active .el-submenu__title,
  .el-menu.el-menu--horizontal {
    border: none;
  }
  .el-menu--horizontal > .el-submenu.is-active .el-submenu__title {
    color: #909399;
  }
  .el-select-dropdown {
    margin: 0;
    .el-select-dropdown__item {
      font-size: 12px;
      line-height: 30px;
      height: 30px;
    }
  }
  .popper__arrow {
    display: none;
  }

  .search-cascader {
    .el-icon-arrow-down::before {
      border-top: 4px solid #909399;
      border-bottom: none !important;
    }
  }

  .search-common-cascader {
    .el-input__suffix {
      .el-icon-circle-close {
        position: relative;
        &::before {
          content: '\E78D';
          border-bottom: none;
          margin: 4px 0 0 0;
        }
      }
    }
  }
}

.search-menu-popper {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .el-menu--popup {
    min-width: 80px;
    .el-menu--popup {
      max-height: 300px;
      overflow-y: auto;
    }
    .el-menu-item {
      text-align: center;
      height: 30px;
      line-height: 30px;
      font-size: 12px;
      &:hover {
        color: #1f66ff;
      }
      &.is-active {
        color: #909399;
      }
    }
  }
  .el-menu--popup-bottom-start {
    margin-top: 0;
  }
  .el-submenu.is-active > .el-submenu__title {
    color: #909399 !important;
  }

  .el-submenu__icon-arrow {
    right: -4px;
    margin-top: -6px;
  }

  .el-submenu {
    .el-menu-item {
      min-width: 80px;
    }

    .el-menu {
      margin-left: 10px;
    }
  }

  .el-submenu__title {
    font-size: 12px;
    height: 30px !important;
    line-height: 30px !important;
  }
}

.search-popper-cascader {
  .el-cascader-node {
    font-size: 12px;
  }
  .el-cascader-menu {
    min-width: 118px;
  }
  .el-cascader-node__label {
    padding: 0;
  }
  .el-cascader-node.in-active-path {
    color: #999;
  }
  .el-radio {
    margin-right: 5px;
  }
}
</style>
