<template>
  <Dialog title="PPT"
    width="800px"
    :visible="visible"
    :close-on-click-modal="true"
    :destroy-on-close="true"
    :append-to-body="true"
    @close="handleClose">
    <div slot="body">
      <iframe scrolling="no"
        src="https://fh-ka.oss-cn-hangzhou.aliyuncs.com/金华市国资委监管系统20220802.pptx"
        frameborder="0"
        width="460px"
        height="40px"
        allowtransparency="true" />
    </div>

    <div slot="footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'

@Component({
  name: 'Login',
  components: {
    Dialog
  }
})
export default class Header extends Vue {
  @Prop() private visible!: boolean

  // 关闭弹窗
  private handleClose() {
    this.$emit('update:visible', false)
  }
}
</script>

<style scoped lang="scss">
.rule-form-box {
  .icon-user {
    font-size: 16px;
  }
}
</style>