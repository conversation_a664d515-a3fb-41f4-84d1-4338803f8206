<template>
  <div ref="AssetMapIframe"
    :class="['asset-map-wrapper', isFullScreen && 'asset-map-wrapper--full']">

    <!-- 除以1.6是应为地图太大拖动会卡顿，适当缩小地图大小，确保字可以看清即可 -->
    <!-- <iframe ref="Iframe"
      className="map_iframe"
      :style="{
        transform: `scale(${1 / cockpitRate / 1.3})`
      }"
      :src='`${host}#/assetmap`'
      frameBorder="0" /> -->

    <!-- 全屏按钮 -->
    <div @click="onFullScreen"
      :class="['full-screen', isFullScreen && 'full-screen--full']">
      <img
        :src="isFullScreen ? require('../images/exitfullScreen.png') : require('../images/fullScreen.png')"
        alt="">
    </div>

    <!-- 搜索框 -->
    <div :class="['search', !isShowSearch && 'search--close', isFullScreen && 'search--full']">
      <div class="options-wrapper">
        <div class="input-wrapper">
          <input v-model="assetForm.keywork"
            class="tipinput"
            id='tipinput'
            placeholder="请输入资产名称"
            autocomplete="off"
            @focus="onInputFocus"
            @input="onInputChange"
            type="text">
          <el-button @click="onSearchShowClick"
            :icon="isShowSearch ? 'el-icon-caret-left' : 'el-icon-search'"></el-button>
        </div>
        <div class="options">
          <div class="item">
            <span>类型</span>
            <div class="">
              <el-checkbox-group v-model="assetForm.typeList"
                @change="onTypeChange">
                <el-checkbox :label="1">房产</el-checkbox>
                <el-checkbox :label="2">土地</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div class="item">
            <span>状态</span>
            <div class="">
              <el-checkbox-group v-model="assetForm.useStatusList">
                <el-checkbox :label="1">已出租</el-checkbox>
                <el-checkbox :label="2">闲置</el-checkbox>
                <el-checkbox :label="3">出借</el-checkbox>
                <el-checkbox :label="4">在用</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
      </div>
      <div v-if="isShowList && markerList.length"
        class="result-list"
        :style="{
          height: `${listHeight}px`
        }">
        <div v-for="(item, index) in markerList"
          :key="`${item.id}${index}`"
          @click="onAddressClick(item)"
          class="result-list__item"
          v-html="getReslutHtml(item.name)">
        </div>
      </div>
    </div>

    <!-- 弹框 -->
    <Dialog class="ichnography-dialog"
      title="房产详情"
      width="100vw"
      height="100vh"
      :fullscreen="true"
      :visible="dialogVisible"
      :close-on-click-modal="true"
      :destroy-on-close="true"
      :append-to-body="true"
      @close="dialogVisible = false">
      <div slot="body">
        <Ichnography :assetInfo="currentItem" />
      </div>
    </Dialog>
  </div>

</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import Ichnography from '@/views/main/cockpitSecondary/charts/Ichnography.vue'
import Dialog from '@/components/Dialog/index.vue'
import { companyList } from '../baseData'
import { MarkerItem } from './AssetMapIframe.vue'

@Component({
  components: {
    Ichnography,
    Dialog
  }
})
export default class extends Vue {
  @Watch('currentCompanyCode')
  private onCurrentCompanyCodeChange() {
    window.postMessage(
      {
        code: 'currentCompanyCode',
        data: this.currentCompanyCode
      },
      `*`
    )
  }

  @Watch('assetForm', { deep: true })
  private onAssetFormChange() {
    window.postMessage(
      {
        code: 'paramsChange',
        data: this.assetForm
      },
      `*`
    )
  }

  private dialogVisible = false
  private mapDialogVisible = false
  private isShowSearch = false
  private isShowList = false
  private isFullScreen = false
  private iframe: any
  // 默认放大3.5
  private cockpitRate = 0.285
  private currentItem: any = {}
  private companyList = companyList
  private markerList: MarkerItem[] = []
  private listHeight = 300
  private assetForm = {
    type: '',
    typeList: [],
    useStatusList: [],
    keywork: '',
    useStatus: '',
    raidus: 10000,
    lon: 119.654566,
    lat: 29.084632
  }

  get currentCompanyCode() {
    let id = this.$route.query ? this.$route.query.id || '' : ''
    return (this.companyList.find((company) => company.id === id) || { code: '' }).code
  }

  get host() {
    if (process.env.NODE_ENV !== 'development') {
      let hrefArr = window.location.href.split('#')
      return hrefArr[0]
    } else {
      return 'http://' + window.location.host + '/'
    }
  }

  get getReslutHtml() {
    return (name: string) => {
      let reg = new RegExp(this.assetForm.keywork)
      return name.replace(reg, ($1) => {
        return `<span style="color: rgba(255, 192, 22, 1);">${$1}</span>`
      })
    }
  }

  mounted() {
    this.iframe = this.$refs.Iframe
    let wrapperDom = this.$refs.AssetMapIframe as HTMLDListElement
    if (wrapperDom) {
      this.listHeight = wrapperDom.clientHeight - 460
    }
  }

  created() {
    this.$bus.$on('cockpitRateChange', (rate: number) => {
      this.cockpitRate = rate
    })
    this.$bus.$on('IchnographyDialog', () => {
      this.dialogVisible = true
    })

    // 添加失焦事件
    window.addEventListener('click', (e: Event) => {
      let target = e.target as HTMLElement
      this.isShowList = !!(target.classList.contains('result-list__item') || target.classList.contains('tipinput'))
    })

    // 监听 iframe
    window.addEventListener('message', (e: any) => {
      let res = e.data
      switch (res.code) {
        case 'Ichnography':
          this.dialogVisible = true
          this.currentItem = res.data
          break
        case 'MarkerList':
          this.markerList = res.data || []
          break
      }
    })
  }

  private onFullScreen() {
    this.isFullScreen = !this.isFullScreen
    this.$bus.$emit('AssetMapFullScreen', this.isFullScreen)
  }

  // 点击 marker
  private onAddressClick(item: MarkerItem) {
    this.isShowList = false
    this.currentItem = item
    this.assetForm.keywork = item.address
    window.postMessage(
      {
        code: 'markerClick',
        data: this.currentItem
      },
      `*`
    )
  }

  private handleClose() {
    this.dialogVisible = false
  }

  private onInputFocus() {
    this.isShowList = true
  }

  private onInputChange(e: InputEvent) {
    this.assetForm.keywork = (e.target as HTMLInputElement).value
  }

  private onSearchShowClick() {
    this.isShowSearch = !this.isShowSearch
  }

  private onTypeChange() {
    if (this.assetForm.typeList.length > 1) {
      this.assetForm.typeList = [this.assetForm.typeList[this.assetForm.typeList.length - 1]]
    }
    this.assetForm.type = this.assetForm.typeList[0]

    if (this.assetForm.useStatusList.length > 1) {
      this.assetForm.useStatusList = [this.assetForm.useStatusList[this.assetForm.useStatusList.length - 1]]
    }
    this.assetForm.useStatus = this.assetForm.useStatusList[0]
  }
}
</script>


<style scoped lang="scss">
.asset-map-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  border-radius: 20px;
  background: rgba(59, 100, 184, 0.2);
}
iframe {
  width: 100%;
  height: 100%;
  transform: scale(3.5);
}

.search {
  position: absolute;
  top: 20px;
  left: 20px;
  padding: 8px;
  border-radius: 8px;
  &--full {
    top: 100px;
    left: 35px;
    .result-list {
      height: 1200px !important;
    }
  }
  // &--full.search--close {
  //   top: 38%;
  //   left: 35.6%;
  // }
  &--close {
    width: 78px;
    height: 78px;
    padding: 0;
    .options-wrapper {
      padding: 0 !important;
      .options {
        display: none;
      }
      .input-wrapper {
        padding: 0;
        input {
          display: none;
        }
        ::v-deep .el-button {
          padding: 0;
          margin-left: 0;
        }
      }
    }
  }
  .options-wrapper {
    position: relative;
    background: rgba($color: #0f307988, $alpha: 1);
    box-shadow: 0px 0px 20px 10px inset rgba(59, 100, 184, 1);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    padding: 10px;
    .options {
      .item {
        display: flex;
        font-size: 38px;
        width: 480px;
        margin-left: 20px;
        & > span {
          display: block;
          line-height: 80px;
          white-space: nowrap;
          color: rgba(74, 151, 248, 1);
        }
        div {
          display: flex;
          flex-wrap: wrap;
          ::v-deep .el-checkbox__label,
          ::v-deep .el-radio__label {
            width: 150px;
            font-size: 38px;
            line-height: 80px;
            color: rgba(74, 151, 248, 1);
            padding-left: 10px !important;
            font-weight: bold;
          }
          ::v-deep .el-checkbox-group,
          ::v-deep .el-radio-group {
            margin-left: 20px !important;
          }
          ::v-deep .el-radio__inner {
            width: 30px;
            height: 30px;
          }
          ::v-deep .el-radio,
          ::v-deep .el-checkbox {
            display: flex;
            align-items: center;
            height: 80px;
            margin-right: 0;
            margin-bottom: 2px;
            .el-checkbox__inner {
              width: 30px;
              height: 30px;
            }
            &.is-checked {
              .el-radio__label,
              .el-checkbox__label {
                color: rgba(255, 192, 22, 1) !important;
              }
              .el-checkbox__inner {
                background-color: rgba(255, 192, 22, 1);
                border-color: rgba(255, 192, 22, 1);
                color: rgba(255, 192, 22, 1) !important;
              }
            }
          }
        }
      }
    }
  }
  .input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    padding: 10px;
    .icon {
      position: absolute;
      font-size: 38px;
      color: #fff;
      top: 50%;
      left: 20px;
      transform: translateY(-50%);
    }
    ::v-deep .el-button {
      height: 80px;
      width: 80px;
      font-size: 38px;
      line-height: 80px;
      background: rgba(6, 16, 50, 0.32);
      border: 1px solid #264071;
      color: #fff;
      font-weight: bold;
      box-sizing: border-box;
      padding: 0;
      margin-left: 2px;
      text-align: center;
      cursor: pointer;
    }
    input {
      box-sizing: border-box;
      width: 400px;
      height: 80px;
      line-height: 80px;
      font-size: 38px;
      padding: 0 10px 0 20px;
      font-weight: bold;
      // color: rgba(74, 151, 248, 1);
      color: #fff;
      background: rgba(6, 16, 50, 0.32);
      border: 1px solid #264071;
      border-radius: 8px;

      &:focus {
        border-color: #0f3079;
        outline: none;
      }
      &::placeholder {
        // color: rgba(74, 151, 248, 0.6);
        color: #ffffff88;
        font-size: 38px;
        line-height: 80px;
      }
    }
  }
  .result-list {
    position: absolute;
    box-shadow: 0px 0px 20px 10px inset rgba(59, 100, 184, 1);
    border-radius: 2px;
    background: #0f307988;
    box-sizing: border-box;
    height: 300px;
    overflow-y: auto;
    overflow-x: hidden;
    color: rgba(74, 151, 248, 1);
    margin-top: 12px;
    border: 1px solid #264071;
    font-size: 38px;
    line-height: 80px;
    // background: rgba(6, 16, 50, 0.61);
    // 滚动条
    &::-webkit-scrollbar-track-piece {
      background: #d3dce633;
    }

    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
      display: none;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf44;
      border-radius: 4px;
    }
    &::-webkit-scrollbar-thumb:hover {
      background: #99a9bfaa;
    }
    &__item {
      width: 518px;
      line-height: 80px;
      padding: 0 16px;
      box-sizing: border-box;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: rgba(143, 193, 255, 1);
      cursor: pointer;
      span {
        color: rgba(255, 192, 22, 1);
      }
    }
  }
}

.full-screen {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 78px;
  height: 78px;
  box-sizing: border-box;
  top: 20px;
  right: 20px;
  padding: 7px;
  border-radius: 7px;
  box-shadow: 0px 0px 20px 10px inset rgba(59, 100, 184, 0.8);
  cursor: pointer;
  color: #fff;
  &--full {
    top: 100px;
    right: 35px;
  }
  img {
    width: 80%;
    height: 80%;
  }
}
</style>