<template>
  <div class="graph-amount">
    <graph-title title="集团预警数量排行(Top 10)" :is-rect="true" />
    <div ref="graph" class="graph"></div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import * as echarts from 'echarts'
import GraphTitle from './GraphTitle.vue'

@Component({
  components: {
    GraphTitle
  }
})
export default class PrewarningAmount extends Vue {
  private async mounted() {
    await this.init()
  }
  private getChartData() {
    // 
  }
  private init() {
    let textSize = 12
    let textColor = '#aeaeae'
    let chart = echarts.init(this.$refs.graph as any)

    let option = {
      color: [
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: '#FF8888' // 0% 处的颜色
            },
            {
              offset: 1,
              color: '#FF8888' // 100% 处的颜色
            }
          ]
        },
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: '#FFD879' // 0% 处的颜色
            },
            {
              offset: 1,
              color: '#FFD879' // 100% 处的颜色
            }
          ]
        }
      ],
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#fff',
        textStyle: {
          color: '#2F3038',
          fontSize: textSize
        }
      },
      grid: {
        top: '8%',
        left: '0%',
        right: '6%',
        bottom: '0%',
        containLabel: true
      },
      xAxis: {
        show: true,
        type: 'value',
        axisLabel: {
          color: '#aeaeae',
          fontSize: textSize
        }
      },
      yAxis: {
        type: 'category',
        axisLabel: {
          color: '#aeaeae',
          fontSize: textSize
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        data: ['集团1', '集团2', '集团3', '集团4', '集团5', '集团6', '集团7', '集团8', '集团9', '集团10']
      },
      series: [
        {
          name: '黄色预警',
          type: 'bar',
          stack: 'total',
          barWidth: textSize * 1,
          label: {
            show: true,
            fontSize: textSize
          },
          emphasis: {
            focus: 'series'
          },
          itemStyle: {},
          showBackground: true,
          backgroundStyle: {
            color: '#fff'
          },
          data: [200, 300, 400, 500, 600, 700, 600, 500, 400, 300]
        },
        {
          name: '红色预警',
          type: 'bar',
          stack: 'total',
          barWidth: textSize,
          label: {
            show: true,
            fontSize: textSize
          },
          emphasis: {
            focus: 'series'
          },
          itemStyle: {},
          showBackground: true,
          backgroundStyle: {
            color: '#fff'
          },
          data: [100, 200, 300, 400, 500, 600, 500, 400, 300, 200]
        }
      ]
    }
    chart.setOption(option as any)
  }
}
</script>

<style lang="scss" scoped>
.graph-amount {
  background: #fff;
  border-radius: 4px;
  margin-left: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  .graph {
    flex: 1;
  }
}
</style>