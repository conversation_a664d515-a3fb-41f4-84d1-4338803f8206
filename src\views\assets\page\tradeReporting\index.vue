<template>
  <el-container class="table-wrapper" direction="vertical">
    <search-bar :items="searchItems"  @onSearch="handleSearch">
      <el-button type="primary" @click="onAdd">新增</el-button>
      <!-- <el-button type="primary"
        @click="onExport">导出</el-button>
      <el-button type="primary"
        @click="onImport">导出</el-button> -->
    </search-bar>

    <ListingList ref="TradeReportingGrid"  :params="searchParams" @detail="onSee" @reset="onReset"></ListingList>

    <!-- 新增 -->
    <AddTradeDialog v-if="addTradeDialogVisible" :visible.sync="addTradeDialogVisible" />

    <!-- 重新发起 -->
    <AddTradeDialog v-if="resetTradeDialogVisible" :visible.sync="resetTradeDialogVisible" :assetId="currentAssetId" mode="reset" />

    <!-- 查看 -->
    <TradeDetailDialog
      :assetId="DetailassetId"
      :initData="currentRow"
      v-if="tradeDetailDialogVisible"
      :visible.sync="tradeDetailDialogVisible"
    />
  </el-container>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import SearchBar from '@/components/SearchBar/index.vue'
import ListingList from './components/ListingList.vue'
import AddTradeDialog from '../../components/TradeDialog/AddTradeDialog.vue'
import TradeDetailDialog from '../../components/TradeDialog/TradeDetailDialog.vue'
import { DetailAsset } from '@/api/assets'

@Component({
  components: { SearchBar, ListingList,  AddTradeDialog, TradeDetailDialog }
})
export default class extends Vue {
  @Watch("addTradeDialogVisible")
  private changeaddTradeDialogVisible(){
    if(this.addTradeDialogVisible==false){
       this.gridRefresh()
    }
  }
  @Watch("resetTradeDialogVisible")
  private changeresetTradeDialogVisible(){
    if(this.resetTradeDialogVisible==false){
       this.gridRefresh()
    }
  }
  private DetailassetId = ''
  private tabValue = 1
  private loading = false
  private addTradeDialogVisible = false
  private tradeDetailDialogVisible = false
  private resetTradeDialogVisible = false
  private currentAssetId = ''
  private currentRow = {}

  private searchParams = {
    type: 1,
    keyword:""
  }

  private searchItems = [
  
    {
      type: 'text',
      key: 'keyword',
      placeholder: '请输入批次号/申请人/坐落',
      width: `330px`
    }
  ]

  get componentName() {
    return this.searchParams.type === 1 ? 'ListingList' : 'HistoryList'
  }

  private async onSee(row: any) {
    this.DetailassetId = row.id
    this.tradeDetailDialogVisible = true
    // this.currentRow=row
    // try {
    //   let res = await DetailAsset({
    //     assetId:id
    //   })
    //   if (res.success) {
    //     this.currentRow = row
    //     this.tradeDetailDialogVisible = true
    //   }
    // } catch (e) {
    //   console.error(e)
    // }
  }

  private tabChange(tabValue: any) {
    this.tabValue = tabValue.value
  }

  private handleSearch(condition: any) {
    this.searchParams=Object.assign(this.searchParams,condition)
    
    this.gridRefresh()
  }

  private onAdd() {
    // 新增
    this.addTradeDialogVisible = true
  }

  // 重新发起
  private onReset(batchNo: string) {  
      
    this.currentAssetId = batchNo
    this.resetTradeDialogVisible = true
  }

  private onExport() {
    //
  }

  private onImport() {
    //
  }

  // 刷新
  private gridRefresh() {
    let grid = this.$refs['TradeReportingGrid'] as any
    grid.refresh()
  }
}
</script>

<style lang="scss" scoped>
.table-wrapper {
  height: 100%;
}
</style>