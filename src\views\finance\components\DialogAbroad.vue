// 捐赠弹窗
<template>
  <div>
    <Dialog
      :title="'企业对外捐赠事项备案-'+getmode"
      width="1100px"
      :visible="visible"
      :close-on-click-modal="true"
      :destroy-on-close="true"
      :append-to-body="true"
      v-loading="loading"
      @close="handleClose"
    >
      <div slot="body" class>
        <el-row :gutter="50">
          <el-form
            ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="80px"
            label-position="left"
            :disabled="mode=='see'?true:false"
          >
            <el-col :span="8">
              <el-form-item label="单据编号" prop="documentNo">
                <el-input
                  disabled
                  v-model="formData.documentNo"
                  placeholder="自动带入单据编号"
                  clearable
                  :style="{ width: '100%' }"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="填报单位" prop="reportDeptName">
                <el-input
                  v-model="formData.reportDeptName"
                  placeholder="请输入填报单位"
                  clearable
                  :style="{ width: '100%' }"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="文号" prop="referenceNo">
                <el-input
                  v-model="formData.referenceNo"
                  placeholder="请输入文号"
                  clearable
                  :style="{ width: '100%' }"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="签发人" prop="issuer">
                <el-input
                  v-model="formData.issuer"
                  placeholder="请输入签发人"
                  clearable
                  :style="{ width: '100%' }"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="拟捐对象" prop="preDonationTarget">
                <el-input
                  v-model="formData.preDonationTarget"
                  placeholder="请输入拟捐对象"
                  clearable
                  :style="{ width: '100%' }"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="拟捐依据" prop="preDonationBasis">
                <el-input
                  v-model="formData.preDonationBasis"
                  placeholder="请输入拟捐依据"
                  clearable
                  :style="{ width: '100%' }"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="拟捐理由" prop="donationReason">
                <el-input
                  v-model="formData.donationReason"
                  placeholder="请输入拟捐理由"
                  clearable
                  :style="{ width: '100%' }"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="拟捐项目" prop="donationProject">
                <el-input
                  v-model="formData.donationProject"
                  placeholder="请输入拟捐项目"
                  clearable
                  :style="{ width: '100%' }"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="捐赠金额" prop="donationAmount">
                <inputNumber
                  type="decimal"
                  v-model="formData.donationAmount"
                  placeholder="请输入捐赠金额"
                  clearable
                  :style="{ width: '100%' }"
                >
                  <span slot="append">元</span>
                </inputNumber>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="拟捐时间" prop="preDonationDate">
                <el-date-picker
                  v-model="formData.preDonationDate"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  :style="{ width: '100%' }"
                  placeholder="请输入拟捐时间"
                  clearable
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="拟捐数量" prop="preDonationNum">
                <el-input
                  v-model="formData.preDonationNum"
                  placeholder="请输入拟捐数量"
                  clearable
                  :style="{ width: '100%' }"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="捐赠形式" prop="donationForm">
                <el-select
                  v-model="formData.donationForm"
                  placeholder="请输入捐赠形式"
                  :style="{ width: '100%' }"
                >
                  <el-option
                    v-for="(item, index) in getDictData('issuance_currency')"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                    :disabled="item.disabled"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="捐赠次数" prop="donationTimes">
                <el-select
                  v-model="formData.donationTimes"
                  placeholder="请输入捐赠次数"
                  clearable
                  :style="{ width: '100%' }"
                >
                  <el-option
                    v-for="(item, index) in getDictData('donation_times_num')"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                    :disabled="item.disabled"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="捐赠用途" prop="donationPurpose">
                <el-input
                  v-model="formData.donationPurpose"
                  placeholder="请输入捐赠用途"
                  clearable
                  :style="{ width: '100%' }"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="列支途径" prop="disbursementWay">
                <el-select
                  v-model="formData.disbursementWay"
                  placeholder="请输入列支途径"
                  clearable
                  :style="{ width: '100%' }"
                >
                  <el-option
                    v-for="(item, index) in getDictData('disbursement_way')"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                    :disabled="item.disabled"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="联系人" prop="contact">
                <el-input
                  v-model="formData.contact"
                  placeholder="请输入联系人"
                  clearable
                  :style="{ width: '100%' }"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="联系电话" prop="contactPhone">
                <el-input
                  v-model="formData.contactPhone"
                  placeholder="请输入联系电话"
                  clearable
                  :style="{ width: '100%' }"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <AccessoryList
          v-model="formData.attachmentFileDTOList"
          dict="financial_foreignDonation_attach"
          title="附件列表"
          :mode="mode=='see'?'see':'upload'"
          class="m-20"
        />
      </div>
      <div slot="footer" :span="24">
        <el-button v-if="mode=='see'" @click="closeDialog">关闭</el-button>
        <el-button v-if="mode!='see'" @click="handleClose">取消</el-button>
        <el-button
          v-if="mode!='see'"
          @click="submitForm"
          type="primary"
        >{{ Diaformdata.id == '' ? '提交' : '确定修改' }}</el-button>
      </div>
    </Dialog>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import { serialNo, DeleteForeignDonation, DetailForeignDonation, AddForeignDonation } from '@/api/finance'
import AccessoryList, { Accessory } from './AccessoryFileList.vue'
import { Confirm } from '@/decorators/index'
import { getYear } from '@/utils/cache'
import inputNumber from '@/components/FormComment/inputNumber.vue'
import { BusinessModule } from '@/store/modules/businessDict'

@Component({
  components: {
    Dialog,
    AccessoryList,
    inputNumber
  }
})
export default class Container extends Vue {
  @Prop() private visible!: boolean
  @Prop() private Diaformdata!: any
  @Prop({ default: 'add' }) private mode!: 'see' | 'edit' | 'add'
  // 文件上传
  private uploaderDlgVisible = false
  private uploaderDlgTitle = '文件上传'
  private uploaderDlgMode = 'upload' //download  upload  see
  private uploadInitList: Array<string> = []
  private currentRow: any = {}
  private loading = false

  private rules: object = {
    documentNo: [
      {
        required: true,
        message: '自动带入单据编号',
        trigger: 'blur'
      }
    ],
    reportDeptName: [
      {
        required: true,
        message: '请输入填报单位',
        trigger: 'blur'
      }
    ],
    referenceNo: [
      {
        required: true,
        message: '请输入文号',
        trigger: 'blur'
      }
    ],
    issuer: [
      {
        required: true,
        message: '请输入签发人',
        trigger: 'blur'
      }
    ],
    preDonationTarget: [
      {
        required: true,
        message: '请输入拟捐对象',
        trigger: 'blur'
      }
    ],
    preDonationBasis: [
      {
        required: true,
        message: '请输入拟捐依据',
        trigger: 'blur'
      }
    ],
    donationReason: [
      {
        required: true,
        message: '请输入拟捐理由',
        trigger: 'blur'
      }
    ],
    donationProject: [
      {
        required: true,
        message: '请输入拟捐项目',
        trigger: 'blur'
      }
    ],
    donationAmount: [
      {
        required: true,
        message: '请输入拟捐金额',
        trigger: 'blur'
      }
    ],
    preDonationDate: [
      {
        required: true,
        message: '请输入拟捐时间',
        trigger: 'change'
      }
    ],
    preDonationNum: [
      {
        required: true,
        message: '请输入拟捐数量',
        trigger: 'blur'
      }
    ],
    donationForm: [
      {
        required: true,
        message: '请输入捐赠形式',
        trigger: 'change'
      }
    ],
    donationTimes: [
      {
        required: true,
        message: '请输入捐赠次数',
        trigger: 'blur'
      }
    ],
    donationPurpose: [
      {
        required: true,
        message: '请输入捐赠用途',
        trigger: 'blur'
      }
    ],
    disbursementWay: [
      {
        required: true,
        message: '请输入列支途径',
        trigger: 'change'
      }
    ],
    contact: [
      {
        required: true,
        message: '请输入联系人',
        trigger: 'blur'
      }
    ],
    contactPhone: [
      {
        required: true,
        message: '请输入联系电话',
        trigger: 'blur'
      },
      { pattern: /^1[3456789]\d{9}$/, message: '手机号格式不对', trigger: 'blur' }
    ],
    attachment1FileUrls: [
      {
        required: true,
        message: '请上传文件',
        trigger: 'blur'
      }
    ],
    attachment2FileUrls: [
      {
        required: true,
        message: '请上传文件',
        trigger: 'blur'
      }
    ]
  }
  //   private formData = this.Diaformdata
  private formData: any = {
    documentNo: '',
    reportDeptName: '',
    referenceNo: '',
    issuer: '',
    preDonationTarget: '',
    preDonationBasis: '',
    donationReason: '',
    donationProject: '',
    donationAmount: '',
    preDonationDate: '',
    preDonationNum: '',
    donationForm: '',
    donationTimes: '',
    donationPurpose: '',
    disbursementWay: '',
    contact: '',
    contactPhone: '',
    attachmentFileDTOList: []
  }
  private disbursementWay: object[] = [
    {
      label: '预算内',
      value: 1
    },
    {
      label: '预算外',
      value: 2
    }
  ]
  private donationTimes: object[] = [
    {
      label: '一次',
      value: 1
    },
    {
      label: '多次',
      value: 2
    }
  ]
  private donationForm: object[] = [
    {
      label: '人民币',
      value: 1
    },
    {
      label: '美元',
      value: 2
    },
    {
      label: '其他',
      value: 3
    }
  ]
  private accessoryList: Accessory[] = [
    {
      fileName: '董事会决议文件',
      isRequired: true,
      prop: 'attachmentFile1',
      fileList: []
    },
    {
      fileName: '捐赠请示文件',
      isRequired: true,
      prop: 'attachmentFile2',
      fileList: []
    }
  ]
  created() {
    if (this.mode == 'see' || this.mode == 'edit') {
      this.getdetail()
    } else {
      this.getSerialNo(3)
    }
  }

  get getmode() {
    switch (this.mode) {
      case 'see':
        return '查看'
      case 'edit':
        return '编辑'
      default:
        return '新增'
    }
  }
  // 打开弹窗

  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  // 获取编辑详情
  private async getdetail() {
    this.loading = true
    try {
      let res: any = await DetailForeignDonation({
        id: this.Diaformdata.id + ''
      })
      if (res.success) {
        res.data.year = res.data.year + ''
        res.data.guaranteePeriod = res.data.guaranteePeriod + ''
        this.formData = res.data
        this.loading = false
      }
    } catch (e) {
      console.error(e)
      this.loading = false
    }
  }
  private async getSerialNo(id: number) {
    this.loading=true;
    try {
      let res: any = await serialNo({
        index: id
      })
      this.formData.documentNo = res.data
    this.loading=false
    } catch (e) {
      this.$message.info('获取编号失败')
    this.loading=false
    }
  }

  get fileList() {
    return this.currentRow.fileList
  }
  @Confirm({
    title: '提示',
    content: `是否提交？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'success'
  })
  private async AddLarge() {
    try {
      let params: any = { ...this.formData }
      params.year = getYear()
      params.lendingPeriod = Number(params.lendingPeriod)
      let res = await AddForeignDonation(params)
      if (res.success) {
        this.$message.success('提交成功')
        this.$emit('changshowDialogAdd', false)
      }
    } catch (e) {
      console.error(e)
    }
  }
  private submitForm() {
    // 校验附件
    if (!this.validateFiles()) return
    ;(this.$refs['elForm'] as any).validate((valid: any) => {
      if (!valid) return
        if(this.formData.attachmentFileDTOList.length==0){
        this.$message.warning("请上传附件")
        return
      }
      // TODO 提交表单
      this.AddLarge()
    })
  }
  ///附件校验 转换
  private validateFiles(): boolean {
    return true
  }
  private resetForm() {
    ;(this.$refs['elForm'] as any).resetFields()
  }
  @Confirm({
    title: '提示',
    content: `是否关闭？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private handleClose() {
    this.$emit('changshowDialogAdd', false)
  }
  private closeDialog() {
    this.$emit('changshowDialogAdd', false)
  }
}
</script>

<style lang="scss" scoped>
::v-deep.el-col-8 {
  height: 60px;
}
::v-deep .el-input.is-disabled .el-input__inner {
  color: #2e2e2e;
  background-color: #fff;
}
</style>

