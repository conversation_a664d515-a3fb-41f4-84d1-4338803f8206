<template>
  <el-container class="container"
    direction="vertical">
    <!-- 头部 -->
    <Header />
    <!-- 侧边栏 -->
    <el-container style="flex: 1;height:100%;">
      <Aside />
      <!-- 内容 -->
      <Main />
    </el-container>
  </el-container>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import Header from './header.vue'
import Aside from './aside.vue'
import Main from '../public/main.vue'

@Component({
  components: {
    Aside,
    Header,
    Main
  }
})
export default class Container extends Vue {
  // 判断是生产还是开发环境
  get getProcess() {
    return process.env.NODE_ENV === 'development'
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
}
</style>

<style lang="scss">
html,
body,
#app {
  height: 100%;
}
// 财务监管
.finance_content {
  padding: 10px;
}

.finance_content {
  padding: 20px 10px;
  .debtApproval_header {
    display: flex;
    height: 36px;
    justify-content: space-between;
    div:first-child {
      width: 370px;
    }
  }
  .operatingBar_content {
    display: flex;
    justify-content: space-around;
    .el-button {
      width: 41px;
      text-align: center;
      padding: 9px 0;
      color: #fff;
      font-size: 12px;
    }
    .el-button:first-child {
      background: #df7575;
      border-radius: 2px;
      border-radius: 2px;
    }
    .el-button:nth-child(2) {
      background: #b43c3c;
      border-radius: 2px;
      border-radius: 2px;
    }
    .el-button:nth-child(3) {
      background: #fff;
      border-radius: 2px;
      border-radius: 2px;
      color: #545675;
    }
  }
}

.finance_submit {
  text-align: end;
}
</style>