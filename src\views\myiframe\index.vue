/* 该页面是 router 路由后台配置 http|https 这种外部链接，并且设置新窗口打开为否 */

<template>
  <section v-loading="loading"
    class="myiframe-wrap">
    <iframe :src="src"
      class="iframe"
      ref="iframe" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'

@Component({})
export default class extends Vue {
  private src = ''
  private loading = false

  // 路由改变时触发
  @Watch('$route')
  private routeChange() {
    this.getIframeSrc()
  }

  private created() {
    this.getIframeSrc()
  }

  // 赋值 iframe 路由地址
  private getIframeSrc() {
    this.loading = true

    let { name, url } = this.$route.query
    this.src = url as string

    setTimeout(() => {
      this.loading = false
    }, 1500)
  }
}
</script>

<style lang="scss" scoped>
.myiframe-wrap {
  height: 100%;
  .iframe {
    width: 100%;
    height: 100%;
    border: 0;
    overflow: hidden;
    box-sizing: border-box;
  }
}
</style>