/* 自定义弹窗：表格详情 */

<template>
  <Dialog :title="title"
    :width="`${width}px`"
    :visible="visible"
    :close-on-click-modal="closeModal"
    :destroy-on-close="true"
    :append-to-body="true"
    :show-close="false"
    @close="handleClose"
    custom-class="custom-cockipt-dialog-wrap">
    <div slot="header"
      class="header-box">
      <div class="mode mode-left">
        <el-select v-if="isYear"
          v-model="yearSelect"
          popper-class="custom-cockipt-select-down"
          class="custom-cockipt-select-wrap m-r-10"
          style="width: 100px;"
          size="small"
          @change="changeYear">
          <el-option v-for="item in yearList"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>

        <el-select v-if="isCode"
          v-model="activeCode"
          popper-class="custom-cockipt-select-down"
          class="custom-cockipt-select-wrap m-r-10"
          style="width: 100px;"
          size="small"
          @change="changeCompanyList">
          <el-option v-for="item in getCompanyList"
            :key="item.orgCode"
            :label="item.orgName"
            :value="item.orgCode">
          </el-option>
        </el-select>
      </div>
      <h4 class="mode mode-middel">{{title}}</h4>
      <div class="mode mode-right">
        <span v-if="unit">单位：{{unit}}</span>
        <i class="close el-icon-circle-close"
          @click="handleClose" />
      </div>
    </div>

    <div slot="body">
      <Grid ref="grid"
        :height="600"
        :show-index="true"
        :border="true"
        :columns="columns"
        :remote-url="remoteUrl"
        :show-pagination="true"
        :overflow-tooltip="true"
        :search-params="gridSearchParams"
        :pageSizes="[10,20,30]"
        class="cutom-cockipt-grid"
        loadingBackground="rgba(0, 0, 0, 0.2)">
        <!-- 智慧预警：个别模块 -->
        <template slot="slotDebtRuleName"
          slot-scope="scope">
          <router-link target="_blank"
            :to="{path:'/prewarning/home'}">
            <i class="el-icon-s-promotion"
              style="color:#fff; margin-right:1px;" />
            <span style="color:#fff;">{{scope.row.ruleName}}</span>
          </router-link>
        </template>

        <template slot="slotDebtAlertLevel"
          slot-scope="scope">
          <span v-if="+scope.row.warningLevel === 1"
            style="color:#fb3f3f;">红色预警</span>
          <span v-if="+scope.row.warningLevel === 2"
            style="color:#ff7500;">橙色预警</span>
          <span v-if="+scope.row.warningLevel === 3"
            style="color:#E6A23C;">黄色预警</span>
          <span v-if="+scope.row.warningLevel === 4"
            style="color:#409EFF;">蓝色预警</span>
        </template>
      </Grid>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { getGroupList } from '@/api/public'
import Grid from '@/components/Grid/index.vue'
import Dialog from '@/components/Dialog/index.vue'

type typeSeries = {
  id: string
  code: string
  name: string
}

@Component({
  components: {
    Grid,
    Dialog
  }
})
export default class Header extends Vue {
  @Prop() private title!: string // 标题
  @Prop() private visible!: boolean // 弹窗显隐
  @Prop() private columns!: any[] // 表格数据
  @Prop() private remoteUrl!: string // 表格接口
  @Prop({ default: true }) private isCode?: string // 是否需要集团筛选
  @Prop({ default: true }) private isYear?: string // 是否需要年份筛选
  @Prop() private code?: string // 集团code
  @Prop() private year?: string // 年份
  @Prop({ default: '万元' }) private unit?: string // 单位
  @Prop({ default: 1000 }) private width?: string // 弹窗宽度
  @Prop({ default: true }) private closeModal?: boolean // 弹窗是否可点击mark关闭
  @Prop({
    default: () => {
      return {}
    }
  })
  private searchParams?: object // 表格其他参数

  private yearSelect = ''
  private activeCode = ''
  private getCompanyList: any[] = []
  private yearList: string[] = []
  private gridSearchParams: any = this.searchParams
  private tabActive: typeSeries = {
    id: '',
    code: '',
    name: ''
  }

  // 组件数据初始化
  private created() {
    this.getOrgList()
  }

  // 组件初始化
  private mounted() {
    this.yearSelect = this.year || (this as any).$moment(new Date()).add('year', 0).format('YYYY')
    this.activeCode = this.code || this.getCompanyList[0].orgCode
    this.gridSearchParams.year = this.yearSelect
    this.gridSearchParams.orgCode = this.activeCode

    this.getYearList()
  }

  // 获取 tabs 数据
  private async getOrgList() {
    let { data } = await getGroupList()

    if (Array.isArray(data) && data.length) {
      // data.push({
      //   orgName: '尖峰集团',
      //   orgCode: '91330000704202954L'
      // })

      this.getCompanyList = data
    } else {
      this.getCompanyList = []
    }
  }

  // 获取年份下拉数据
  private getYearList() {
    let list = []
    let yearNow = (this as any).$moment(new Date()).add('year', 0).format('YYYY')
    for (let i = 0; i < 5; i++) {
      let year = yearNow - i
      list.push(String(year))
    }
    this.yearList = list
  }

  // 年份改变
  private changeYear() {
    this.gridSearchParams.year = this.yearSelect
    this.refreshGrid()
  }

  // 集团切换
  private changeCompanyList() {
    this.gridSearchParams.orgCode = this.activeCode
    this.refreshGrid()
  }

  // 表格搜索
  private refreshGrid(isCurrent = false) {
    ;(this.$refs.grid as Grid).refresh(isCurrent)
  }

  // 关闭弹窗
  private handleClose() {
    this.$emit('update:visible', false)
  }
}
</script>

<style lang="scss">
.custom-cockipt-select-down {
  $color: #2eb6f6;
  margin-top: 0 !important;

  $modeHoverBg: linear-gradient(
    to right,
    rgba(5, 186, 253, 0) 0%,
    rgba(5, 186, 253, 0.1) 10%,
    rgba(5, 186, 253, 0.3) 20%,
    rgba(5, 186, 253, 0.5) 30%,
    rgba(5, 186, 253, 0.8) 40%,
    rgba(5, 186, 253, 1) 50%,
    rgba(5, 186, 253, 0.8) 60%,
    rgba(5, 186, 253, 0.5) 70%,
    rgba(5, 186, 253, 0.4) 80%,
    rgba(5, 186, 253, 0.3) 90%,
    rgba(5, 186, 253, 0.1) 100%
  );

  background: rgba($color: #00287e, $alpha: 1);
  border-color: $color;

  .el-select-dropdown__list {
    padding: 0;

    .el-select-dropdown__item {
      height: 30px;
      line-height: 30px;
      color: #dfdfdf;
      padding: 0 16px;
    }
    .hover {
      background: $modeHoverBg;
    }
  }
  .popper__arrow {
    border: none !important;
    &::after {
      top: 0 !important;
      border-bottom-color: $color !important;
    }
  }
}
</style>

<style lang="scss">
// 驾驶舱自定义弹窗
.custom-cockipt-dialog-wrap {
  @keyframes keyCockiptDialogMove {
    0% {
      transform: rotateY(90deg);
    }
    100% {
      transform: rotateY(0);
    }
  }

  position: relative;
  left: 0;
  padding-bottom: 0 !important;
  background: #072979;
  border-radius: 10px;
  border: 1px solid #2eb6f6;
  // background: url('../../images/cockipt_dialog_bg.png') no-repeat center center;
  // background-size: 100% 100%;
  transform: rotateY(0);
  animation: keyCockiptDialogMove 1s ease;

  h4,
  p {
    margin: 0;
  }

  .body-close {
    position: absolute;
    right: 35px;
    top: 26px;
    color: rgb(0, 204, 255);
    font-size: 24px;
    cursor: pointer;
  }

  .header-box {
    display: flex !important;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    padding-right: 0 !important;
    .mode {
      font-weight: normal;
    }
    .title {
      color: #fff;
    }
    .close {
      font-size: 20px;
      margin-left: 10px;
      cursor: pointer;
      &:hover {
        color: #dfdfdf;
      }
    }
    .mode-left {
      width: 200px;
    }
    .mode-right {
      width: 200px;
      color: #2eb6f6;
      font-size: 13px;
      text-align: right;
      display: flex;
      align-items: center;
      justify-content: end;
    }
    .mode-middel {
      flex: 1;
      color: #dfdfdf;
      font-size: 20px;
      text-align: center;
    }
  }

  .el-dialog__header {
    background: none !important;
    padding-top: 10px !important;
    &::after {
      background: none !important;
    }
    .el-dialog__headerbtn {
      top: -18px !important;
      right: 18px !important;
      .el-dialog__close {
        color: #0899fa;
        border: 1px solid #0899fa;
        border-radius: 50%;
      }
    }
  }
  .el-dialog__body {
    padding: 0 0 0 !important;
    min-height: 100px;
  }

  .cutom-cockipt-grid {
    $color: #2eb6f6;

    border-radius: 0 !important;
    box-shadow: none !important;
    .el-table--border::after,
    .el-table--group::after,
    .el-table::before {
      background: $color !important;
    }

    .el-table--border::before,
    .el-table--group::before,
    .el-table::before {
      top: 0;
      right: 0;
      width: 1px;
      height: 100%;
    }
    .el-table__body-wrapper {
      height: auto !important;
      .el-table__empty-text {
        color: #b5b5b5;
        font-size: 18px;
      }
    }

    .el-table {
      max-height: 420px;
      overflow-y: auto;
      background: none;
      border-radius: 0;
      border: none;
      border-bottom: 1px solid $color;
      &::before {
        background: $color;
      }
      th,
      tr {
        color: #dfdfdf;
        background: none;
      }

      .el-table__cell {
        text-align: center;
      }

      .el-table__header {
        &::before {
          background: none;
        }
        th {
          border: 1px solid $color;
          border-left: none;
          .cell {
            font-size: 13px;
            font-weight: normal;
            color: #2eb6f6;
          }
        }
        th:nth-child(1) {
          border-left: 1px solid $color;
        }
      }

      .el-table__body {
        td {
          height: 36px;
          line-height: 36px;
          padding: 0;
          border: 1px solid $color;
          border-left: none;
          border-bottom: none;
          background: none;
          .cell {
            color: #dfdfdf;
          }
        }
        td:nth-child(1) {
          border-left: 1px solid $color;
        }

        .el-table__row {
          &:hover {
            background: rgba($color: #0899fa, $alpha: 0.2);
          }
        }

        .el-table__cell {
          background: none;
        }
      }
    }

    .footer {
      color: #dfdfdf;
      background: none;
      padding: 0 0;
      .el-pagination {
        text-align: right;
        padding: 20px 0;
        span {
          color: $color;
        }
        li {
          color: $color;
        }
        .el-icon {
          color: $color;
        }
        .active {
          color: #2eb6f6;
          border-radius: 3px;
          border: 1px solid $color;
        }
      }
      .el-select {
        .el-input__inner {
          color: $color;
          border-color: $color;
          background: none;
        }
        .el-select__caret {
          color: $color;
        }
      }
      .el-pagination__jump {
        .el-input__inner {
          color: $color;
          border-color: $color;
          background: none;
        }
      }
    }
  }
}
</style>