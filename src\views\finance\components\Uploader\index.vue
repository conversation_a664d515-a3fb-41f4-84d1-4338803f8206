/**
  组件描述: 上传组件
*/
<template>
  <div>
    <el-col :span="8">
      <el-form-item label="" required label-width="0px" prop="attachment2FileUrls">
        <div class="file-box">
          <p>市属国有企业对外担保备案表盖章扫描件</p>
          <p v-if="formData.attachment2FileUrls == ''">
            <el-button
              style="width: 50%; text-align: center"
              type="primary"
              @click="uploadfile('attachment2FileUrls')"
              icon="el-icon-upload"
            >
              上传
            </el-button>
          </p>
          <p v-else>
            <el-button type="text" @click="uploadfile('attachment2FileUrls')">附件已上传</el-button>
          </p>
        </div>
      </el-form-item>
    </el-col>

    <!-- 导入 -->
    <uploader
      :title="uploaderDlgTitle"
      :visible.sync="uploaderDlgVisible"
      :init-list="fileList"
      :uploadable="uploadable"
      :show-cover="true"
      :is-private="false"
      :is-attachment="true"
      @change="onFilesChange"
      @uploadComplete="handleUploadComplete"
      :append-to-body="true"
    ></uploader>
  </div>
</template>

<script lang="ts">
import { Component, Emit, Model, Prop, Vue } from 'vue-property-decorator'
import Uploader from '@/components/Uploader/index.vue'
export interface Accessory {
  fileName: string
  fileList: string[]
  isRequired: boolean
  mobelUrl?: string
  prop?: string // 对应接口字段名
}
@Component({
  components: {
    Uploader
  }
})
export default class extends Vue {
  @Prop() FileList!:Array<any>
  // 文件上传
  private uploaderDlgVisible = false
  private uploaderDlgTitle = '文件上传'
  private uploaderDlgMode = 'upload' //download  upload  see
  private uploadInitList: Array<string> = []
  private currentRow: Partial<Accessory> = {}
  private uploadtype!: string
  // upload end

  // private uploaderDlgVisible = false
get uploadable() {
    return this.uploaderDlgMode === 'upload'
  }
  get fileList() {
    return this.currentRow.fileList
  }
  private uploadfile(value: string) {
    this.uploadtype = value
    this.uploaderDlgVisible = true
  }
  private onFilesChange(fileList: string[]) {
    this.currentRow.fileList = [...fileList]
  }
  private handleUploadComplete(fileList: Array<any>) {
    // this.formData[this.uploadtype] = fileList[0].url
    // Object.assign(this.formData,{})
    // this.$set(this.formData, this.uploadtype + '', fileList[0].url)
    //
  }
}
</script>


<style scoped lang="scss">
.uploader-wrapper {
  margin-top: 12px;
  width: 260px;
}
.uploader {
  background: #fffcfc;
}
.title {
  margin-bottom: 8px;
}

.file-list {
  display: flex;
  flex-direction: column;
}
.uploader-btn {
  color: #b43c3c;
  margin-left: 12px;
  font-size: 12px;
  cursor: pointer;
}
</style>