/* 不动产 */

<template>
  <section class="cockpit-realestate-wrap">
    <TitleCom title="不动产"
      module="RealEstate"
      :isFullScreen="true"
      :orgCode="orgActive.orgCode"
      @fullScreenHandle="fullScreenHandle" />

    <div v-loading="loading"
      class="content-box">
      <div class="total">
        <div v-for="(item, index) of dataList"
          :key="index"
          class="mode">
          <h4>{{item.name}}</h4>
          <CountTo v-if="String(item.value).split('.').length === 1"
            :decimals="0"
            :startVal="0"
            :endVal="+item.value"
            :duration="1500" />
          <CountTo v-else
            :decimals="2"
            :startVal="0"
            :endVal="+item.value"
            :duration="1500" />
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { immovablesOverview } from '@/api/cockpit'
import CountTo from 'vue-count-to'
import TitleCom from '@/views/main/cockpitcren/components/Public/TitleCom.vue'

@Component({
  components: {
    TitleCom,
    CountTo
  }
})
export default class extends Vue {
  private loading = false
  private dataList: any[] = []

  // 所属集团+时间切换 选中数据
  private orgActive: {
    orgCode: string
  } = {
    orgCode: '0'
  }
  private params = {
    year: String(new Date().getFullYear()),
    month: ''
  }

  // 初始化
  private mounted() {
    this.listenerDate()
  }

  // 监听全局事件触发
  private listenerDate() {
    // 集团 切换
    this.$bus.$on('BusTabsChange', (orgActive: any) => {
      this.orgActive = orgActive
      this.initData()
    })

    // 年份 切换
    this.$bus.$on('BusYearTimeChange', (year: string) => {
      this.params.year = year
      this.initData()
    })

    // 月份 切换
    // this.$bus.$on('BusMoonTimeChange', (month: string) => {
    //   this.params.month = month
    //   this.initData()
    // })
  }

  // 获取渲染视图数据
  @Loading('loading')
  private async initData() {
    await immovablesOverview({
      orgCode: this.orgActive.orgCode,
      year: this.params.year
    })
      .then((res) => {
        let data = res.data || {}
        this.dataList = [
          {
            name: '不动产(处)',
            value: data.count
          },
          {
            name: '房产(万m²)',
            value: data.buildingArea
          },
          {
            name: '土地(万m²)',
            value: data.landArea
          }
        ]
      })
      .catch(() => {
        this.dataList = []
      })
  }

  // 放大/缩小展示
  private fullScreenHandle(isFull: boolean) {
    this.$emit('parentFullScreenHandle', isFull)
  }
}
</script>

<style scoped lang="scss">
.cockpit-realestate-wrap {
  position: relative;
  width: 100%;
  height: 270px;
  .content-box {
    .total {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
      .mode {
        flex: 1;
        padding: 14px 10px;
        margin-right: 24px;
        border-radius: 4px;
        text-align: center;
        margin-right: 0;
        border-right: 1px solid #062675;
        border: 1px solid #062675;
        background: url('../../images/mode_bj.png') no-repeat left top;
        background-size: 100% 100%;
        &:nth-last-child(1) {
          margin-right: 0;
          border-right: none;
        }
        h4 {
          font-size: 34px;
          margin: 0;
          margin-bottom: 2px;
        }
        span {
          color: #3eeeff;
          font-size: 44px;
          font-weight: normal;
          font-family: 'PangMenZhengDao';
        }
      }
      .active {
        border: 1px solid #3eeeff;
        box-shadow: 0 0 20px #3eeeff inset;
      }
    }
  }
}
</style>