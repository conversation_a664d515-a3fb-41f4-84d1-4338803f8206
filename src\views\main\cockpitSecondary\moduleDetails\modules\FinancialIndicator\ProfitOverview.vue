/* 利润概况 */

<template>
  <section class="profit-overview-wrap">
    <CommonTitle :title="title"
      class="title-box" />

    <div class="total-debt-box">金额单位：亿元元</div>

    <div class="content-box"
      ref="refEcharts1" />

    <div class="content-box"
      ref="refEcharts2" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { echartConfigure } from '@/views/main/cockpitcren/baseData'
import { deepClone } from '@/utils'
import { bigNumberFormat } from '@/utils'
import * as echarts from 'echarts'
import CommonTitle from '@/views/main/cockpitSecondary/components/CommonTitle.vue'

type EChartsOption = echarts.EChartsOption

@Component({
  components: {
    CommonTitle
  }
})
export default class extends Vue {
  @Prop() private title!: string // 标题
  @Prop() private echartsData!: any // 渲染数据

  private chartDom1: HTMLElement | null = null
  private myChart1: any = null
  private chartDom2: HTMLElement | null = null
  private myChart2: any = null
  private option1: EChartsOption = {}
  private option2: EChartsOption = {}
  private seriesData1: any[] = []
  private seriesData2: any[] = []
  private yAxisData: string[] = []

  // 数据变化，渲染视图
  @Watch('echartsData', { deep: true })
  private changeEchartsData() {
    this.initData()
  }

  // 万元数据转化成亿元
  get getBigNumberFormat() {
    return (num: string | number) => {
      return bigNumberFormat(+num).value || 0
    }
  }

  // 组件初始化
  private mounted() {
    this.chartDom1 = this.$refs.refEcharts1 as HTMLElement
    this.myChart1 = echarts.init(this.chartDom1 as HTMLElement)

    this.chartDom2 = this.$refs.refEcharts2 as HTMLElement
    this.myChart2 = echarts.init(this.chartDom2 as HTMLElement)
  }

  // 请求数据接口
  private async initData() {
    let data = deepClone(this.echartsData)

    // 组装echarts数据
    let yAxisData: string[] = []
    let seriesData1: any[] = []
    let seriesData2: any[] = []
    let colorList = ['#FCB45E', '#F27741', '#00AFFF', '#4CB766']
    data.forEach((item: any, index: number) => {
      yAxisData.push(item.name)
      let pric = this.getBigNumberFormat(item.value)

      if (+pric > 0) {
        seriesData1.push({
          value: pric,
          label: {
            position: 'left',
            fontWeight: 'bold',
            padding: [0, 0, 0, 80]
          },
          itemStyle: {
            fontWeight: 'bold',
            color: colorList[index],
            borderRadius: [0, 10, 10, 0]
          }
        })

        seriesData2.push({
          value: pric,
          label: {
            fontWeight: 'bold',
            position: 'inside'
          },
          itemStyle: {
            color: colorList[index],
            borderRadius: [0, 10, 10, 0]
          }
        })
        return
      }

      if (+pric <= 0) {
        seriesData1.push({
          value: pric,
          label: {
            position: 'right',
            fontWeight: 'bold',
            padding: [0, 0, 0, 60]
          },
          itemStyle: {
            color: colorList[index],
            borderRadius: [10, 0, 0, 10]
          }
        })

        seriesData2.push({
          value: pric,
          label: {
            fontWeight: 'bold',
            position: 'inside'
          },
          itemStyle: {
            color: colorList[index],
            borderRadius: [10, 0, 0, 10]
          }
        })
        return
      }
    })

    this.yAxisData = yAxisData
    this.seriesData1 = seriesData1
    this.seriesData2 = seriesData2

    this.initEcharts()
    this.initEcharts2()
  }

  // 初始化数据 refEcharts1
  private initEcharts() {
    let textSize = echartConfigure.textSize
    let seriesData = this.seriesData1
    let yAxisData = this.yAxisData

    this.option1 = {
      grid: {
        top: 100,
        bottom: '8%'
      },
      xAxis: {
        type: 'value',
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: 'rgba(0, 175, 255, 0)'
          }
        },
        nameTextStyle: {
          color: '#5db0ea',
          fontSize: textSize / 1.2,
          padding: [0, 0, 0, 50]
        },
        axisLabel: {
          color: '#5db0ea',
          fontWeight: 'bold',
          fontSize: textSize * 1.1,
          fontFamily: echartConfigure.fontFamilyNumber,
          formatter: '{value}'
        }
      },
      yAxis: {
        type: 'category',
        axisLine: { show: false },
        axisLabel: { show: false },
        axisTick: { show: false },
        splitLine: { show: false },
        data: yAxisData
      },
      series: [
        {
          name: '金额',
          type: 'bar',
          stack: 'Total',
          barWidth: 50,
          label: {
            show: true,
            color: '#00AFFF',
            padding: 20,
            fontSize: textSize,
            formatter: (params: any) => {
              return `${params.name}`
            }
          },
          data: seriesData
        }
      ]
    }

    this.myChart1 && this.myChart1.setOption(this.option1, true)
  }

  // 初始化数据 refEcharts2
  private initEcharts2() {
    let textSize = echartConfigure.textSize
    let seriesData = this.seriesData2
    let yAxisData = this.yAxisData

    this.option2 = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#00000000',
        shadowColor: '#00000000',
        borderWidth: 0,
        formatter: (params: any) => {
          if (!Array.isArray(params) || !params.length) return ''

          let data = params[0]
          let rote = ''
          let dom = ''
          let values = data.value

          let filterRote = this.echartsData.filter((item: any) => {
            return item.name === data.name
          })

          if (filterRote.length) rote = filterRote[0].rote

          if (values > 0) {
            dom = `
              <div>
                <span style="padding-right: 40px;">${data.seriesName}</span>
                <span>${values}亿元</span>
              </div>
              <div>
                <span style="padding-right: 40px;">同比</span>
                <span style="color: #fb3f3f;">
                  <img style="width:26px;" src="${require('../../../images/thows.png')}" />${rote}%
                </span>
              </div>
            `
          } else {
            dom = `
              <div>
                <span style="padding-right: 40px;">${data.seriesName}</span>
                <span>${values}亿元</span>
              </div>
              <div>
                <span style="padding-right: 40px;">同比</span>
                <span style="color: #00a92b;">
                  <img style="width:26px;" src="${require('../../../images/thowx.png')}" />${rote}%
                </span>
              </div>
            `
          }

          return `
            <div style="font-size:40px; line-height: 70px; color: #fff; border-radius:10px;border:2px solid #0C3EB6; padding: 20px; display: flex;flex-direction: column; background: linear-gradient(0deg, rgba(0, 103, 177, 1), rgba(0, 103, 177, 1));">
              <div style="font-weight:bold;">${data.name}</div>
              ${dom}
            </div>
          `
        }
      },
      grid: {
        top: 100,
        bottom: '8%'
      },
      xAxis: {
        type: 'value',
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: 'rgba(0, 175, 255, 0.5)'
          }
        },
        nameTextStyle: {
          color: '#5db0ea',
          fontSize: textSize / 1.2,
          padding: [0, 0, 0, 50]
        },
        axisLabel: {
          color: '#5db0ea',
          fontWeight: 'bold',
          fontSize: textSize * 1.1,
          fontFamily: echartConfigure.fontFamilyNumber,
          formatter: '{value}'
        }
      },
      yAxis: {
        type: 'category',
        axisLine: { show: false },
        axisLabel: { show: false },
        axisTick: { show: false },
        splitLine: { show: false },
        data: yAxisData
      },
      series: [
        {
          name: '金额',
          type: 'bar',
          stack: 'Total',
          barWidth: 50,
          label: {
            show: true,
            color: '#fff',
            padding: 20,
            fontSize: textSize,
            formatter: '{c}亿元'
          },
          data: seriesData
        }
      ],
      animationDelay: function (idx) {
        return idx * 1000
      }
    }

    this.myChart2 && this.myChart2.setOption(this.option2, true)
  }
}
</script>

<style scoped lang="scss">
.profit-overview-wrap {
  position: relative;
  height: 100%;
  .title-box {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
  }
  .content-box {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }

  .total-debt-box {
    position: absolute;
    right: 0;
    top: 20px;
    color: #5db0ea;
    font-size: 32px;
    display: flex;
    align-items: baseline;
  }
}
</style>