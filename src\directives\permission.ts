import Vue from 'vue'
import { DirectiveBinding } from 'vue/types/options'
import { PermissionModule } from '@/store/modules/permissionDict'
import { getLocalStorage } from '@/utils/cache'

Vue.directive('permission', {
    inserted(el: HTMLElement, binding: DirectiveBinding) {
        const { value } = binding
        const permissionDict = getLocalStorage('permission_dict')
        const permissionList = permissionDict ? JSON.parse(permissionDict).content : []

        if (value && value.length > 0) {
            const hasPermission = permissionList.includes(value)
            // console.log("🚀 有这个按钮吗", hasPermission, permissionList, value)

            if (!hasPermission) {
                el.parentNode && el.parentNode.removeChild(el)
            }
        }
    }
}) 