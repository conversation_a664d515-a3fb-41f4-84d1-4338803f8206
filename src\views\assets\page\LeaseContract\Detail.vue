<template>
  <Dialog title="资产信息详情" width="800px" :visible="visible" @close="handleClose">
    <div slot="body" v-loading="loading" class="dialog_content">
      <el-descriptions border size="" title :colon="false" :column="3">
        
        <el-descriptions-item label="备注">{{detailData}}</el-descriptions-item>
      </el-descriptions>
    </div>
    <div slot="footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import { LeaseDetail } from '@/api/assets'
export default interface DetailInfo {
  id: string | number
}
@Component({
  components: {
    Dialog
  }
})
export default class extends Vue {
  @Prop() private visible!: boolean
  @Prop() private detailInfo!: any
  private loading = false
  private detailData = {}
  created() {
    if (this.detailInfo) {
      this.getDetail()
    }
  }
  private async getDetail() {
    this.loading = true
    let res: any = {}
    try {
      res = await LeaseDetail({
        id: this.detailInfo.id
      })
      if (res.success) {
        this.detailData = res.data
        // this.$message.success(res.msg)
      }
    } catch (e) {
      this.$message.error(res.msg)
      this.handleClose()
    } finally {
      this.loading = false
    }
  }
  private handleClose() {
    //
    this.$emit('update:visible', false)
  }
}
</script>

<style lang="scss" scoped>
.dialog_content {
    padding: 0 !important;
  min-height: 40vh;
}
</style>