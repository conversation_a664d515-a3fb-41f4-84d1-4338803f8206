// 新增资产交易

<template>
  <Dialog
    :title="mode=='reset'?'重新发起挂牌申请':'新增挂牌申请'"
    width="1000px"
    :visible="visible"
    :append-to-body="true"
    @close="closeDlg"
    v-loading="loading"
  >
    <div class="body" slot="body">
      <el-steps :active="stepActive" finish-status="success" simple>
        <el-step title="出租信息"></el-step>
        <el-step title="出租方信息"></el-step>
        <el-step title="承租方资格条件"></el-step>
        <el-step :title="submitTitle"></el-step>
      </el-steps>

      <div class="content">
        <!-- 出租信息 -->
        <RentalInfoForm v-show="stepActive === 0" :initData="rentalInfoData" ref="RentalInfoForm" />

        <!-- 出租方信息 -->
        <LessorInfoForm v-show="stepActive === 1" :initData="lessorInfoData" ref="LessorInfoForm" />

        <LesseeQualificationsForm
          v-show="stepActive === 2"
          :initData="lesseeQualificationsData"
          ref="LesseeQualificationsForm"
        />

        <div v-show="stepActive === 3">
          <el-result
            v-if="submitResult"
            icon="success"
            title="提交成功"
            subTitle="信息提交成功，结果将以短信的形式发送给联系人，请注意查收！"
          >
            <template slot="extra">
              <el-button type="primary" size="medium" @click="debugging1">查看详情</el-button>
            </template>
          </el-result>
          <el-result v-else icon="error" title="提交失败" subTitle="信息提交失败,点击查看详情可重新提交">
            <template slot="extra">
              <el-button type="primary" @click="debugging1">查看详情</el-button>
            </template>
          </el-result>
        </div>
      </div>
    </div>

    <div slot="footer" class="footer">
      <div class="footer-left">
        <!-- <el-button @click="onPrint">打印</el-button> -->
      </div>
      <div class="footer-right">
        <el-button @click="closeDlg">关闭</el-button>
        <!-- <el-button @click="debugging">调试+1</el-button> -->
        <!-- <el-button  @click="debugging1">调试-1</el-button> -->
        <el-button v-if="stepActive > 0 && stepActive !== 3" type="primary" @click="toStep(-1)">上一步</el-button>
        <el-button v-if="stepActive < 2" type="primary" @click="toStep(1)">下一步</el-button>
        <el-button
          v-if="stepActive === 2 && isupload"
          type="primary"
          v-loading="loadingsubmit"
          @click="submitForm"
        >提交</el-button>
      </div>
    </div>
  </Dialog>
</template>
<script lang="ts">
import { Component, Emit, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import RentalInfoForm from './components/RentalInfoForm.vue'
import LessorInfoForm from './components/LessorInfoForm.vue'
import LesseeQualificationsForm from './components/LesseeQualificationsForm.vue'
import { Loading, Throttle } from '@/decorators'
import { DetailAsset } from '@/api/assets'
import { applyAssertransaction, getTransactionDetailOfbatch, reApply, getAssetbatch } from '@/api/projectInvestment'
import { TradeDetail } from './TradeDetailDialog.vue'
import { deepClone } from '../../../../utils'

@Component({
  name: 'Container',
  components: {
    Dialog,
    RentalInfoForm,
    LessorInfoForm,
    LesseeQualificationsForm
  }
})
export default class Container extends Vue {
  @Prop() private visible!: boolean
  @Prop() private mode!: 'reset' | 'add' // 新增、重新发起（与新增一致，只是需请求原数据）
  @Prop() private assetId!: string // 批号，用于请求原数据

  @Emit('close')
  private onClose() {
    return
  }

  private loading = false
  private loadingsubmit = false
  private stepActive = 0
  private submitResult = 0
  private submitTitle = '提交结果'
  private isupload = true
  private rentalInfoData: any = {}
  private lessorInfoData: any = {}
  private lesseeQualificationsData: any = {}

  created() {
    if (this.mode === 'reset') {
      // 请求原数据
      
      this.getdetail(this.assetId)
      // this.featchInitData()
    }
  }
  //调试
  private debugging() {
    this.stepActive = this.stepActive + 1
  }
  private debugging1() {
    this.stepActive = this.stepActive - 1
  }
  // // 初始化数据
  // @Loading('loading')
  // private async featchInitData() {
  //   let res = await getTransactionDetailOfbatch(this.batchNo)
  //   if (res.success) {
  //     let data = res.data as TradeDetail
  //     this.rentalInfoData = Object.assign(data.basic, { assetList: data.assetList })
  //     this.lessorInfoData = data.lessor
  //     this.lesseeQualificationsData = data.lessee
  //   }
  // }
  // 初始化数据
   private async getdetail(id: string | ''|number) {
    this.loading = true
    try {
      let res = await DetailAsset({
        id: id
      })
        if (res.success) {
      let data = deepClone(res.data)
      this.rentalInfoData = Object.assign(data.basic, { assetList: data.assetList })
      this.lessorInfoData = deepClone(data.lessor)
      this.lesseeQualificationsData = deepClone(data.lessee)
      // 处理数据
      this.lesseeQualificationsData = Object.assign(this.lesseeQualificationsData, {
        depositBank: this.lessorInfoData.depositBank,
        accountName: this.lessorInfoData.accountName,
        bankAccount: this.lessorInfoData.bankAccount,
        invoiceType: this.lessorInfoData.invoiceType,
        receiveEmail: this.lessorInfoData.receiveEmail,
        receivePhone: this.lessorInfoData.receivePhone,
        invoicingRemark: this.lessorInfoData.invoicingRemark
      })
      this.loading = false
    }
    await (this.$refs.RentalInfoForm as any).setData()
    // this.
    this.$forceUpdate()
    } catch (e) {
         this.loading = false
    }
  }
  // @Loading('loading')
  private async featchInitData() {
    this.loading = true
    let res = await getAssetbatch(this.assetId)
    if (res.success) {
      let data = deepClone(res.data)
      this.rentalInfoData = Object.assign(data.basic, { assetList: data.assetList })

      this.lessorInfoData = deepClone(data.lessor)
      this.lesseeQualificationsData = deepClone(data.lessee)
      // 处理数据
      this.lesseeQualificationsData = Object.assign(this.lesseeQualificationsData, {
        depositBank: this.lessorInfoData.depositBank,
        accountName: this.lessorInfoData.accountName,
        bankAccount: this.lessorInfoData.bankAccount,
        invoiceType: this.lessorInfoData.invoiceType,
        receiveEmail: this.lessorInfoData.receiveEmail,
        receivePhone: this.lessorInfoData.receivePhone,
        invoicingRemark: this.lessorInfoData.invoicingRemark
      })
      this.loading = false
    }
    await (this.$refs.RentalInfoForm as any).setData()
    // this.
    this.$forceUpdate()
  }

  // 1 下一步 -1 上一步
  private toStep(direction: 1 | -1) {
    if (direction > 0) {
      if (this.stepActive === 0) {
        let rentalInfoForm = this.$refs.RentalInfoForm as RentalInfoForm

        rentalInfoForm.validate().then(() => {
          if (rentalInfoForm.getData().assetList.length == 0) {
            this.$message.warning('请增加房产申请表')
            return false
          }
          this.rentalInfoData = rentalInfoForm.getData()
          this.stepActive = this.stepActive + direction
          // 获取数据
        })
      } else if (this.stepActive === 1) {
        let lessorInfoForm = this.$refs.LessorInfoForm as LessorInfoForm
        lessorInfoForm.validate().then(() => {
          this.stepActive = this.stepActive + direction
          // 获取数据
          this.lessorInfoData = lessorInfoForm.getData()
        })
      } else if (this.stepActive === 2) {
        this.stepActive = this.stepActive + direction
      }
    } else {
      this.stepActive = this.stepActive + direction
    }
  }

  // 点击关闭
  private closeDlg() {
    this.$confirm(`此操作将关闭窗口, 是否继续?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      dangerouslyUseHTMLString: true,
      type: 'warning'
    })
      .then(res => {
        this.$emit('update:visible', false)
        this.onClose()
      })
      .catch(err => {
        this.$message('已取消')
      })
  }
  @Throttle
  private async submitForm() {
    let lesseeQualificationsForm = this.$refs.LesseeQualificationsForm as LesseeQualificationsForm
    lesseeQualificationsForm.validate().then(async () => {
      // 获取数据
      this.lesseeQualificationsData = lesseeQualificationsForm.getData()
      // 特殊处理，出租方指定账号和发票信息 赋值到rentalInfoData
      this.lessorInfoData = Object.assign(this.lessorInfoData, {
        depositBank: this.lesseeQualificationsData.depositBank,
        accountName: this.lesseeQualificationsData.accountName,
        bankAccount: this.lesseeQualificationsData.bankAccount,
        invoiceType: this.lesseeQualificationsData.invoiceType,
        receiveEmail: this.lesseeQualificationsData.receiveEmail,
        receivePhone: this.lesseeQualificationsData.receivePhone,
        invoicingRemark: this.lesseeQualificationsData.invoicingRemark
      })

      // 组装数据
      let resultData = Object.assign(this.rentalInfoData, {
        lessee: this.lesseeQualificationsData,
        lessor: this.lessorInfoData
      })
      let formData = JSON.parse(JSON.stringify(resultData))
      if (this.mode == 'reset') {
        this.loadingsubmit = true
        try {
          let res = await reApply(formData)
          if (res.success) {
            this.submitResult = 1
            this.isupload = false
            this.loadingsubmit = false
            this.toStep(1)
          }
        } catch (err) {
          this.submitResult = 0
          this.loadingsubmit = false
          this.toStep(1)
        }
      } else {
        try {
          let res = await applyAssertransaction(formData)
          this.loadingsubmit = true
          if (res.success) {
            this.submitResult = 1
            this.isupload = false
            this.loadingsubmit = false
            this.toStep(1)
          }
        } catch (err) {
          this.submitResult = 0
          this.loadingsubmit = false
          this.toStep(1)
        }
      }
      // formData = this.dealwithFile(formData)
    })
  }
  private dealwithFile(data: any) {
    //  let asslist= data.assetList.map((item:any)=>{
    //     item.attachmentFile1=this.fileSelect(item.attachmentFile1)
    //   })
    //   data.attachmentFile1 = this.fileSelect(data.attachmentFile1)
    //   data.attachmentFile2 = this.fileSelect(data.attachmentFile2)
    //   data.attachmentFile3 = this.fileSelect(data.attachmentFile3)
    //   data.attachmentFile4 = this.fileSelect(data.attachmentFile4)
    //   data.attachmentFile5 = this.fileSelect(data.attachmentFile5)
    //   data.attachmentFile6 = this.fileSelect(data.attachmentFile6)
    //   data.attachmentFile7 = this.fileSelect(data.attachmentFile7)
    //   data.attachmentFile8 = this.fileSelect(data.attachmentFile8)
    //   data.lessor.attachmentFile1 = this.fileSelect(data.lessor.attachmentFile1)
    //   data.lessor.attachmentFile2 = this.fileSelect(data.lessor.attachmentFile2)
    //   data.lessor.attachmentFile3 = this.fileSelect(data.lessor.attachmentFile3)
    //   data.lessee.attachmentFile1 = this.fileSelect(data.lessee.attachmentFile1)
    //   data.lessee.attachmentFile2 = this.fileSelect(data.lessee.attachmentFile2)
    //   data.lessee.attachmentFile3 = this.fileSelect(data.lessee.attachmentFile3)
    //   data.lessee.attachmentFile4 = this.fileSelect(data.lessee.attachmentFile4)
    return data
  }
  private fileSelect(fileList: any) {
    let fileArray: any = fileList.map((res: any) => {
      return res.id
    })
    return fileArray
  }
  private onPrint() {
    //
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  // max-height:75vh;
  padding: 0 !important;
  padding-bottom: 20px !important;
  .dialog-body {
    padding: 0 !important;
  }
}
::v-deep .el-step.is-simple:not(:last-of-type) .el-step__title {
  max-width: 70%;
}

::v-deep .el-select {
  width: 100%;
}
.body {
  min-height: 600px;
}
.content {
  padding: 10px 24px;
  max-height: 70vh;
  overflow: scroll;
}

.footer {
  display: flex;
  justify-content: space-between;
}
</style>
