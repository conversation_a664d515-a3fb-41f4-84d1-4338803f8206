/* * input 数字输入控件：目前支持（最多两位小数｜正整数） * 相关参数同 elementUi el-input 组件 * 引入：import InputNumber from
'@/components/FormComment/inputNumber.vue' * 调用：
<InputNumber v-model="data" ...props type="integer" @updateHandle="updateHandle">
          描述符号 slot
        </InputNumber>

* @updateHandle 是 oninput 事件触发后的回调，用来通知父级组件 */

<template>
  <el-select v-model="inputValue" v-bind="$attrs" v-on="$listeners" placeholder="请选择" @change="changeorg">
    <el-option v-for="item in compTree" :key="item.id" :label="item.deptName" :value="item.deptCode"></el-option>
  </el-select>
</template>

<script lang="ts">
import { Throttle } from '@/decorators'
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { getAstCompTree } from '@/api/projectInvestment'

@Component({})
export default class extends Vue {
  @Prop() private value!: string // v-modle 绑定的值
  private inputValue?: string = ''
  private compTree = []
  //获取机构数组
  private async getCompTree() {
    let options = []
    let res = await getAstCompTree({ parentId: 0, deptCategory: 1 })
    if (res.success) {
      options = res.data
      this.compTree = options
    }
  }
}
</script>
