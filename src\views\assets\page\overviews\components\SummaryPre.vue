<template>
  <div ref="chartDom"
    class="chartDom" />
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import * as echarts from 'echarts'

let chartDom = null
let myChart: any = null

@Component({})
export default class extends Vue {
  @Prop() private infoData!: {
    title: string
    unit: string
    total: number
  }
  @Prop() private echarsData!: Array<{
    value: number
    name: string
  }>

  // 组件初始化
  private mounted() {
    this.$nextTick(() => {
      chartDom = this.$refs.chartDom
      myChart = echarts.init(chartDom as HTMLElement)

      this.initEcharts()
    })
  }

  // 初始化echarts组件
  private initEcharts() {
    let { title, total, unit } = this.infoData
    let text = title
    let subtext = ''
    let seriesData = this.echarsData.filter((item) => {
      return item.value
    })


    if(total) {
      subtext = total + unit
    } else {
      let num = 0
      this.echarsData.forEach((item) => {
        num += +item.value
      })
      
      num = +(num.toFixed(2))
      subtext = num + unit
    }

    let option = {
      tooltip: {
        trigger: 'item',
        valueFormatter: (value: string) => `${value}${unit}`
      },
      title: {
        top: '42%',
        left: 'center',
        text: text,
        subtext: subtext,
        textStyle: {
          fontSize: 13,
          fontWeight: 'bold'
        },
        subtextStyle: {
          fontSize: 13,
          lineHeight: 10,
          color: '#fb3f3f'
        }
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '60%'],
          itemStyle: {
            borderRadius: 4,
            borderWidth: 1,
            borderColor: '#fff'
          },
          label: {
            formatter: (parmes: any) => {
              return ` {name|${parmes.data.name}}\n{value|${parmes.data.value}}`
            },
            rich: {
              name: {
                fontSize: 12
              },
              value: {
                color: '#666',
                fontSize: 12,
                lineHeight: 20
              }
            }
          },
          labelLine: {
            length2: 0
          },
          data: seriesData
        }
      ]
    }

    myChart && myChart.setOption && myChart.setOption(option)
  }
}
</script>

<style scoped lang="scss">
.chartDom {
  width: 100%;
  height: 100%;
}
</style>