<template>
  <section v-loading="loading"
    element-loading-text="资产地图加载中，请稍等......"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
    class="assets-map-wrap">
    <div id="map" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import { assetHouseCoordinate } from '@/api/cockpit'
import { Loading, Throttle } from '@/decorators'
import { styleJson } from './baseData/styleJson'
import { queryString } from '@/utils'

declare const BMapGL: any // 声明 declare
declare const map: any // 声明 declare

@Component({
  components: {}
})
export default class extends Vue {
  private panorama: any = null
  private params = {}
  private assetsData = []
  private loading = false
  private isZoom = false
  private mapLocation = [119.647444, 29.079059]

  // 各集团切换导致iframe路由改变
  @Watch('$route', { immediate: true, deep: true })
  changeRouter() {
    this.getAssetsData()
  }

  // 组件初始化
  private mounted() {
    this.initMap()
    this.getAssetsData()

    // 监听父级页面传递的数据
    window.addEventListener('message', (e) => {
      let res = e.data
      switch (res.code) {
        case 'IsPanoramicHide': // 退出全景地图
          this.outPanoramic()
          break
      }
    })
  }

  // 初始化地图
  private initMap() {
    ;(window as any).map = new BMapGL.Map('map', {
      minZoom: 10
    })

    this.setMap()
    this.toolbar()
  }

  // 地图相关设置
  private setMap() {
    let map = (window as any).map
    let mapLocation = this.mapLocation

    map.setMapStyleV2({ styleJson: styleJson }) // 设置地图主题(https://lbsyun.baidu.com/customv2/help.html)
    map.centerAndZoom(new BMapGL.Point(mapLocation[0], mapLocation[1]), 15) // 设置中心点坐标和地图级别、地图3D视角
    map.enableScrollWheelZoom(true) // 开启鼠标滚轮缩放
    map.setHeading(64.5)
    map.setTilt(76)
  }

  // 给地图添加工具栏
  private toolbar() {
    let map = (window as any).map

    let scaleCtrl = new BMapGL.ScaleControl() // 添加比例尺控件
    map.addControl(scaleCtrl)
    let zoomCtrl = new BMapGL.ZoomControl() // 添加缩放控件
    map.addControl(zoomCtrl)
    let navi3DCtrl = new BMapGL.NavigationControl3D() // 添加3D控件
    map.addControl(navi3DCtrl)
  }

  // 获取资产分布数据
  @Throttle
  @Loading('loading')
  private async getAssetsData() {
    let map = (window as any).map
    let parmes = queryString(window.location.href)

    // 资产类型
    let assetTypes = parmes.assetTypes.split(',') || []
    let assetTypesList: number[] = []
    assetTypes.forEach((item: string) => {
      if (item !== '') {
        assetTypesList.push(+item)
      }
    })

    // 资产用途
    let purposes = parmes.purposes.split(',') || []
    let purposesList: number[] = []
    purposes.forEach((item: string) => {
      if (item !== '') {
        purposesList.push(+item)
      }
    })

    // 从链接上获取的 keyword，然后转成中文
    let keyword = decodeURIComponent(parmes.keyword || '')

    // 请求接口，获取资产位置数据
    let { data } = await assetHouseCoordinate({
      // lon: this.mapLocation[0],
      // lat: this.mapLocation[1],
      lon: '',
      lat: '',
      raidus: 1,
      assetTypes: assetTypesList,
      purposes: purposesList,
      keyword: keyword,
      orgCode: parmes.companyCode || ''
    })

    // 这个地方先加载1000条数据，提升速度
    if (Array.isArray(data) && data.length >= 1000) {
      data.length = 1000
    }

    this.assetsData = data || []

    // 无资产分布数据处理：清除点标记和还原地图
    if (!this.assetsData.length) {
      this.removeOverlay()
      map.centerAndZoom(new BMapGL.Point(this.mapLocation[0], this.mapLocation[1]), 15)
      this.$message.warning('暂无资产分布')
      return
    }

    // 设置中心点坐标和地图级别
    if (!this.isZoom) {
      this.isZoom = true
      if (Array.isArray(data) && data.length) {
        map.centerAndZoom(new BMapGL.Point(data[0].longitude, data[0].latitude), 18)
        map.enableScrollWheelZoom(true)
        map.setHeading(64.5)
        map.setTilt(76)
      }
    }
    this.isZoom = false

    // 由于数据量庞大，所以分批次加载，并且每次先清除地图上的点
    let len = data.length
    let max = 500

    this.removeOverlay()
    for (let i = 0; i < Math.ceil(len / max); i++) {
      setTimeout(() => {
        this.addMarkers(i * max, (i + 1) * max)
      }, i * 10 * 500)
    }
  }

  // 给地图添加动态点标记
  private addMarkers(start: number, end: number) {
    let map = (window as any).map
    let that = this as any
    let assetsList = JSON.parse(JSON.stringify(this.assetsData))
    let list = []

    if (Array.isArray(assetsList) && assetsList.length) {
      list = assetsList.slice(start, end)
    } else {
      list = []
    }

    list.forEach((item: any) => {
      if (!+item.longitude || !+item.latitude) {
        return
      }

      let point = new BMapGL.Point(+item.longitude, +item.latitude)
      let marker = new BMapGL.Marker(point)
      map.addOverlay(marker)

      let opts = {
        title: item.location
      }

      let content = `
        <div class="BMap-marker-info">
          <div class="mode">
            <strong>资产类型：</strong>
            <span>${+item.assetType === 1 ? '房产' : '土地'}</span>
          </div>
          <div class="mode">
            <strong>资产编码：</strong>
            <span title="${item.assetNo}">${item.assetNo}</span>
          </div>
          <div class="mode">
            <strong>资产地址：</strong>
            <span title="${item.address}">${item.address}</span>
          </div>
          <div class="mode">
            <strong>所属单位：</strong>
            <span title="${item.orgName}">${item.orgName}</span>
          </div>
          <div class="mode">
            <strong>是否空置：</strong>
            <span>${+item.isVacancy === 1 ? '是' : '否'}</span>
          </div>
          <div class="mode">
            <strong>房间数量：</strong>
            <span>${item.itemNum || '--'}间</span>
          </div>
          <div class="mode">
            <strong>租金收缴率：</strong>
            <span title="${item.collectionRate}%">${item.collectionRate || '--'}%</span>
          </div>
          <div class="mode">
            <strong>出租率：</strong>
            <span>${item.vacancyRate || '--'}%</span>
          </div>

          <div class="footer-box">
            <span class="detail" id="${item.assetNo}_detail">查看详情</span>
            <span class="panoramic" id="${item.assetNo}_panoramic">进入全景地图</span>
          </div>
        </div>
      `
      let infoWindow = new BMapGL.InfoWindow(content, opts)

      // 点击图标开启信息窗口
      marker.addEventListener('click', function () {
        map.openInfoWindow(infoWindow, point)

        // 点击“查看详情”按钮
        let detailDom: HTMLElement | null = document.getElementById(`${item.assetNo}_detail`)
        detailDom &&
          detailDom.addEventListener('click', function () {
            that.openDetail(item.assetNo)
          })

        // 点击“进入全景地图”按钮
        let panoramicDom: HTMLElement | null = document.getElementById(`${item.assetNo}_panoramic`)
        panoramicDom &&
          panoramicDom.addEventListener('click', function () {
            that.openMapPanorama(+item.longitude, +item.latitude)
          })
      })
    })
  }

  // 进入全景模式
  private openMapPanorama(log: number, lat: number) {
    this.panorama = new BMapGL.Panorama('map')
    this.panorama.setPov({ heading: -40, pitch: 6 })
    this.panorama.setOptions({ navigationControl: false })
    this.panorama.setPosition(new BMapGL.Point(log, lat))

    window.parent.postMessage({ code: 'InPanorama' }, `*`)
  }

  // 退出全景模式
  private outPanoramic() {
    this.panorama.hide()
    window.parent.postMessage({ code: 'OutPanorama' }, `*`)
  }

  // 点击查看详情
  private openDetail(assetNo: string) {
    let currentItem: any =
      this.assetsData.find((item: { assetNo: string }) => {
        return item.assetNo === assetNo
      }) || {}

    window.parent.postMessage(
      {
        code: 'Ichnography',
        data: currentItem
      },
      `*`
    )
  }

  // 清除覆盖物
  private removeOverlay() {
    let map = (window as any).map
    map.clearOverlays()
  }

  // 组件销毁
  private destroyed() {
    this.panorama = null
    this.assetsData = []
    ;(window as any).map = null
  }
}
</script>

<style scoped lang="scss">
.assets-map-wrap {
  height: 100vh;
  position: relative;
  background: rgba($color: #eee, $alpha: 0.2);
  #map {
    height: 100%;
    width: 100%;
  }
  #panorama {
    height: 100%;
    width: 100%;
  }
}
</style>

<style lang="scss">
.BMap_bubble_pop {
  padding: 0 0 0 8px !important;
}
.BMap-marker-info {
  position: relative;
  .mode {
    display: flex;
    align-items: center;
    strong {
      width: 90px;
      color: #333;
      font-weight: normal;
    }
    span {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .footer-box {
    display: flex;
    height: 20px;
    align-items: center;
    justify-content: space-between;
    .detail {
      display: inline-block;
      color: #409eff;
      font-size: 16px;
      cursor: pointer;
    }
    .panoramic {
      color: #ce4c4c;
      font-size: 16px;
      cursor: pointer;
    }
  }
}

.anchorBL {
  display: none !important;
}
</style>