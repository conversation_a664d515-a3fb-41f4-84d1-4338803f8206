{
  // .vue文件template格式化支持，并使用js-beautify-html插件
  "vetur.format.defaultFormatter.html": "js-beautify-html",
  // js-beautify-html格式化配置，属性强制换行
  "vetur.format.defaultFormatterOptions": {
    "js-beautify-html": {
      "wrap_attributes": "force",
      "wrap_line_length": 100
    }
  },
  // 根据文件后缀名定义vue文件类型
  "files.associations": {
    "*.vue": "vue"
  },
  // 保存时eslint自动修复错误
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  // 保存自动格式化
  "editor.formatOnSave": true,
  // 路径用 @ 开头配置自动补全路径
  "path-intellisense.mappings": {
    "@": "${workspaceRoot}/src"
  }
}