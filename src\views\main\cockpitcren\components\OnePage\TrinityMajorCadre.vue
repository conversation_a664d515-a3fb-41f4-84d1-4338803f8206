/* 三重一大，重要人事任免事项 */

<template>
  <section v-loading="loading"
    class="trinity-majorcadre-wrap">
    <h4 class="cockipt-mode-vice-title">重要人事任免事项</h4>
    <div id="TrinityMajorCadre" />

    <div class="grid-text">
      <CountTo :decimals="0"
        :startVal='0'
        :endVal='texts'
        :duration='2000'
        class="count" />
      <span class="text">总次数</span>
    </div>
    <div class="halo" />
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { majorLeader } from '@/api/cockpit'
import { Loading } from '@/decorators'
import { echartConfigure, colorSixList } from '@/views/main/cockpitcren/baseData'
import * as echarts from 'echarts'
import CountTo from 'vue-count-to'

type EChartsOption = echarts.EChartsOption
type typeTabItem = {
  id: string
  code: string
  name: string
}

@Component({
  components: {
    CountTo
  }
})
export default class extends Vue {
  private loading = false
  private texts = 100
  private year = ''
  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private option: EChartsOption = {}
  private echartsDatas: any[] = []
  private datas: any[] = []
  private tabActive: typeTabItem = {
    id: '',
    code: '',
    name: ''
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('TrinityMajorCadre') as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)
    this.year = String((this as any).$moment(new Date()).add('year', 0).format('YYYY') - 1)

    this.initData()

    // 中间各tabs切换触发数据更新
    this.$bus.$on('BusTabsChange', (data: typeTabItem) => {
      this.tabActive = data
      this.filterEchartsData()
    })

    // 时间改变时触发数据更新
    this.$bus.$on('BusYearTimeChange', (data: string) => {
      this.year = data
      this.initData()
    })
  }

  // 获取数据
  @Loading('loading')
  private async initData() {
    let { data } = await majorLeader({
      year: this.year
    })

    if (!Array.isArray(data) || !data.length) {
      this.datas = []
    }

    this.echartsDatas = data as any[]
    this.filterEchartsData()
  }

  // 筛选相关数据
  private filterEchartsData() {
    let texts = 0
    let dataList: any[] = []
    let filterSeries = this.echartsDatas.filter((item) => {
      return +item.companyCode === +this.tabActive.code
    })

    filterSeries.forEach((item: { itemName: string; itemValue: string }) => {
      let name = item.itemName.substring(0, 2)
      texts += +item.itemValue
      dataList.push({
        value: +item.itemValue,
        name: `${name} ${+item.itemValue}人`
      })
    })

    this.texts = texts
    this.datas = dataList
    this.initEcharts()
  }

  // 初始化数据
  private initEcharts() {
    let legendSize = echartConfigure.legendSize
    let textSize = echartConfigure.textSize
    let texts = String(~~this.texts)
    let datas = this.datas
    let tooltipData: any = Object.assign(
      {
        trigger: 'item'
      },
      echartConfigure.tooltip
    )

    this.option = {
      color: colorSixList,
      // title: {
      //   top: '33%',
      //   left: 'center',
      //   text: texts,
      //   subtext: '总人数',
      //   textStyle: {
      //     color: '#E6B607',
      //     fontWeight: 'normal',
      //     fontSize: textSize * 2.4,
      //     fontFamily: echartConfigure.fontFamilyNumber
      //   },
      //   subtextStyle: {
      //     color: '#fff',
      //     fontWeight: 'bold',
      //     fontSize: textSize,
      //     lineHeight: 20
      //   }
      // },
      legend: {
        orient: 'vertical',
        top: 'center',
        right: 0,
        itemHeight: textSize / 1.4,
        itemWidth: textSize / 1.4,
        itemStyle: {
          borderCap: 'butt'
        },
        lineStyle: {
          type: [0, 10],
          dashOffset: 5
        },
        textStyle: {
          lineHeight: 34,
          color: '#fff',
          fontWeight: 'bold',
          fontSize: 30,
          fontFamily: echartConfigure.fontFamilyNumber
        }
      },
      grid: {
        left: '0%',
        containLabel: true
      },
      tooltip: tooltipData,
      animationDuration: echartConfigure.animationDuration,
      animationDurationUpdate: echartConfigure.animationDuration,
      series: [
        {
          type: 'pie',
          radius: ['46%', '60%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            color: '#fff',
            position: 'inner',
            fontSize: textSize / 1.4,
            formatter: '{d}%'
          },
          labelLine: {
            show: true
          },
          tooltip: {
            valueFormatter: function (value) {
              return value + ' 人'
            }
          },
          data: datas
        }
      ]
    }

    this.myChart && this.myChart.setOption(this.option)
  }

  // 销毁相关数据
  private destroyed() {
    this.chartDom = null
    this.myChart = null
  }
}
</script>

<style scoped lang="scss">
.trinity-majorcadre-wrap {
  position: relative;

  @keyframes keyHaloMove {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
    100% {
      opacity: 1;
    }
  }

  #TrinityMajorCadre {
    width: 740px;
    height: 400px;
    margin-top: 58px;
    margin-left: -244px;
  }

  .cockipt-mode-vice-title {
    left: 0;
    top: 25px;
  }

  .grid-text {
    position: absolute;
    left: 26px;
    top: 100px;
    z-index: 2;
    width: 200px;
    height: 200px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .count {
      margin: 0;
      color: #e6b607;
      font-size: 68px;
      font-family: 'PangMenZhengDao';
    }
    .text {
      font-size: 32px;
    }
  }

  .halo {
    position: absolute;
    left: 26px;
    top: 100px;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: url('../../images/yuanquan.png') no-repeat center center;
    background-size: 100% 100%;
    animation: keyHaloMove 5s infinite;
  }
}
</style>


