/* 本月处置 */

<template>
  <section class="dispose-month-wrap">
    <CommonTitle :title="title"
      :isRotuerThrow="true"
      :routerObj="{
        bizAppCode: 'alert',
        detailsPath: '/prewarning/events'
      }"
      class="title-box" />

    <ul class="ul-list">
      <li v-for="(item,index) of deepData"
        :key="index">
        <h6>{{item.label}}</h6>
        <strong :class="`color${index+1}`">{{~~item.value}}</strong>
        <span class="m-r-10">条</span>
        <span class="m-r-10">已解决</span>
        <i>{{item.rote}}%</i>
      </li>
    </ul>

    <div class="content-box"
      ref="refEcharts" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { echartConfigure } from '@/views/main/cockpitcren/baseData'
import { deepClone } from '@/utils'
import * as echarts from 'echarts'
import CountTo from 'vue-count-to'
import CommonTitle from '@/views/main/cockpitSecondary/components/CommonTitle.vue'

type EChartsOption = echarts.EChartsOption

@Component({
  components: {
    CountTo,
    CommonTitle
  }
})
export default class extends Vue {
  @Prop() private title!: string // 标题
  @Prop() private echartsData!: any // 渲染数据

  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private deepData: any[] = []
  private seriesData: any[] = []
  private option: EChartsOption = {}

  // 数据变化，渲染视图
  @Watch('echartsData', { deep: true })
  private changeEchartsData() {
    this.initData()
  }

  // 组件初始化
  private mounted() {
    this.chartDom = this.$refs.refEcharts as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)
  }

  // 请求数据接口
  private async initData() {
    let data: any = deepClone(this.echartsData)

    // 组装echarts格式数据
    let baseOption = {
      type: 'gauge',
      startAngle: 90,
      endAngle: 360,
      clockwise: false,
      center: ['45%', '50%'],
      axisLine: {
        lineStyle: {
          width: 20,
          color: [[1, 'rgba(9, 48, 145, 1)']]
        }
      },
      pointer: {
        show: false
      },
      progress: {
        show: true,
        overlap: false,
        roundCap: true,
        clip: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false
      },
      axisLabel: {
        show: false
      },
      anchor: {
        show: false
      },
      title: {
        show: false
      }
    }

    let seriesData: any[] = []
    Array.isArray(data) &&
      data.forEach((item: any, index: number) => {
        let radius = 80 - index * 20
        let data = [
          {
            value: item.value,
            name: item.label
          }
        ]

        seriesData.push(
          Object.assign(
            {
              min: 0,
              max: +item.total ? +item.total : 100,
              radius: `${radius}%`,
              detail: {
                show: false,
                valueAnimation: true,
                fontSize: 20,
                color: '#fff'
              },
              data: data
            },
            baseOption
          )
        )
      })

    this.deepData = data
    this.seriesData = seriesData
    this.initEcharts()
  }

  // 初始化数据
  private initEcharts() {
    let seriesData = this.seriesData
    let tooltipData: any = Object.assign(
      {
        trigger: 'item',
        valueFormatter: (value: number) => `${value}条`
      },
      echartConfigure.tooltip
    )

    this.option = {
      color: ['#fb3f3f', '#ff7500', '#F5B331', '#249DF7'],
      tooltip: tooltipData,
      series: seriesData,
      animationDelay: function (idx) {
        return idx * 300
      }
    }

    this.myChart && this.myChart.setOption(this.option, true)
  }
}
</script>

<style scoped lang="scss">
.dispose-month-wrap {
  position: relative;
  height: 100%;
  h6,
  p {
    margin: 0;
  }
  .color1 {
    color: #fb3f3f;
  }
  .color2 {
    color: #ff7500;
  }
  .color3 {
    color: #f5b331;
  }
  .color4 {
    color: #249df7;
  }

  .title-box {
    position: absolute;
    width: 100%;
  }
  .content-box {
    position: absolute;
    z-index: 2;
    width: 97%;
    height: 100%;
    transform: translateY(60px) translateX(-30px);
  }

  .ul-list {
    position: absolute;
    z-index: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    box-sizing: border-box;
    left: 322px;
    top: 124px;
    width: 526px;
    height: 244px;
    li {
      display: flex;
      align-items: center;
      height: 30px;
      overflow: hidden;
      font-size: 26px;
      h6 {
        font-size: 26px;
        margin-right: 7px;
      }
      strong {
        font-size: 40px;
        margin-right: 4px;
        font-weight: normal;
        font-family: 'PangMenZhengDao';
      }
      i {
        font-size: 28px;
      }
    }
  }
}
</style>


