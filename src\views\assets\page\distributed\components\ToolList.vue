<template>
  <div class="assets-map-tool">
    <header class="map-tools-header">
             <el-cascader
            v-model="searchform.orgId"
            :props="{label:'title',value:'orgId'}"
            placeholder="公司名称"
            :options="compTree"
            @change="changSelect"
            clearable=""
          ></el-cascader>
      <!-- <el-select  v-model="searchform.companyCode" @change="changeMarkerPoint(searchform)" placeholder="请选择" >
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" @change="changSelect"></el-option>
      </el-select> -->
      <el-input @keydown.enter="changSelect"   clearable placeholder="请输入关键词" @change="changSelect(searchform)" v-model="searchform.keyword"  >
         <el-button class="bcfff"   slot="append" 
          icon="el-icon-search" 
          @click="changSelect"/>
      </el-input>
      <el-button type="text" v-show="!showPutAway" @click="btnputAway(true)" class="m-l-10 m-r-10">
        <span class="updown">
          <i class="el-icon-arrow-down"></i>
          <i class="el-icon-arrow-down"></i>
        </span>
      </el-button>
      <el-button type="text" v-show="showPutAway" @click="btnputAway(false)" class="m-l-10 m-r-10">
        <span class="updown">
          <i class="el-icon-arrow-up"></i>
          <i class="el-icon-arrow-up"></i>
        </span>
      </el-button>
    </header>

    <transition name="el-zoom-in-left">
      <section v-if="showPutAway && !showType" style="background: #fff; padding: 20px 5px">
        <div class="">
          <span class="table-header">类型
             
          </span>
          <div class="p-l-15">
            <el-checkbox :indeterminate="isTypeCheckAll" v-model="TypeCheckAll" @change="handleCheckAllChange">全选</el-checkbox>
            <el-checkbox-group v-model="searchform.assetBizStatus" @change="changSelect">
              <el-checkbox v-for="(city,index) in getDictData('asset_biz_status')" :label="city.value" :key="index">{{ city.label }}</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <!-- <div>
          <span class="table-header">城市</span>
          <div class="p-l-15">
            <el-button type="text">全部</el-button>
            <el-button type="text">金华</el-button>
          </div>
        </div> -->
        <div>
          <span class="table-header">区域</span>
          <div class="p-l-15">
            <el-radio-group v-model="searchform.areaCode" @change="changSelect">
              <el-radio class="p-t-10" v-for="(item,index) in getDictData('areaCode')" :label="item.value" :key="index">{{ item.label }}</el-radio>
            </el-radio-group>
          </div>
        </div>
        <div>
          <span class="table-header">详情数据</span>
          <!-- 表格 -->
          <el-table :data="tableData" style="width: 100%" v-loading="loadingTable">
            <el-table-column prop="propertyTypeDesc" label="类型" width="60"></el-table-column>
            <el-table-column prop="assetNum" label="不动产(处)" width="80"></el-table-column>
            <el-table-column prop="landArea" label="土地面积(㎡)" width="85"></el-table-column>
            <el-table-column prop="constructionArea" label="建筑面积(㎡)" width="85"></el-table-column>
            <el-table-column prop="rentRate" label="出租率" width="80"></el-table-column>
          </el-table>
        </div>
        <!-- <el-button slot="prepend" icon=""></el-button> -->
      </section>
    </transition>

    <!-- 详情数据 -->
    <transition name="el-zoom-in-left">
      <section v-show="showPutAway && showType">
        <com-marker-info :Markerinfo="Markerinfo" @changeShowType="changeShowType" />
      </section>
    </transition>
  </div>
</template>
<script lang="ts">
import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator'
import ComMarkerInfo from './MarkerInfo.vue'
import { frontPageList } from '@/api/assets'
import { Throttle } from "@/decorators";
  import { BusinessModule } from '@/store/modules/businessDict'
import { createPlanIni, createProject, getCompTree } from '@/api/projectInvestment'
import { deepClone } from '../../../../../utils';

@Component({
  components: {
    ComMarkerInfo
  }
})
export default class Container extends Vue {
  @Prop() private showPutAway!: boolean
  @Prop() private showType!: boolean
  @Prop() private Markerinfo!: { assetNo: ""; bizStatus: ""; latitude: ""; longitude: "" }
  @Watch("searchform",{deep:true})
  private changeSearch(){
      // 控制全选
      if(this.searchform.assetBizStatus.length!=this.types.length&&this.searchform.assetBizStatus.length!=0){
          this.TypeCheckAll=false
      }
  }
  private loadingTable = false
  private tableData :any= []
  private TypeCheckAll=false
  private isTypeCheckAll=false
  private searchform = {
    // companyCode: "1",
    city: 0,
    assetBizStatus: [],
    // assetTypeSubclass: 0, //资产子类，0:取所有，1-住宅，2-商铺，3-土地，4-其他
    areaCode: "0",
    // size: 999,
    // current: "0",
    orgId:'',
    keyword:""
  }
  private options = [
    {
      value: '1',
      label: '全部'
    },
    {
      value: '2',
      label: '城投集团'
    },
    {
      value: '3',
      label: '交投集团'
    },
    {
      value: '4',
      label: '金投集团'
    },
    {
      value: '5',
      label: '轨道集团'
    }
  ]
  // types = ['住宅', '楼宇', '园区', '商铺', '土地', '其他']
//  private types = ['住宅', '楼宇', '园区', '商铺', '土地', '其他']
 private types = this.getDictData("asset_biz_status")
  private area = this.getDictData("areaCode")
  private searchvalue = '' //搜索值
  // 组织机构值
private compTree = []
   mounted() {
    this.getfrontPageList()
  }
  created () {
    this.getCompTree()
    this.handleCheckAllChange(true)
    // this.isTypeCheckAll=true
    this.TypeCheckAll=true
  }
get getDictData() {
  return (key: string) => {
    let dictionary: any = BusinessModule.dictLaodData || {}
    return dictionary[key] ? dictionary[key] : []
  }
}
  // 拉取接口远程数据
  // @Throttle
  private async getfrontPageList() {    
    this.loadingTable = true
    let params = {
      ...this.searchform
    }
     params.orgId=this.searchform.orgId[this.searchform.orgId.length-1]
    try {
      let res = await frontPageList(params)
      if (res.success) {
        this.tableData = res.data
        this.loadingTable = false
      }
    } catch (e) {
      this.loadingTable = false
    }
  }
  // 触发全选
  private handleCheckAllChange(val:any){
        if(val){
          this.searchform.assetBizStatus=this.types.map((item:any)=>{
            return item.value
          })
          //  this.isTypeCheckAll=false
        }
        else{
          this.searchform.assetBizStatus=[]
          // this.isTypeCheckAll=false
        }
        this.changSelect()
  }
    //获取机构数组
  private async getCompTree() {
    let options = []
    let res = await getCompTree(
      {category:1}
    )
    if (res.success) {
      options = res.data
    }
    this.compTree = options
  }
  // 改变列表数据之后切换数据
  @Throttle
  private changSelect() {
    this.btnputAway(true)
    this.changeShowType(false)
    this.getfrontPageList()
    this.changeMarkerPoint(this.searchform)
  }
  @Emit('changeShowPutAway')
  private btnputAway(state: boolean) {
    return state
  }
  @Emit('changeShowType')
  private changeShowType(state: boolean) {
    return state
  }
  @Emit('changeMarkerinfo')
  private changeMarkerPoint(searchform:any) {
    let params:any=deepClone(searchform)
    
   params.orgId=this.searchform.orgId[this.searchform.orgId.length-1]
    return params
  }
}
</script>

<style lang="scss" scoped>
.el-input-group__prepend {
  background-color: #fff !important;
  border: none !important;
}
.bcfff{
  background-color: #fff !important;
  border: 0px ;
}
.assets-map-tool {
  ::v-deep .el-input__inner{
    border: 1px solid #fff;
  }
  position: relative;
  .table-header {
    border-left: 5px solid #303133;
    padding-left: 15px;
    color: #303133;
  }
  border-radius: 5px;
  width: 420px;
  //   height: 500px;
  // background: #fff;
  // padding: 15px;
  .map-tools-header {
    background: #fff;

    // margin: 10px 0;
    height: 36px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .search-section {
    display: flex;
    justify-content: space-around;
  }
  section {
    position: absolute;
    top: 40px;
    // background: #fff;
    // padding: 20px;
    ::v-deep .el-descriptions-item__label {
      line-height: 30px;
      font-size: 14px;
    }
    margin: 0 0;
    line-height: 25px;
    div {
      margin: 5px 0;
      // font-size: 15px;
    }
    ::v-deep .el-table__header-wrapper {
      font-size: 12px;
    }
    ::v-deep .el-table th.el-table__cell > .cell {
      font-size: 12px;
      padding: 0 5px;
    }
  }
  .updown {
    display: flex;
    flex-wrap: wrap;
    margin: 0;
    i:nth-child(2) {
      transform: translateY(-4px);
    }
  }
}

</style>
