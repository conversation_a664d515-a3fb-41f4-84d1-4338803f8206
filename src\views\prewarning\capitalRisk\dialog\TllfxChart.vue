<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts'
// import resize from '@/views/dashboard/mixins/resize.js'

export default {
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
  },

  data () {
    return {
      chart: null,
      option: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        legend: {
          data: [
            {
              name: '偏离度',
              textStyle: { color: '#666' }
            },
            // {
            //   name: '实际盈亏率',
            //   textStyle: { color: '#666' }
            // }
          ],
          top: '2%',
          show: true,
          itemGap: 30,
        },
        grid: {
          left: '0%',
          right: '0%',
          bottom: '5%',
          top: '15%',
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          axisTick: { show: false },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#D3D3D3",
              width: 1
            }
          },
          axisLabel: {
            color: '#666',
            fontSize: 12,
            interval: 0,
            width: 45,        // 设置标签容器宽度
            overflow: 'truncate', // 超出显示省略号
            ellipsis: '...'   // 自定义省略号样式
          },
          data: ['现金流动负债比率', '资产负债率', '带息负债比率', '已获利息倍数', '速动比率', '净资产收益率']
        }],
        yAxis: [{
          type: 'value',
          show: true,
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: '#E8E8E8'
            }
          },
          axisLabel: {
            color: '#666',
            formatter: '{value}'
          }
        }],
        series: [
          {
            name: '偏离度',
            type: 'bar',
            barWidth: '20%',
            itemStyle: {
              color: '#C23531' // 红色系
            },
            data: [15, 20, -15, -3, 17, 5]
          },
          // {
          //   name: '实际盈亏率',
          //   type: 'bar',
          //   barWidth: '20%',
          //   itemStyle: {
          //     color: '#ffa500' // 橙色系
          //   },
          //   data: [-5, 8, 7, -10, -15, 15]
          // }
        ]
      }
    }
  },

  beforeDestroy () {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },

  methods: {

    preInitChart () {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
      this.$nextTick(() => {
        this.initChart()
      })
    },

    initChart () {
      this.chart = echarts.init(this.$el, 'macarons')

      this.chart.setOption(this.option)
    }
  }
}
</script>
