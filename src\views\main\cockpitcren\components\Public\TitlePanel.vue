<template>
  <section class="panel-title-wrap">
    <!-- 左侧 -->
    <div class="panel panel-left">
      <i class="el-icon-time icons" />
      <span class="time m-r-20">{{nowTime}} {{momentHms}}</span>

      <el-tooltip effect="dark"
        content="架构图"
        placement="top-start">
        <i class="el-icon-set-up icons m-r-20"
          @click="visibleArchitecture = true" />
      </el-tooltip>

      <!-- <el-tooltip effect="dark"
        content="PPT"
        placement="top-start">
        <i class="icons el-icon-document-copy m-r-30"
          @click="viewPPT('https://fh-ka.oss-cn-hangzhou.aliyuncs.com/监管系统20220802.pptx')" />
      </el-tooltip> -->
    </div>

    <!-- 大标题 -->
    <div class="panel-middel">
      <span class="gd" />
      <CockpitTitle title="金华市国资委数智监管系统"
        :height="174"
        :fontSize="80"
        color="#fff"
        class="m-f-t-10" />
    </div>

    <!-- 右侧 -->
    <div class="panel panel-right">
      <Weather />
      <img v-if="!isWindowFull"
        src="@/views/main/cockpitcren/images/full.png"
        class="full"
        @click="inWindowFullScreen" />
      <el-button v-else
        type="primary"
        icon="el-icon-s-unfold"
        class="full full-out"
        title="退出全屏"
        @click="outWindowExitFullScreen()" />
    </div>

    <!-- 查看架构图 -->
    <Architecture v-if="visibleArchitecture"
      :visible.sync="visibleArchitecture" />
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { viewOffice, windowExitFullScreen } from '@/utils'
import Weather from '@/components/Weather/index.vue'
import Architecture from '@/views/main/cockpitcren/components/Public/Architecture.vue'
import CockpitTitle from '@/views/main/cockpitcren/components/Public/CockpitTitle.vue'

@Component({
  components: {
    Weather,
    Architecture,
    CockpitTitle
  }
})
export default class extends Vue {
  private momentHms = ''
  private nowTime = ''
  private isWindowFull = false
  private visibleArchitecture = false
  private optionTimes: any[] = []


  // 组件初始化
  private mounted() {
    let that = this as any
    this.nowTime = that.$moment(new Date()).add('year', 0).format('YYYY-MM-DD')

    this.windowFullScreen()
    this.setInterHms()
  }

  // 监听全屏触发
  private windowFullScreen() {
    this.$bus.$on('BusWindowFullScreen', (data: boolean) => {
      this.isWindowFull = data
    })
  }

  // 获取当前星期
  private getMomentWeek() {
    let that = this as any
    return that.$moment().format('dddd')
  }

  // 设置倒计时
  private setInterHms() {
    setInterval(() => {
      let that = this as any
      this.momentHms = that.$moment().format('HH:mm:ss')
    }, 1000)
  }

  // 查看ppt文件
  private viewPPT(src: string) {
    viewOffice(src)
  }

  // 进入全屏
  private inWindowFullScreen() {
    this.$emit('inWindowFullScreen')
  }

  // 退出全屏
  private outWindowExitFullScreen() {
    return windowExitFullScreen()
  }
}
</script>

<style scoped lang="scss">
.panel-title-wrap {
  $textColor: #63f1ff;

  position: relative;
  display: flex;
  justify-content: space-between;
  color: $textColor;
  padding: 0 30px;
  background: url('../../images/panel_title_bg.png') no-repeat center center;
  background-size: 100%;

  @keyframes keyGdMove {
    0% {
      left: -100px;
      opacity: 0.8;
    }
    25% {
      left: 163px;
      opacity: 1;
    }
    50% {
      left: 463px;
      opacity: 0.8;
    }
    75% {
      left: 163px;
      opacity: 1;
    }
    100% {
      left: -100px;
      opacity: 0.8;
    }
  }

  .pointer {
    cursor: pointer;
  }

  .panel {
    position: relative;
    display: flex;
    align-items: center;
    width: 860px;
    margin-top: -20px;
  }

  .panel-left {
    margin-right: 30px;
    .icons {
      font-size: 50px;
      margin-right: 14px;
      margin-top: -5px;
      cursor: pointer;
    }
    .time {
      width: 450px;
      font-size: 45px;
      font-weight: normal;
      // font-family: 'digital-7';
    }
  }

  .panel-middel {
    position: relative;
    flex: 1;
    height: 174px;
    background: url('../../images/panel_title.png') no-repeat center top;
    background-size: 100% 100%;
    .gd {
      position: absolute;
      left: -100px;
      bottom: -38px;
      width: 1000px;
      height: 158px;
      background: url('../../images/head_gd.png') no-repeat center top;
      background-size: 100% 100%;
      animation: keyGdMove 40s ease infinite;
    }
  }

  .panel-right {
    justify-content: end;
    .full {
      width: 140px;
      font-size: 40px;
      margin-right: -20px;
      cursor: pointer;
    }
    .full-out {
      color: #3aa7d2;
      width: 100px;
      margin-right: 20px;
      border: 1px solid #3aa7d2;
      background: none !important;
    }
  }
}
</style>