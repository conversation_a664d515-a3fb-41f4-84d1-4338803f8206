// 成交结果
<template>
  <section class="rental-info">
    <!-- 基本情况 -->
    <el-descriptions class="margin-top"
      title=""
      :column="24"
      :labelStyle="{
        width: '120px'
      }"
      border>
      <el-descriptions-item label="标的名称"
        :span="24">
        {{ listingInfoDetail.currentAsset.subjectName }}
      </el-descriptions-item>
      <el-descriptions-item label="承租方名称"
        :span="24">
        {{ listingInfoDetail.currentAsset.originalLessee }}
      </el-descriptions-item>

      <el-descriptions-item label="年平均租金(元)"
        :span="12">
        {{ listingInfoDetail.deal.npjzj }}
      </el-descriptions-item>

      <el-descriptions-item label="是否分级计价"
        :span="12">
        {{ listingInfoDetail.deal.sffbjj }}
      </el-descriptions-item>

      <el-descriptions-item label="成交价格(元)"
        :span="12">
        {{ listingInfoDetail.deal.cjjg }}
      </el-descriptions-item>

      <el-descriptions-item label="成交价单位"
        :span="12">
        {{ listingInfoDetail.deal.cjjdw }}
      </el-descriptions-item>

      <el-descriptions-item label="场内结算金额(元)"
        :span="12">
        {{ listingInfoDetail.deal.cjzjzjHfwf }}
      </el-descriptions-item>

      <el-descriptions-item label="场内结算金额（不含服务费）(元)"
        :span="12">
        {{ listingInfoDetail.deal.cjzjzjBhfwf }}
      </el-descriptions-item>
      <el-descriptions-item label="出租面积（平方米）"
        :span="12">
        {{ listingInfoDetail.deal.czmj }}
      </el-descriptions-item>
      <el-descriptions-item label="租赁时长"
        :span="12">
        {{ listingInfoDetail.deal.zlscdwYear }}年
      </el-descriptions-item>
      <el-descriptions-item label="起租日"
        :span="12">
        {{ listingInfoDetail.deal.qzr }}
      </el-descriptions-item>
      <el-descriptions-item label="到期日"
        :span="12">
        {{ listingInfoDetail.deal.dqr }}
      </el-descriptions-item>
      <el-descriptions-item label="租金调整方式"
        :span="12">
        {{ listingInfoDetail.deal.zjtdfs }}
      </el-descriptions-item>

      <el-descriptions-item label="租金要求"
        :span="12">
        {{  listingInfoDetail.deal.zjzfyq }}
      </el-descriptions-item>

      <el-descriptions-item label="其他租金调整方式说明"
        :span="24">
        {{ listingInfoDetail.deal.qtzjtzfs }}
      </el-descriptions-item>

      <el-descriptions-item label="保证金金额(%)"
        :span="12">
        {{ listingInfoDetail.deal.bzjje }}
      </el-descriptions-item>

      <el-descriptions-item label="首府付款比例(%)"
        :span="12">
        {{ listingInfoDetail.deal.sqfkbl }}
      </el-descriptions-item>

      <el-descriptions-item label="尾款截止日期"
        :span="12">
        {{ listingInfoDetail.deal.wkjzrq }}
      </el-descriptions-item>

      <el-descriptions-item label="结算方式"
        :span="12">
        {{ listingInfoDetail.deal.jsfs }}
      </el-descriptions-item>

      <el-descriptions-item label="是否外币结算"
        :span="12">
        {{ listingInfoDetail.deal.sfwbjs }}
      </el-descriptions-item>

      <el-descriptions-item label="交易方式"
        :span="12">
        {{ listingInfoDetail.deal.jyfs }}
      </el-descriptions-item>

      <el-descriptions-item label="实际交易方式"
        :span="12">
        {{ listingInfoDetail.deal.sjjyfs }}
      </el-descriptions-item>

      <el-descriptions-item label="结果公示起始日期"
        :span="12">
        {{ listingInfoDetail.deal.jggsksrq }}
      </el-descriptions-item>

      <el-descriptions-item label="结果公示周期(日期)"
        :span="12">
        {{ listingInfoDetail.deal.jggszq }}
      </el-descriptions-item>

      <el-descriptions-item label="结果公示截止日期"
        :span="12">
        {{ listingInfoDetail.deal.jggsjsrq }}
      </el-descriptions-item>

      <el-descriptions-item label="成交日期"
        :span="12">
        {{ listingInfoDetail.deal.cjrq }}
      </el-descriptions-item>

      <el-descriptions-item label="年平均租金(元/年)"
        :span="12">
        {{ listingInfoDetail.deal.pjnzj }}
      </el-descriptions-item>

      <el-descriptions-item label="首年租金(元)"
        :span="12">
        {{ listingInfoDetail.deal.snzj }}
      </el-descriptions-item>

      <el-descriptions-item label="平均月租金(元/月)"
        :span="12">
        {{ listingInfoDetail.deal.pjyzj }}
      </el-descriptions-item>

      <el-descriptions-item label="鉴证编号"
        :span="12">
        {{ listingInfoDetail.deal.jzbh }}
      </el-descriptions-item>
    </el-descriptions>
  </section>
</template>

<script lang="ts">
import { decisionTypeList } from '@/views/assets/filterOptions'
import { ElForm } from 'element-ui/types/form'
import { Component, Model, Vue } from 'vue-property-decorator'

export interface ListingInfoDetail {
  internalDecisionSituation: number
  otherDecision: string
  approvedEntityName: string
  approvedDocumentName: string
  isStateOwnedAssets: number
  departmentName: string
}

@Component
export default class extends Vue {
  @Model('change') private initData!: ListingInfoDetail

  private listingInfoDetail: ListingInfoDetail = {
    internalDecisionSituation: 0,
    otherDecision: '',
    approvedEntityName: '',
    approvedDocumentName: '',
    isStateOwnedAssets: 0,
    departmentName: ''
  }

  private decisionTypeList = decisionTypeList

  created() {
    this.listingInfoDetail = Object.assign(this.listingInfoDetail, this.initData)
  }

  public validate(): Promise<boolean> {
    let form = this.$refs.RentalInfoForm as ElForm
    return form.validate()
  }
}
</script>


<style scoped lang="scss">
::v-deep .el-radio__label {
  line-height: 36px;
}
::v-deep .department-name-input {
  .el-form-item {
    margin-bottom: 0px !important;
  }
}
</style>