<template>
  <Dialog
    title="企业发债审批-详情"
    width="980px"
    :visible="visible"
    :close-on-click-modal="true"
    :destroy-on-close="true"
    :append-to-body="true"
    @close="handleClose"
    @open="openDiaHandle"
  >
    <div slot="body" class="descriptions-body">
      <Descriptions v-bind="$attrs"></Descriptions>
      <!-- <l-divider></l-divider> -->
      <!-- 审批流 -->
      <!-- <Stel></Stel> -->
      <AccessoryFileList v-model="DetailForm.attachmentFileDTOList" dict="financial_examinationMean_attach" mode="see" />
    </div>
    <div slot="footer">
        <div></div>
        <!-- <el-button @click="handleClose">附件列表</el-button> -->
        <el-button @click="handleClose">关闭</el-button>
        <!-- <el-button type="primary"  @click="handleClose" class="finance-red-btn">确认</el-button> -->
    </div>
  </Dialog>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import Descriptions from './Descriptions/index.vue'
import Dialog from '@/components/Dialog/index.vue'
import Stel from './Stel/index.vue'
import AccessoryFileList from "./AccessoryFileList.vue"
@Component({
  components: {
    Descriptions,
    Dialog,
    Stel,
    AccessoryFileList
  }
})
export default class Container extends Vue {
  @Prop() private visible!: boolean
  @Prop() private DetailForm!: {}

  private handleClose() {
    // this.visible = false
    this.$emit("changeShowDialogDetail",false)
  }
  openDiaHandle() {
    // this.visible = true
  }
}
</script>

<style lang="scss" scoped>
.descriptions-body {
  ::v-deep.el-descriptions__body .el-descriptions__table .el-descriptions-item__cell {
    font-size: 14px;
    color: #303133;
  }
}
.finance_red_btn {
    background: #b43c3c;
    border-radius: 2px;
    border-radius: 2px;
    color: #fff;
    width: 82px;
    height: 36px;
    padding: 0;

}

.finance_white_btn {
    background: #fff;
    border-radius: 2px;
    border-radius: 2px;
    color: #b43c3c;
    width: 82px;
    height: 36px;
}

</style>
