// 附件列表

<template>
  <section class="accessory-list">
    <div class="accessory-list__title">
      <span style="color:#fb3f3f;"
        v-if="mode == 'upload'">*</span>
      {{ title }}
      <span style="margin-left:15px;"
        class="p-l-60">
        <el-button icon="el-icon-upload"
          :type="btntype"
          @click="onUpload">
          附件{{ mode == 'upload' ? '上传' : '查看' }}({{ Filelists.length || 0 }})
        </el-button>
      </span>
    </div>

    <!-- 导入 -->
    <uploader v-model="Filelists"
      :title="uploaderDlgTitle"
      :visible.sync="uploaderDlgVisible"
      :uploadable="mode == 'upload' ? true : false"
      url-key="link"
      :show-cover="false"
      :is-private="false"
      :is-attachment="false"
      :uploader-tags="getDictData(dict)"
      @change="onFilesChange"
      @uploadComplete="handleUploadComplete"
      :uploaderTip="uploaderTip" />
  </section>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch, Model } from 'vue-property-decorator'
import Grid from '@/components/Grid/index.vue'
import Operation, { OperationItem } from '@/components/Operation/index.vue'
import Uploader from '@/components/Uploader/index.vue'
import { BusinessModule } from '@/store/modules/businessDict'

export interface AttachmentFile {
  attachSize: number // 附件大小
  createDept: number // 创建部门
  createTime: string // 创建时间
  createUser: number // 创建人
  domainUrl: string // 附件域名
  extension: string // 附件拓展名
  id: number // 附件ID
  isDeleted: number // 是否已删除
  link: string // 附件地址
  name: string // 附件名称
  originalName: string // 附件原名
  status: number // 业务状态
  tenantId: string // 租户ID
  updateTime: string // 更新时间
  updateUser: string | number //	更新人
}

// 对应接口字段名
export interface Accessory {
  fileName: string
  fileList: any[]
  isRequired: boolean
  mobelUrl?: string
  slotName?: string
  prop?: string
}

@Component({
  components: {
    Grid,
    Operation,
    Uploader
  }
})
export default class extends Vue {
  @Prop() private dict!: string
  @Prop({ default: '附件列表：' }) private title!: string
  @Prop({ default: 'primary' }) private btntype!: string
  @Prop({ default: 'download' }) private mode!: 'upload' | 'download' | 'see' // 上传、下载
  @Prop({ default: true }) private isAttachment!: boolean // 上传、下载
  @Watch('list', { deep: true, immediate: true })
  uploaderTipset() {
    this.uploaderTip =
      '请上传：' +
      this.getDictData(this.dict)
        .map((item: any, index: number) => {
          // return index + 1 + ',' + item.label
          if (item.isRequired == 1) {
            return index + 1 + '.' + item.label
          } else {
            return ''
          }
        })
        .join(' ')
    this.Filelists = this.list

    this.$forceUpdate()
    //  await (this.$refs.RentalInfoForm as any).setData()
  }
  private onListChange() {
    this.Filelists = this.list
  }
  @Model('input') private list: any
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  created() {
    this.Filelists = this.list || []
    this.uploaderDlgMode = this.mode
  }
  private uploaderTip = ''
  private uploaderDlgVisible = false
  private uploaderDlgMode = 'download'
  private currentRow: Partial<Accessory> = {}
  private Filelists = []
  // private check=1  //0 不校验 1需要未通过 2，通过校验
  get cols() {
    return [
      {
        slotName: 'fileNameSlot',
        label: '文件名',
        minWidth: 350
      },
      {
        slotName: 'statusSlot',
        label: '状态',
        minWidth: 80
      },
      {
        slotName: 'numSlot',
        label: '附件数量',
        minWidth: 100
      },
      {
        prop: '',
        label: '操作',
        width: this.mode === 'upload' ? 145 : 80,
        slotName: 'operationSlot',
        fixed: 'right',
        labelAlign: 'center',
        minWidth: 100
      }
    ]
  }

  // 操作按钮
  get operationList(): OperationItem[] {
    return [
      {
        label: '上传',
        style: 2,
        click: this.onUpload,
        visible: (row: any) => {
          return this.mode === 'upload'
        }
      },
      {
        label: '查看',
        click: this.onSee,
        style: 3,
        visible: (row: any) => {
          return true
        }
      }
    ]
  }

  get fileList() {
    return this.currentRow.fileList
  }

  get uploaderDlgTitle() {
    return this.uploaderDlgMode === 'upload' ? '上传' : '查看'
  }

  get uploadable() {
    return this.uploaderDlgMode === 'upload'
  }
  private getStateValue(values: any = '') {
    return this.getDictData(this.dict).find((res: any) => {
      return res.value == values
    }).label
  }
  private onSee(row: Accessory) {
    this.uploaderDlgMode = 'see'
    // this.currentRow = row
    this.uploaderDlgVisible = true
  }
  private onUpload(row: Accessory) {
    this.uploaderDlgMode = 'upload'
    // this.currentRow = row
    this.uploaderDlgVisible = true
  }

  private onFilesChange(fileList: string[]) {
    // this.currentRow.fileList = [...fileList]
  }
  // 对文件类型做出校验
  private checkFile() {
    return new Promise<void>((resolve: Function, reject: Function) => {
      let selectFile: any = []
      // this.getDictData(this.dict).forEach((item:any,index:number)=>{
      // 判断是否选择标签
      this.Filelists.forEach((item: any, index: number) => {
        if (item.tag == undefined) {
          this.$message.error(`请选择${item.name}的文件属性`)
          reject(false)
        }
        let isupload: any = this.getDictData(this.dict).find((res: any) => {
          return res.value == item.tag
        })
        // this.getDictData(this.dict).forEach((i:any,n:number)=>{
      })
      resolve(true)
    })
  }
  private handleUploadComplete(fileList: any) {
    this.Filelists = Object.assign(this.Filelists, fileList)
    this.$emit('input', this.Filelists)
  }

  // 下载模板
  private downMobel() {
    let time = new Date().getTime()
    let { protocol } = window.location
    window.open(protocol + `${this.currentRow.mobelUrl}?time=` + time)
  }

  // 刷新表格
  private refresh() {
    let grid = this.$refs['grid'] as Grid
    grid && grid.refresh()
  }
}
</script>

<style scoped lang="scss">
.accessory-list {
  &__title {
    font-size: 15px;
    font-weight: 500;
    color: #535353;
    margin: 12px 0 16px;
  }
}

::v-deep .file-name {
  display: flex;
  align-items: center;
  .text {
    margin-right: 12px;
  }
}
</style>
