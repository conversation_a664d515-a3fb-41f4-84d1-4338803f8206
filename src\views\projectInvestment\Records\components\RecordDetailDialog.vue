/**
  组件描述:  新增年度计划投资弹框
*/
<template>
  <Dialog :title="`投资项目计划备案-${mode === 'see' ? '查看' : '审批'}`"
    width="980px"
    :visible="visible"
    :close-on-click-modal="true"
    :destroy-on-close="true"
    :append-to-body="true"
    @close="closeDlg"
    v-loading="loading">
    <div slot="body"
      class="">

      <el-descriptions title="">
        <el-descriptions-item label="备案编号">{{ investProjectPlanFiling.planNo }}
        </el-descriptions-item>
        <el-descriptions-item label="填报单位">{{ investProjectPlanFiling.orgName }}
        </el-descriptions-item>
        <el-descriptions-item label="年份">{{ investProjectPlanFiling.year }}</el-descriptions-item>
      </el-descriptions>

      <div class="plan-table-wrapper">
        <PlanTable ref="PlanTable"
          :list="investProjectPlanFiling.filingDetailList"
          :loading="loading"
          :recordNumber="investProjectPlanFiling.id"
          mode="see" />
      </div>

      <!-- <div class="uploader-list">
        <Uploader title="1.投资计划报告"
          mode="download"
          v-model="investProjectPlanFiling.investmentPlanReport" />
        <Uploader title="2.项目计划表"
          mode="download"
          v-model="investProjectPlanFiling.projectSchedule" />
        <Uploader title="3.董事会决议"
          mode="download"
          v-model="investProjectPlanFiling.boardResolution" />
        <Uploader title="4.其他决策文件"
          mode="download"
          v-model="investProjectPlanFiling.otherDecisionDocuments" />
      </div> -->

      <AccessoryList v-if="fileList.length!=0"
        v-model="fileList"
        mode="see"
        dict="invest_plan_file_type"
        class="m-20" />

      <!-- 审批流 -->
      <!-- <AuditFlow :mode="mode" /> -->

    </div>
    <div slot="footer">
      <el-button v-if="mode === 'see'"
        @click="closeDlg">关闭</el-button>
      <el-button v-if="mode === 'audit'"
        @click="closeDlg">取消</el-button>
      <el-button v-if="mode === 'audit'"
        class="primary-buttom"
        type="primary"
        :loading="loading"
        @click="submitForm">确认</el-button>
    </div>
  </Dialog>
</template>
<script lang="ts">
import { Component, Emit, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import { Loading } from '@/decorators'
import PlanTable from '../../components/PlanTable/PlanGrid.vue'
import Uploader from '../../components/Uploader/index.vue'
import AuditFlow from '../../components/AuditFlow.vue'
import AccessoryList, { Accessory } from '@/views/assets/components/TradeDialog/components/AccessoryFileList.vue'
import { investPLanInfo } from '@/api/projectInvestment'
import { number } from 'echarts'
interface InvestProjectPlanFiling {
  boardResolution: string
  businessSubmissionTime: string
  companyCode: string
  enterpriseSubmitter: string
  investmentPlanReport: string
  otherDecisionDocuments: string
  projectSchedule: string
  recordNumber: string
  year: string
  id: string | number
}

@Component({
  components: {
    Dialog,
    Uploader,
    PlanTable,
    AuditFlow,
    AccessoryList
  }
})
export default class extends Vue {
  @Prop() private visible!: boolean
  @Prop() private title!: string
  @Prop() private mode!: 'audit' | 'see'
  @Prop() private detail!: InvestProjectPlanFiling

  private loading = true
  private fileList = []

  private investProjectPlanFiling: any = {
    id: '',
    boardResolution: '',
    businessSubmissionTime: '',
    companyCode: '',
    enterpriseSubmitter: '',
    investmentPlanReport: '',
    otherDecisionDocuments: '',
    projectSchedule: '',
    recordNumber: '',
    year: String(new Date().getFullYear() - 1),
    FileList: [],
    filingDetailList: []
  }

  private accessoryList: Accessory[] = [
    {
      fileName: '投资计划报告',
      isRequired: true,
      prop: 'investmentPlanReport',
      fileList: []
    },
    {
      fileName: '项目计划表',
      isRequired: true,
      prop: 'projectSchedule',
      fileList: []
    },
    {
      fileName: '董事会决议',
      isRequired: true,
      prop: 'boardResolution',
      fileList: []
    },
    {
      fileName: '其他决策文件',
      isRequired: true,
      prop: 'otherDecisionDocuments',
      fileList: []
    }
  ]

  // 弹窗关闭
  @Emit('close')
  private onClose() {
    //
  }
  @Loading('loading')
  async created() {
    if (this.mode == 'see') {
      this.investProjectPlanFiling = Object.assign({}, this.detail)
      try {
        let res = await investPLanInfo({
          id: this.detail.id
        })
        if (res.success) {
          this.investProjectPlanFiling = Object.assign(this.investProjectPlanFiling, res.data)
          this.fileList = res.data.fileList
          this.$forceUpdate()
          this.loading = false
        }
      } catch (e) {
        this.loading = false
      }
    }
  }

  @Loading('loading')
  private async submitForm() {
    // 审批只需对审核流进行校验
  }
  // 关闭弹窗
  private closeDlg() {
    this.$emit('update:visible', false)
    this.onClose()
  }
}
</script>

<style lang="scss" scoped>
.uploader-list {
  display: flex;
  flex-wrap: wrap;
  ::v-deep .uploader-wrapper {
    margin: 12px 24px;
  }
}
::v-deep .el-descriptions {
  padding: 0 20px;
}
</style>

