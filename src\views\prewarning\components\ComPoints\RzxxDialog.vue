<template>
  <Dialog width="1200px"
    :visible="visible"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    :append-to-body="true"
    @close="handleClose">
    <!-- 自定义标题 -->
    <div slot="header"
      class="header-box">
      <div class="title">查看详情</div>
    </div>

    <!-- 内容区域 -->
    <div v-loading="loading"
      slot="body"
      class="body-box">
      <el-form :model="queryParams"
        ref="queryForm"
        size="mini"
        label-width="68px">
        <el-row :gutter="5">
          <el-col :span="5">
            <el-form-item label="关键字"
              prop="keyword"
              class="w-full">
              <el-input v-model="queryParams.keyword"
                placeholder="请输入"
                clearable />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="融资金额"
              prop="minAmt"
              class="w-full">
              <el-input v-model="queryParams.minAmt"
                placeholder="请输入"
                clearable />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="——"
              label-width="50px"
              prop="maxAmt"
              class="w-full">
              <el-input v-model="queryParams.maxAmt"
                placeholder="请输入"
                clearable />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="单位名称"
              prop="ourCorpName"
              class="w-full">
              <el-input v-model="queryParams.ourCorpName"
                placeholder="请输入"
                clearable />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <div class="fcenter">
              <el-button type="primary"
                size="mini"
                @click="handleQuery">搜索</el-button>
              <el-button @click="resetQuery"
                size="mini">清空</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>

      <div class="unitWrap">
        <el-button type="primary"
          @click="exportHandle"
          size="mini">导出</el-button>
        <PricUnit :value="queryParams.amtUnit"
          @updataHandle="updataUnitHandle" />
      </div>

      <el-table :data="infoList"
        stripe>
        <el-table-column type="index"
          label="#"
          width="55"
          align="center"
          fixed="left" />
        <el-table-column label="所属集团"
          prop="corpName"
          align="center"
          fixed="left"
          show-overflow-tooltip />
        <el-table-column label="单位名称"
          prop="ourCorpName"
          align="center"
          fixed="left"
          show-overflow-tooltip />
        <el-table-column label="授信合同号"
          prop="creditNo"
          align="center"
          show-overflow-tooltip />
        <el-table-column label="融资合同号"
          prop="contractNo"
          align="center"
          show-overflow-tooltip />
        <el-table-column label="融资机构"
          prop="borrowOrgName"
          align="center"
          show-overflow-tooltip />
        <el-table-column label="融资帐号"
          prop="borrowAccount"
          align="center"
          show-overflow-tooltip />
        <el-table-column label="融资类型"
          prop="finTypeDesc"
          align="center"
          show-overflow-tooltip />
        <el-table-column label="融资资金用途"
          prop="borrowPurpose"
          align="center"
          width="100"
          show-overflow-tooltip />
        <el-table-column label="债务代码"
          prop="debtCode"
          align="right"
          show-overflow-tooltip />
        <el-table-column label="结息方式"
          prop="repayMethodDesc"
          align="center"
          show-overflow-tooltip />
        <el-table-column label="融资本金"
          prop="borrowAmt"
          align="right"
          show-overflow-tooltip />
        <el-table-column label="融资余额"
          prop="borrowSurplusAmt"
          align="right"
          show-overflow-tooltip />
        <el-table-column label="折本位币"
          prop="localCurrency"
          align="right"
          show-overflow-tooltip />
        <el-table-column label="汇率(%)"
          prop="exchangeRate"
          align="center"
          show-overflow-tooltip />
        <el-table-column label="币种"
          prop="currencyCode"
          align="center"
          show-overflow-tooltip />
        <el-table-column label="融资开始时间"
          prop="borrowStartDate"
          align="center"
          width="100"
          show-overflow-tooltip />
        <el-table-column label="融资截止时间"
          prop="borrowEndDate"
          align="center"
          width="100"
          show-overflow-tooltip />
        <el-table-column label="到期回售日期"
          prop="dueResaleDate"
          align="center"
          width="100"
          show-overflow-tooltip />
        <el-table-column label="展期到期日"
          prop="deferDate"
          align="center"
          show-overflow-tooltip />
        <el-table-column label="融资利率(%)"
          prop="borrowRate"
          align="center"
          width="100"
          show-overflow-tooltip />
        <el-table-column label="融资利息"
          prop="borrowInterest"
          align="right"
          show-overflow-tooltip />
        <el-table-column label="担保方式"
          prop="guaranteeWayDesc"
          align="center"
          show-overflow-tooltip />
        <el-table-column label="担保人/物"
          prop="guaranteeName"
          align="center"
          show-overflow-tooltip />
        <el-table-column label="担保金额"
          prop="guaranteeAmt"
          align="right"
          show-overflow-tooltip />
        <el-table-column label="交易发生时间"
          prop="transTime"
          align="center"
          width="100"
          show-overflow-tooltip />
        <!-- <el-table-column label="关闭日期"
          prop="closeDate"
          align="center"
          show-overflow-tooltip /> -->
        <el-table-column label="备注"
          prop="remark"
          align="center"
          show-overflow-tooltip />
        <el-table-column label="报送时间"
          prop="reportTime"
          align="center"
          show-overflow-tooltip />
        <el-table-column label="报送人"
          prop="operateName"
          align="center"
          show-overflow-tooltip />
      </el-table>
    </div>

    <div slot="footer"
      class="footer-box">
      <el-pagination clsss="pagination"
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="queryParams.current"
        :page-size="queryParams.size"
        :total="total"
        :page-sizes="[10, 30, 50]"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>
  </Dialog>
</template>

<script>
import Dialog from '@/components/Dialog/index.vue'
import { getFinancialDetailCapital } from '@/api/prewarning'
import PricUnit from '@/components/PricUnit/main.vue'
import { borrowExport } from '@/api/risk'
import { downloadXls } from '@/utils'

export default {
  components: { Dialog, PricUnit },

  data() {
    return {
      loading: false,
      visible: false,
      infoList: [],
      total: 0,
      queryParams: {
        current: 1,
        size: 10,
        keyword: '',
        minAmt: null,
        maxAmt: null,
        ourCorpName: '',
        includeIdList: null,
        amtUnit: 2
      },
      cokReserve: null
    }
  },

  methods: {
    handleOpen(row) {
      console.log('🚀 传递的参数', row)
      this.visible = true
      this.queryParams.includeIdList = row.reserve.split(',')
      this.cokReserve = JSON.parse(JSON.stringify(row.reserve.split(',')))
      this.getList()
    },

    handleQuery() {
      this.queryParams.current = 1
      this.getList()
    },

    getList() {
      this.loading = true
      getFinancialDetailCapital(this.queryParams).then((res) => {
        console.log('列表', res)
        this.infoList = res.data.records
        this.total = res.data.total
        this.loading = false
      })
    },

    handleSizeChange(size) {
      this.queryParams.size = size
      this.getList()
    },

    handleCurrentChange(pageNo) {
      this.queryParams.current = pageNo
      this.getList()
    },

    resetQuery() {
      this.queryParams = {
        current: 1,
        size: 10,
        keyword: '',
        minAmt: null,
        maxAmt: null,
        ourCorpName: '',
        includeIdList: this.cokReserve,
        amtUnit: 2
      }
      this.getList()
    },

    // 单位切换执行
    updataUnitHandle(val) {
      this.queryParams.amtUnit = val
      this.getList()
    },

    handleClose() {
      this.visible = false
    },

    exportHandle() {
      console.log('导出')
      let time = new Date().getTime()
      let queryExport = JSON.parse(JSON.stringify(this.queryParams))
      queryExport.current = 0
      queryExport.size = 0
      borrowExport(queryExport).then((res) => {
        // console.log('集团单位导出', res)
        downloadXls(res.data, `融资借款明细${time}.xlsx`)
        this.$message.success(res.msg || '导出成功')
      })
    }
  }
}
</script>

<style scoped lang="scss">
.body-box {
  width: 100%;
  height: 560px;
}

.fcenter {
  display: flex;
  justify-content: flex-end;
  box-sizing: border-box;
  padding-right: 8px;
  align-items: center;
}

.unitWrap {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  box-sizing: border-box;
  padding-left: 12px;
}
</style>

