<template>
  <el-container class="events-points-wrap"
    direction="vertical">
    <!-- 搜索头 -->
    <SearchBar :items="searchItems"
      @onSearch="handleSearch">
      <el-button type="primary"
        :disabled="!gridData.length"
        @click="batchDisposalHandle">批量处置</el-button>
      <el-button v-loading="loadingExport"
        :disabled="!gridData.length"
        type="primary"
        @click="exportHandle">导出</el-button>
    </SearchBar>
    <!-- 表格 -->
    <Grid ref="grid"
      :columns="columns"
      :border="true"
      :show-index="true"
      :show-selection="true"
      :show-pagination="true"
      :overflow-tooltip="true"
      :remote-url="remoteUrl"
      :search-params="searchParams"
      @row-click="rowClick"
      @onLoaded="onLoadedHandle"
      @selection-change="selectionChangeHandle"
      @select-all="selectionChangeHandle">
      <template slot="slotLevel"
        slot-scope="scope">
        <span
          :class="[{ hot: +scope.row.level === 1 }, { orange: +scope.row.level === 2 }, { yellow: +scope.row.level === 3 }, { blue: +scope.row.level === 4 }]">
          {{ getAlertLabel(scope.row.level) }}
        </span>
      </template>

      <template slot="slotServiceNo"
        slot-scope="scope">
        <el-button size="medium"
          type="text"
          @click.stop="assetsHandle(scope.row)">{{scope.row.serviceNo}}</el-button>
      </template>

      <template slot="hasFeedBackSlot"
        slot-scope="scope">
        <div>{{scope.row.hasFeedBack == 0 ? '未反馈':'已反馈'}}</div>
      </template>

      <template slot="operationSlot"
        slot-scope="scope">
        <Operation :list="operationItems"
          :row="scope.row" />

      </template>
    </Grid>

    <!-- 预警事件 处置 -->
    <EarlyWarningDispose v-if="visibleDispose"
      :visible.sync="visibleDispose"
      :type="typeOperation"
      :params="paramsData"
      @updataHandle="refresh(true)" />

    <!-- 预警事件 批量处置 -->
    <EarlyWarningBatchDisposal v-if="visibleBatchDisposal"
      :visible.sync="visibleBatchDisposal"
      :checkedList="checkedList"
      @updataHandle="refresh(true)" />

    <!-- 预警事件 详情 -->
    <EarlyWarningDetail v-if="visibleDetail"
      :visible.sync="visibleDetail"
      :type="typeOperation"
      :params="paramsData"
      @updataHandle="refresh(true)" />

    <!-- 预警事件 反馈 -->
    <EarlyWarningFeedBack v-if="visibleFeedBack"
      :visible.sync="visibleFeedBack"
      :type="typeOperation"
      :params="paramsData"
      @updataHandle="refresh(true)" />

    <!-- 资产弹窗：详情 -->
    <DetailCom v-if="visibleDetailDia"
      :visible.sync="visibleDetailDia"
      :itemNo="paramsData.serviceNo"
      :orgCode="paramsData.orgCode"
      :id="paramsData.id"
      :isAssets="false" />

    <FrzlDialog ref="frzlDialog" />
    <RzxxDialog ref="rzxxDialog" />
  </el-container>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import { deepClone } from '@/utils'
import { BusinessModule } from '@/store/modules/businessDict'
import { getDictBizData } from '@/api/public'
import { alertTmetricDict, getPrewarningExport } from '@/api/prewarning'
import { getAstCompTree } from '@/api/projectInvestment'
import { Loading, Confirm } from '@/decorators'
import { downloadXls } from '@/utils'
import Grid from '@/components/Grid/index.vue'
import SearchBar from '@/components/SearchBar/index.vue'
import EarlyWarningDispose from '@/views/prewarning/components/ComPoints/EarlyWarningDispose.vue'
import EarlyWarningBatchDisposal from '@/views/prewarning/components/ComPoints/EarlyWarningBatchDisposal.vue'
import EarlyWarningDetail from '@/views/prewarning/components/ComPoints/EarlyWarningDetail.vue'
import EarlyWarningFeedBack from '@/views/prewarning/components/ComPoints/EarlyWarningFeedBack.vue'
import FrzlDialog from '@/views/prewarning/components/ComPoints/FrzlDialog.vue'
import DetailCom from '@/views/assets/page/manage/components/DetailCom.vue'
import RzxxDialog from '@/views/prewarning/components/ComPoints/RzxxDialog.vue'

@Component({
  components: {
    Grid,
    SearchBar,
    DetailCom,
    EarlyWarningDetail,
    EarlyWarningDispose,
    EarlyWarningBatchDisposal,
    EarlyWarningFeedBack,
    FrzlDialog,
    RzxxDialog
  }
})
export default class extends Vue {
  private remoteUrl = ''
  private paramsData = {}
  private detailRow = {}
  private typeOperation = ''
  private loadingExport = false
  private visibleDispose = false
  private visibleDetail = false
  private visibleFeedBack = false
  private visibleDetailDia = false
  private visibleBatchDisposal = false
  private gridData: any[] = []
  private checkedList: any[] = []
  private alertDictBizData: any[] = []
  private pointTypeDictBizData: any[] = []
  private condition = {}
  private searchParams: any = {
    dealStatus: 0
  }
  private searchItems: any[] = [
    {
      type: 'tab',
      key: 'dealStatus',
      options: [
        {
          label: '未处置',
          value: 0,
          selected: true
        },
        {
          label: '已处置',
          value: 1
        }
      ]
    },
    {
      type: 'select',
      key: 'orgCode',
      placeholder: '集团名称',
      options: []
    },
    {
      type: 'select',
      key: 'level',
      placeholder: '预警级别',
      options: []
    },
    {
      type: 'select',
      key: 'ruleStatus',
      placeholder: '预警状态',
      options: [
        {
          label: '已启用',
          value: 1
        },
        {
          label: '未启用',
          value: 0
        }
      ]
    },
    {
      type: 'select',
      key: 'hasFeedBack',
      placeholder: '反馈状态',
      hidden: false,
      options: [
        {
          label: '已反馈',
          value: 1
        },
        {
          label: '未反馈',
          value: 0
        }
      ]
    },
    {
      type: 'select',
      key: 'metricsId',
      placeholder: '预警分类',
      options: []
    },
    // {
    //   type: 'daterange',
    //   key: ['startTime', 'endTime'],
    //   placeholder: ['开始日期', '结束日期']
    // },
    {
      type: 'select',
      key: 'warnDay',
      placeholder: '预警天数',
      options: [
        {
          label: '10天以内',
          value: 1
        },
        {
          label: '11天至20天',
          value: 2
        },
        {
          label: '20天以上',
          value: 3
        }
      ]
    },
    {
      type: 'text',
      key: 'keyword',
      width: '80px',
      placeholder: '业务编号/名称'
    },
    {
      type: 'date',
      key: 'startTime',
      placeholder: '开始时间',
      width: '150px'
    },
    {
      type: 'date',
      key: 'endTime',
      placeholder: '结束时间',
      width: '150px'
    }
  ]
  private operationItems: any[] = [
    {
      label: '查看',
      params: { type: 'see' },
      click: this.operateHandel,
      permissionId: 'prewarning_events_get'
    },
    {
      label: '处置',
      params: { type: 'dispose' },
      click: this.operateHandel,
      visible: (row: any) => {
        return row.dealStatus == 0
      },
      permissionId: 'prewarning_events_handle'
    },
    {
      label: '反馈',
      params: { type: 'feedback' },
      click: this.operateHandel,
      visible: (row: any) => {
        return row.dealStatus == 0
      },
      permissionId: 'prewarning_events_feedback'
    }
  ]
  private columns = [
    {
      label: '事件编号',
      prop: 'eventNo',
      minWidth: 160
    },
    {
      label: '业务编号 ',
      prop: 'serviceNo',
      minWidth: 160,
      slotName: 'slotServiceNo'
    },
    {
      label: '反馈状态',
      prop: 'hasFeedBack',
      slotName: 'hasFeedBackSlot'
    },
    {
      label: '预警名称',
      prop: 'ruleName',
      minWidth: 200
    },
    {
      label: '事件内容',
      prop: 'results',
      minWidth: 200
    },
    {
      label: '预警等级',
      prop: 'levelDesc',
      minWidth: 120,
      slotName: 'slotLevel'
    },
    {
      label: '集团名称',
      prop: 'orgName',
      minWidth: 120
    },
    {
      label: '当前触发时间',
      prop: 'currentTime',
      minWidth: 160
    },
    {
      label: '处置时间',
      prop: 'dealTime',
      minWidth: 160
    },
    {
      label: '操作',
      width: 130,
      fixed: 'right',
      slotName: 'operationSlot'
    }
  ]

  // 获取字典项
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }

  // 判断是否是资产监管里面的预警记录
  get getAssetsRoture() {
    let isAssets = false

    if (window.location.hash.includes('/assets/')) {
      isAssets = true
    } else {
      isAssets = false
    }

    return isAssets
  }

  // 获取预警等级文案
  get getAlertLabel() {
    return (level: number) => {
      let levelList = this.alertDictBizData.filter((item: any) => {
        return +level === +item.value
      })

      if (levelList.length) {
        return levelList[0].label
      } else {
        return ''
      }
    }
  }

  // 监听 dealStatus 变化
  @Watch('searchParams.dealStatus')
  onDealStatusChange(newVal: number) {
    const hasFeedBackIndex = this.searchItems.findIndex((item) => item.key === 'hasFeedBack')
    if (hasFeedBackIndex !== -1) {
      this.$set(this.searchItems[hasFeedBackIndex], 'hidden', newVal === 1)
      // 重置 hasFeedBack 的值
      if (newVal === 1) {
        this.searchParams.hasFeedBack = undefined
      }
    }
  }

  // 数据初始化
  private created() {
    // 是否需要筛选出资产相关的预警
    let { meta } = this.$route
    if (meta && meta.moduleType) {
      this.searchParams.moduleType = meta.moduleType
    }
    // end

    // 判断是否是资产监管里面的预警记录
    if (this.getAssetsRoture) {
      this.remoteUrl = '/fht-monitor/ewm/event/page-for-ast'
    } else {
      this.remoteUrl = '/fht-monitor/ewm/event/page'
    }
    // end

    this.getGroupNameDictBizData()
    this.getAlertDictBizData()
    this.getPointTypeDictBizData()
  }

  // 表格数据加载完成后触发
  private onLoadedHandle(data: any) {
    this.gridData = data.records || []
  }

  // 弹窗资产详情
  private assetsHandle(row: any) {
    if (row.metricsId == 49) {
      // 检查$refs.frzlDialog是否存在并调用handleOpen方法
      if (this.$refs.frzlDialog) {
        const frzlDialogRef = this.$refs.frzlDialog as any
        if (typeof frzlDialogRef.handleOpen === 'function') {
          frzlDialogRef.handleOpen(row)
        }
      }
    } else if (row.metricsId == 12) {
      // console.log('🚀 点击业务编号', row)
      if (this.$refs.rzxxDialog) {
        const rzxxDialogRef = this.$refs.rzxxDialog as any
        if (typeof rzxxDialogRef.handleOpen === 'function') {
          rzxxDialogRef.handleOpen(row)
        }
      }
    } else {
      if (+row.ruleId === 1 || +row.ruleId === 2 || +row.ruleId === 26) {
        this.paramsData = row
        this.visibleDetailDia = true
      }
    }
  }

  // 获取"集团名称"字典数据
  private async getGroupNameDictBizData() {
    let { data } = await getAstCompTree({ parentId: 0, deptCategory: 1 })

    let list: any[] = []
    if (Array.isArray(data)) {
      data.forEach((item) => {
        list.push({
          label: item.deptName,
          value: item.deptCode
        })
      })
    }

    let index = this.searchItems.findIndex((item) => {
      return item.key === 'orgCode'
    })

    this.$set(this.searchItems, index, {
      type: 'select',
      key: 'orgCode',
      placeholder: '集团名称',
      options: list || []
    })
  }

  // 获取"预警等级"字典数据
  private async getAlertDictBizData() {
    let { data } = await getDictBizData({
      code: 'alert_level'
    })

    let list: any[] = []
    if (Array.isArray(data)) {
      data.forEach((item) => {
        list.push({
          label: item.dictValue,
          value: item.dictKey
        })
      })
    }

    this.alertDictBizData = list

    let index = this.searchItems.findIndex((item) => {
      return item.key === 'level'
    })

    let valueLevel = this.$route.query.level ? this.$route.query.level : ''

    this.$set(this.searchItems, index, {
      type: 'select',
      key: 'level',
      placeholder: '预警级别',
      value: valueLevel,
      options: list || []
    })

    // 如果链接有带预警等级参数
    console.log('1111', this.$route)
    if (this.$route.query.level) {
      this.handleSearch({
        level: this.$route.query.level
      })
    }
  }

  // 获取"预警分类"接口数据
  private async getPointTypeDictBizData() {
    let { data } = await alertTmetricDict()

    let list: any[] = []
    if (Array.isArray(data)) {
      data.forEach((item) => {
        list.push({
          label: item.name,
          value: item.id
        })
      })
    }

    this.pointTypeDictBizData = list

    // 如果是资产监管里面的预警记录，就去掉资产之外的选项
    if (this.getAssetsRoture) list = list.splice(0, list.length - 1)
    // end

    let index = this.searchItems.findIndex((item) => {
      return item.key === 'metricsId'
    })

    this.$set(this.searchItems, index, {
      type: 'select',
      key: 'metricsId',
      placeholder: '预警分类',
      options: list || []
    })
  }

  // 列表加载
  private refresh(isCurrent = false) {
    ;(this.$refs.grid as Grid).refresh(isCurrent)
  }

  // 列表数据搜索
  private handleSearch(condition: any) {
    console.log('🚀 搜索', condition)

    this.searchParams = Object.assign(
      {
        dealStatus: this.searchParams.dealStatus
      },
      deepClone(condition)
    )

    this.condition = condition
    this.refresh()
  }

  // 列表操作
  private operateHandel(row: any = {}, index: number, params: any) {
    if (params.type === 'dispose') {
      this.visibleDispose = true
    } else if (params.type === 'see') {
      this.visibleDetail = true
    } else if (params.type === 'feedback') {
      this.visibleFeedBack = true
    }

    this.typeOperation = params.type
    this.paramsData = row
  }

  // 点击行查看详情
  private rowClick(row: any) {
    this.visibleDetail = true
    this.typeOperation = 'see'
    this.paramsData = row
  }

  // 表格多选
  private selectionChangeHandle(checkedList: any[]) {
    let list = [...checkedList]
    let ids: string[] = []

    if (list.length) {
      list.forEach((item) => {
        ids.push(item.id)
      })

      this.checkedList = ids
    } else {
      this.checkedList = []
    }
  }

  // 批量处置
  private batchDisposalHandle() {
    if (this.checkedList.length) {
      this.visibleBatchDisposal = true
    } else {
      this.$message.warning('请至少勾选一个数据进行处置')
    }
  }

  // 导出
  @Loading('loadingExport')
  @Confirm({
    title: '提示',
    content: `是否确认导出数据`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private async exportHandle() {
    let res = await getPrewarningExport(this.searchParams)
    let text = +this.searchParams.dealStatus === 1 ? '已处置' : '未处置'
    let time = new Date().getTime()

    downloadXls(res.data, `预警事件(${text})_${time}.xlsx`)
    this.$message.success(res.msg || '导出成功')
  }
}
</script>

<style scoped lang="scss">
.events-points-wrap {
  $hot: #fb3f3f;
  $yellow: #e6a23c;
  $blue: #409eff;
  $orange: #ff6700;

  height: 100%;

  .hot {
    color: $hot;
  }
  .yellow {
    color: $yellow;
  }
  .blue {
    color: $blue;
  }
  .orange {
    color: $orange;
  }
}
</style>
