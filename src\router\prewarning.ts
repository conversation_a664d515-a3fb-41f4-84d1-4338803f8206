/*
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-08-14 15:53:47
 * @LastEditors: wjb
 * @LastEditTime: 2025-08-14 16:09:33
 */
/* 预警系统 */

import Layout from '@/layouts/public/index.vue'

export const routes = [
  {
    path: '/prewarning',
    name: '预警系统',
    component: Layout,
    redirect: "/prewarning/home",
    children: [
      {
        path: 'home',
        name: '预警控制台',
        component: () => import('@/views/prewarning/home.vue')
      },
      {
        path: 'points',
        name: '预警规则',
        component: () => import('@/views/prewarning/points.vue')
      },
      {
        path: 'events',
        name: '预警事件',
        props: (route: any) => ({ level: route.query.level }),
        component: () => import('@/views/prewarning/events.vue')
      },

      {
        path: 'capitalRisk/riskDetail',
        name: '风险指标明细',
        component: () => import('@/views/prewarning/capitalRisk/riskDetail.vue')
      },
      {
        path: 'capitalRisk/riskReport',
        name: '风险预警报告',
        component: () => import('@/views/prewarning/capitalRisk/riskReport.vue')
      },
    ]
  },
]

export default routes