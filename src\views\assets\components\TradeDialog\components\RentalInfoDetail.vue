// 出租信息
<template>
  <section class="rental-info">
    <!-- 基本情况 -->
    <el-descriptions class="margin-top"
      title=""
      :column="6"
      :labelStyle="{
        width: '180px'
      }"
      border>
      <el-descriptions-item label="标的名称"
        :span="3">
        {{ detail.currentAsset.subjectName }}
      </el-descriptions-item>
      <el-descriptions-item label="资产编号"
        :span="3">
        {{ detail.currentAsset.assetNo }}
      </el-descriptions-item>
      <el-descriptions-item label="房屋坐落"
        :span="4">
        {{ detail.currentAsset.location }}
      </el-descriptions-item>

      <el-descriptions-item label="资产类别"
        :span="2">
        {{ detail.currentAsset.assetCategoryDesc }}
      </el-descriptions-item>

      <el-descriptions-item label="省"
        :span="2">
        {{ detail.currentAsset.province }}
      </el-descriptions-item>

      <el-descriptions-item label="市"
        :span="2">
        {{ detail.currentAsset.city }}
      </el-descriptions-item>

      <el-descriptions-item label="区"
        :span="2">
        {{ detail.currentAsset.county }}
      </el-descriptions-item>

      <el-descriptions-item label="不动产证号"
        :span="3">
        {{ detail.currentAsset.realEstateCertificateNo }}
      </el-descriptions-item>

      <el-descriptions-item label="房产证"
        :span="3">
        {{ detail.currentAsset.titleDeedNo }}
      </el-descriptions-item>
      <el-descriptions-item label="土地证号"
        :span="3">
        {{ detail.currentAsset.landCertificateNo }}
      </el-descriptions-item>
      <el-descriptions-item label="规划证号"
        :span="3">
        {{ detail.currentAsset.planCertificateNo }}
      </el-descriptions-item>
      <el-descriptions-item label="施工证号"
        :span="3">
        {{ detail.currentAsset.constructionCertificateNo }}
      </el-descriptions-item>
      <el-descriptions-item label="竣工证号"
        :span="3">
        {{ detail.currentAsset.completionCertificateNo }}
      </el-descriptions-item>
      <el-descriptions-item label="建筑面积"
        :span="3">
        {{ detail.currentAsset.constructionArea }}
      </el-descriptions-item>

      <el-descriptions-item label="建筑面积单位"
        :span="3">
        <!-- {{detail.currentAsset.constructionAreaUnitDesc}} -->
        <el-radio-group v-model="detail.currentAsset.constructionAreaUnit"
          :disabled="true">
          {{ChangeN(detail.currentAsset.constructionAreaUnit)}}
                <el-radio :key="item.value" :label="item.value" v-for="item in getDictData('construction_area_unit')">{{item.label}}</el-radio>
        </el-radio-group>
        <el-input v-if="detail.currentAsset.constructionAreaUnit === '9'"
          v-model="detail.currentAsset.otherConstructionAreaUnit"
          :disabled="true"
          placeholder="请输入其他建筑面积单位" />
      </el-descriptions-item>

      <el-descriptions-item label="房屋当前状态"
        :span="3">
        {{ detail.currentAsset.houseStatus }}
      </el-descriptions-item>

      <el-descriptions-item label="房屋设计用途"
        :span="3">
        {{ detail.currentAsset.houseDesignPurpose }}
      </el-descriptions-item>

      <el-descriptions-item label="其他权利情况"
        :span="6">
        {{detail.currentAsset.otherRightsDesc}}
     
      </el-descriptions-item>

      <el-descriptions-item label="内部决策情况"
        :span="6">
        <div class="m-b-12">{{detail.basic.internalDecisionDesc}}</div>
      </el-descriptions-item>

      <el-descriptions-item label="出租行为批准情况"
        :span="6">
        <el-descriptions class="margin-top"
          :column="1">
          <el-descriptions-item label="批准单位名称"
            :span="1">
            {{ detail.basic.approvalOrganName }}
          </el-descriptions-item>
          <el-descriptions-item label="批准文件名称"
            :span="1">
            {{ detail.basic.approvalFile }}
          </el-descriptions-item>
        </el-descriptions>
      </el-descriptions-item>

      <el-descriptions-item label="是否国有资产"
        :span="3">
        {{ detail.basic.stateAssetDesc }}
      </el-descriptions-item>

      <el-descriptions-item contentClassName="department-name-input"
        :labelStyle="{
            width: '180px'
          }"
        label="国家出租企业或主管部门名称">
        {{ detail.basic.competentDepartment }}
      </el-descriptions-item>
    </el-descriptions>

    <!-- 附件列表 -->
    <AccessoryList v-model="detail.basic.attachmentFileDTOList" dict="asset_basic_attach" mode="see" />
    <AccessoryList title="房产附件：" v-model="detail.currentAsset.attachmentFileDTOList"  mode="see" />
  </section>
</template>

<script lang="ts">
import { deepClone } from '@/utils'
import { decisionTypeList } from '@/views/assets/filterOptions'
import { ElForm } from 'element-ui/types/form'
import { Component, Model, Prop, Vue } from 'vue-property-decorator'
import { Basic, TradeDetail } from '../TradeDetailDialog.vue'
import AccessoryList, { Accessory, AttachmentFile } from './AccessoryFileList.vue'
import { BusinessModule } from '@/store/modules/businessDict'
type FileKey =
  | 'attachmentFile1'
  | 'attachmentFile2'
  | 'attachmentFile3'
  | 'attachmentFile4'
  | 'attachmentFile5'
  | 'attachmentFile6'
  | 'attachmentFile7'
  | 'attachmentFile8'

@Component({
  components: {
    AccessoryList
  }
})
export default class extends Vue {
  @Prop({ default: () => ({}) }) private initData!: TradeDetail

  private detail: any = {
    assetList: [],
    lessee: {},
    lessor: {},
    basic: {},
    currentAsset: {},
    verify: {}
  }
  private ChangeN(string:any){
      return Number(string)
  }
  private decisionTypeList = this.getDictData("internal_decision")
    get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  private accessoryList: Accessory[] = [
    {
      fileName: '授权委托人的身份证明',
      isRequired: true,
      prop: 'attachmentFile1',
      fileList: []
    },
    {
      fileName: '授权委托书',
      isRequired: true,
      prop: 'attachmentFile2',
      fileList: []
    },
    {
      fileName: '内部决策文件',
      isRequired: true,
      prop: 'attachmentFile3',
      fileList: []
    },
    {
      fileName: '审批文件',
      isRequired: true,
      prop: 'attachmentFile4',
      fileList: []
    },
    {
      fileName: '资产权属证明',
      isRequired: true,
      prop: 'attachmentFile5',
      fileList: []
    },
    {
      fileName: '资产评估报告',
      isRequired: true,
      prop: 'attachmentFile6',
      fileList: []
    },
    {
      fileName: '其他资产相关权利人的意思表示（如涉及）',
      isRequired: false,
      prop: 'attachmentFile7',
      fileList: []
    },
    {
      fileName: '其他具有法律效力的权属证明文件（如涉及）',
      isRequired: false,
      prop: 'attachmentFile8',
      fileList: []
    }
  ]

  created() {
    this.detail = deepClone(this.initData)    
    // 附件列表赋值
    
  }
}
</script>


<style scoped lang="scss">
::v-deep .el-radio__label {
  line-height: 36px;
}
::v-deep .department-name-input {
  .el-form-item {
    margin-bottom: 0px !important;
  }
}
</style>