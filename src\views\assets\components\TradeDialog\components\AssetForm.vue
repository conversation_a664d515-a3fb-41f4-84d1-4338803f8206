// 房产
<template>
  <div>
    <Dialog :title="title"
      width="1000px"
      :visible="visible"
      :append-to-body="true"
      @close="closeDlg">
      <div slot="body">
        <el-form ref="HouseForm"
          :rules="houseFormRules"
          :model="houseForm"
          label-width="120px">
          <el-descriptions class="margin-top"
            title="基本情况"
            :column="12"
            :labelStyle="{
            width: '10px'
          }"
            border>
            <el-descriptions-item label
              :span="6">
              <el-form-item label="标的名称"
                prop="subjectName">
                <el-select v-model="houseForm.subjectName"
                  :multiple="false"
                  filterable
                  remote
                  placeholder="请输入关键词"
                  :remote-method="querySearch"
                  :loading="loadingsearch"
                  @change="selectName">
                  <el-option v-for="item in subjectNameoptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"></el-option>
                </el-select>
                <!-- <el-autocomplete
                  v-model="houseForm.subjectName"
                  @select="selectName"
                  :fetch-suggestions="querySearch"
                  :disabled="disabled"
                  placeholder="请输入"
                  clearable
                ></el-autocomplete>-->
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label
              :span="6">
              <el-form-item label="资产编号"
                prop="assetNo">
                <el-input v-model="houseForm.assetNo"
                  disabled
                  placeholder />
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label="房屋坐落"
              :span="8">
              <el-form-item label="房屋坐落"
                prop="location">
                <el-input v-model="houseForm.location"
                  disabled
                  maxlength="100"
                  placeholder />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item :span="4"
              label="资产类别">
              <el-form-item label="资产类别"
                prop="assetCategory">
                <el-radio-group v-model="houseForm.assetCategory"
                  disabled>
                  <el-radio :key="item.value"
                    :label="item.value"
                    v-for="item in getDictData('asset_category')">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item :span="4"
              label="省">
              <el-form-item label="省"
                prop="province">
                <el-select v-model="houseForm.province"
                  disabled
                  placeholder
                  @change="changecity(houseForm.province)">
                  <el-option v-for="item in provinceList"
                    :key="item.value"
                    :label="item.title"
                    :value="item.title"></el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item :span="4"
              label="市">
              <el-form-item label="市"
                prop="city">
                <el-select v-model="houseForm.city"
                  disabled
                  @change="ChangeCounty(houseForm.city)"
                  placeholder>
                  <el-option v-for="item in cityList"
                    :key="item.value"
                    :label="item.title"
                    :value="item.title"></el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item :span="4"
              label="区">
              <el-form-item label="区（县）"
                prop="county">
                <el-select v-model="houseForm.county"
                  placeholder
                  disabled>
                  <el-option v-for="item in districtList"
                    :key="item.value"
                    :label="item.title"
                    :value="item.title"></el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label="不动产证号"
              :span="12">
              <el-form-item label="不动产证号"
                prop="realEstateCertificateNo">
                <el-input maxlength="50"
                  show-word-limit
                  v-model="houseForm.realEstateCertificateNo"
                  disabled
                  placeholder />
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label="房产证"
              :span="12">
              <el-form-item label="房产证"
                prop="titleDeedNo">
                <el-input maxlength="50"
                  show-word-limit
                  v-model="houseForm.titleDeedNo"
                  disabled
                  placeholder />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="土地证号"
              :span="12">
              <el-form-item label="土地证号"
                prop="landCertificateNo">
                <el-input v-model="houseForm.landCertificateNo"
                  disabled
                  placeholder
                  maxlength="50"
                  show-word-limit />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="规划证号"
              :span="12">
              <el-form-item label="规划证号"
                prop="planCertificateNo">
                <el-input v-model="houseForm.planCertificateNo"
                  disabled
                  placeholder
                  maxlength="50"
                  show-word-limit />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="施工证号"
              :span="12">
              <el-form-item label="施工证号"
                prop="constructionCertificateNo">
                <el-input v-model="houseForm.constructionCertificateNo"
                  disabled
                  maxlength="50"
                  show-word-limit
                  placeholder />
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label="竣工证号"
              :span="12">
              <el-form-item label="竣工证号"
                prop="completionCertificateNo">
                <el-input maxlength="50"
                  show-word-limit
                  v-model="houseForm.completionCertificateNo"
                  disabled
                  placeholder />
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label="建筑面积"
              :span="6">
              <el-form-item label="建筑面积"
                prop="constructionArea">
                <InputNumber v-model="houseForm.constructionArea"
                  clearable
                  disabled
                  type="decimal"
                  placeholder></InputNumber>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label="建筑面积单位"
              :label-style="{
              width: '120px'
            }"
              :span="14">
              <el-form-item label="建筑面积单位"
                prop="constructionAreaUnit">
                <el-radio-group v-model="houseForm.constructionAreaUnit"
                  :disabled="disabled">
                  <el-radio :key="item.value"
                    :label="item.value"
                    v-for="item in getDictData('construction_area_unit')">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item v-if="houseForm.constructionAreaUnit == '3'"
                label="其他建筑面积单位"
                class="m-t-8"
                label-width="150px"
                prop="otherConstructionAreaUnit">
                <el-input v-model="houseForm.otherConstructionAreaUnit"
                  disabled
                  placeholder="请输入其他建筑面积单位" />
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label="房屋当前状态"
              :span="4">
              <el-form-item label="房屋当前状态"
                prop="houseStatus">
                <el-input v-model="houseForm.houseStatus"
                  disabled
                  placeholder />
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label="房屋设计用途"
              :span="12">
              <el-form-item label="房屋设计用途"
                prop="houseDesignPurpose">
                <el-input v-model="houseForm.houseDesignPurpose"
                  :disabled="disabled"
                  placeholder="请输入" />
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label="其他权利情况"
              :label-style="{
              width: '120px'
            }"
              :span="10">
              <el-form-item label="其他权利情况"
                prop="otherRights">
                <el-radio-group v-model="houseForm.otherRights"
                  :disabled="disabled">
                  <el-radio :key="item.value"
                    :label="item.value"
                    v-for="item in getDictData('other_rights')">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item v-if="houseForm.otherRights == '9'"
                label="其他权利情况"
                class="m-t-8"
                label-width="100px"
                prop="otherCoveredAreaUnit">
                <el-input v-model="houseForm.otherRightsDesc"
                  :disabled="disabled"
                  placeholder="请输入其他权利情况" />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label
              :span="2">
              <el-button type="primary"
                @click="uploaderDlgVisible=true"
                icon="el-icon-upload
el">房产图片({{houseForm.attachmentFileDTOList.length||0}})</el-button>
            </el-descriptions-item>
          </el-descriptions>

          <el-descriptions class="margin-top m-t-18"
            title="出租信息"
            :column="2"
            :labelStyle="{
            width: '120px'
          }"
            border>
            <el-descriptions-item label="标的状态"
              :span="2">
              <el-form-item label="标的状态"
                prop="subjectStatus">
                <el-radio-group v-model="houseForm.subjectStatus"
                  :disabled="disabled">
                  <el-radio :label="1">空置</el-radio>
                  <el-radio :label="2">
                    使用
                    <span v-if="houseForm.subjectStatus ==2">
                      （原合同已到期/原合同于
                      <el-date-picker v-model="houseForm.expiryDate"
                        type="date"
                        value-format="yyyy-MM-dd"
                        :disabled="disabled"
                        placeholder="选择日期"></el-date-picker>截止）
                    </span>
                  </el-radio>
                  <el-radio :label="99">其他</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="分别计价"
              :span="1">
              <el-form-item label="分别计价"
                prop="isPricingSeparately">
                <el-radio-group :disabled="disabled"
                  v-model="houseForm.isPricingSeparately">
                  <el-radio label="0">否</el-radio>
                  <el-radio label="1">是</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-descriptions-item>
            <!-- <el-descriptions-item label="标的状态" :span="24">
            <el-form-item label="" label-width="0px" prop="subjectStatus">
              <el-radio-group v-model="houseForm.subjectStatus" :disabled="disabled">
                <el-radio :label="1">空置</el-radio>
                <el-radio :label="2">
                  使用
                  <span v-if="houseForm.subjectStatus === 2">
                    （原合同已到期/原合同于
                    <el-date-picker
                      v-model="houseForm.expiryDate"
                      type="date"
                      value-format="yyyy-MM-dd"
                      :disabled="disabled"
                      placeholder="选择日期"
                    ></el-date-picker>
                    截止）
                  </span>
                </el-radio>
                <el-radio :label="9">其他</el-radio>
              </el-radio-group>
            </el-form-item>
            </el-descriptions-item>-->
            <el-descriptions-item label="是否有优先承租权"
              :span="1">
              <el-form-item label="是否有优先承租权"
                label-width="140px"
                prop="isPriorityLease">
                <el-radio-group v-model="houseForm.isPriorityLease"
                  :disabled="disabled">
                  <el-radio-button label="1">是</el-radio-button>
                  <el-radio-button label="0">否</el-radio-button>
                </el-radio-group>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="原承租人名称"
              :span="1">
              <el-form-item label="原承租人名称"
                prop="originalLessee">
                <el-input v-model="houseForm.originalLessee"
                  :disabled="disabled"
                  placeholder="请输入" />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item :span="1"
              label="租金挂牌价">
              <el-form-item label="租金挂牌价"
                prop="rentListingPrice">
                <InputNumber v-model="houseForm.rentListingPrice"
                  clearable
                  :disabled="disabled"
                  type="decimal"
                  placeholder="请输入金额">
                  <template slot="append">元</template>
                </InputNumber>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item :span="1"
              label="租金挂牌价单位">
              <el-form-item label="租金挂牌价单位"
                label-width="130px"
                prop="listingPriceUnit">
                <el-select v-model="houseForm.listingPriceUnit"
                  :disabled="disabled"
                  placeholder="请选择">
                  <el-option v-for="item in getDictData('listing_price_unit')"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label="出租面积(可租赁面积)"
              :span="1">
              <el-form-item label="出租面积(可租赁面积)"
                label-width="170px"
                prop="rentalArea">
                <InputNumber v-model="houseForm.rentalArea"
                  clearable
                  :disabled="disabled"
                  type="decimal"
                  placeholder="请输入面积"></InputNumber>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label="租金支付要求"
              :span="2">
              <el-form-item label="租金支付要求"
                prop="paymentRequirements">
                <el-input type="textarea"
                  v-model="houseForm.paymentRequirements"
                  :disabled="disabled"
                  :autosize="{ minRows: 3, maxRows: 5}"
                  placeholder="请输入"
                  maxlength="300"
                  show-word-limit />
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label="房产使用用途要求"
              :span="2">
              <el-form-item label="房产使用用途要求"
                label-width="140px"
                prop="useRequirements">
                <el-radio-group v-model="houseForm.useRequirements"
                  :disabled="disabled">
                  <el-radio :key="item.value"
                    :label="item.value"
                    v-for="item in getDictData('property_use_requirements')">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label
              :span="2"
              v-if="houseForm.useRequirements==99">
              <el-form-item label="其他房产使用用途"
                label-width="140px">
                <el-input v-model="houseForm.otherUseRequirements"
                  :disabled="disabled"
                  placeholder="请输入" />
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label="租期"
              :span="1">
              <el-form-item label="租期"
                prop="leaseTerm">
                <!-- <el-input v-model="houseForm.leaseTerm" :disabled="disabled" placeholder="请输入租期"></el-input> -->
                <el-select @change="changeElse()"
                  style="width: 100%"
                  v-model="houseForm.leaseTerm"
                  :disabled="disabled"
                  placeholder="请选择">
                  <el-option v-for="item in getDictData('lease_term')"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"></el-option>
                </el-select>
                <!-- </div> -->
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="其他租期"
              :span="1"
              v-if="houseForm.leaseTerm=='99'">
              <el-form-item label="其他租期"
                prop="leaseEndDate">
                <el-date-picker v-model="houseForm.leaseEndDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择截止日期日期"></el-date-picker>
              </el-form-item>
            </el-descriptions-item>
            <!-- 
          <el-descriptions-item label="租期" :span="1" v-if="showLeaseTerm">
            <el-input
              class="p-l-20"
              style="width: 95%"
              v-model="houseForm.leaseTerm"
              clearable
              :disabled="disabled"
              type="decimal"
              placeholder="请输入其他租期"
            ></el-input>
            </el-descriptions-item>-->

            <el-descriptions-item label="免租期"
              :span="1">
              <el-form-item label="免租期"
                label-width="80px"
                prop="hasRentFreePeriod">
                <el-radio-group v-model="houseForm.hasRentFreePeriod"
                  :disabled="disabled">
                  <el-radio label="0">无</el-radio>
                  <el-radio label="1">免租期</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="免租期时间"
              :span="1"
              v-if="houseForm.hasRentFreePeriod == '1'">
              <el-form-item label="免租期时间"
                prop="rentFreePeriod">
                <el-select v-model="houseForm.rentFreePeriod"
                  :disabled="disabled"
                  placeholder="请选择">
                  <el-option v-for="item in getDictData('rent_free_period')"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"></el-option>
                </el-select>
                <!-- <el-input
                v-model="houseForm.rentFreePeriod"
                clearable
                :disabled="disabled"
                type="decimal"
                placeholder="请输入免租期"
              >
              rent_free_period 
                </el-input>-->
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item v-if="houseForm.hasRentFreePeriod == '1'"
              label="是否包含在租期内"
              :label-style="{
              width: '130px'
            }"
              :span="1">
              <el-form-item label="是否包含在租期内"
                label-width="140px"
                prop="inLeaseTerm">
                <el-radio-group v-model="houseForm.inLeaseTerm"
                  :disabled="disabled">
                  <el-radio-button :label="1">是</el-radio-button>
                  <el-radio-button :label="0">否</el-radio-button>
                </el-radio-group>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label="竞拍保证金"
              :span="1">
              <el-form-item label="竞拍保证金"
                prop="bidSecurity">
                <InputNumber v-model="houseForm.bidSecurity"
                  clearable
                  :disabled="disabled"
                  type="decimal"
                  placeholder="请输入金额">
                  <template slot="append">万元</template>
                </InputNumber>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item label="加价幅度"
              :span="1">
              <el-form-item label="加价幅度"
                prop="markupRange">
                <InputNumber v-model="houseForm.markupRange"
                  clearable
                  :disabled="disabled"
                  type="decimal"
                  placeholder="请输入金额">
                  <template slot="append">元/次</template>
                </InputNumber>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
      </div>

      <div slot="footer"
        class="footer">
        <el-button @click="closeDlg">关闭</el-button>
        <el-button v-if="mode === 'add'"
          type="primary"
          @click="onContinue">继续添加</el-button>
        <el-button v-if="mode !== 'see'"
          type="primary"
          @click="submitForm">保存</el-button>
      </div>
    </Dialog>
    <!-- 文件上传 -->
    <uploader title="附件上传"
      :visible.sync="uploaderDlgVisible"
      v-model="houseForm.attachmentFileDTOList"
      url-key="link"
      :show-cover="true"
      :is-private="false"
      :is-attachment="true"
      @change="onFilesChange"
      @uploadComplete="handleUploadComplete"
      maxLimit="10"
      :uploadable="mode=='see'?false:true"></uploader>
  </div>
</template>
<script lang="ts">
import { Component, Emit, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import { priceUnitList, tenantUnsolicitedTypeList } from '@/views/assets/filterOptions'
import { ElForm } from 'element-ui/types/form'
import InputNumber from '@/components/FormComment/inputNumber.vue'
import { getAreaCode } from '@/api/assets'
import { BusinessModule } from '@/store/modules/businessDict'
import Uploader from '@/components/Uploader/index.vue'
import { ListBaseAsset } from '@/api/assets'
import { deepClone } from '../../../../../utils'
export interface AssetForm {
  assetCategory: number | string // 资产类别,1:房屋,2:土地,3:房屋+土地
  assetCategoryDesc: string
  assetNo: string // 资产编号	string
  assignUser: number | '' // 分配人员
  assignUserName: string // 分配人员	string
  auditFailReason: string // 审核失败原因	string
  auditPassTime: string // 审核通过时间	string
  auditStatus: number | '' //	审核状态，0：草稿,1:待审核,1：审核成功,2：审核失败
  auditStatusDesc: string //
  batchNo: string // 申请批次号
  bidSecurity: number | '' // 竞拍保证金(万)
  circulationStatus: number | '' //	流转状态，0：草稿,1；审核中，2：未认领，3：已认领，4：挂牌，5：撤牌，6：摘牌，7：成交，8：已中止，9：已终结
  circulationStatusDesc: string //
  city: string // 市	string
  completionCertificateNo: string // 竣工证号	string
  constructionArea: number | '' //	建筑面积
  constructionAreaUnit: number | string //	建筑面积单位，1:平方米，2：亩，3：其它
  constructionCertificateNo: string // 施工证号	string
  county: string // 区（县）	string
  createDept: number | '' //	创建部门
  dealStatus: number | '' //	成交状态：0:未成交,1:已成交,2：已中止，3:已终结,4：已归档
  dealStatusDesc: string //		string
  expiryDate: string // 到期日	string
  hasRentFreePeriod: number | string // 有无免租期,1-是 0-否
  hasRentFreePeriodDesc: string //		string
  houseDesignPurpose: string // 房屋设计用途	string
  houseStatus: string // 房屋当前状态	string
  id: number | '' //		integer
  inLeaseTerm: number | string //	是否包含在租期内,1-是 0-否
  inLeaseTermDesc: string // 		string
  isPriorityLease: number | string //	是否有优先承租权，1-是 0-否
  isPriorityLeaseDesc: string // 		string
  landCertificateNo: string // 土地证号	string
  leaseTerm: string // 租期	string
  listingFailReason: string // 挂牌失败原因	string
  listingPassTime: string // 挂牌时间	string
  listingPriceUnit: number | string //	挂牌价单位，详见字典ListingPriceUnitEnum
  listingPriceUnitDesc: string // 		string
  listingStatus: number | '' // 	挂牌状态，1：已挂牌,2：撤牌，3：摘牌，4:挂牌失败
  listingStatusDesc: string // 		string
  location: string | '' // 房屋坐落	string
  markupRange: number | '' //	加价幅度(元/次)
  originalLessee: string // 原承租人名称	string
  otherConstructionAreaUnit: string // 其它建筑面积单位	string
  otherRights: number | string //	其他权利情况，0：无，1：抵押，2：共有，9：其它
  otherRightsDesc: string // 其他权利说明	string
  otherUseRequirements: string // 其它房产使用用途要求	string
  paymentRequirements: string // 租金支付要求	string
  planCertificateNo: string // 规划证号	string
  province: string // 省	string
  realEstateCertificateNo: string // 不动产证号	string
  remark: string // 备注	string
  rentFreePeriod: string // 免租期时间	string
  rentListingPrice: string // 租金挂牌价	string
  rentalArea: string //	出租面积(可租赁面积)	string
  status: number | '' //	有效状态，1-正常，0-作废
  statusDesc: string // 		string
  subjectAddress: string // 出租标的地址	string
  subjectName: string // 标的名称	string
  subjectStatus: number | string //标的状态，1：空置，2：使用，9：其它
  isPricingSeparately: number | string //是否分别计价，1-是 0-否
  subjectStatusDesc: string // 		string
  titleDeedNo: string // 房产证号	string
  useRequirements: number | string //	房产使用用途要求，详见字典PropertyUseRequirementsEnum
  useRequirementsDesc: string
  attachmentFileDTOList: any[]
  tenantUnsolicitedDesc: string //
}

@Component({
  name: 'Container',
  components: {
    Dialog,
    InputNumber,
    Uploader
  }
})
export default class extends Vue {
  @Prop({ default: () => ({}) }) private initData!: AssetForm
  @Prop() private visible!: boolean
  @Prop({ default: 'see' }) private mode!: 'add' | 'edit' | 'see' // 新增、编辑、查看
  @Prop({}) private houseTableList: any
  @Emit('close')
  private onClose() {
    return
  }
  // 文件上传事件
  private uploaderDlgVisible = false
  private uploaderTagsList = [
    { label: '附件1', value: 1 },
    { label: '附件2', value: 2 },
    { label: '附件3', value: 3 },
    { label: '附件4', value: 4 },
    { label: '附件4', value: 4 }
  ]
  private attachmentFileDTOList = []
  // 标远程搜索选型
  private subjectNameoptions = []
  private provinceList = []

  private cityList = []
  private loadingsearch = false
  private districtList = []
  private priceUnitList = priceUnitList

  private houseForm: any = {
    assetCategory: '',
    assetCategoryDesc: '',
    assetNo: '',
    assignUser: 0,
    assignUserName: '',
    auditFailReason: '',
    auditPassTime: '',
    auditStatus: 0,
    auditStatusDesc: '',
    batchNo: '',
    bidSecurity: '',
    circulationStatus: 0,
    circulationStatusDesc: '',
    city: '',
    completionCertificateNo: '',
    constructionArea: '',
    constructionAreaUnit: '',
    constructionCertificateNo: '',
    county: '',
    createDept: 0,
    dealStatus: 0,
    dealStatusDesc: '',
    expiryDate: '',
    hasRentFreePeriod: '',
    hasRentFreePeriodDesc: '',
    houseDesignPurpose: '',
    houseStatus: '',
    id: 0,
    inLeaseTerm: '',
    inLeaseTermDesc: '',
    isPriorityLease: '',
    isPriorityLeaseDesc: '',
    landCertificateNo: '',
    leaseTerm: '',
    listingFailReason: '',
    listingPassTime: '',
    listingPriceUnit: '3',
    listingPriceUnitDesc: '',
    listingStatus: 0,
    listingStatusDesc: '',
    location: '',
    markupRange: '',
    originalLessee: '',
    otherConstructionAreaUnit: '',
    otherRights: '',
    otherRightsDesc: '',
    otherUseRequirements: '',
    paymentRequirements:
      '采用先付款后使用的原则，租金共分20期支付，以6个租赁月度为1期，按该租赁年度租金的50%支付。首期租金按成交年度租金的50%支付，在租赁合同签订后5个工作日内交纳至出租方指定账户。第二期起租金支付时间具体以租赁合同约定为准。',
    planCertificateNo: '',
    province: '',
    realEstateCertificateNo: '',
    remark: '',
    rentFreePeriod: '',
    rentListingPrice: '',
    rentalArea: '',
    status: 0,
    statusDesc: '',
    subjectAddress: '',
    subjectName: '',
    subjectStatus: '',
    isPricingSeparately: '',
    subjectStatusDesc: '',
    titleDeedNo: '',
    useRequirements: '',
    useRequirementsDesc: '',
    tenantUnsolicitedDesc: '',
    attachmentFileDTOList: [],
    leaseEndDate: ''
  }

  private showLeaseTerm = false
  private houseFormRules = {
    assetNo: [{ required: true, trigger: 'change', message: '请重新选择资产' }],
    bidSecurity: [{ required: true, trigger: 'change', message: '请输入竞拍保证金' }],
    city: [{ required: true, trigger: 'change', message: '请重新选择资产' }],
    assetCategory: [{ required: true, trigger: 'change', message: '请选择资产类别' }],
    constructionAreaUnit: [{ required: true, trigger: 'change', message: '请选择建筑面积单位' }],
    otherRights: [{ required: true, trigger: 'change', message: '请选择其他权利情况' }],
    isPricingSeparately: [{ required: true, trigger: 'change', message: '请选择' }],
    isPriorityLease: [{ required: true, trigger: 'change', message: '请选择' }],
    inLeaseTerm: [{ required: true, trigger: 'change', message: '请选择' }],
    subjectStatus: [{ required: true, trigger: 'change', message: '请选择' }],
    hasRentFreePeriod: [{ required: true, trigger: 'change', message: '请选择' }],
    // completionCertificateNo: [{ required: true, trigger: 'change', message: '请输入竣工证号' }],
    constructionArea: [{ required: true, trigger: 'change', message: '请重新选择资产' }],
    // constructionCertificateNo: [{ required: true, trigger: 'change', message: '请输入施工证号' }],
    expiryDate: [{ required: true, trigger: 'change', message: '请选择截止日期' }],
    // houseDesignPurpose: [{ required: true, trigger: 'change', message: '请输入房屋设计用途' }],
    // houseStatus: [{ required: true, trigger: 'change', message: '请输入当前房间状态' }],
    leaseTerm: [{ required: true, trigger: 'change', message: '请输入租期' }],
    leaseEndDate: [{ required: true, trigger: 'change', message: '请选择截止租期' }],
    listingPriceUnit: [{ required: true, trigger: 'change', message: '请选择挂牌价单位' }],
    location: [{ required: true, trigger: 'change', message: '请重新选择资产' }],
    // markupRange: [{ required: true, trigger: 'change', message: '请输入加价幅度' }],
    // originalLessee: [{ required: true, trigger: 'change', message: '请输入原承租人名称' }],
    otherConstructionAreaUnit: [{ required: true, trigger: 'change', message: '请输入其他建筑面积单位' }],
    paymentRequirements: [{ required: true, trigger: 'change', message: '请输入租金支付要求' }],
    province: [{ required: true, trigger: 'change', message: '请重新选择资产' }],
    county: [{ required: true, trigger: 'change', message: '请重新选择资产' }],
    // realEstateCertificateNo: [{ required: true, trigger: 'change', message: '请输入不动产证号' }],
    rentFreePeriod: [{ required: false, trigger: 'change', message: '请选择免租时间' }],
    rentListingPrice: [{ required: true, trigger: 'change', message: '请输入租金挂牌价' }],
    rentalArea: [{ required: true, trigger: 'change', message: '请输入出租面积(可租赁面积)' }],
    // subjectAddress: [{ required: true, trigger: 'change', message: '请输入出租标的地址' }],
    subjectName: [{ required: true, trigger: 'change', message: '请选择' }],
    // titleDeedNo: [{ required: true, trigger: 'change', message: '请输入房产证号' }],
    useRequirements: [{ required: true, trigger: 'change', message: '请选择房产使用用途要求' }]
  }
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  get title() {
    return this.mode === 'add' ? '添加房产' : this.mode === 'see' ? '查看房产' : '修改房产'
  }

  get disabled() {
    return this.mode === 'see'
  }

  async created() {
    if (this.mode !== 'add') {
      this.houseForm = Object.assign(this.houseForm, this.initData)
    }
    await this.getArea('provinceList', '00')
    await this.getcitYModel()
  }
  // 已有数据时选择：
  private async getcitYModel() {
    await this.changecity(this.houseForm.province)
    await this.ChangeCounty(this.houseForm.city)
  }
  // 远程搜索选项
  private remoteMethod() {
    //
  }
  // 远程搜索建议
  private async querySearch(queryString: string, cb: any) {
    this.loadingsearch = true
    let asserList = this.houseTableList.map((item: any) => {
      return item.assetNo || ''
    })
    let res = await ListBaseAsset({
      assetNos: asserList,
      input: queryString
    })
    if (res.success) {
      let list = await res.data.map((item: any) => {
        return Object.assign(
          {
            label: item.subjectName,
            value: item.assetNo
          },
          item
        )
      })
      this.subjectNameoptions = list
      this.loadingsearch = false
      // await   cb(list)
    }
  }
  //
  //  选择只会给表格赋值
  private selectName(val: any) {
    let row: any = this.subjectNameoptions.find((item: any) => {
      return item.assetNo == val
    })
    if (row == undefined) return
    // this.houseForm=Object.assign(this.houseForm,row)
    this.$set(this.houseForm, 'location', row.location)
    this.$set(this.houseForm, 'province', row.province + '')
    this.$set(this.houseForm, 'county', row.county + '')
    this.$set(this.houseForm, 'city', row.city + '')
    this.$set(this.houseForm, 'constructionArea', row.constructionArea + '')
    this.$set(this.houseForm, 'assetNo', row.assetNo)
    this.$set(this.houseForm, 'houseStatus', row.houseStatus + '')
    this.$set(this.houseForm, 'houseDesignPurpose', row.houseDesignPurpose + '')
    this.$set(this.houseForm, 'otherRights', row.otherRights + '')
    this.$set(this.houseForm, 'assetCategory', row.assetCategory + '')
    this.$set(this.houseForm, 'constructionAreaUnit', row.constructionAreaUnit + '')
    // this.$set(this.houseForm, 'subjectName', row.subjectName)
    // this.houseForm.attachmentFileDTOList.push(row.attachmentFileDTOList)
    this.houseForm.attachmentFileDTOList = Object.assign(this.houseForm.attachmentFileDTOList, row.attachmentFileDTOList)
    // this.$set(this.houseForm,"attachmentFileDTOList",String(row.attachmentFileDTOList))
    // 设置城市列表选型，区域选型默认在浙江，金华
    // this.getArea('cityList',"33" )
    // this.getArea('districtList', "3307")
  }
  // 选择省区数据
  private async getArea(Type: any = 'provinceList', Code: any = '00') {
    let res: any = await getAreaCode(Code)
    if (res.code == 200) {
      this.$set(this, Type, res.data)
    }
  }
  // 选择省份之后回调市域
  private async changecity(label: string) {
    this.provinceList.forEach((res: any) => {
      if (res.title == label) {
        this.getArea('cityList', res.id)
      }
    })
    if (this.mode == 'add') {
      this.houseForm.city = ''
      this.houseForm.county = ''
    }
  }
  // 选择市区之后回调县域
  private async ChangeCounty(label: string) {
    this.cityList.forEach((res: any) => {
      if (res.title == label) {
        this.getArea('districtList', res.id)
      }
    })
    if (this.mode == 'add') {
      this.houseForm.county = ''
    }
  }
  // 选择市区之后
  private getAreaLabel(row: any) {
    //
  }
  // 点击关闭
  private closeDlg() {
    this.$emit('update:visible', false)
    this.onClose()
  }

  // 继续
  private onContinue() {
    if (this.houseForm.attachmentFileDTOList.length == 0) {
      this.$message('请上传房屋图片')
      return ''
    }
    let form = this.$refs.HouseForm as ElForm
    form.validate((valid) => {
      if (!valid) return
      let item: any = {}
      Object.assign(item, this.houseForm)
      this.$emit('success', item)
      this.houseForm.attachmentFileDTOList = []
      form.resetFields()
    })
  }
  private changeElse() {
    if (this.houseForm.leaseTerm == '其他') {
      this.showLeaseTerm = true
    } else {
      this.showLeaseTerm = false
    }
  }
  private submitForm() {
    let form = this.$refs.HouseForm as ElForm

    form.validate((valid) => {
      if (!valid) return
      if (this.houseForm.attachmentFileDTOList.length == 0) {
        this.$message('请上传房屋图片')
        return ''
      }
      this.$emit('success', this.houseForm)
      this.closeDlg()
    })
  }
  //  文件上传
  private onFilesChange() {
    //
  }
  private handleUploadComplete() {
    //
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  .dialog-body {
    padding-bottom: 16px !important;
  }
}

::v-deep .el-form-item {
  margin-bottom: 0px !important;
}

::v-deep .el-select {
  width: 100%;
}
.w50 {
  width: 50%;
}
::v-deep .el-descriptions-item__label.is-bordered-label {
  display: none;
}
:v-deep.el-radio__input.is-disabled .el-radio__inner,
.el-radio__input.is-disabled.is-checked .el-radio__inner {
  background-color: #b43c3c;
}
.content {
  padding: 10px 24px;
}

.footer {
  display: flex;
  justify-content: flex-end;
}
</style>
