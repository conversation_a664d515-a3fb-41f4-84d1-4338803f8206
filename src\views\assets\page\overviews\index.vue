<template>
  <section v-loading="loading"
    class="echarts-overviews-wrap">
    <div class="header-box">
      <el-form ref="form"
        :inline="true"
        :model="formData">
        <el-form-item style="text-align: left;">
          <el-radio-group v-model="formData.orgCode">
            <el-radio-button v-for="(item, index) of compTree"
              :key="index"
              :label="item.deptCode"
              :value="item.deptName">{{item.deptName}}</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item style="margin-right: 0;">
          <el-date-picker v-model="formData.year"
            :clearable="false"
            type="year"
            placeholder="年份"
            value-format="yyyy"
            style="width: 150px;">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </div>

    <div v-if="isShow"
      class="conter-box">
      <!-- 集团资产汇总 -->
      <CompanySummary :year="formData.year"
        :orgCode="formData.orgCode" />

      <!-- 经营性资产使用情况、资产交易挂牌 -->
      <div class="modules">
        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>经营性资产使用情况</span>
          </h4>
          <div class="cter">
            <BusinessTrend :year="formData.year"
              :orgCode="formData.orgCode" />
          </div>
        </div>

        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>年度租金收入</span>
          </h4>
          <div class="cter">
            <TransactionListing :year="formData.year"
              :orgCode="formData.orgCode" />
          </div>
        </div>
      </div>

      <!-- 资产收缴率、合同到期 -->
      <div v-if="false" class="modules">
        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>租金收缴率</span>
          </h4>
          <div class="cter">
            <ReceivableOverYears :year="formData.year"
              :orgCode="formData.orgCode" />
          </div>
        </div>

        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>合同到期</span>
          </h4>
          <div class="cter">
            <ContractExpires :year="formData.year"
              :orgCode="formData.orgCode" />
          </div>
        </div>
      </div>

      <!-- 资产营收情况1 -->
      <div v-if="false"
        class="modules">
        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>资产营收情况</span>
          </h4>
          <div class="cter">
            <YearlyRateReturn :year="formData.year"
              :orgCode="formData.orgCode"
              @emitChangeArea="emitChangeArea" />
          </div>
        </div>
      </div>

      <!-- 资产营收情况2 -->
      <div v-if="false"
        class="modules">
        <div class="mode">
          <div class="cter">
            <RevenueRateReturn :year="formData.year"
              :orgCode="formData.orgCode"
              :areaCode="areaCode" />
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { getGroupList, getOrgCodeList } from '@/api/public'
import BusinessTrend from './components/BusinessTrend.vue'
import CompanySummary from './components/CompanySummary.vue'
import ContractExpires from './components/ContractExpires.vue'
import YearlyRateReturn from './components/YearlyRateReturn.vue'
import TransactionListing from './components/TransactionListing.vue'
import ReceivableOverYears from './components/ReceivableOverYears.vue'
import RevenueRateReturn from './components/RevenueRateReturn.vue'

@Component({
  components: {
    BusinessTrend,
    CompanySummary,
    ContractExpires,
    YearlyRateReturn,
    RevenueRateReturn,
    TransactionListing,
    ReceivableOverYears
  }
})
export default class extends Vue {
  private isShow = true
  private loading = false
  private timer: any = null
  private areaCode = ''
  private compTree = []
  private orgIdList = []
  private formData: {
    orgCode: string | null
    year: string | null
  } = {
    orgCode: '',
    year: ''
  }

  // 数据初始化
  private created() {
    this.formData.year = (this as any).$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 组件初始化
  private mounted() {
    this.getCompTree()

    window.addEventListener('resize', () => {
      clearTimeout(this.timer)
      this.isShow = false
      this.loading = true
      this.timer = setTimeout(() => {
        this.isShow = true
        this.loading = false
      }, 500)
    })
  }

  // 获取集团列表
  private async getCompTree() {
    let { data } = await getOrgCodeList()

    data.unshift({
      deptCode: '0',
      deptName: '全部'
    })

    // 设置第一个选中数据
    if (Array.isArray(data) && data.length) this.formData.orgCode = data[0].deptCode

    this.compTree = data || []
  }

  // 资产营收情况：区域改变时触发
  private emitChangeArea(code: string) {
    this.areaCode = code
  }

  // 组件销毁
  private destroyed() {
    clearTimeout(this.timer)
    this.timer = null
  }
}
</script>