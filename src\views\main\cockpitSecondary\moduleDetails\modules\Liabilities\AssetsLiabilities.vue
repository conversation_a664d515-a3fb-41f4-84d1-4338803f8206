/* 历年资产负债率 */

<template>
  <section class="assets-liabilities-wrap">
    <CommonTitle :title="title"
      :isRotuerThrow="true"
      :routerObj="{
        bizAppCode: 'finance',
        detailsPath: '/wel/index?bizAppCode=finance&path=/business/finance/debtStatistics/search/stateEntriseSum'
      }"
      class="m-t-14 p-l-20 p-r-20" />

    <!-- 是否排除金华银行 -->
    <div v-if="orgCode === '0' || orgCode === 'JH1200'"
      class="tabs-box">
      <el-checkbox v-model="exclude"
        :true-label="0"
        :false-label="1">{{getExcludeText}}</el-checkbox>
    </div>

    <!-- 视图 -->
    <div v-loading="loading"
      class="assets-liabilities-content"
      ref="refEcharts" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { echartConfigure } from '@/views/main/cockpitcren/baseData'
import { liabilitiesHistory } from '@/api/cockpit'
import { Loading } from '@/decorators'
import { bigNumberFormat } from '@/utils'
import * as echarts from 'echarts'
import CommonTitle from '@/views/main/cockpitSecondary/components/CommonTitle.vue'

type EChartsOption = echarts.EChartsOption

@Component({
  components: {
    CommonTitle
  }
})
export default class extends Vue {
  @Prop() private title!: string // 标题
  @Prop() private orgCode!: string // 集团
  @Prop() private year!: string // 年份
  @Prop() private moon?: string // 月份
  @Prop() private echartsData!: any // 渲染数据

  private exclude = 0
  private loading = false
  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private seriesData: any[] = []
  private option: EChartsOption = {}
  private legendData: string[] = []
  private xAxisData: string[] = []

  // 数据变化，渲染视图
  @Watch('orgCode', { deep: true })
  @Watch('year', { deep: true })
  @Watch('moon', { deep: true })
  @Watch('exclude', { deep: true })
  private changeEchartsData() {
    this.initData()
  }

  // 金华银行tab切换文案改变
  get getExcludeText() {
    let text = '金华银行'

    if (this.orgCode === '0') text = '金华银行'
    if (this.orgCode === 'JH1200') text = '大合并'

    return text
  }

  // 万元数据转化成亿元
  get getBigNumberFormat() {
    return (num: string | number) => {
      return bigNumberFormat(+num).value || 0
    }
  }

  // 组件初始化
  private mounted() {
    this.chartDom = this.$refs.refEcharts as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let exclude = this.orgCode === '0' || this.orgCode === 'JH1200' ? this.exclude : undefined

    let { data } = await liabilitiesHistory({
      year: this.year,
      moon: this.moon,
      orgCode: this.orgCode,
      exclude: exclude
    })

    // 组装echarts数据
    if (!Array.isArray(data) || !data.length) return

    let legendList: string[] = []
    let xAxisList: string[] = []
    let seriesList: any[] = []
    data.forEach((item, index) => {
      xAxisList.push(item.year)
      item.list.forEach((itemChild: any) => {
        !index && legendList.push(itemChild.label)
      })
    })

    legendList.forEach((item, index) => {
      let arr: number[] = []
      data.forEach((itemData: any) => {
        if (item === itemData.list[index].label) {
          if (item.includes('负债率')) {
            arr.push(itemData.list[index].value)
          } else {
            arr.push(+this.getBigNumberFormat(itemData.list[index].value))
          }
        }
      })

      switch (item.includes('负债率')) {
        case false:
          seriesList.push({
            name: item,
            data: arr,
            type: 'bar',
            barWidth: 20,
            tooltip: {
              valueFormatter: function (value: string) {
                return value + ' 亿元'
              }
            },
            itemStyle: {
              borderRadius: [70, 70, 0, 0]
            }
          })
          break
        case true:
          seriesList.push({
            name: item,
            data: arr,
            type: 'line',
            symbol: 'circle',
            yAxisIndex: 1,
            lineStyle: {
              width: 4
            },
            tooltip: {
              valueFormatter: function (value: string) {
                return value + ' %'
              }
            }
          })
          break
      }
    })

    this.legendData = legendList
    this.xAxisData = xAxisList
    this.seriesData = seriesList
    this.initEcharts()
  }

  // 初始化数据
  private initEcharts() {
    let textSize = echartConfigure.textSize
    let legendSize = echartConfigure.legendSize
    let legendData = this.legendData
    let xAxisData = this.xAxisData
    let seriesData = this.seriesData

    this.option = {
      color: [
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(252, 180, 94, 0.1)'
            },
            {
              offset: 0.3,
              color: 'rgba(252, 180, 94, 0.7)'
            },
            {
              offset: 1,
              color: 'rgba(252, 180, 94, 1)'
            }
          ],
          global: false // 缺省为 false
        },
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(62, 238, 255, 0.1)'
            },
            {
              offset: 0.3,
              color: 'rgba(62, 238, 255, 0.7)'
            },
            {
              offset: 1,
              color: 'rgba(62, 238, 255, 1)'
            }
          ],
          global: false // 缺省为 false
        },
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(169, 3, 198, 1)'
            },
            {
              offset: 0.3,
              color: 'rgba(169, 3, 198, 1)'
            },
            {
              offset: 1,
              color: 'rgba(169, 3, 198, 1)'
            }
          ],
          global: false
        },
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(205, 58, 37, 1)'
            },
            {
              offset: 1,
              color: 'rgba(205, 58, 37, 1)'
            }
          ],
          global: false
        }
      ],
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#00000000',
        shadowColor: '#00000000',
        borderWidth: 0,
        formatter: (params: any) => {
          let dom = ''
          let year = params[0].axisValue || ''

          params.forEach((item: any, index: number) => {
            let style = ''
            let unit = item.seriesName.includes('负债率') ? '%' : '亿元'

            style = `
              <span style="padding-right: 40px;">${item.seriesName}</span>
              <span style="color: #fff;">
                ${item.value}${unit}
              </span>
            `

            dom += `
              <div style="display: flex; align-items: center; justify-content: space-between;">
                ${style}
              </div>
            `
          })

          return `
            <div style="font-size:40px; line-height: 70px; color: #fff; border-radius:10px;border:2px solid #0C3EB6; padding: 20px; display: flex;flex-direction: column; background: linear-gradient(0deg, rgba(0, 103, 177, 1), rgba(0, 103, 177, 1));">
              <div style="font-weight:bold;">${year}年</div>
              ${dom}
            </div>
          `
        }
      },
      legend: {
        show: true,
        top: '2%',
        right: '3%',
        itemGap: 40,
        padding: 0,
        itemHeight: 24,
        itemWidth: 24,
        itemStyle: {
          borderCap: 'butt'
        },
        lineStyle: {
          type: [0, 10],
          dashOffset: 5
        },
        textStyle: {
          color: '#fff',
          fontSize: legendSize,
          fontWeight: 'bold'
        },
        data: legendData
      },
      grid: {
        top: '20%',
        left: '1%',
        right: '1%',
        bottom: '1%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          data: xAxisData,
          axisPointer: {
            type: 'shadow'
          },
          axisLabel: {
            fontSize: textSize * 1.1,
            fontWeight: 'bold'
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#5db0ea'
            }
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: '亿元',
          min: 0,
          nameTextStyle: {
            color: '#5db0ea',
            align: 'left',
            fontWeight: 'bold',
            fontSize: textSize
          },
          axisLabel: {
            fontSize: textSize * 1.1,
            fontWeight: 'bold'
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#5db0ea'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0, 103, 177, 0.6)'
            }
          }
        },
        {
          type: 'value',
          name: '负债率(%)',
          min: 0,
          max: 100,
          nameTextStyle: {
            color: '#5db0ea',
            align: 'right',
            fontWeight: 'bold',
            fontSize: textSize
          },
          axisLabel: {
            fontSize: textSize * 1.1,
            fontWeight: 'bold'
          },
          axisLine: {
            lineStyle: {
              color: '#5db0ea'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0, 103, 177, 0)'
            }
          }
        }
      ],
      series: seriesData,
      animationDelay: function (idx) {
        return idx * 300
      }
    }

    this.myChart && this.myChart.setOption(this.option, true)
  }
}
</script>

<style scoped lang="scss">
.assets-liabilities-wrap {
  position: relative;
  height: 82%;
  .assets-liabilities-content {
    width: 100%;
    height: 100%;
    transform: translateY(-20px);
  }

  .tabs-box {
    position: absolute;
    left: 134px;
    top: 122px;
    z-index: 10;
    font-size: 34px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    span {
      margin-right: 20px;
    }

    ::v-deep .el-checkbox {
      transform: scale(2.4) translateX(-28px);
      .el-checkbox__label {
        color: #ccc;
        padding-left: 5px;
      }
      .el-checkbox__inner {
        background-color: #ccc;
        border-color: #ccc;
      }
      .el-checkbox__input.is-checked .el-checkbox__inner {
        background-color: #2dc4d3;
        border-color: #2dc4d3;
      }
    }

    ::v-deep .is-checked {
      .el-checkbox__label {
        color: #00ffff;
      }
    }
  }
}
</style>


