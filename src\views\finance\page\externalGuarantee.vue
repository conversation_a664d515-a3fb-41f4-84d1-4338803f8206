 <!--  对外担保备案 -->
<template>
  <el-container direction="vertical"
    class="content">
    <search-bar :items="searchItems"
      @onSearch="handleSearch">
      <div>
        <el-button type="primary"
          @click="AddForm">新增</el-button>
      </div>
    </search-bar>
    <Grid @row-click="loaddetail"
      :columns="cols"
      ref="grid"
      :remote-url="remoteUrl"
      :show-pagination="true"
      :search-params="searchParams">
      <template slot="documentNo"
        slot-scope="scope">
        <span>{{ scope.row.documentNo }}</span>
      </template>
      <template slot="state"
        slot-scope="scope">
        <span v-if="scope.row.state == 1">审批中</span>
        <span v-else-if="scope.row.state == 2">审批通过</span>
        <span v-else-if="scope.row.state == 3"
          style="color: #df7575">审批不通过</span>
      </template>
      <!-- 决策机构 -->
      <!-- <div v-for="(item,index) in cols" :key="index" slot="item.slotName" slot-scope="scope">
            <span v-if="'filterOptions' in item">{{ scope.row[item.prop] }}</span>
          </div> -->
      <template slot="issuanceCurrency"
        slot-scope="scope">
        <span v-if="scope.row.state == 1">人民币</span>
        <span v-else-if="scope.row.state == 2">美元</span>
        <span v-else-if="scope.row.state == 3">其他</span>
      </template>
      <template slot="operatingBar"
        slot-scope="scope">
        <div class="operatingBar_content">
          <el-button type="text"
            @click.stop="loaddetail(scope.row)">查看</el-button>
          <el-button type="text"
            @click.stop="loofinfo(scope.row)">编辑</el-button>
          <!-- <el-button type="text" slot="reference">删除</el-button> -->
        </div>
      </template>
    </Grid>
    <!-- 查看 -->
    <Dialog-add :mode="dialogMode"
      :visible.sync="showDialogAdd"
      v-if="showDialogAdd"
      @changshowDialogAdd="changeShowDialogAdd"
      :Diaformdata="Diaformdata" />
    <!-- 查看详情 -->
    <DetailAsset :fileList="pageData.attachmentFileDTOList"
      dict="financial_externalGuarantee_attach"
      :visible="showDetailAsset"
      :list="Diadetaillist"
      @changeShowDetail="changeShowDetail"></DetailAsset>
  </el-container>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import Grid from '@/components/Grid/index.vue'
import DialogAdd from '../components/Dialogexternal.vue'
import DetailAsset from '../components/DetailAsset.vue'
import { AssetList } from '../date'
import { deleteFormData } from '@/api/finance'
import searchBar from '@/components/SearchBar/index.vue'
import { equityRelationship } from '../filterOptions'
import { deepClone } from '@/utils/index'
@Component({
  name: 'Container',
  components: {
    Grid,
    DialogAdd,
    DetailAsset,
    searchBar
  }
})
export default class Container extends Vue {
  private pageData = {}
  private Diadetaillist: object[] = [] //查看详情列表
  private Diaformdata: object = {}
  private showDialogAdd = false
  private showDetailAsset = false
  private searchParams = {} //表格搜索条件
  private dialogMode = ''
  private remoteUrl = '/fht-monitor/fin/externalGuarantee/page'
  private searchItems = [
    {
      type: 'text',
      key: 'keyword',
      placeholder: '请输入关键字进行查询',
      width: '200px'
    }
  ]
  private data: object[] = AssetList
  private cols = [
    {
      label: '单据编号',
      prop: 'documentNo',
      slotName: 'documentNo',
      minWidth: 180
    },
    {
      label: '公司名称',
      prop: 'companyName',
      minWidth: 180
    },
    {
      label: '申请日期',
      prop: 'applicationDate',
      minWidth: 180
    },

    {
      label: '公司地址',
      prop: 'companyAddress',
      minWidth: 110
    },
    {
      label: '法定代表人',
      prop: 'legalRepresentative',
      minWidth: 110
    },
    {
      label: '所有者权益(元)',
      prop: 'ownersEquity',
      minWidth: 120
    },
    {
      label: '董事会决议文号',
      prop: 'boardResolutionNum',
      minWidth: 120
    },
    {
      label: '被担保人',
      prop: 'guarantor',
      minWidth: 100
    },

    {
      label: '与被担保人关系',
      prop: 'equityRelationshipDesc',
      // slotName: 'equityRelationship',
      minWidth: 140,
      filterOptions: equityRelationship
    },
    {
      label: '持股比例(%)',
      prop: 'shareholdingRatio',
      minWidth: 100
    },
    {
      label: '担保金额(元)',
      prop: 'guaranteeAmount',
      minWidth: 100
    },
    {
      label: '担保期限(年)',
      prop: 'guaranteePeriod',
      minWidth: 100
    },
    {
      label: '反担保措施',
      prop: 'counterGuaranteeMeasures',
      minWidth: 80
    },
    {
      label: '担保项目概况',
      prop: 'projectDesc',
      minWidth: 100
    },
    {
      label: '累计对外担保金额（元）',
      prop: 'accumulatedGuaranteeAmount',
      minWidth: 150
    },
    {
      label: '备注',
      prop: 'remark',
      minWidth: 80
    },
    // {
    //   label: '创建人',
    //   prop: 'createUser',
    //   minWidth: 80
    // },
    {
      label: '创建日期',
      prop: 'createTime',
      minWidth: 80
    },
    {
      label: '操作',
      prop: 'operatingBar',
      slotName: 'operatingBar',
      fixed: 'right',
      minWidth: 80
    }
  ]

  // search搜索
  private handleSearch(condition: any) {
    this.searchParams = condition
    let grid: any = this.$refs.grid
    grid.refresh()
  }
  // 新增按钮
  private AddForm() {
    this.Diaformdata = {
      id: ''
    }
    this.dialogMode = 'add'
    this.showDialogAdd = true
  }
  // 改变显示隐藏
  private changeShowDialogAdd(state: boolean) {
    if (state == false) {
      ;(this.$refs.grid as Grid).refresh(true)
    }
    this.showDialogAdd = state
  }
  private changeShowDetail(state: boolean) {
    this.showDetailAsset = state
  }
  //点击编辑
  private loofinfo(row: any) {
    this.dialogMode = 'edit'
    this.Diaformdata = row
    // this.$set(this.Diaformdata,"id",row.id)
    this.showDialogAdd = true
  }
  //加载查看数据
  private loaddetail(row: any) {
    this.dialogMode = 'see'
    this.Diaformdata = deepClone(row)
    // this.$set(this.Diaformdata,"id",row.id)
    this.showDialogAdd = true
  }
  // 删除
  private async deleteinfo() {
    // 拉取接口远程数据
    let params = {}
    try {
      let res = await deleteFormData(params)
      if (res.success) {
        // this.option = res.data
      }
    } catch (e) {
      //
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
}
</style>
