<template>
  <div class="tool-content">
    <el-menu :default-active="tab" class="el-menu-demo" mode="horizontal">
      <el-menu-item index="0">全部</el-menu-item>
      <el-menu-item index="1">房产</el-menu-item>
      <el-menu-item index="2">土地</el-menu-item>
    </el-menu>
    <!--   资产统计 -->
    <el-alert title="共有12个单位，45个资产" type="info" show-icon></el-alert>
    <!-- 目录 -->
    <el-collapse accordion v-model="activeNames" >
      <el-collapse-item
        v-for="(item,index) in companyList"
        :key="index"
        :title="item.name"
        :name="index"
      >
        <div slot="title">
          <i :style="{color:getcolor(index)}" class="el-icon-star-on"></i>
          {{item.name}}
        </div>
        <div v-for="(assetItem,i) in item.List" :key="i" class="asset-list">
          <div>
            <span class="asset-type-tip">房</span>
            <span>{{assetItem.name}}</span>
          </div>
          <el-button type="text">详情</el-button>
        </div>
      </el-collapse-item>
    </el-collapse>
    <div > 
      <el-button @click="showdetail">显示</el-button>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { colorSixList } from '@/views/main/cockpitcren/baseData'
@Component({
  components: {}
})
export default class extends Vue {
  private tab = '0'
  private activeNames = 99
  private companyList = [
    
  ]
  get getcolor() {
    return (index: number) => {
      return colorSixList[index]
    }
  }
  //m 显示详情
  private showdetail(){
    this.$emit("showdetail",true)
  }
}
</script>

<style lang="scss" scoped>
.tool-content {
  z-index: 99;
  height: 100%;
  width: 100%;
  background-color: #fff;
}
.asset-type-tip {
  display: inline-block;
  padding: 3px;
  font-size: 18px;
}
.asset-list{
    box-sizing: border-box;
    padding: 0 8px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    justify-items: center;
}
</style>