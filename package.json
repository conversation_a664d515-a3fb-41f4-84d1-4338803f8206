{"name": "vue-project-ts-pc", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.19.2", "compression-webpack-plugin": "^6.1.2", "compressorjs": "^1.0.7", "core-js": "^3.6.5", "echarts": "^5.3.2", "echarts-liquidfill": "^3.1.0", "element-ui": "^2.15.8", "html2canvas": "^1.4.1", "js-cookie": "^2.2.1", "js-md5": "^0.7.3", "jspdf": "^2.5.2", "lodash": "^4.17.21", "moment": "^2.29.3", "qs": "^6.9.4", "v-viewer": "^1.5.1", "vue": "^2.6.11", "vue-class-component": "^7.2.3", "vue-count-to": "^1.0.13", "vue-jsmind": "^1.5.0", "vue-print-nb": "^1.7.5", "vue-property-decorator": "^8.4.2", "vue-router": "^3.2.0", "vuedraggable": "^2.24.3", "vuex": "^3.4.0", "vxe-table": "^3.6.6", "xe-utils": "^3.5.7"}, "devDependencies": {"@types/js-cookie": "^3.0.2", "@types/js-md5": "^0.4.3", "@types/lodash": "^4.14.182", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "^4.3.0", "@vue/cli-plugin-eslint": "^4.3.0", "@vue/cli-plugin-router": "^4.3.0", "@vue/cli-plugin-typescript": "^4.3.0", "@vue/cli-plugin-vuex": "^4.3.0", "@vue/cli-service": "^4.3.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^6.2.2", "prettier": "^1.19.1", "sass": "^1.26.5", "sass-loader": "^8.0.2", "speed-measure-webpack-plugin": "^1.5.0", "typescript": "~3.9.3", "vue-template-compiler": "^2.6.11", "vuex-module-decorators": "^0.9.11"}}