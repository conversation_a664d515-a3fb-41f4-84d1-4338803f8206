// 出租信息
<template>
  <section class="rental-info">
    <el-form
      ref="RentalInfoForm"
      :rules="rentalInfoFormRules"
      :model="rentalInfoForm"
      label-width="140px"
    >
      <!-- 基本情况 -->
      <el-descriptions
        class="margin-top"
        title="基本情况"
        :column="3"
        :labelStyle="{
          width: '120px'
        }"
        border
      >
        <el-descriptions-item label="内部决策情况" :span="24">
          <div class="m-b-12">以下决议已按有关法律法规要求完成，议事规则和决策程序符合规定。</div>
          <el-radio-group v-model="rentalInfoForm.internalDecision">
            <el-radio
              v-for="(decision, index) in getDictData('internal_decision')"
              :key="index"
              :label="decision.value"
            >{{ decision.label }}</el-radio>
          </el-radio-group>
          <el-form-item
            v-if="rentalInfoForm.internalDecision == '9'"
            label
            label-width="0px"
            prop="otherInternalDecision"
          >
            <el-input
              type="textarea"
              class="m-t-12"
              autosize
              placeholder="其它内容请输入"
              v-model="rentalInfoForm.otherDecision"
            ></el-input>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="出租行为批准情况" :span="24">
          <el-form-item label="批准单位名称" label-width="108px" prop="approvalOrgan">
            <el-select
              v-model="rentalInfoForm.approvalOrgan"
              placeholder
              @change="changeapprovalOrgan(rentalInfoForm.approvalOrgan)"
            >
              <el-option
                v-for="item in getDictData('approval_organ')"
                :label="item.label"
                :value="Number(item.value)"
                :key="item.value"
              >{{ item.label }}</el-option>
            </el-select>
            <!-- <el-input v-model="rentalInfoForm.approvalOrgan" placeholder="请输入" /> -->
          </el-form-item>
          <el-form-item
            label="批准单位名称"
            v-show="rentalInfoForm.approvalOrgan == 99"
            label-width="108px"
            prop="approvalOrganName"
          >
            <el-input v-model="rentalInfoForm.approvalOrganName" placeholder="请输入批准单位名称" />
          </el-form-item>
          <el-form-item label="批准文件名称" label-width="108px" prop="approvalFile">
            <el-input v-model="rentalInfoForm.approvalFile" placeholder="请输入" />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item label="是否国有资产">
          <el-radio-group v-model="rentalInfoForm.stateAsset">
            <el-radio-button :label="1">是</el-radio-button>
            <el-radio-button :label="0">否</el-radio-button>
          </el-radio-group>
        </el-descriptions-item>
        <el-descriptions-item
          contentClassName="department-name-input"
          :labelStyle="{
            width: '180px'
          }"
          label="国家出租企业或主管部门名称"
        >
          <el-form-item label label-width="0px">
            <el-input v-model="rentalInfoForm.competentDepartment" placeholder="请输入国家出租企业或主管部门名称" />
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
    </el-form>

    <!-- 房产申请表 -->
    <RealEstateApplicationTable v-model="rentalInfoForm.assetList" 
     />

    <!-- 附件列表 -->
    <AccessoryList  v-model="rentalInfoForm.attachmentFileDTOList" dict="asset_basic_attach" mode="upload" />
  </section>
</template>

<script lang="ts">
import { decisionTypeList } from '@/views/assets/filterOptions'
import { ElForm } from 'element-ui/types/form'
import { Component, Model, Prop, Vue, Watch } from 'vue-property-decorator'
import RealEstateApplicationTable from './RealEstateApplicationTable.vue'
import AccessoryList, { Accessory } from './AccessoryFileList.vue'
import { AssetForm } from './AssetForm.vue'
import { BusinessModule } from '@/store/modules/businessDict'
import { number } from 'echarts'
export interface RentalInfo {
  internalDecision: number | string
  otherInternalDecision: string
  approvalFile: string
  approvalOrgan: string | number
  approvalOrganName: string
  stateAsset: number | string
  competentDepartment: string
  assetList: AssetForm[]
  attachmentFileDTOList: any[]
}

@Component({
  components: {
    RealEstateApplicationTable,
    AccessoryList
  }
})
export default class extends Vue {
  @Prop() private initData!: any
  @Watch('initData')
  private changeInitdate() {
    this.setData()
  }
  private rentalInfoForm: RentalInfo = {
    internalDecision: '0',
    otherInternalDecision: '',
    approvalFile: '',
    approvalOrgan: 99,
    approvalOrganName: '',
    stateAsset: 1,
    competentDepartment: '',
    assetList: [],
    attachmentFileDTOList: []
  }
  private rentalInfoFormRules = {
    otherInternalDecision: [{ required: true, trigger: 'blur', message: '请输入其它内容' }],
    approvalOrgan: [{ required: true, trigger: 'blur', message: '请选择批准单位名称' }],
    approvalOrganName: [{ required: true, trigger: 'blur', message: '请输入批准单位名称' }],
    approvalFile: [{ required: true, trigger: 'blur', message: '请输入批准文件名称' }],
    competentDepartment: [{ required: true, trigger: 'blur', message: '请输入国家出租企业或主管部门名称' }]
  }

  private decisionTypeList = this.getDictData('internal_decision')
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  private changeapprovalOrgan(e: any) {
    let dict = this.getDictData('approval_organ')
    let opt = dict.find((res: any) => {
      return res.value == e
    })
    if (e == 99) {
      this.rentalInfoForm.approvalOrganName = ''
    } else {
      this.rentalInfoForm.approvalOrganName = opt.label
    }
  }
  private accessoryList: Accessory[] = [
    {
      fileName: '授权委托人的身份证明',
      isRequired: true,
      prop: 'attachmentFile1',
      fileList: []
    },
    {
      fileName: '授权委托书',
      isRequired: true,
      prop: 'attachmentFile2',
      fileList: []
    },
    {
      fileName: '内部决策文件',
      isRequired: true,
      prop: 'attachmentFile3',
      fileList: []
    },
    {
      fileName: '审批文件',
      isRequired: true,
      prop: 'attachmentFile4',
      fileList: []
    },
    {
      fileName: '资产权属证明',
      isRequired: true,
      prop: 'attachmentFile5',
      fileList: []
    },
    {
      fileName: '资产评估报告',
      isRequired: true,
      prop: 'attachmentFile6',
      fileList: []
    },
    {
      fileName: '其他资产相关权利人的意思表示（如涉及）',
      isRequired: false,
      prop: 'attachmentFile7',
      fileList: []
    },
    {
      fileName: '其他具有法律效力的权属证明文件（如涉及）',
      isRequired: false,
      prop: 'attachmentFile8',
      fileList: []
    }
  ]

  created() {
    this.setData()
  }
  private setData() {
    this.rentalInfoForm = Object.assign(this.rentalInfoForm, this.initData)

    // 附件列表赋值
    // this.accessoryList.forEach((item) => {
    //   item.fileList = this.rentalInfoForm[item.prop as FileKey] || []
    // })
  }
  // 获取数据,先执行 validate 再执行 getData
  public getData() {
    // 附件赋值
    return this.rentalInfoForm
  }
  public validate(): Promise<boolean> {
    let form = this.$refs.RentalInfoForm as ElForm
    return form.validate().then(
      () => {
          if(this.rentalInfoForm.attachmentFileDTOList.length==0){
             this.$message.warning(`请上传附件 !`)
            return Promise.reject(false)
          }

        return Promise.resolve(true)
      },
      () => {
        this.$message.warning('请完善出租信息！')
        return Promise.reject(false)
      }
    )
  }
}
</script>


<style scoped lang="scss">
::v-deep .department-name-input {
  .el-form-item {
    margin-bottom: 0px !important;
  }
}
</style>