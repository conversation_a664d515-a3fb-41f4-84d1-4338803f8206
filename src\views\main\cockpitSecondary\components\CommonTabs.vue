<template>
  <section class="common-tabs-default-wrap">
    <el-tabs v-model="activeTabs"
      type="card"
      :stretch="true"
      @tab-click="changeCompanyTabs">
      <el-tab-pane v-for="(item, index) of getCompanyList"
        :key="index"
        :label="item.name"
        :name="String(item.code)" />
    </el-tabs>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { companyList } from '@/views/main/cockpitSecondary/baseData'

@Component
export default class extends Vue {
  @Prop({ default: false }) private typeStyle?: boolean // 样式

  private activeTabs = '0'

  // 组件初始化
  private mounted() {
    this.$nextTick(() => {
      this.$emit('commonTabsHandle', this.activeTabs)
      this.$bus.$emit('BusCompanyTabs', this.activeTabs)
    })
  }

  // tabs：获取各个集团数据
  get getCompanyList() {
    return companyList
  }

  // 各个集团 tabs 切换触发
  private changeCompanyTabs() {
    this.$emit('commonTabsHandle', this.activeTabs)
    this.$bus.$emit('BusCompanyTabs', this.activeTabs)
  }
}
</script>

<style scoped lang="scss">
.common-tabs-default-wrap {
  $color: #4a97f8;
  $bg: #3eeeff;
  $activeColor: #021d5c;

  margin: 12px 0;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid rgba($color: $color, $alpha: 1);

  ::v-deep .el-tabs {
    .el-tabs__header {
      margin: 0;
      border-bottom: 1px solid $color;
      .el-tabs__nav {
        border-color: $color;
      }
      .el-tabs__item {
        color: #fff;
        font-size: 34px;
        border-color: $color;
        font-family: 'Alibaba-PuHuiTi-Bold';
        &:hover {
          border: 1px solid rgba($color: $color, $alpha: 1);
          box-shadow: 0 0 30px 0px inset rgba($color: $color, $alpha: 0.6);
        }
      }
      .is-active {
        color: $activeColor !important;
        background: $bg;
        box-shadow: none !important;
        border: 1px solid rgba($color: $bg, $alpha: 1) !important;
      }
    }
    .el-tabs__item {
      height: auto;
      padding: 14px 10px;
    }
  }
}
</style>