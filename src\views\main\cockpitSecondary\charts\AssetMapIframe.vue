/** 资产地图 */

<template>
  <div class="asset-map">
    <!-- 地图区域 -->
    <div class="asset-map__content">
      <div id="allmap" />
    </div>
  </div>
</template>

<script lang="ts">
import { assetHouseCoordinate } from '@/api/cockpit'
import { Loading } from '@/decorators'
import { Component, Inject, Vue, Watch } from 'vue-property-decorator'
import { companyList } from '../baseData'

export interface MarkerItem {
  lnglat: [number, number] //点标记位置
  name: string
  id: number
  assetName: string // 资产名称
  address: string // 坐落地址
  assetNo: string // 资产编号
  assetType: string // 资产类型 1-房产 2-土地
  bizStatus: string // 业态情况 1-住宅 2-综合办公楼 3-商铺 4-其他
  companyCode: number // 所属公司code
  companyName: string // 所属公司名称
  constructionArea: number // 建筑面积
  currentFloors: number // 所在层数
  originalValue: number
  property: string // 资产性质 1-住宅 2-营业房 3-商品房 4-自管房 5-拆迁安置房 6-其他
  source: string // 资产来源, -1-未知 1-划拨转入 2-自购 3-自建 4-其他
  style: number
}

declare const AMap: any // 声明 declare

@Component
export default class extends Vue {
  // 监听公司id的改变
  @Watch('currentCompanyCode')
  private onCurrentCompanyCode() {
    this.getAssetHouseCoordinate()
  }

  // 后端暂时不会处理传参，所以这里前端做筛选
  @Watch('assetForm.useStatus', { deep: true })
  private onKeywordChange() {
    this.realList = this.markerList.filter((item) => item.bizStatus === this.assetForm.useStatus)
    this.createMassMarks()
  }

  // 后端暂时不会处理传参，所以这里前端做筛选
  @Watch('assetForm.type', { deep: true })
  private onTypeChange() {
    this.realList = this.markerList.filter((item) => item.assetType === this.assetForm.type)
    this.createMassMarks()
  }

  @Watch('assetForm.keyword', { deep: true })
  private onAssetFormChange() {
    if (this.timer) {
      clearTimeout(this.timer)
    }
    this.timer = setTimeout(() => {
      this.getAssetHouseCoordinate()
    }, 300)
  }

  private map: any
  private infoWindow: any
  private isShowSearch = false
  private isShowList = false
  private dialogVisible = false
  private loading = false
  private isFullScreen = false
  private timer: any
  private currentAssetInfo: any = {}
  private currentCompanyCode = 0
  private companyList = companyList
  private checkedMarker: any
  private massMarks: any
  private realList: MarkerItem[] = []
  private assetForm = {
    type: '',
    keyword: '',
    useStatus: '',
    raidus: 1000,
    lon: 119.654566,
    lat: 29.084632
  }
  private currentItem: MarkerItem = {
    lnglat: [0, 0], //点标记位置
    name: '',
    id: 0,
    assetName: '',
    address: '',
    assetNo: '',
    assetType: '',
    bizStatus: '',
    companyCode: 0, // 所属公司code
    companyName: '', // 所属公司名称
    constructionArea: 0, // 建筑面积
    currentFloors: 0, // 所在层数
    originalValue: 0,
    property: '', // 资产性质 1-住宅 2-营业房 3-商品房 4-自管房 5-拆迁安置房 6-其他
    source: '', // 资产来源, -1-未知 1-划拨转入 2-自购 3-自建 4-其他
    style: 1
  }

  private markerList: MarkerItem[] = []

  get host() {
    return window.location.host
  }

  created() {
    // 监听父级 postMessage
    window.parent.addEventListener('message', (e: any) => {
      let res = e.data
      switch (res.code) {
        // 集团详情改变
        case 'currentCompanyCode':
          this.currentCompanyCode = +res.data
          break
        // 筛选条件改变
        case 'paramsChange':
          this.assetForm = Object.assign(this.assetForm, res.data)
          break
        // 点击地址
        case 'markerClick':
          this.currentItem = res.data
          this.onMarkerClick(this.currentItem)
          break
      }
    })

    // 点击信息框详情事件
    ;(window as any).openDatailDialog = (id: any) => {
      this.currentItem = this.markerList.find((item) => item.id === id) || {
        lnglat: [0, 0], //点标记位置
        name: '',
        id: 0,
        assetName: '',
        address: '',
        assetNo: '',
        assetType: '',
        bizStatus: '',
        companyCode: 0, // 所属公司code
        companyName: '', // 所属公司名称
        constructionArea: 0, // 建筑面积
        currentFloors: 0, // 所在层数
        originalValue: 0,
        property: '', // 资产性质 1-住宅 2-营业房 3-商品房 4-自管房 5-拆迁安置房 6-其他
        source: '', // 资产来源, -1-未知 1-划拨转入 2-自购 3-自建 4-其他
        style: 1
      }

      // 向 topwindow 传入 当前选中的资产信息
      window.parent.postMessage(
        {
          code: 'Ichnography',
          data: this.currentItem
        },
        `*`
      )
    }
    // 请求数据
    this.getAssetHouseCoordinate()
  }

  mounted() {
    this.initGDMap()
  }

  // 初始化高德地图
  private initGDMap() {
    if (!AMap) return

    this.map = new AMap.Map('allmap', {
      zoom: 12,
      viewMode: '2D',
      center: [119.654566, 29.084632],
      resizeEnable: true,
      jogEnable: false,
      mapStyle: 'amap://styles/d74b0ffce8f16be41c9dc6e84eb6a64e'
    })

    // 信息框
    this.infoWindow = new AMap.InfoWindow({ offset: new AMap.Pixel(8, 0), anchor: 'middle-left' })
    this.infoWindow.on('close', () => {
      if (this.checkedMarker) {
        // 如果关闭信息框移除高亮效果
        this.checkedMarker.hide()
      }
    })
  }

  // 根据zoom层级返回公里范围
  private zoomToScale(zoom: number) {
    switch (Math.round(zoom)) {
      case 3:
        return 1000000
      case 4:
        return 500000
      case 5:
        return 200000
      case 6:
        return 100000
      case 7:
        return 50000
      case 8:
        return 30000
      case 9:
        return 20000
      case 10:
        return 10000
      case 11:
        return 5000
      case 12:
        return 2000
      case 13:
        return 1000
      case 14:
        return 500
      case 15:
        return 200
      case 16:
        return 100
      case 17:
        return 50
      case 18:
        return 25
      case 19:
        return 10
      case 20:
        return 5
    }
  }

  // @Loading('loading')
  private async getAssetHouseCoordinate() {
    let center = this.map ? this.map.getCenter() : [129, 22]
    let params: Record<string, any> = Object.assign(
      {
        companyCode: +this.currentCompanyCode,
        raidus: 10000,
        lon: center[0],
        lat: center[1]
      },
      this.assetForm
    )

    let res = await assetHouseCoordinate(params)
    if (res.success) {
      this.markerList = []
      res.data.forEach((item: any, index: any) => {
        this.markerList.push({
          lnglat: [+item.longitude, +item.latitude], //点标记位置
          name: item.address,
          id: item.id,
          style: item.assetType - 1,
          assetName: item.assetName || '',
          address: item.address || '',
          assetNo: item.assetNo || '',
          assetType: item.assetType || '',
          bizStatus: item.bizStatus || '',
          companyCode: item.companyCode || 0, // 所属公司code
          companyName: item.companyName || '', // 所属公司名称
          constructionArea: item.constructionArea || '', // 建筑面积
          currentFloors: item.currentFloors || 1, // 所在层数
          originalValue: item.originalValue || 0,
          property: item.property, // 资产性质 1-住宅 2-营业房 3-商品房 4-自管房 5-拆迁安置房 6-其他
          source: item.source || '' // 资产来源, -1-未知 1-划拨转入 2-自购 3-自建 4-其他
        })
      })
      this.realList = [...this.markerList]
      this.createMassMarks()

      // 更新 topwindow中的markerList
      window.parent.postMessage(
        {
          code: 'MarkerList',
          data: this.realList
        },
        `*`
      )
    }
  }

  private onInputFocus() {
    this.isShowList = true
  }

  // 海量点标记
  private createMassMarks() {
    // 如果未创建massMarker创建，如果有只更新数据
    if (!AMap) return
    if (!this.massMarks) {
      // 创建样式对象
      let styleList = [
        {
          url: require('../images/icon_location.png'), // 图标地址
          size: new AMap.Size(20, 20), // 图标大小
          anchor: new AMap.Pixel(10, 10), // 图标显示位置偏移量，基准点为图标左上角
          zIndex: 1
        },
        {
          url: require('../images/land1.png'), // 图标地址
          size: new AMap.Size(20, 20), // 图标大小
          anchor: new AMap.Pixel(10, 10), // 图标显示位置偏移量，基准点为图标左上角
          zIndex: 2
        }
      ]
      this.massMarks = new AMap.MassMarks(this.realList, {
        zIndex: 5, // 海量点图层叠加的顺序
        zooms: [3, 20], // 在指定地图缩放级别范围内展示海量点图层
        style: styleList, // 设置样式对象
        cursor: 'pointer',
        opacity: 1
      })

      this.massMarks.on('click', (e: any) => {
        this.onMarkerClick(e.data || {})
      })
      this.massMarks.setMap(this.map)
      // this.map.setFitView()
    } else {
      this.massMarks.setData(this.realList)
      this.massMarks.setMap(this.map)
    }
  }

  private onMarkerClick(data: any) {
    if (!AMap) return

    this.map.setCenter(data.lnglat)
    this.currentItem = data
    if (!this.checkedMarker) {
      // 创建一个 marker, 覆盖之前的marker, 实现高亮
      let icon = new AMap.Icon({
        size: new AMap.Size(30, 30), // 图标尺寸
        image: require(`../images/${+data.assetType === 1 ? 'icon_location_active' : 'icon_location_acitve'}.png`), // Icon的图像
        imageOffset: new AMap.Pixel(-2, -4), // 图像相对展示区域的偏移量，适于雪碧图等
        imageSize: new AMap.Size(30, 30) // 根据所设置的大小拉伸或压缩图片
      })
      this.checkedMarker = new AMap.Marker({
        icon: icon,
        map: this.map,
        zIndex: 1,
        clickable: true,
        position: data.lnglat, // 基点位置
        offset: new AMap.Pixel(-10, -10) // 相对于基点的偏移位置
      })
    } else {
      this.checkedMarker.show()
      this.checkedMarker.setPosition(data.lnglat)
    }
    this.infoWindow.setContent(` 
    <div
      class="marker-content" style="cursor: pointer;" >
        <div class="marker-content__title"><span>${data.assetName}</span></div>
        <div class="marker-content__info">
          <div>
            <p>资产类型：</p>
            <span>${data.assetType === 1 ? '房产' : '土地'}</span>
          </div>
          <div>
            <p>项目所属：</p>
            <span title="${data.companyName}">${data.companyName}</span>
          </div>
          <div>
            <p>资产估值：</p>
            <span>${data.originalValue}万</span>
          </div>
          <div>
            <p>资产来源：</p>
            <span title="${data.source}">${data.source}</span>
          </div>
        </div>
        <div class="marker-content__detail" onclick="openDatailDialog(${data.id})">点击查看详情</div>
      </div>
    `)
    this.infoWindow.open(this.map, data.lnglat)
  }
}
</script>

<style scoped lang="scss">
.img {
  width: 100%;
}
.asset-map {
  width: 100%;
  height: 100%;
  margin-top: -34px;
  background: #1d276b44;
  border: 2px solid #0f3079;
  border-radius: var(--normal-border-radius);
  overflow: hidden;
  &__content {
    position: relative;
    width: 100%;
    height: 100%;
    ::v-deep .amap-info-outer {
      background: none;
    }
    ::v-deep .amap-info-content {
      padding: 10px;
      border-radius: 4px;
      border: 1px solid #249df7;
      background: rgb(5 53 145 / 90%);
    }
    ::v-deep .amap-info-content {
      .marker-content {
        width: 160px;
        height: 140px;
        &__title {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 18px;
          color: #fff;
          margin-bottom: 2px;
          span {
            display: inline-block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 280px;
          }
        }
        &__info {
          font-size: 14px;
          font-weight: normal;
          color: #62f1ff;
          padding: 0px 4px;
          line-height: 20px;
          p {
            margin: 0;
          }

          .lease-unused {
            color: rgba(255, 68, 68, 1);
          }
          .lease-out {
            color: rgba(72, 185, 28, 1);
          }
          & > div {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
          }
          span {
            flex: 1;
            display: inline-block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        &__detail {
          color: #fff;
          font-size: 14px;
          text-align: center;
        }
      }
      .amap-info-close {
        color: #fff;
        font-size: 18px;
      }
    }
    ::v-deep .amap-info-sharp {
      border-right: 8px solid #0b56f1;
    }
    #allmap {
      width: 100%;
      height: 100%;
    }
  }
}
</style>