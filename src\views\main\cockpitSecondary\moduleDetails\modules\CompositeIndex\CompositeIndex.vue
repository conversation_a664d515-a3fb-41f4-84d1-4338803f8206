/* 发展指数 */

<template>
  <section class="composite-index-wrap">
    <CommonTitle title="国企发展指数"
      class="m-l-30 m-t-14" />

    <!-- 优秀、良好等切换 -->
    <div class="composite-tabs-box">
      <p v-for="item of scoreList"
        :key="item.value"
        :class="{'active':+scoreActive === +item.value}"
        @click="changeScoreActive(item.value)">{{item.label}}</p>
    </div>

    <!-- echarts 视图1 -->
    <CompositeRadar :echartsData="echartsData"
      :year="year"
      :orgCode="orgCode"
      :orgName="orgName" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import CompositeRadar from './CompositeRadar.vue'
import CommonTitle from '@/views/main/cockpitSecondary/components/CommonTitle.vue'

@Component({
  components: {
    CompositeRadar,
    CommonTitle
  }
})
export default class extends Vue {
  @Prop() private orgCode?: string // 集团code
  @Prop() private orgName?: string // 集团名称
  @Prop() private year?: string // 年份
  @Prop() private echartsData!: any // 渲染数据

  private scoreActive = 1
  private scoreList = Object.freeze([
    {
      label: '优秀',
      value: 1
    },
    {
      label: '良好',
      value: 2
    },
    {
      label: '平均',
      value: 3
    },
    {
      label: '较低',
      value: 4
    },
    {
      label: '较差',
      value: 5
    }
  ])

  // 组件初始化
  private mounted() {
    this.$emit('scoreTabsHandle', this.scoreActive)
  }

  // 评分模块切换
  private changeScoreActive(value: number) {
    this.scoreActive = value
    this.$emit('scoreTabsHandle', value)
  }
}
</script>

<style scoped lang="scss">
.composite-index-wrap {
  $color: #0195e2;
  $colorActive: #40eeff;

  $yx: #00f6ff;
  $lh: #ffea00;
  $pj: #ff7f18;
  $jd: #903ff9;
  $jc: #fb3f3f;

  position: relative;

  .composite-tabs-box {
    position: absolute;
    top: 8px;
    right: 15px;
    color: $color;
    font-size: 40px;
    display: flex;
    align-items: center;
    justify-content: center;

    // position: absolute;
    // z-index: 10;
    // right: 15px;
    // top: 50%;
    // width: 47px;
    // font-size: 40px;
    // transform: translateY(-50%);
    // writing-mode: lr;
    // text-align: center;

    & .active:nth-child(1) {
      border-bottom: 4px solid $yx;
    }
    & .active:nth-child(2) {
      border-bottom: 4px solid $lh;
    }
    & .active:nth-child(3) {
      border-bottom: 4px solid $pj;
    }
    & .active:nth-child(4) {
      border-bottom: 4px solid $jd;
    }
    & .active:nth-child(5) {
      border-bottom: 4px solid $jc;
    }
    p {
      margin: 0;
      margin: 0 20px;
      padding-bottom: 10px;
      letter-spacing: 4px;
      cursor: pointer;
      &:nth-child(1) {
        color: $yx;
      }
      &:nth-child(2) {
        color: $lh;
      }
      &:nth-child(3) {
        color: $pj;
      }
      &:nth-child(4) {
        color: $jd;
      }
      &:nth-child(5) {
        color: $jc;
      }
    }
  }
}
</style>


