/** 进度图表 */
<template>
  <div class="progress-chart"
    :id="chartId">
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import { deepMerge } from '@/utils'
import { echartConfigure } from '@/views/main/cockpitcren/baseData'
import * as echarts from 'echarts'

type EChartsOption = echarts.EChartsOption
type TitleComponentOption = echarts.TitleComponentOption
type GaugeSeriesOption = echarts.GaugeSeriesOption

interface ProgressChartSeriesData {
  // 计划
  plan: number
  // 完成
  complete: number
}

@Component
export default class extends Vue {
  @Prop() private readonly chartId!: string
  @Prop() private readonly seriesData!: ProgressChartSeriesData
  @Prop() private readonly titleOptions!: TitleComponentOption | TitleComponentOption[]
  @Prop({ default: () => ({}) }) private readonly detailOptions!: GaugeSeriesOption['detail']
  @Prop() private readonly name!: string
  @Prop() private readonly dataTitleOptions!: GaugeSeriesOption['title']
  @Prop({ default: () => ({}) }) private readonly individuationOptions!: EChartsOption // 个性化options

  // @Watch('individuationOptions', { deep: true })
  // private onIndividuationOptionsChange() {
  //   this.initEcharts()
  // }

  @Watch('seriesData', { deep: true })
  private onSeriesDataChange() {
    this.initEcharts()
  }

  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}

  get rate() {
    return +(((+this.seriesData.complete || 0) / (+this.seriesData.plan || 0)) * 100).toFixed(2)
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById(this.chartId) as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    this.initEcharts()
  }

  // 初始化数据
  private initEcharts() {
    let textSize = 22
    let tooltipData: any = Object.assign(
      {
        valueFormatter: (value: number) => `${value}%`
      },
      echartConfigure.tooltip
    )

    this.option = {
      grid: {
        left: '0%',
        right: '0%',
        top: '0%',
        bottom: '0%',
        containLabel: true
      },
      tooltip: tooltipData,
      title: this.titleOptions,
      series: [
        {
          type: 'gauge',
          startAngle: 180,
          endAngle: 0,
          min: 0,
          radius: '120%',
          max: 100,
          center: ['50%', '80%'],
          splitNumber: 5,
          itemStyle: {
            color: '#58D9F9',
            shadowColor: 'rgba(0,138,255,0.45)',
            shadowBlur: 10,
            shadowOffsetX: 2,
            shadowOffsetY: 2
          },
          progress: {
            show: true,
            roundCap: true,
            width: 36,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: this.rate < 30 ? '#7CB7FF44' : this.rate < 60 ? '#7CB7FF44' : '#7CB7FF44' // 0% 处的颜色
                  },
                  {
                    offset: 0.7,
                    color: this.rate < 30 ? '#4680FF' : this.rate < 60 ? '#4680FF' : '#4680FF' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          },
          pointer: {
            show: false
          },
          axisLine: {
            roundCap: true,
            lineStyle: {
              width: 36,
              color: [[1, '#0E233A']]
            }
          },
          axisTick: {
            splitNumber: 2,
            lineStyle: {
              width: 2,
              color: '#0E233A'
            }
          },
          splitLine: {
            show: false
          },
          axisLabel: {
            distance: 30,
            color: '#5db0ea',
            fontSize: 24
          },
          title: {
            show: false
          },
          detail: Object.assign(
            {
              width: '100%',
              lineHeight: 40,
              height: 40,
              color: 'RGBA(217, 219, 220, 1)',
              borderRadius: 8,
              offsetCenter: [0, '-10%'],
              valueAnimation: true,
              fontSize: 50,
              fontWeight: 'bold',
              formatter: () => {
                return `{a|${this.seriesData.complete}}`
              },
              rich: {
                a: {
                  fontSize: 68,
                  color: '#fff',
                  fontWeight: 'bold',
                  opacity: 0.8
                }
              }
            },
            this.detailOptions
          ),
          data: [
            {
              value: this.rate,
              name: this.name,
              title: Object.assign(
                {
                  offsetCenter: ['0%', '-25%'],
                  show: true,
                  color: '#5db0ea',
                  fontSize: 40,
                  fontWeight: 'bold'
                },
                this.dataTitleOptions
              )
            }
          ]
        }
      ]
    }

    // 合并传入 options
    let resOptions = deepMerge(this.option, this.individuationOptions)

    this.myChart && this.myChart.setOption && this.myChart.setOption(resOptions ? resOptions : this.option)
  }
}
</script>

<style scoped lang="scss">
.progress-chart {
  width: 100%;
  height: 100%;
}
</style>