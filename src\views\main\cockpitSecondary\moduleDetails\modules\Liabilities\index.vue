/* 债务监管 */

<template>
  <section class="liabilites-content-wrap">
    <el-row :span="24"
      :gutter="24"
      class="hegith100">
      <el-col :span="7"
        class="module-box">
        <div class="mode-content pis-relative mode-bg1 cockipt-approach-left-top"
          style="height:710px;">
          <!-- 数据改变提示层 -->
          <img v-if="visibleFlicker"
            class="cockipt-bg-flicker"
            src="@/views/main/cockpitcren/images/panel_bg1_cg.png" />

          <DebtTypes title="负债概况"
            :orgCode="orgCode"
            :year="year"
            :moon="moon" />
        </div>
        <div class="mode-content pis-relative mode-bg1 cockipt-approach-left-bottom"
          style="height:710px;">
          <!-- 数据改变提示层 -->
          <img v-if="visibleFlicker"
            class="cockipt-bg-flicker"
            src="@/views/main/cockpitcren/images/panel_bg1_cg.png" />

          <DebtFlows title="债务分类"
            :orgCode="orgCode"
            :year="year"
            :moon="moon" />
        </div>
      </el-col>
      <el-col :span="10"
        class="module-box">
        <div class="mode-content mode-bg2 mode-custom-middle cockipt-approach-middel-top">
          <!-- 各集团tabs  -->
          <CommonTabs :orgCode="orgCode"
            module="Liabilities"
            class="cockipt-approach-middel-top m-b-20"
            @commonTabsHandle="commonTabsHandle" />

          <div class="mode-content pis-relative mode-bg2"
            style="height:730px;">
            <!-- 数据改变提示层 -->
            <img v-if="visibleFlicker"
              class="cockipt-bg-flicker"
              src="@/views/main/cockpitcren/images/panel_bg2_cg.png" />

            <AssetsLiabilities title="负债趋势"
              :orgCode="orgCode"
              :year="year"
              :moon="moon" />
          </div>
        </div>
        <div class="mode-content pis-relative mode-bg2 cockipt-approach-middel-bottom"
          style="height:580px;">
          <DebtRatioRanking title="负债排名"
            :year="year"
            :moon="moon"
            :orgCode="orgCode" />
        </div>
      </el-col>
      <el-col :span="7"
        class="module-box">
        <div class="mode-content pis-relative mode-bg1 cockipt-approach-right-top"
          style="height:710px;">
          <!-- 数据改变提示层 -->
          <img v-if="visibleFlicker"
            class="cockipt-bg-flicker"
            src="@/views/main/cockpitcren/images/panel_bg1_cg.png" />

          <LiabilityWarning title="负债预警"
            :year="year"
            :moon="moon"
            :orgCode="orgCode" />
        </div>
        <div class="mode-content pis-relative mode-bg1 cockipt-approach-right-bottom"
          style="height:710px;">
          <!-- 数据改变提示层 -->
          <img v-if="visibleFlicker"
            class="cockipt-bg-flicker"
            src="@/views/main/cockpitcren/images/panel_bg1_cg.png" />

          <DebtDebt title="债务到期"
            :year="year"
            :moon="moon"
            :orgCode="orgCode" />
        </div>
      </el-col>
    </el-row>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import CommonTabs from '@/views/main/cockpitSecondary/components/CommonActiveTabs.vue'
import AssetsLiabilities from './AssetsLiabilities.vue'
import DebtRatioRanking from './DebtRatioRanking.vue'
import LiabilityWarning from './LiabilityWarning.vue'
import DebtTypes from './DebtTypes.vue'
import DebtFlows from './DebtFlows.vue'
import DebtDebt from './DebtDebt.vue'

@Component({
  components: {
    CommonTabs,
    DebtTypes,
    DebtFlows,
    DebtDebt,
    LiabilityWarning,
    DebtRatioRanking,
    AssetsLiabilities
  }
})
export default class extends Vue {
  private year = ''
  private moon = ''
  private orgCode = ''
  private visibleFlicker = false

  // 数据初始化
  private created() {
    this.year = String(new Date().getFullYear())

    // 如果链接上带有集团code，需要让tabs选中该集团
    setTimeout(() => {
      let { query} = this.$route
      if(query && query.orgCode) {
        this.orgCode = (query.orgCode as string) || ''
      }
    }, 0);
    // end
  }

  // 组件初始化
  private mounted() {
    // 年份改变
    this.$bus.$on('BusYearTimeChange', (data: string) => {
      this.year = data
      this.flickerHandle()
    })

    // 月份改变
    this.$bus.$on('BusMoonTimeChange', (data: string) => {
      this.moon = data
      this.flickerHandle()
    })
  }

  // 各集团模块切换
  private commonTabsHandle(data: string) {
    this.orgCode = data
    this.flickerHandle()
  }

  // 触发背景闪烁效果
  private flickerHandle() {
    this.visibleFlicker = true
    setTimeout(() => {
      this.visibleFlicker = false
    }, 3000)
  }
}
</script>

<style scoped lang="scss">
.liabilites-content-wrap {
  position: relative;
  height: 100%;

  .hegith100 {
    height: 100%;
  }
  .bg-none {
    background: none !important;
  }

  .module-box {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    box-sizing: border-box;
    padding: 0 0 10px 0;
    @extend .hegith100;

    .mode-content {
      font-size: 30px;
      padding: 50px 70px;
    }
    .mode-bg1 {
      background: url('../../../images/panel_bg1.png') no-repeat left top;
      background-size: 100% 100%;
    }
    .mode-bg2 {
      background: url('../../../images/panel_bg2.png') no-repeat left top;
      background-size: 100% 100%;
    }
    .mode-bg3 {
      background: url('../../../images/panel_bg3.png') no-repeat left top;
      background-size: 100% 100%;
    }
    .mode-custom-middle {
      height: 780px;
      padding: 20px 0;
      background: none;
    }
  }
}
</style>




