/* 驾驶舱 api */

import request from './request'
import { RemoteResponse } from '@/types'

// 房产地址
export const assetHouseCoordinate = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/ast/info/map', params)
}

// 驾驶舱大屏-财务指标
export const capitalProfile = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/home/<USER>/capitalProfile', params)
}

// 驾驶舱大屏-资产统计
export const astHomeSummary = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/ast/home/<USER>', params)
}

// 驾驶舱大屏-历年趋势统计
export const astHomeHistory = (params = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/ast/home/<USER>', params)
}

// 驾驶舱大屏-资产构成情况
export const capitalComponent = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/home/<USER>/capitalComponent', params)
}

// 驾驶舱大屏-集团发展指数
export const comprehensiveIndex = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/home/<USER>/comprehensiveIndex', params)
}

// 驾驶舱大屏-集团人员
export const stuffProfile = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/home/<USER>/stuffProfile', params)
}

// 驾驶舱大屏-负债情况
export const debtProfile = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/home/<USER>/debtProfile', params)
}

// 驾驶舱大屏-三重一大：重要人事任免事项
export const majorLeader = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/home/<USER>/majorLeader', params)
}

// 驾驶舱大屏-三重一大：重大决策事项
export const majorMatter = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/home/<USER>/majorMatter', params)
}

// 驾驶舱大屏-三重一大：重大项目安排事项
export const majorProjectCount = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/home/<USER>/majorProjectCount', params)
}

// 驾驶舱大屏-三重一大：大额资金运作事项
export const majorProjectSum = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/home/<USER>/majorProjectSum', params)
}

// 驾驶舱大屏-三重一大：项目投资情况
export const projectInvest = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/home/<USER>/projectInvestNew', params)
}

// 驾驶舱大屏-组织人事
export const perHomeSummary = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/per/home/<USER>', params)
}

// 驾驶舱大屏-企业数据汇总
export const entHomeSummary = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/ent/home/<USER>', params)
}

// 驾驶舱大屏-企业运营数据汇总
export const entextHomeSummary = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/ent-ext/home/<USER>', params)
}

// 获取门户首页tabs数据
export const debtWarningTabs = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/alert/event/getMsgInfo', params)
}

// 预警已读和未读
export const debtWarningErase = (params: { id: number }): Promise<RemoteResponse> => {
  return request('/fht-monitor/alert/event/erase', params)
}

// 预警详情接口
export const debtWarningDetail = (params: { id: number }): Promise<RemoteResponse> => {
  return request('/fht-monitor/alert/event/detail', params)
}

/*------------------驾驶舱二级页面：不动产管理----------------------*/
// 汇总
export const immovablesOverview = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/ast/overview', params)
}

export const immovablesCollectionRate = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/ast/collectionRate', params)
}

// 资产使用状态
export const assetStatusRatio = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/ast/countGroupUseStatus', params)
}

// 出租率情况
export const assetOccupancyRate = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/ast/countOccupancyRate', params)
}

// 资产分类统计
export const assetClassify = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/ast/countGroupManageType', params)
}

// 年度租金收入
export const annualRevenueGrowth = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/ast/countRevenueGrowth', params)
}

// 运营情况
export const operationStatus = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/screen/immovables/operationStatus', params)
}

// 合同情况
export const contractBill = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/ast/countContract', params)
}

// 逾期情况
export const overdueStatus = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/screen/immovables/overdueStatus', params)
}

/*----------------驾驶舱二级页面：发展指数-------------------*/

// 发展指数
export const performanceEvaluation = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/devt/index', params)
}

// 发展指数
export const performanceFiveYearTrends = (params: any = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/devt/fiveYearTrends', params)
}

// 关联的财务指标明细
export const financialIndicator = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/devt/detail', params)
}
/*----------------驾驶舱二级页面：智慧预警-------------------*/

/**
 *
 * @param params
 *  预警业务数量top10
 */
export const getPperationalWarningsTop = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/alert/event/getEventClass', params)
}
/**
 * @param params
 *  集团年度预警数量
 */
export const getEventStatistics = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/alert/event/getEventStatistics', params)
}
/**
 * @param params
 *  预警事件列表配置（预警分类）
 */
export const getDictWarningeventlist = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/alert/pointtype/page', params)
}

// 驾驶舱大屏-三重一大：智慧预警
export const realtimeWarning = (params: any): Promise<RemoteResponse> => {
  let obj = Object.assign(params)
  return request('/fht-monitor/ds/ewm/eventPage', obj)
}

/*----------------驾驶舱二级页面：债务监管-------------------*/
// 负债概括
export const simpleSummary = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/screen/liabilities/simpleSummary', params)
}

// 历年资产负债率
export const liabilitiesHistory = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/screen/liabilities/history', params)
}

// 历年资产负债率排行
export const liabilitiesRateRank = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/screen/liabilities/rateRank', params)
}

// 各维度汇总
export const liabilitiesSummary = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/screen/liabilities/summary', params)
}

// 负债预警
export const liabilitiesWarn = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/screen/liabilities/warn', params)
}

// 各维度汇总：负债种类+债务到期
export const liabilitiesFinSummary = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/screen/liabilities/fin/summary', params)
}

// 各维度汇总：货币资金余额
export const liabilitiesFinSurplus = (params: any = {}): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/screen/liabilities/fin/surplus/summary', params)
}

/*----------------驾驶舱二级页面：财务监管-------------------*/
// 资产统计
export const assetsFinancialAssets = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/screen/assets/assets', params)
}

// 历年趋势统计
export const assetsFinancialHistory = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/screen/assets/history', params)
}

// 资产概况
export const assetsFinancialSummary = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/screen/assets/summary', params)
}

/*----------------驾驶舱二级页面：智慧预警-------------------*/
// 今日概况
export const debtInfaceTodayOverview = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/ewm/todayOverview', params)
}

// 本月处置
export const debtInfaceCurrentMonthDeal = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/ewm/currentMonthDeal', params)
}

// 预警历史趋势
export const debtInfaceTrendsOverYear = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/ewm/trendsOverYear', params)
}

// 预警总数
export const debtInfaceAlertTotal = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/ewm/alertTotal', params)
}

// 预警各集团处置总数
export const debtInfaceAlertTotalByCompany = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/ewm/alertTotalByCompany', params)
}

// 预警分类统计
export const debtInfaceAlertClassification = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/ewm/metrics/summary', params)
}

// 主要指标概览查询
export const mainIndicatorsSummary = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/fin/gzw/main-indicators/summary', params)
}

/*----------------财务监管 end-------------------*/

export interface ProjectProgressParams {
  orgCode: string
  companyCode: number // 公司代码，0-取所有集团数据
  month?: string // 月
  projectProperty?: string // 项目标签 项目标签，QY-企业 ZF-政府 DJ-代建 不传取所有
  year: string // 年
}

// 返回数据项
export interface ProjectProgressDataItem {
  indicatorCount: string // 数量
  indicatorName: string // 名称
  indicatorRate: string // 同比
  indicatorValue: string // 数值
  month: string // 月
  year: string // 年
}

// 项目投资概况
export const getInvestmentProfile = (params: ProjectProgressParams): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/inv/profile', params)
}

// 历年总投资趋势
export const getInvestmentTrends = (params: ProjectProgressParams): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/inv/totalInvestmentTrends', params)
}

// 项目投资概况
export const getNewProject = (params: ProjectProgressParams): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/inv/new', params)
}

// 计划完成情况
export const getPlanCompleted = (params: ProjectProgressParams): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/inv/planCompleted', params)
}

// 年度计划投资情况
export const getPlanCompletedYear = (params: ProjectProgressParams): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/inv/planInvestmentTrends', params)
}

// 项目进度完成情况
export const getProjectProgress = (params: ProjectProgressParams): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/inv/progress', params)
}

// 建设时序占比
export const getProjectTimingRatio = (params: ProjectProgressParams): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/inv/typeRate', params)
}

// 项目分类占比
export const getProjectTypeRate = (params: ProjectProgressParams): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/inv/categoryRate', params)
}

// 企业经营数据阶段趋势统计
export const getEntExtHistory = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/ds/ent-ext/home/<USER>', params)
}