<template>
  <section class="house-land-info-wrap">
    <h4 class="title">土地统计</h4>
    <div class="content">
      <div class="mode">
        <h6>土地总数</h6>
        <strong>
          <span>{{ getDetailData('astTotal', 'value1') }}</span>
          <i>处</i>
        </strong>
        <!-- <p>土地总面积：{{ getDetailData('astTotal', 'value2') }} m²</p> -->
      </div>

      <div class="mode">
        <h6>土地总面积</h6>
        <strong>
          <span>{{ getDetailData('astTotal', 'value2') }}</span>
          <i>m²</i>
        </strong>
        <!-- <p>出租房间数：{{getDetailData('occupancyRateTotal', 'value2')}} 个</p> -->
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

@Component({})
export default class extends Vue {
  @Prop() private detailData!: any

  get getDetailData() {
    return (type: string, value: string) => {
      if (this.detailData[type] && this.detailData[type][value]) {
        return this.detailData[type][value]
      } else {
        return '-'
      }
    }
  }
}
</script>
<style scoped>

</style>
