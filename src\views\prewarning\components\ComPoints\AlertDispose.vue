<template>
  <section v-loading="loading"
    class="alert-dispose-wrap">
    <!-- 预警详情 -->
    <div class="detail-content">
      <el-descriptions border
        :column="3">
        <el-descriptions-item label="预警编号">{{ detailData.eventNo }}</el-descriptions-item>
        <el-descriptions-item label="预警名称">{{ detailData.ruleName }}</el-descriptions-item>
        <el-descriptions-item label="预警级别">{{ detailData.levelDesc }}</el-descriptions-item>
        <el-descriptions-item label="预警分类">{{ detailData.metricsDesc }}</el-descriptions-item>
        <el-descriptions-item label="预警时间">{{ detailData.createTime }}</el-descriptions-item>
        <el-descriptions-item label="所属集团">{{ detailData.orgName }}</el-descriptions-item>
        <el-descriptions-item label="规则状态"
          :span="3">{{ detailData.ruleStatusDesc }}</el-descriptions-item>
        <el-descriptions-item label="预警说明"
          :span="3">{{ detailData.ruleDesc }}</el-descriptions-item>
        <el-descriptions-item label="政策说明"
          :span="3">{{ detailData.policyDesc }}</el-descriptions-item>
        <el-descriptions-item label="事件内容"
          :span="3">{{ detailData.results || params.results }}</el-descriptions-item>
        <el-descriptions-item label="反馈人">{{ detailData.operateName }}</el-descriptions-item>
        <el-descriptions-item label="联系方式">{{ detailData.operatePhone }}</el-descriptions-item>
        <el-descriptions-item label="附件">
          <el-button v-if="Array.isArray(detailData.feedBackFiles)"
            type="text"
            size="medium"
            style="padding: 0;"
            @click="showUploader = true">查看附件({{+detailData.feedBackFiles.length}})</el-button>
        </el-descriptions-item>
        <el-descriptions-item label="采取的措施" :span="3">
          {{ detailData.measures }}
        </el-descriptions-item>
        <el-descriptions-item label="核实意见">
          <!-- 处置表单 -->
          <el-form :model="ruleForm"
            :rules="rules"
            :hide-required-asterisk="true"
            label-position="left"
            ref="ruleForm"
            label-width="90px"
            class="form-content">
            <el-form-item label=""
              label-width="0"
              prop="dealContent">
              <el-input v-model.trim="ruleForm.dealContent"
                :rows="4"
                :disabled="disabled"
                clearable
                type="textarea"
                placeholder="请输入..."
                maxlength="200"
                show-word-limit
                class="input-max" />
            </el-form-item>
          </el-form>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 底部按钮 -->
    <div class="footer-box">
      <template v-if="type==='dispose'">
        <el-button :loading="loading"
          type="primary"
          @click="validateForm">确 认</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </template>
    </div>

    <!-- 查看附件 -->
    <Uploader v-if="Array.isArray(detailData.feedBackFiles)"
      v-model="detailData.feedBackFiles"
      title="查看附件"
      :visible.sync="showUploader"
      :uploadable="false"
      :is-private="false"
      :show-cover="false" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { ElForm } from 'element-ui/types/form'
import { debtWarningDetail, disposeDealResp } from '@/api/prewarning'
import Uploader from '@/components/Uploader/index.vue'

@Component({
  components: {
    Uploader
  }
})
export default class extends Vue {
  @Prop() private params!: any // 预警详情数据
  @Prop({
    validator: (value: string) => {
      return ['dispose', 'see'].indexOf(value) !== -1
    }
  })
  readonly type!: string // 组件类型(处置、查看)

  private disabled = false
  private loading = false
  private showUploader = false
  private detailData = {}
  private ruleForm = {
    dealContent: ''
  }
  private rules = {
    dealContent: [{ required: true, message: '请输入处置意见', trigger: 'blur' }]
  }

  // 组件初始化
  private mounted() {
    switch (this.type) {
      case 'dispose':
        this.disabled = false
        this.initData()
        break
      case 'see':
        this.disabled = true
        this.initData()
        break
    }
  }

  // 获取详情数据
  @Loading('loading')
  private async initData() {
    let { data } = await debtWarningDetail({
      id: this.params.id
    })

    this.detailData = data || {}
    this.ruleForm.dealContent = data.dealContent
  }

  // 校验必填数据
  private validateForm() {
    ;(this.$refs.ruleForm as ElForm).validate((valid: boolean) => {
      if (valid) {
        this.submitForm()
      } else {
        this.$message.warning('请输入必填信息')
      }
    })
  }

  // 提交数据
  @Loading('loading')
  private async submitForm() {
    let objData = Object.assign(
      {
        id: this.params.id
      },
      this.ruleForm
    )

    let res = await disposeDealResp(objData)
    this.$message.success(res.msg || '提交成功')
    this.updataHandle()
    this.handleClose()
  }

  // 更新父组件
  private updataHandle() {
    this.$emit('updataHandle')
  }

  // 关闭弹窗
  private handleClose() {
    this.$emit('handleClose')
  }
}
</script>

<style scoped lang="scss">
.alert-dispose-wrap {
  position: relative;
  .detail-content {
    margin-bottom: 10px;
    ::v-deep .el-descriptions {
      .el-descriptions__table {
        font-size: 14px;
        padding-bottom: 20px;
      }
      .el-descriptions-item__label {
        width: 100px;
      }
    }

    ::v-deep .el-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }

  .footer-box {
    text-align: right;
  }
}
</style>