/** 通用模块 wrapper */

<template>
  <section ref="commonModuleWrapper"
    :class="[
      'common-module-wrapper',
      `common-module-wrapper--bg${hasBorder ? borderGbMode : ''}`,
      hasBorder && 'common-module-wrapper--has-border',
      isFullScreen && componentsName === 'AssetMapIframe' && 'common-module-wrapper--fullscreen',
      isSwitch && 'common-module-wrapper--switch',
      isContentFull && 'common-module-wrapper--content-full',
      wrapperClass
    ]"
    :style="{
      padding: hasBorder ? '40px 50px 40px 50px' : `10px 0px `,
      overflow: title || Array.isArray(componentsName) ? 'hidden' : 'initial'
    }">

    <!-- 如果是左右切换模式, 切换模式 componentsName 一定为 数组 -->
    <template v-if="isSwitch">
      <!-- 向左切换按钮 -->
      <!-- v-if="hasLeftSection" -->
      <div @click="onModuleSwitch(-1)"
        class="common-module-wrapper__left-btn">
        <img :style="{
          display: hasLeftSection ? 'block' : 'none',
            opacity: hasLeftSection ? 1 : 0,
            width:  hasLeftSection ? '100%' : 0
          }"
          src="../images/thow2.png"
          alt="">
      </div>

      <div :style="{
          height: `${borderGbMode * 10 + 70 + (height || 320)}px`,
          padding:`${verticelPadding}px ${horizontalPadding}px ${verticelPadding * 1.2}px ${horizontalPadding}px`
        }"
        class="switch-wrapper">

        <div v-for="(item, index) in componentsName"
          :key="`${item.name}_${index}`"
          :class="[
          'common-module-wrapper__section',
          currentSwitchIndex === index && 'common-module-wrapper__section--current',
          currentSwitchIndex < index && 'common-module-wrapper__section--right',
          currentSwitchIndex > index && 'common-module-wrapper__section--left',
        ]">
          <div v-if="item.title"
            class="common-module-wrapper__title"
            :style="{
              width: `${getTitleLength(item.title)}px`
            }"
            @jumpTo="jumpTo(item.name)">
            <div :style="{
                width: `${getTitleLength(item.title)}px`,
                position: 'relative'
              }"
              @jumpTo="jumpTo(item.name)">
              <img src="../images/mode_title.png" />
              <span>{{ item.title }}</span>
            </div>
            <img v-if="detailsPath"
              class="detail-btn detail-tab-btn"
              width="80px"
              height="30px"
              src="../images/thow_more.png"
              @click="detailsHandle(detailsPath, bizAppCode)" />
            <div v-if="item.tabsPosition === 'title'"
              :style="{ width: `calc(100% - ${getTitleLength(item.title) + 100}px)`}"
              class="tabs-wrapper tabs-wrapper--title">
              <Tabs v-if="item.tabType"
                @tabsHandel="tabsHandler"
                :type="tabType" />
            </div>
          </div>

          <div v-if="item.tabsPosition === 'content'"
            :style="{padding: `0 ${item.tabsPadding || 0}%`}"
            class="tabs-wrapper">
            <Tabs v-if="item.tabType"
              @tabsHandel="tabsHandler"
              :type="item.tabType" />

          </div>
          <div v-loading="loading"
            element-loading-background="#07277500"
            :style="{
            height: `${ height ||  320}px`
          }"
            class="common-module-wrapper__content">

            <component v-if="!loading"
              :is='item.name'
              :activeTabs="activeTabs"
              :currentTabCode="currentTabCode"
              :currentYear="currentYear"
              @itemClick="onItemClick"
              v-bind="item.attrs"></component>
          </div>
        </div>
      </div>

      <!-- 向右切换按钮 -->
      <!-- v-if="hasRightSection" -->
      <div @click="onModuleSwitch(1)"
        class="common-module-wrapper__right-btn">
        <img :style="{
          display: hasRightSection ? 'block' : 'none',
          opacity: hasRightSection ? 1 : 0,
          width:  hasRightSection ? '100%' : 0
        }"
          src="../images/thow1.png"
          alt="">
      </div>
    </template>

    <!-- 正常模式 -->
    <template v-else>
      <!-- 判断 componentsName 是否为数组  -->
      <div v-if="!Array.isArray(componentsName)"
        class='common-module-wrapper__section'>
        <div v-if="title"
          class="common-module-wrapper__title">
          <div :style="{
              width: `${getTitleLength(title)}px`,
              position: 'relative',
            }"
            @jumpTo="jumpTo(name)">
            <img src="../images/mode_title.png" />
            <span>{{ title }}</span>
          </div>
          <img v-if="detailsPath"
            class="detail-btn"
            width="80px"
            height="30px"
            src="../images/thow_more.png"
            @click="detailsHandle(detailsPath, bizAppCode)" />
          <div v-if="tabsPosition === 'title'"
            :style="{width: `calc(100% - ${getTitleLength(title) + 100}px)`}"
            class="tabs-wrapper tabs-wrapper--title">
            <Tabs v-if="tabType && !$slots['tabs']"
              @tabsHandel="tabsHandler"
              :type="tabType" />

            <slot v-if="$slots['tabs']"
              name="tabs" />
          </div>
        </div>

        <div v-if="tabsPosition === 'content'"
          :style="{padding: `0 ${tabsPadding}%`}"
          class="tabs-wrapper">
          <Tabs v-if="tabType"
            @tabsHandel="tabsHandler"
            :type="tabType" />
        </div>
        <div v-loading="loading"
          element-loading-background="#07277500"
          :style="{
            height: `${height ? height : '320'}px`
          }"
          class="common-module-wrapper__content">
          <component v-if="!loading"
            :is='componentsName'
            :activeTabs="activeTabs"
            :currentTabCode="currentTabCode"
            :currentYear="currentYear"
            v-bind="$attrs"
            @itemClick="onItemClick" />
        </div>
      </div>

      <!-- 如果是数组 -->
      <div v-else
        class='common-module-wrapper__section'>
        <div v-for="(item, index) in componentsName"
          :key="`${item.name}_${index}`"
          :style="{margin: `${item.margin && item.margin}`}">
          <div v-if="item.title"
            class="common-module-wrapper__title">
            <div class="module-title"
              :style="{
                width: `${getTitleLength(item.title)}px`,
                position: 'relative'
              }"
              @jumpTo="jumpTo(item.name)">
              <img src="../images/mode_title.png" />
              <span>{{ item.title }}</span>
            </div>

            <img v-if="item.detailsPath"
              class="detail-btn"
              width="80px"
              height="30px"
              src="../images/thow_more.png"
              @click="detailsHandle(item.detailsPath, item.bizAppCode)" />

            <div v-if="item.tabsPosition === 'title'"
              :style="{ width: `calc(100% - ${getTitleLength(item.title) + 100}px)`}"
              class="tabs-wrapper tabs-wrapper--title">
              <Tabs v-if="item.tabType"
                :type="item.tabType"
                @tabsHandel="tabsHandler" />
            </div>
          </div>

          <div v-if="item.tabsPosition === 'content'"
            :style="{padding: `0 ${item.tabsPadding || 0}%`}"
            class="tabs-wrapper">
            <Tabs v-if="item.tabType"
              @tabsHandel="tabsHandler"
              :type="tabType" />
          </div>

          <div v-loading="loading"
            element-loading-background="#07277500"
            :style="{
              height: `${ item.height ||  320}px`
            }"
            class="common-module-wrapper__content">

            <component v-if="!loading"
              v-bind="item.attrs"
              :is='item.name'
              :activeTabs="activeTabs"
              :currentTabCode="currentTabCode"
              :currentYear="currentYear"
              @itemClick="onItemClick" />
          </div>
        </div>
      </div>
    </template>
  </section>
</template>

<script lang="ts">
import { Component, Emit, Prop, Vue } from 'vue-property-decorator'
import Tabs from '@/views/main/cockpitcren/components/Public/Tabs.vue'

export interface ComponentItem {
  name: ComponentRegister | ComponentItem[]
  borderGbMode?: 1 | 2 | 3 | 4 | 5 | 6 | 7
  tabsPosition?: 'title' | 'content'
  wrapperClass?: string
  title?: string
  tabType?: string
  height?: number
  tabsPadding?: number
  isSwitch?: boolean
  hasBorder?: boolean
  hasTitle?: boolean
  hasTabs?: boolean
  margin?: string
  bizAppCode?: string
  detailsPath?: string
  attrs?: Record<string, any>
}

const componentsList = {
  GeneralAssessment: () => import('@/views/main/cockpitSecondary/companyDetails/components/GeneralAssessment.vue'),
  AssetComposition: () => import('@/views/main/cockpitSecondary/companyDetails/components/AssetComposition.vue'),
  ProjectImplementation: () => import('@/views/main/cockpitSecondary/companyDetails/components/ProjectImplementation.vue'),
  ProjectProfitabilityRanking: () => import('@/views/main/cockpitSecondary/companyDetails/components/ProjectProfitabilityRanking.vue'),
  TitlesOfMembers: () => import('@/views/main/cockpitSecondary/companyDetails/components/TitlesOfMembers.vue'),
  EducationalBackgroundDistribution: () =>
    import('@/views/main/cockpitSecondary/companyDetails/components/EducationalBackgroundDistribution.vue'),
  IndexOverview: () => import('@/views/main/cockpitSecondary/companyDetails/components/IndexOverview.vue'),
  SanZhongYiDa: () => import('@/views/main/cockpitSecondary/companyDetails/components/SanZhongYiDa.vue'),
  ProjectImplementationOverview: () => import('@/views/main/cockpitSecondary/companyDetails/components/ProjectImplementationOverview.vue'),
  ProjectAmountDistribution: () => import('@/views/main/cockpitSecondary/companyDetails/components/ProjectAmountDistribution.vue'),
  MembershipStatus: () => import('@/views/main/cockpitSecondary/companyDetails/components/MembershipStatus.vue'),
  ProfitOnAssets: () => import('@/views/main/cockpitSecondary/companyDetails/components/ProfitOnAssets.vue'),
  UtilizationOfAssets: () => import('@/views/main/cockpitSecondary/companyDetails/components/UtilizationOfAssets.vue'),
  ScaleOfCapitalUtilization: () => import('@/views/main/cockpitSecondary/companyDetails/components/ScaleOfCapitalUtilization.vue'),
  ProjectClassification: () => import('@/views/main/cockpitSecondary/companyDetails/components/ProjectClassification.vue'),
  AgeOfMembers: () => import('@/views/main/cockpitSecondary/companyDetails/components/AgeOfMembers.vue'),
  MajorDistribution: () => import('@/views/main/cockpitSecondary/companyDetails/components/MajorDistribution.vue'),
  DepositAndLoanScale: () => import('@/views/main/cockpitSecondary/companyDetails/components/DepositAndLoanScale.vue'),
  ProjectImplementationBar: () => import('@/views/main/cockpitSecondary/companyDetails/components/ProjectImplementationBar.vue'),
  ProjectClassificationPie: () => import('@/views/main/cockpitSecondary/companyDetails/components/ProjectClassificationPie.vue'),
  GeneralInformation: () => import('@/views/main/cockpitSecondary/companyDetails/components/GeneralInformation.vue'),
  TabsList: () => import('@/views/main/cockpitSecondary/components/TabsList.vue'),
  TabsList1: () => import('@/views/main/cockpitSecondary/components/TabsList1.vue'),
  AssetMapIframe: () => import('@/views/main/cockpitSecondary/charts/AssetMapWrapper.vue'),
  ProportionOfRing: () => import('@/views/main/cockpitSecondary/charts/ProportionOfRing.vue'),
  CommonLineChart: () => import('@/views/main/cockpitSecondary/charts/LineCharts.vue'),
  RevenueGrowth: () => import('@/views/main/cockpitSecondary/moduleDetails/modules/RealEstate/RevenueGrowth.vue'),
  TableList: () => import('@/views/main/cockpitSecondary/components/TableList.vue'),
  IncrementRanking: () => import('@/views/main/cockpitSecondary/moduleDetails/components/IncrementRanking.vue'),
  OperationSituation: () => import('@/views/main/cockpitSecondary/moduleDetails/modules/RealEstate/OperationSituation.vue'),
  ContractSituation: () => import('@/views/main/cockpitSecondary/moduleDetails/modules/RealEstate/ContractSituation.vue'),
  OverdueSituation: () => import('@/views/main/cockpitSecondary/moduleDetails/modules/RealEstate/OverdueSituation.vue'),
  Summarize: () => import('@/views/main/cockpitSecondary/moduleDetails/components/Summarize.vue'),
  BarChart: () => import('@/views/main/cockpitSecondary/charts/BarCharts.vue'),
  SanZhongYiDaBarGounp: () => import('@/views/main/cockpitSecondary/moduleDetails/modules/SanZhongYiDa/BarChartGounp.vue'),
  AssetOverview: () => import('@/views/main/cockpitSecondary/moduleDetails/modules/AssetComposition/AssetOverview.vue'),
  ConstructionInProcess: () => import('@/views/main/cockpitSecondary/moduleDetails/modules/AssetComposition/ConstructionInProcess.vue'),
  FixedAssets: () => import('@/views/main/cockpitSecondary/moduleDetails/modules/AssetComposition/FixedAssets.vue'),
  ProjectInvestment: () => import('@/views/main/cockpitSecondary/moduleDetails/modules/ProjectProgress/ProjectInvestment.vue'),
  InvestmentTrend: () => import('@/views/main/cockpitSecondary/moduleDetails/modules/ProjectProgress/InvestmentTrend.vue'),
  NewProject: () => import('@/views/main/cockpitSecondary/moduleDetails/modules/ProjectProgress/NewProject.vue'),
  PlanCompletion: () => import('@/views/main/cockpitSecondary/moduleDetails/modules/ProjectProgress/PlanCompletion.vue'),
  PlannedInvestment: () => import('@/views/main/cockpitSecondary/moduleDetails/modules/ProjectProgress/PlannedInvestment.vue'),
  ProjectProgress: () => import('@/views/main/cockpitSecondary/moduleDetails/modules/ProjectProgress/ProjectProgress.vue'),
  ConstructionSequence: () => import('@/views/main/cockpitSecondary/moduleDetails/modules/ProjectProgress/ConstructionSequence.vue'),
  NewProjectClassification: () => import('@/views/main/cockpitSecondary/moduleDetails/modules/ProjectProgress/NewProjectClassification.vue')
}

export type ComponentRegister = keyof typeof componentsList

@Component({
  components: Object.assign({ Tabs }, componentsList)
})
export default class extends Vue {
  @Prop({ default: false }) private loading!: boolean
  @Prop({ default: 1 }) private borderGbMode!: 1 | 2 | 3 | 4 | 5 | 6 | 7
  @Prop() private wrapperClass!: string
  @Prop({ default: true }) private hasBorder!: boolean
  @Prop() private componentsName!: ComponentRegister | ComponentItem[]
  @Prop({ default: 'content' }) private tabsPosition!: 'title' | 'content' // tabs 的位置， title表示在标题的右侧， content 表示在内容顶部
  @Prop({ default: false }) private isSwitch!: boolean // 是否为切换模式
  @Prop({ default: false }) private isContentFull!: boolean // 内容是否铺满整个模块，用于一些图表

  @Prop() private title!: string
  @Prop() private bizAppCode?: string
  @Prop() private detailsPath?: string
  @Prop() private tabType!: string
  @Prop() private activeTabs!: string
  @Prop({ default: true }) private hasTitle!: boolean
  @Prop({ default: false }) private hasTabs!: boolean
  @Prop({ default: 500 }) private height!: number
  @Prop({ default: 6 }) private tabsPadding!: number
  @Prop({ default: '' }) private margin?: string

  @Emit('jumpTo')
  private jumpTo(componentsName: string) {
    return componentsName
  }

  // 当前展示模块，切换模式下使用
  private currentSwitchIndex = 0

  // 切换模式下的自动切换定时器
  private timer: any
  // 自动切换间隔时间 s
  private interval = 20

  private currentYear = ''
  private currentTabCode = ''
  private isFullScreen = false

  // 模块整体高度
  get wrapperHeight() {
    // 如果是切换模式 高度为 prop height
    // 如果是多个模块且不是切换模式，则高度为子级相加
    // 如果没有标题且不是切换模式，则高度为子级相加
    // 否则高度需加上title高度
    if (this.isSwitch) {
      return this.height
    } else if (Array.isArray(this.componentsName)) {
      return this.componentsName.reduce((height: number, item: any) => (height += item.height), 0) + 60
    } else if (this.title) {
      return this.height
    } else {
      return this.height + 100
    }
  }

  // 根据不同border计算 padding
  get horizontalPadding() {
    return this.wrapperHeight * [0.06, 0.13, 0.14, 0.06, 0.04, 0.08, 0.08][this.borderGbMode - 1]
  }

  // 根据不同border计算 padding
  get verticelPadding() {
    return this.wrapperHeight * [0.055, 0.105, 0.1, 0.08, 0.04, 0.06, 0.08][this.borderGbMode - 1]
  }

  // 切换模式下左侧是否有模块
  get hasLeftSection() {
    return this.currentSwitchIndex > 0
  }

  // 切换模式下右侧是否有模块
  get hasRightSection() {
    return this.isSwitch ? this.currentSwitchIndex < this.componentsName.length - 1 : false
  }

  // 获取标题长度
  get getTitleLength() {
    return (title: string) => {
      // 匹配中文
      let reg = /[\u4e00-\u9fa5]/g
      let nonChineseTitle = title.replace(reg, '')
      return title.length * 56 - nonChineseTitle.length * 23
    }
  }

  created() {
    // 资产地图全屏
    this.$bus.$on('AssetMapFullScreen', (isFullScreen: boolean) => {
      this.isFullScreen = isFullScreen
    })
  }

  mounted() {
    // 如果是切换模式开启自动切换
    this.isSwitch && this.setTimer()
  }

  private tabsHandler(currentTab: { code: string }) {
    this.currentTabCode = currentTab.code
    // 当components 为数组时, 应该最多只有一个 tabs, 两个模块进行联动，所以这里不上传
    //
    this.$emit('tabChange', currentTab)
  }

  /**
   * @desc tableList 组件 单列点击事件
   * @params index number 索引
   * @params index item 内容
   */
  private onItemClick(index: number, item: any) {
    this.$emit('itemClick', index, item)
  }

  /**
   * @desc 模块切换
   * @params direction 方向 -1 - 左 | 1 - 右
   */
  private onModuleSwitch(direction: -1 | 1) {
    this.currentSwitchIndex += direction
    this.setTimer()
  }

  // 点击箭头，跳转到配置的页面
  private detailsHandle(path: string, code?: string) {
    this.$bus.$emit('BustoBusiness', {
      code,
      path
    })
  }

  /**
   * @desc 切换模式下自动切换定时器
   */
  private setTimer() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    this.timer = setInterval(() => {
      // 切换
      this.onModuleSwitch(this.hasRightSection ? 1 : -1)
    }, this.interval * 1000)
  }

  destroyed() {
    // 清除定时器
    clearInterval(this.timer)
  }
}
</script>

<style scoped lang="scss">
$Cockiptpad: 20px;
$CockiptColor: #1cacff;

.common-module-wrapper {
  margin: 18px 0px;
  width: 100%;
  box-sizing: border-box;
  transition: all 0.4s linear;
  transform-origin: center center;
  background: none;
  position: relative;
  // overflow: hidden;
  // padding: 12px;

  // 边框
  &--bg1 {
    background-image: url('../../cockpitcren/images/panel_bg1.png');
  }
  &--bg2 {
    background-image: url('../../cockpitcren/images/panel_bg2.png');
  }
  &--bg3 {
    background-image: url('../../cockpitcren/images/panel_bg3.png');
  }
  &--bg4 {
    background-image: url('../../cockpitcren/images/panel_bg2.png');
  }
  &--bg5 {
    background-image: url('../../cockpitcren/images/panel_bg1.png');
  }
  &--bg6 {
    background-image: url('../../cockpitcren/images/panel_bg6.png');
  }
  &--bg7 {
    background-image: url('../images/panel_bg7.png');
  }

  &__section {
    overflow: hidden;
    position: relative;
  }

  // 左右切换样式
  .switch-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    overflow: hidden;
  }

  &--content-full {
    .common-module-wrapper {
      &__title {
        position: absolute;
      }
    }
  }

  &--switch {
    // overflow: hidden;
    .common-module-wrapper__fill {
      width: 100%;
    }
    .common-module-wrapper__left-btn,
    .common-module-wrapper__right-btn {
      position: absolute;
      top: 50%;
      transform: translate(0%, -50%);
      z-index: 10;

      width: 80px;
      cursor: pointer;

      img {
        width: 100%;
        transition: width 0.3s linear;
      }
    }

    .common-module-wrapper__left-btn {
      left: 0;
      animation: keyMoveThow2 5s infinite;
    }
    .common-module-wrapper__right-btn {
      right: 0;
      animation: keyMoveThow1 5s infinite;
    }

    .common-module-wrapper__section {
      position: absolute;
      top: 0;
      overflow: hidden;

      width: 100%;
      box-sizing: border-box;
      transition: all 0.4s ease-in-out;
      // 当前
      &--current {
        left: 0;
      }

      // 左侧
      &--left {
        left: -100%;
      }

      // 右侧
      &--right {
        left: 100%;
      }
    }
  }

  &--fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: 0;
    padding: 0;
    z-index: 100000000;
    transition: all 0.4s linear;
    .common-module-wrapper__section,
    .common-module-wrapper__content {
      height: 100% !important;
      transition: all 0.4s linear;
      transform-origin: center center;
    }
  }

  &--has-border {
    background-size: 100% 100%;
  }

  &__title {
    position: relative;
    width: 100%;
    & > div:first-child {
      font-size: 48px;
      line-height: 80px;
      padding-left: 20px;
      font-weight: normal;
      font-family: 'FZZZHONGHJW';
      font-style: italic;
      box-sizing: border-box;
    }

    .detail-btn {
      width: 80px;
      height: 30px;
      top: 22px;
      right: 10px;
      z-index: 10;
      animation: keyViceTitle 3s ease;
      cursor: pointer;
      &:hover {
        transform: translateX(10px);
      }
    }
    .detail-tab-btn {
      right: -420px;
    }

    img {
      position: absolute;
      height: 80px;
      right: -100px;
      animation: keyViceTitle 3s ease;
    }
  }

  .tabs-content-wrap {
    margin-bottom: 16px;
  }
  .tabs-wrapper {
    width: 100%;
    box-sizing: border-box;
    margin-top: 10px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    ::v-deep .tabs-content-wrap {
      width: 100%;
    }
    &--title {
      position: absolute;
      right: 0;
      top: 70px;
    }
  }
  &__content {
    width: 100%;
    position: relative;
    transform: rotate(0deg);
    box-sizing: border-box;
    margin-bottom: 12px;
    //overflow: hidden;
  }
}

@keyframes keyViceTitle {
  0% {
    opacity: 0;
    transform: translateX(100px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes keyShadowMode {
  0% {
    box-shadow: 0 0 $Cockiptpad rgba($color: $CockiptColor, $alpha: 0.55) inset, 0 0 $Cockiptpad rgba($color: $CockiptColor, $alpha: 0.55);
  }
  50% {
    box-shadow: 0 0 $Cockiptpad rgba($color: $CockiptColor, $alpha: 0.2) inset, 0 0 $Cockiptpad rgba($color: $CockiptColor, $alpha: 0.2);
  }
  100% {
    box-shadow: 0 0 $Cockiptpad rgba($color: $CockiptColor, $alpha: 0.55) inset, 0 0 $Cockiptpad rgba($color: $CockiptColor, $alpha: 0.55);
  }
}

@keyframes keyMoveThow1 {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  50% {
    opacity: 0.5;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes keyMoveThow2 {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  50% {
    opacity: 0.5;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>

<!-- 二级页面公共样式 -->
<style lang="scss">
.cockipt-vice-title {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 40px;
  justify-content: space-between;

  @keyframes keyViceTitle {
    0% {
      opacity: 0;
      transform: translateX(100px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  h4,
  p {
    margin: 0;
  }

  .title {
    height: 80px;
    font-size: 50px;
    font-weight: normal;
    font-family: 'FZZZHONGHJW';
    padding-right: 120px;
    background: url('../images/mode_title.png') no-repeat right bottom;
    background-size: auto 100%;
    animation: keyViceTitle 5s ease;
  }

  .details {
    position: relative;
    z-index: 10;
    width: 80px;
    height: 30px;
    background: url('../images/thow_more.png') no-repeat center center;
    background-size: 100%;
    transition: 0.5s;
    cursor: pointer;
    &:hover {
      transform: translateX(10px);
    }
  }
}
</style>