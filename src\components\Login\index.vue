<template>
  <Dialog title="登 录"
    width="400px"
    :visible="visible"
    :close-on-click-modal="true"
    :destroy-on-close="true"
    :append-to-body="true"
    @close="handleClose">
    <div slot="body">
      <el-form :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="80px"
        label-position="left"
        class="rule-form-box">
        <el-form-item label="用户名"
          prop="userName">
          <el-input v-model.trim="ruleForm.userName"
            clearable
            autocomplete="off"
            placeholder="请输入">
            <i slot="prepend"
              class="icon-user el-icon-user" />
          </el-input>
        </el-form-item>
        <el-form-item label="密码"
          prop="userPassword">
          <el-input v-model.trim="ruleForm.userPassword"
            clearable
            autocomplete="off"
            placeholder="请输入">
            <i slot="prepend"
              class="icon-user el-icon-lock" />
          </el-input>
        </el-form-item>
      </el-form>
    </div>

    <div slot="footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary"
        @click="validateForm">确认</el-button>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { ElForm } from 'element-ui/types/form'
import { setLocalStorage } from '@/utils/cache'
import Dialog from '@/components/Dialog/index.vue'

@Component({
  name: 'Login',
  components: {
    Dialog
  }
})
export default class Header extends Vue {
  @Prop() private visible!: boolean

  private ruleForm = {
    userName: '',
    userPassword: ''
  }
  private rules = {
    userName: [{ required: true, message: '请输入', trigger: 'blur' }],
    userPassword: [{ required: true, message: '请输入', trigger: 'blur' }]
  }

  // 验证数据
  private validateForm() {
    ;(this.$refs.ruleForm as ElForm).validate((valid: boolean) => {
      valid && this.submitData()
    })
  }

  // 提交数据
  private submitData() {
    let userName = this.ruleForm.userName
    setLocalStorage('UserName', userName)
    this.handleClose()
  }

  // 关闭弹窗
  private handleClose() {
    window.location.reload()
    this.$emit('update:visible', false)
  }
}
</script>

<style scoped lang="scss">
.rule-form-box {
  .icon-user {
    font-size: 16px;
  }
}
</style>