/** 固定资产 */
<template>
  <section class="fixed-assets">
    <Ratio :mode="1"
      :ratioData="assetCountData" />
    <Ratio :mode="1"
      :ratioData="landAreaData" />
    <Ratio :mode="1"
      :ratioData="coveredAreaData" />
    <Ratio :mode="1"
      :ratioData="originalValueData" />
    <Ratio :mode="1"
      :ratioData="accumulatedDepreciationData" />
    <LineCharts chartId="FixedAssetsLineCharts" />
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import Ratio, { RatioData } from './Ratio.vue'
import LineCharts from './lineCharts.vue'

@Component({
  components: {
    Ratio,
    LineCharts
  }
})
export default class extends Vue {
  private assetCountData: RatioData = {
    label: '经营性资产总数',
    // 值
    value: (+this.getData(10000)).toFixed(0),
    // 同比
    YOY: +this.getData(30),
    // 环比
    QOQ: +this.getData(30),
    unit: '处'
  }

  private landAreaData: RatioData = {
    label: '土地面积',
    // 值
    value: this.getData(),
    // 同比
    YOY: +this.getData(30),
    // 环比
    QOQ: +this.getData(30),
    unit: 'KM²'
  }
  private coveredAreaData: RatioData = {
    label: '建筑面积',
    // 值
    value: this.getData(),
    // 同比
    YOY: +this.getData(30),
    // 环比
    QOQ: +this.getData(30),
    unit: 'KM²'
  }
  private originalValueData: RatioData = {
    label: '固资原值',
    // 值
    value: this.getData(),
    // 同比
    YOY: +this.getData(30),
    // 环比
    QOQ: +this.getData(30),
    unit: '亿元'
  }
  private accumulatedDepreciationData: RatioData = {
    label: '累计折旧',
    // 值
    value: this.getData(),
    // 同比
    YOY: +this.getData(30),
    // 环比
    QOQ: +this.getData(30),
    unit: '亿元'
  }

  private getData(max = 100) {
    return (Math.random() * max).toFixed(2)
  }
}
</script>


<style scoped lang="scss">
.fixed-assets {
  padding: 0 10px;
}

::v-deep .ratio {
  height: 183px;
}
</style>