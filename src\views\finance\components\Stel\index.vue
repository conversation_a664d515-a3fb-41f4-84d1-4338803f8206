<template>
  <div class="finance-step">
    <el-steps direction="vertical"
      :active="active">
      <el-step v-for="(item, index) in itemconf"
        :key="index">
        <div v-if="index == active"
          slot="description">
          <span>{{ item.title }}(我的审批)</span>
          <el-form ref="elForm"
            :model="formData"
            :rules="rules"
            size="medium"
            label-width="80px">
            <el-form-item label="审核状态"
              prop="state">
              <el-radio-group v-model="formData.state"
                size="small">
                <el-radio label="1"
                  border>通过</el-radio>
                <el-radio label="2"
                  border>驳回</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="审核意见"
              prop="auditOpinion">
              <el-input v-model="formData.auditOpinion"
                placeholder="请输入审核意见"
                clearable
                :style="{ width: '100%' }"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div v-else
          slot="description"
          style="stel_content">{{ item.title }}</div>
      </el-step>
    </el-steps>
  </div>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
@Component({
  components: {}
})
export default class Container extends Vue {
  private active = 2
  private formData = {}
  private rules: object = {
    auditOpinion: [
      {
        required: true,
        message: '请输入单据编号',
        trigger: 'blur'
      }
    ],
    state: [
      {
        required: true,
        message: '请输入单据编号',
        trigger: 'blur'
      }
    ]
  }
  private itemconf: object[] = [
    {
      content: ' 当前',
      title: '提交审批(张财神)'
    },
    {
      title: '市国资财务监管处审批（我的审批）',
      content: ' 当前'
    },
    {
      title: '市国资财务监管处审批（我的审批）',
      content: ' 当前'
    },
    {
      title: '市国资财务监管处审批（我的审批）',
      content: ' 当前',
      soltName: 'bottom'
    }
  ]
}
</script>

<style lang="scss" >
.finance-step {
  .el-step__icon {
    display: inline-block;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    margin-right: 10px;
    background: #fb3f3f;
    box-shadow: 0 0 0 2px rgba(63, 140, 255, 0.2);
    margin: -4px 0 0 5px;
  }
  .el-step__icon-inner {
    display: none;
  }

  .el-step__main {
    padding: 10px 0;
    transform: translateY(-15px);
    font-size: 14px;
  }
  .el-step__description.is-finish {
    font-size: 14px;
    color: #303133;
  }
}
</style>
