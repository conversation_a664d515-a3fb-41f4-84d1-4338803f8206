/* 财务监管 */

<template>
  <section class="financial-indicator-wrap">
    <!-- 顶部资产信息 -->
    <AssetsRateInfo v-loading="loadingAssetsRate"
      :echartsData="assetsRateData"
      class="cockipt-approach-middel-top" />

    <!-- 内容区域 -->
    <div class="indicator-content-box">
      <!-- 左侧 -->
      <div class="module-box module-left-box">
        <div class="mode-box pis-relative mode-bg1 cockipt-approach-left-top"
          style="height: 840px;">
          <!-- 数据改变提示层 -->
          <img v-if="visibleFlicker"
            class="cockipt-bg-flicker"
            src="@/views/main/cockpitcren/images/panel_bg1_cg.png" />

          <AssetProfile v-loading="loadingFinancialAssets"
            title="资产概况"
            :typeBizObj="{
              bizAppCode: 'finance',
              detailsPath: '/wel/index?bizAppCode=finance&path=/business/finance/analyReg/quickReport/index'
            }"
            :echartsData="assetProfileData" />
        </div>
        <div class="mode-box pis-relative mode-bg2 cockipt-approach-left-bottom"
          style="height: 590px;">
          <!-- 数据改变提示层 -->
          <img v-if="visibleFlicker"
            class="cockipt-bg-flicker"
            src="@/views/main/cockpitcren/images/panel_bg2_cg.png" />

          <AssetEquity v-loading="loadingFinancialHistory"
            title="资产趋势"
            :typeBizObj="{
              bizAppCode: 'finance',
              detailsPath: '/wel/index?bizAppCode=finance&path=/business/finance/analyReg/yearReport/index'
            }"
            :echartsData="assetTrendData" />
        </div>
      </div>

      <!-- 中间 -->
      <div class="module-box module-middle-box">
        <!-- 各集团tabs  -->
        <CommonTabs :orgCode="tabActive"
          module="FinancialIndicator"
          class="cockipt-approach-middel-top"
          @commonTabsHandle="commonTabsHandle" />
        <div class="ranking-box cockipt-approach-middel-top"
          style="height: 90px;"
          @click="visibleDetail = true">
          <span>全省11市本级监管企业主要指标排名</span>
        </div>
        <div class="mode-box pis-relative mode-bg6 cockipt-approach-middel-top"
          style="height: 594px;">
          <!-- 数据改变提示层 -->
          <img v-if="visibleFlicker"
            class="cockipt-bg-flicker"
            src="@/views/main/cockpitcren/images/panel_bg6_cg.png" />

          <ProfitOverview v-loading="loadingFinancialAssets"
            title="利润概况"
            :year="year"
            :typeBizObj="{
              bizAppCode: 'finance',
              detailsPath: '/wel/index?bizAppCode=finance&path=/business/finance/analyReg/quickReport/index'
            }"
            :echartsData="profitOverviewData" />
        </div>

        <div class="mode-box pis-relative mode-bg6 cockipt-approach-middel-bottom"
          style="height: 600px;">
          <!-- 数据改变提示层 -->
          <img v-if="visibleFlicker"
            class="cockipt-bg-flicker"
            src="@/views/main/cockpitcren/images/panel_bg6_cg.png" />

          <ProfitTrend v-loading="loadingFinancialHistory"
            title="利润趋势"
            :typeBizObj="{
              bizAppCode: 'finance',
              detailsPath: '/wel/index?bizAppCode=finance&path=/business/finance/analyReg/yearReport/index'
            }"
            :echartsData="profitTrendData" />
        </div>
      </div>

      <!-- 右侧 -->
      <div class="module-box module-right-box">
        <div class="mode-box pis-relative mode-bg1 cockipt-approach-right-top"
          style="height: 840px;">
          <!-- 数据改变提示层 -->
          <img v-if="visibleFlicker"
            class="cockipt-bg-flicker"
            src="@/views/main/cockpitcren/images/panel_bg1_cg.png" />

          <OwnersEquity v-loading="loadingFinancialAssets"
            title="所有者权益"
            :typeBizObj="{
              bizAppCode: 'finance',
              detailsPath: '/wel/index?bizAppCode=finance&path=/business/finance/analyReg/quickReport/index'
            }"
            :echartsData="ownersEquityData" />
        </div>
        <div class="mode-box pis-relative mode-bg2 cockipt-approach-right-bottom"
          style="height: 590px;">
          <!-- 数据改变提示层 -->
          <img v-if="visibleFlicker"
            class="cockipt-bg-flicker"
            src="@/views/main/cockpitcren/images/panel_bg2_cg.png" />

          <AssetEquity v-loading="loadingFinancialHistory"
            title="权益趋势"
            :typeBizObj="{
              bizAppCode: 'finance',
              detailsPath: '/wel/index?bizAppCode=finance&path=/business/finance/analyReg/yearReport/index'
            }"
            :echartsData="equityTrendData" />
        </div>
      </div>
    </div>

    <!-- 弹窗：查看详情 -->
    <ProfitOverviewDetail v-if="visibleDetail"
      :visible.sync="visibleDetail"
      :closeModal="false"
      :year="year"
      title="全省11市本级监管企业主要指标排名"
      width="1100px" />
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { assetsFinancialAssets, assetsFinancialHistory, assetsFinancialSummary } from '@/api/cockpit'
import CommonTabs from '@/views/main/cockpitSecondary/components/CommonActiveTabs.vue'
import ProfitOverviewDetail from './Components/ProfitOverviewDetail.vue'
import AssetsRateInfo from './AssetsRateInfo.vue'
import ProfitOverview from './ProfitOverviewCp.vue'
import ProfitTrend from './ProfitTrend.vue'
import AssetEquity from './AssetEquity.vue'
import AssetProfile from './AssetProfile.vue'
import OwnersEquity from './OwnersEquity.vue'

@Component({
  components: {
    CommonTabs,
    AssetsRateInfo,
    ProfitOverview,
    ProfitTrend,
    AssetEquity,
    AssetProfile,
    OwnersEquity,
    ProfitOverviewDetail
  }
})
export default class extends Vue {
  private year = ''
  private moon = ''
  private tabActive = ''

  // 各数据模块
  private visibleFlicker = false
  private loadingAssetsRate = false
  private loadingFinancialAssets = false
  private loadingFinancialHistory = false
  private visibleDetail = false
  private ownersEquityData: object = {}
  private assetProfileData: object = {}
  private profitTrendData: any[] = []
  private assetsRateData: any[] = []
  private assetTrendData: any[] = []
  private equityTrendData: any[] = []
  private profitOverviewData: any[] = []

  // 数据初始化
  private created() {
    this.year = String(new Date().getFullYear())

    // 如果链接上带有集团code，需要让tabs选中该集团
    let { query} = this.$route
    if(query && query.orgCode) {
      this.tabActive = (query.orgCode as string) || '0'
    }
    // end
  }

  // 组件初始化
  private mounted() {
    // 年份改变
    this.$bus.$on('BusYearTimeChange', (data: string) => {
      this.year = data
      this.initData()
    })

    // 月份改变
    this.$bus.$on('BusMoonTimeChange', (data: string) => {
      this.moon = data
      this.initData()
    })
  }

  // 各集团模块切换
  private commonTabsHandle(data: string) {
    this.tabActive = data
    this.initData()
  }

  // 获取各模块数据
  private initData() {
    this.flickerHandle()
    this.infaceAssetsRateData()
    this.infaceFinancialAssets()
    this.infaceFinancialHistory()
  }

  // 接口：顶部资产信息
  @Loading('loadingAssetsRate')
  private async infaceAssetsRateData() {
    let { data } = await assetsFinancialSummary({
      year: this.year,
      month: this.moon,
      orgCode: this.tabActive
    })

    this.assetsRateData = data || []
  }

  // 接口：资产统计
  @Loading('loadingFinancialAssets')
  private async infaceFinancialAssets() {
    let { data } = await assetsFinancialAssets({
      year: this.year,
      month: this.moon,
      orgCode: this.tabActive
    })

    if (!data) return

    // 获取资产概况数据
    this.assetProfileData = data.assets

    // 获取利润概况数据
    this.profitOverviewData = data.profit.list

    // 获取所有者权益数据
    this.ownersEquityData = data.equity
  }

  // 接口：历年趋势统计
  @Loading('loadingFinancialHistory')
  private async infaceFinancialHistory() {
    let { data } = await assetsFinancialHistory({
      year: this.year,
      month: this.moon,
      orgCode: this.tabActive
    })

    // 获取历年资产趋势数据
    this.assetTrendData = data.assets || []

    // 获取历年利润趋势数据
    this.profitTrendData = data.profit || []

    // 获取历年权益趋势数据
    this.equityTrendData = data.equity || []
  }

  // 触发背景闪烁效果
  private flickerHandle() {
    this.visibleFlicker = true
    setTimeout(() => {
      this.visibleFlicker = false
    }, 3000)
  }
}
</script>

<style scoped lang="scss">
.financial-indicator-wrap {
  position: relative;
  font-size: 30px;
  height: 100%;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  box-sizing: border-box;
}

.indicator-content-box {
  flex: 1;
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
  .module-box {
    flex: 1;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    box-sizing: border-box;
  }

  .module-middle-box {
    flex: none;
    width: 1205px;
    padding: 0 20px;
    margin-bottom: 16px;
    .mode-box {
      padding: 40px 60px;
    }
  }

  .mode-box {
    padding: 50px 70px;
    box-sizing: border-box;
  }
  .mode-bg1 {
    background: url('../../../../cockpitcren/images/panel_bg1.png') no-repeat left top;
    background-size: 100% 100%;
  }
  .mode-bg2 {
    background: url('../../../../cockpitcren/images/panel_bg2.png') no-repeat left top;
    background-size: 100% 100%;
  }
  .mode-bg3 {
    background: url('../../../../cockpitcren/images/panel_bg3.png') no-repeat left top;
    background-size: 100% 100%;
  }
  .mode-bg4 {
    background: url('../../../../cockpitcren/images/panel_bg4.png') no-repeat left top;
    background-size: 100% 100%;
  }
  .mode-bg5 {
    background: url('../../../../cockpitcren/images/panel_bg5.png') no-repeat left top;
    background-size: 100% 100%;
  }
  .mode-bg6 {
    background: url('../../../../cockpitcren/images/panel_bg6.png') no-repeat left top;
    background-size: 100% 100%;
  }
}

.ranking-box {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #3eeeff;
  border-radius: 10px;
  cursor: pointer;
  &:hover {
    background: rgba($color: #3eeeff, $alpha: 0.8);
  }
  span {
    font-size: 50px;
    font-weight: normal;
    font-family: 'FZZZHONGHJW';
  }
}
</style>




