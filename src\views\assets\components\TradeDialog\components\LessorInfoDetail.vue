// 出租方信息
<template>
  <section class="rental-info">
    <!-- 法人及其他组织机构 -->
    <el-descriptions class="margin-top"
      title=""
      :column="4"
      :labelStyle="{
        width: '120px'
      }"
      border>
      <el-descriptions-item label="公司名称"
        :span="4">
        {{ lessorInfoDetail.companyName }}
      </el-descriptions-item>
      <el-descriptions-item label="注册地(住所)"
        :span="4">
        {{ lessorInfoDetail.registrationPlace }}
      </el-descriptions-item>
      <el-descriptions-item label="注册资本(万)"
        :span="2">
        {{ lessorInfoDetail.registeredCapital }}
      </el-descriptions-item>
      <el-descriptions-item label="经济性质"
        :span="2">
        {{ getEconomicNatureListStr(lessorInfoDetail.economicNature) }}
      </el-descriptions-item>

      <el-descriptions-item label="企业类型"
        :span="2">
        {{ getEnterpriseTypeStr(lessorInfoDetail.enterpriseType) }}
        <!-- {{ lessorInfoDetail.enterpriseType }} -->
      </el-descriptions-item>

      <el-descriptions-item label="法定代表人"
        :span="2">
        {{ lessorInfoDetail.legalPerson }}
      </el-descriptions-item>

      <el-descriptions-item label="所属行业类型"
        :span="2">
        {{ getIndustryTypeStr(lessorInfoDetail.industryType) }}
        <!-- {{ lessorInfoDetail.industryType }} -->
      </el-descriptions-item>

      <el-descriptions-item label="所属行业"
        :span="2">
        {{ getIndustryStr(lessorInfoDetail.industry) }}
      </el-descriptions-item>

      <el-descriptions-item label="统一社会信用代码"
        :span="2">
        {{ lessorInfoDetail.lessorSocialCreditCode }}
      </el-descriptions-item>
    </el-descriptions>

    <!-- 自然人 -->
    <el-descriptions class="margin-top m-t-12"
      title="自然人"
      :column="6"
      :labelStyle="{
        width: '120px'
      }"
      border>
      <el-descriptions-item label="姓名"
        :span="6">
        {{ lessorInfoDetail.naturalPerson }}
      </el-descriptions-item>
      <el-descriptions-item label="证件类型"
        :span="2">
        {{ getNaturalPersonCardTypeStr(lessorInfoDetail.naturalPersonCardType) }}
      </el-descriptions-item>
      <el-descriptions-item label="证件号码"
        :span="4">
        {{ lessorInfoDetail.naturalPersonCertificateNo }}
      </el-descriptions-item>
    </el-descriptions>

    <!-- 联系方式 -->
    <el-descriptions class="margin-top m-t-12"
      title="联系方式"
      :column="6"
      :labelStyle="{
        width: '120px'
      }"
      border>
      <el-descriptions-item label="联系人"
        :span="3">
        {{ lessorInfoDetail.contacts }}
      </el-descriptions-item>
      <el-descriptions-item label="联系人电话"
        :span="3">
        {{ lessorInfoDetail.contactPhone }}
      </el-descriptions-item>
      <el-descriptions-item label="邮箱"
        :span="3">
        {{ lessorInfoDetail.email }}
      </el-descriptions-item>

      <el-descriptions-item label="联系地址"
        :span="3">
        {{ lessorInfoDetail.contactAddress }}
      </el-descriptions-item>
    </el-descriptions>

    <!-- 附件列表 -->
    <AccessoryList v-model="lessorInfoDetail.attachmentFileDTOList"
      dict="asset_lessor_attach" />
  </section>
</template>

<script lang="ts">
import { deepClone } from '@/utils'
import { decisionTypeList, economicNatureList } from '@/views/assets/filterOptions'
import { Component, Model, Prop, Vue } from 'vue-property-decorator'
import AccessoryList, { Accessory } from './AccessoryFileList.vue'
import { LessorInfo } from './LessorInfoForm.vue'
import { BusinessModule } from '@/store/modules/businessDict'
type FileKey = 'attachmentFile1' | 'attachmentFile2' | 'attachmentFile3'

@Component({
  components: {
    AccessoryList
  }
})
export default class extends Vue {
  @Prop({ default: () => ({}) }) private initData!: LessorInfo

  private lessorInfoDetail: Partial<LessorInfo> = {}

  private decisionTypeList = decisionTypeList
  private economicNatureList: any = this.getDictData('jjxz')
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  private accessoryList: Accessory[] = [
    {
      fileName: '营业执照或其他主体资格证明材料',
      isRequired: true,
      prop: 'attachmentFile1',
      fileList: []
    },
    {
      fileName: '法定代表人身份证明',
      isRequired: true,
      prop: 'attachmentFile2',
      fileList: []
    },
    {
      fileName: '自然人身份证明（填写自然人信息的需要上传）',
      isRequired: false,
      prop: 'attachmentFile3',
      fileList: []
    }
  ]

  get getEnterpriseTypeStr() {
    return (enterpriseType: number) => {
      return (this.getDictData('enterprise_type').find((item: any) => item.value == enterpriseType) || { label: '' }).label
    }
  }

  get getEconomicNatureListStr() {
    return (economicNature: number) => {
      return (this.economicNatureList.find((item: any) => item.value == economicNature) || { label: '' }).label
    }
  }

  get getIndustryTypeStr() {
    return (industryType: number) => {
      return (this.getDictData('sshy').find((item: any) => item.value == industryType) || { label: '' }).label
    }
  }

  get getIndustryStr() {
    let list: any = this.getDictData('sshy').find((item: any) => item.value == this.lessorInfoDetail.industryType)

    return (industry: number) => {
      if (list == undefined) {
        return '-'
      }
      return (list.children.find((item: any) => item.value == industry) || { label: '' }).label
    }
  }

  get getNaturalPersonCardTypeStr() {
    return (naturalPersonCardType: number) => {
      return (this.getDictData('zjlx').find((item: any) => item.value == naturalPersonCardType) || { label: '' }).label
    }
  }

  created() {
    this.lessorInfoDetail = deepClone(this.initData)
  }
}
</script>


<style scoped lang="scss">
::v-deep .el-form-item {
  margin-bottom: 0px !important;
}
::v-deep .department-name-input {
  .el-form-item {
    margin-bottom: 0px !important;
  }
}
</style>