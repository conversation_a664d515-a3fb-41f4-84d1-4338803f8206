<!-- 土地证书 -->
<template>
  <el-container class="container"
    direction="vertical">
    <search-bar :items="searchItems"
      @onSearch="handleSearch"
      v-if="isSearch">
      <el-button v-loading="loadingExport"
        type="primary"
        @click="exportHandle">导 出</el-button>
    </search-bar>
    <grid :remote-url="remoteUrl"
      :columns="cols"
      :show-pagination="true"
      :search-params="searchParams"
      :overflow-tooltip="true"
      @row-click="loaddetail"
      ref="grid"
      border>
      <template slot="contractLeasePeriod"
        slot-scope="scope">
        {{ scope.row.startTime + ' 至 ' + scope.row.endTime }}
      </template>
      <template slot="operatingBar"
        slot-scope="scope">
        <el-button type="text"
          @click.stop="loaddetail(scope.row)">查看</el-button>
      </template>
    </grid>
    <el-dialog title=""
      top="30vh"
      :modal="true"
      :visible.sync="centerDialogVisible"
      width="30%"
      center>
      <h3>
        <i class="el-icon-info"></i>
        暂未开放
      </h3>
      <span slot="footer"
        class="dialog-footer"></span>
    </el-dialog>
  </el-container>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import Grid from '@/components/Grid/index.vue'
import SearchBar from '@/components/SearchBar/index.vue'
import Uploader from '@/components/Uploader/index.vue'
import { BusinessModule } from '@/store/modules/businessDict'
import { getAstCompTree } from '@/api/projectInvestment'
import { downloadXls } from '@/utils'
import { ExportNoteva } from '@/api/assetsv2'
import { Loading, Confirm } from '@/decorators'
@Component({
  components: {
    Grid,
    SearchBar,
    Uploader
  }
})
export default class HouseCertificate extends Vue {
  private loadingExport = false

  // 文件上传
  private isSearch = false
  private centerDialogVisible = true
  private showUploader = false
  private UploaderList = []
  private compTree = [
    {
      value: '11',
      label: '11'
    }
  ]
  // 详细表格数据
  private appendixsLength!: number
  private Diadetaillist: any = {}
  private visvileDetail = false
  private visvileDetailif = false
  private remoteUrl = '/fht-monitor/ast/report/not-eva'
  private searchItems = [
    {
      type: 'text',
      key: 'keyword',
      placeholder: '资产名称/编号',
      width: '300px'
    },
    {
      type: 'select',
      key: 'orgCode',
      placeholder: '直属单位',
      width: '150px',
      options: this.compTree
    },
    {
      type: 'select',
      key: 'assetPurpose',
      placeholder: '房产用途',
      width: '150px',
      options: this.getDictData('asset_purpose')
    }
  ]
  // 合同编号、合同状态、合同租金、租赁总面积、承租方名称、承租方联系方式、合同租赁期限、签约时间、交易方式、所属集团
  private cols = [
    {
      label: '直属单位',
      minWidth: 120,
      prop: 'orgName'
    },
    {
      prop: 'itemName',
      label: '房产名称',
      minWidth: 120
    },
    {
      label: '房产用途',
      minWidth: 120,
      prop: 'assetPurposeDesc'
    },
    {
      prop: 'coveredArea',
      label: '建筑面积(m²)',
      minWidth: 120
    },
    {
      prop: 'leasePrice',
      label: '租赁价格（元）',
      minWidth: 120
    },
    {
      prop: 'rentPeriod',
      label: '租期',
      minWidth: 110
    },
    {
      prop: 'originalValue',
      label: '账面原值（元）',
      minWidth: 120
    },
    {
      prop: 'houseCertNo',
      label: '房产证编号',
      minWidth: 110
    },
    // 承租方联系方式
    {
      prop: 'realEstateCertNo',
      label: '不动产证编号',
      minWidth: 120
    },
    {
      prop: 'landCertNo',
      label: '土地证编号',
      minWidth: 160
    },

    {
      prop: 'useStatusDesc',
      label: '使用情况',
      minWidth: 120
    },
    {
      prop: 'occupancyCoveredArea',
      label: '出租面积(m²)',
      minWidth: 120
    },
    // 承租方联系方式

    {
      prop: 'reason',
      label: '未评估原因',
      minWidth: 140
    },
    {
      prop: 'remark',
      label: '备注',
      minWidth: 140
    }
  ]
  private searchParams = {}
  // 是显示附件
  created() {
    this.getCompTree()
  }
  private uploaderDetail(List: any) {
    this.UploaderList = List
    this.showUploader = true
  }
  // 详情附件
  private showUpdaload(list: any) {
    this.uploaderDetail = list
    this.showUploader = true
  }

  // return () => {
  //   getAstCompTree({ parentId: 0, deptCategory: 1 }).then(res => {
  //     if (res.success) {
  //       let options = res.data.map((res: any) => {
  //         return {
  //           label: res.deptName,
  //           value: res.deptCode
  //         }
  //       })
  //       return Object.assign(this.compTree, options)
  //     }
  //   })
  // }
  // }

  // 导出
  @Loading('loadingExport')
  @Confirm({
    title: '提示',
    content: `是否确认导出数据`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private async exportHandle() {
    let res = await ExportNoteva(this.searchParams)
    let time = new Date().getTime()
    downloadXls(res.data, `未评估资产_${time}.xlsx`)
    this.$message.success(res.msg || '导出成功')
  }
  //获取机构数组
  private async getCompTree() {
    let options = []
    let res = await getAstCompTree({ parentId: 0, deptCategory: 1 })
    this.isSearch = false
    if (res.success) {
      let options = res.data.map((res: any) => {
        return {
          label: res.deptName,
          value: res.deptCode
        }
      })
      await this.$nextTick(() => {
        Object.assign(this.compTree, options)
        this.isSearch = true
      })
    }
  }
  // 改变详情显示隐藏
  private changeShowDialogDetail(state: boolean) {
    this.visvileDetailif = state
    this.$nextTick(() => {
      this.visvileDetail = state
    })
  }
  private handleSearch(condition: any) {
    this.searchParams = condition
    ;(this.$refs['grid'] as any).refresh()
  }
  // 字典
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  //  操作
  private loaddetail(row: any) {
    this.Diadetaillist = row
    this.visvileDetailif = true
    this.$nextTick(() => {
      this.visvileDetail = true
    })
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 120%;
}
</style>
