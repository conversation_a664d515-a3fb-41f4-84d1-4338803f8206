/*
* 操作缓存的方法
*/
import Cookies from "js-cookie"

// 判断用户是否登录的 cookie
const TokenKey = 'saber-token'

// token 失效key
const TokenInvalidTime = 'token_invalid_time'

/*
* localStorage
*/
// 获取
export const getLocalStorage = (key: string) => localStorage.getItem(key)
// 设置
export const setLocalStorage = (key: string, value: any) => localStorage.setItem(key, value)
// 删除
export const removeLocalStorage = (key: string) => localStorage.removeItem(key)

/*
* sessionStorage
*/
// 获取
export const getSessionStorage = (key: string) => sessionStorage.getItem(key)
// 设置
export const setSessionStorage = (key: string, value: any) => sessionStorage.setItem(key, value)
// 删除
export const removeSessionStorage = (key: string) => sessionStorage.removeItem(key)

/*
* cookies
*/
// 获取
export function getToken() {
  return Cookies.get(TokenKey)
}
// 设置
export function setToken(token: string) {
  return Cookies.set(TokenKey, token)
}
// 删除
export function removeToken() {
  return Cookies.remove(TokenKey)
}

/*
* token 失效相关设置
*/
// 设置失效时间
export function setInvalidTokenTime() {
  return localStorage.setItem(TokenInvalidTime, String(new Date().getTime()))
}

// 获取失效时间
export function getInvalidTokenTime() {
  return localStorage.getItem(TokenInvalidTime)
}

/**
 * String
 */
export function getYear() {
  return new Date().getFullYear() + ''
}

export const setStore = (key: string, value: string) => {
  if (!value) {
    return
  }
  localStorage.setItem('saber-' + key, JSON.stringify({
    content: value,
    dataType: 'string',
    datetime: new Date().valueOf()
  }))
}

export const getStore = (key: string): { content: string, dataType: string, datetime: number } | undefined => {
  let json = localStorage.getItem('saber-' + key)
  return json ? JSON.parse(json) : undefined
}