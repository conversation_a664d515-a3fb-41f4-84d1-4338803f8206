<template>
  <div :loading="loading"
    :switchList="switchList"
    :currentModule="companyId"
    @moduleChange="onModuleChange">
    <!-- 内容区域 -->
    <div class="content"
      slot="content">
      <el-row :span="24"
        :gutter="24">
        <el-col :span="6">
          <div class="content__left">
            <CommonModuleWrapper v-for="(com, index) in leftComponentsList"
              :key="index"
              :loading="loading"
              :hasTitle="!!com.title"
              :hasTabs="com.hasTabs"
              :tabType="com.tabType"
              :height="com.height"
              :companyId="companyId"
              :borderGbMode="1"
              :componentsName="com.name"
              :title="com.title" />
          </div>
        </el-col>
        <el-col :span="12">
          <div class="content__middle">
            <!-- 各集团tabs  -->
            <CommonTabs class="cockipt-approach-middel-top m-b-30 m-t-30" />

            <!-- 如果是资产地图去除加载, 地图加载太慢 -->
            <CommonModuleWrapper v-for="(com, index) in middleComponentsList"
              :key="index"
              :loading="com.name === 'AssetMapIframe' ? false : loading"
              :hasTabs="com.hasTabs"
              :tabType="com.tabType"
              :hasTitle="!!com.title"
              :height="com.height"
              :hasBorder="com.hasBorder"
              :companyId="companyId"
              :componentsName="com.name"
              :borderGbMode="3"
              :title="com.title" />
          </div>
        </el-col>
        <el-col :span="6">
          <div class="content__right">
            <CommonModuleWrapper v-for="(com, index) in rightComponentsList"
              :key="index"
              :loading="loading"
              :hasTitle="!!com.title"
              :hasTabs="com.hasTabs"
              :tabType="com.tabType"
              :height="com.height"
              :companyId="companyId"
              :borderGbMode="1"
              :componentsName="com.name"
              :title="com.title" />
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import CommonWrapper from '../components/commonWrapper.vue'
import CommonModuleWrapper, { ComponentItem, ComponentRegister } from '../components/CommonModuleWrapper.vue'
import { SwitchItem } from '../components/CommonHeader.vue'
import CommonTabs from '@/views/main/cockpitSecondary/components/CommonTabs.vue'

export type CurrentCompanyId =
  | 'CT' // 城投
  | 'KT' // 金投
  | 'JT' // 交投
  | 'GD' // 轨道
  | 'SW' // 水务
  | 'SF' // 社发

@Component({
  components: {
    CommonTabs,
    CommonWrapper,
    CommonModuleWrapper
  }
})
export default class extends Vue {
  @Watch('$route.query.id', { immediate: true })
  private moduleNameChange() {
    if (this.$route.query && this.$route.query.id) {
      this.companyId = this.$route.query ? (this.$route.query.id as CurrentCompanyId) || 'CT' : 'CT'
      this.loading = true
      setTimeout(() => {
        this.loading = false
      }, 1000)
    }
  }

  // 组件列表  左侧
  private leftComponentsList: ComponentItem[] = [
    {
      name: 'GeneralAssessment',
      title: '资产总体情况',
      height: 420,
      hasTabs: true,
      tabType: 'assets'
    },
    {
      name: 'AssetComposition',
      title: '资产构成情况',
      height: 420
    },
    {
      name: [
        {
          name: 'ProjectImplementation',
          height: 540,
          title: '项目执行度'
        },
        {
          name: 'ProjectImplementationBar',
          height: 480,
          title: ''
        }
      ]
    },
    {
      name: 'ProjectProfitabilityRanking',
      title: '项目盈利排名',
      height: 500
    },
    {
      name: 'TitlesOfMembers',
      title: '成员职称情况',
      height: 500
    },
    {
      name: 'EducationalBackgroundDistribution',
      title: '成员学历分布情况',
      height: 580
    }
  ]

  // 组件列表 中间
  private middleComponentsList: ComponentItem[] = [
    {
      name: 'IndexOverview',
      title: '',
      height: 180,
      hasTitle: false,
      hasBorder: false
    },
    // {
    //   name: 'AssetMapIframe',
    //   title: '',
    //   height: 1040,
    //   hasBorder: false,
    //   hasTitle: false
    // },
    {
      name: 'SanZhongYiDa',
      title: '三重一大总览',
      height: 320,
      hasBorder: true
    },
    {
      name: 'ProjectImplementationOverview',
      title: '项目执行概况',
      height: 540
    },
    {
      name: 'ProjectAmountDistribution',
      title: '项目金额分布',
      hasBorder: true,
      height: 440
    },
    {
      name: 'MembershipStatus',
      title: '成员去留情况',
      hasBorder: true,

      height: 480
    },
    {
      name: 'GeneralInformation',
      title: '成员总体情况',
      hasBorder: true,

      height: 460
    }
  ]

  // 组件列表 右侧
  private rightComponentsList: ComponentItem[] = [
    {
      name: 'ProfitOnAssets',
      title: '资产利润情况',
      height: 520,
      hasTitle: false
    },
    {
      name: 'UtilizationOfAssets',
      title: '资产利用情况',
      height: 495
    },
    {
      name: 'DepositAndLoanScale',
      title: '资金存贷规模',
      height: 380
    },
    {
      name: [
        {
          name: 'ProjectClassification',
          height: 500,
          title: '项目分类情况'
        },
        {
          name: 'ProjectClassificationPie',
          height: 520,
          title: ''
        }
      ]
    },
    {
      name: 'AgeOfMembers',
      title: '成员年龄情况',
      height: 500
    },
    {
      name: 'MajorDistribution',
      title: '成员专业分布情况',
      height: 600
    }
  ]

  // 当前年月
  get getCurrentDate() {
    let date = new Date()
    return `截止到${date.getFullYear()}年${date.getMonth() + 1}月`
  }

  private companyId: CurrentCompanyId = 'CT'
  private loading = false
  private switchList: SwitchItem<string>[] = [
    {
      name: 'ALL',
      label: '国资总况'
    },
    {
      name: 'SW',
      label: '国资运营'
    },
    {
      name: 'CT',
      label: '城投集团'
    },
    {
      name: 'JT',
      label: '交投集团'
    },
    {
      name: 'KT',
      label: '金投集团'
    },
    {
      name: 'GD',
      label: '轨道集团'
    }
  ]

  private onModuleChange(moduleName: any) {
    this.$router.push({ path: '', query: { id: moduleName } })
  }
}
</script>


<style lang="scss">
.content {
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  &__middle {
    padding: 0 20px;
  }
}
.content::-webkit-scrollbar {
  display: none;
}
</style>