<template>
  <div class="marker-info"
    v-loading="loading">
    <h5 class="marker-header marker-info-h5">
      {{ Markerinfo.assetNo }}
      <span type="text"
        @click="changeShowType">
        <i class="el-icon-arrow-left"></i>
        返回
      </span>
    </h5>
    <section class="marker-info-header">
      <el-descriptions title=""
        :column="2"
        label="-">
        <el-descriptions-item label="资产编号"
          span="2">{{ infodata.assetInfo.assetNo }}</el-descriptions-item>
        <el-descriptions-item label="资产类型"
          span="2">{{ infodata.assetInfo.assetTypeDesc }}</el-descriptions-item>
        <el-descriptions-item label="土地用途"
          span="2">{{ infodata.assetInfo.landPurpose }}</el-descriptions-item>
        <el-descriptions-item label="房屋类别"
          span="2">{{ infodata.assetInfo.houseType }}</el-descriptions-item>
        <el-descriptions-item
          label="房屋用途">{{ infodata.assetInfo.housingPurposes }}</el-descriptions-item>

        <el-descriptions-item
          label="获取方式">{{ infodata.assetInfo.obtainingMethod }}</el-descriptions-item>
        <el-descriptions-item label="使用状态">{{ infodata.assetInfo.useStatus }}</el-descriptions-item>
        <el-descriptions-item
          label="权利性质">{{ infodata.assetInfo.rightsNature }}</el-descriptions-item>
        <el-descriptions-item label="坐落地址"
          span="2">{{ infodata.assetInfo.location }}</el-descriptions-item>
        <el-descriptions-item label="直属单位"
          span="2">{{ infodata.assetInfo.subordinateUnits }}</el-descriptions-item>
        <el-descriptions-item label="产权证编号"
          span="2">{{ infodata.assetInfo.certNo }}</el-descriptions-item>
      </el-descriptions>
      <!-- <div>

        <div class="block">
          <el-carousel trigger="click" height="100px">
            <el-carousel-item v-for="item in 4" :key="item">
            </el-carousel-item>
          </el-carousel>
        </div>
      </div> -->
    </section>
    <h5 class="marker-info-h5">
      资产概述
      <el-link type="primary">查看附加</el-link>
    </h5>
    <el-descriptions title=""
      direction="vertical"
      :column="4"
      border
      class="markerinfo-bottom-table">
      <el-descriptions-item label="土地面积(㎡)">{{ infodata.assetInfo.landArea}}</el-descriptions-item>
      <el-descriptions-item
        label="建筑面积(㎡)">{{ infodata.assetInfo.buildingArea }}</el-descriptions-item>
      <el-descriptions-item
        label="资产原值(万)">{{ infodata.assetInfo.originalValueAssets }}</el-descriptions-item>
      <el-descriptions-item
        label="最新估值(万)">{{ infodata.assetInfo.latestValuation }}</el-descriptions-item>
    </el-descriptions>
    <div v-if="Markerinfo.bizStatus == '住宅'">
      <h5 class="marker-info-h5">租赁情况</h5>
      <el-descriptions title=""
        direction="vertical"
        :column="4"
        class="markerinfo-bottom-table">
        <el-descriptions-item label="状态">{{ infodata.assetInfo.state }}</el-descriptions-item>
        <el-descriptions-item label="入住类型">{{ infodata.assetInfo.stayType }}</el-descriptions-item>
        <el-descriptions-item
          label="合同周期">{{ infodata.assetInfo.contractPeriod }}</el-descriptions-item>
        <el-descriptions-item
          label="年租金(元)">{{ infodata.assetInfo.annualRent }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="rentalSituation-box"
      v-if="Markerinfo.bizStatus == '楼宇'">
      <div>
        <p>
          <i class="el-icon-arrow-right"></i>
          租赁情况
        </p>
        <echarts-huan ref="rentalSituationhuan"
          :assetLeasingOverviewVO="infodata.assetLeasingOverviewVO"></echarts-huan>
      </div>
      <div>
        <p>
          <i class="el-icon-arrow-right"></i>
          入住类型
        </p>
        <echarts-list :assetOccupancyTypeVO="infodata.assetOccupancyTypeVO"></echarts-list>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator'
import echartsHuan from './rentalSituationhuan.vue'
import echartsList from './rentalSituationlist.vue'
import { assetInformationbuild, assetInformationHouse } from '@/api/assets'

@Component({
  name: 'Container',
  components: {
    echartsHuan,
    echartsList
  }
})
export default class Container extends Vue {
  @Prop() private Markerinfo!: any
  private loading = false
  private assetInfo: {} = {}
  private infodata: {
    assetInfo?: any
    assetProfile?: any
    leasingSituation?: any
    assetLeasingOverviewVO?: any
    assetOccupancyTypeVO?: any
  } = { assetInfo: {}, assetProfile: {}, assetLeasingOverviewVO: [], assetOccupancyTypeVO: [], leasingSituation: {} }
  @Watch('Markerinfo', { deep: true })
  private changidEvent() {
    // id更改之后的事件
    this.loading = true
    try {
      if (this.Markerinfo.bizStatus === '住宅') {
        this.getAssetInformationHouse()
      } else if (this.Markerinfo.bizStatus == '商铺') {
        this.getAssetInformationbuild()
      } else {
        this.getAssetInformationHouse()
      }
    } catch (e) {
      this.loading = false
    }
  }
  @Emit('changeShowType')
  private changeShowType() {
    return false
  }
  // 拉取接口远程数据（住宅）
  private async getAssetInformationHouse() {
    try {
      let res = await assetInformationHouse({ id: this.Markerinfo.id })
      if (res.success) {
        // this.option = res.data
        this.infodata.assetInfo = res.data.assetInfo
        this.infodata.assetProfile = res.data.assetProfile
        this.infodata.leasingSituation = res.data.leasingSituation
        this.loading = false
      }
    } catch (e) {
      this.loading = false
    }
  }
  // 拉取接口远程数据(楼宇)
  private async getAssetInformationbuild() {
    this.loading = true
    try {
      let res = await assetInformationHouse(this.Markerinfo)
      if (res.success) {
        // this.option = res.data
        this.infodata.assetInfo = res.data.assetInfo
        this.infodata.assetProfile = res.data.assetProfile
        this.infodata.assetOccupancyTypeVO = res.data.assetOccupancyTypeVO
        this.infodata.assetProfile = res.data.assetProfile
        this.loading = false
      }
    } catch (e) {
      this.loading = false
    }
  }
}
</script>

<style lang="scss" scoped>
.rentalSituation-box {
  display: flex;
  justify-content: space-evenly;
  div {
    width: 48%;
  }
}
.marker-header {
  font-size: 14px;
  line-height: 14px;

  span {
    color: #b43c3c;
    letter-spacing: 0;
    cursor: pointer;
  }
}
.marker-info {
  max-height: 80vh;
  overflow: scroll;
  // display: none;
  width: 400px;
  background-color: #fff;
  padding: 10px;
  .markerinfo-bottom-table {
    ::v-deep.el-descriptions-item__label {
      font-size: 12px !important;
      background: none;
      color: #303133;
      font-weight: 600;
    }
  }

  .el-carousel__item h3 {
    color: #475669;
    font-size: 14px;
    opacity: 0.75;
    line-height: 150px;
    margin: 0;
  }

  .el-carousel__item:nth-child(2n) {
    background-color: #99a9bf;
  }

  .el-carousel__item:nth-child(2n + 1) {
    background-color: #d3dce6;
  }
  .marker-info-header {
    flex-wrap: nowrap;
    display: flex;
    div:nth-child(1) {
      flex: 5;
    }
    div:nth-child(2) {
      flex: 2;
      height: 50px;
    }
    ::v-deep.el-carousel__button {
      width: 5px;
    }
    ::v-deep.el-descriptions-item__label {
      line-height: 18px;
    }
    ::v-deep.el-descriptions-item__content {
      line-height: 18px;
      color: #303133;
      font-weight: 600;
    }
  }
  .marker-info-h5 {
    border-left: 5px solid #99a9bf;
    padding-left: 8px;
    display: flex;
    justify-content: space-between;
  }
}
</style>
