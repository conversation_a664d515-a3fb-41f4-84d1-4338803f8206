/** 概述 */

<template>
  <div class="summarize">
    截止2022年度04月，{{ indexLabel }}为 <span class="value"> {{ bigNumberFormat(source.sum).value }}
    </span><span>{{ bigNumberFormat(source.sum).unit }}</span>，同比增长 <span
      :class="source.rate > 0 ? 'up' : 'down'">
      <img class="icon"
        v-if="+source.rate  > 0"
        src="../../images/thows.png" />
      <img class="icon"
        v-if="+source.rate  < 0"
        src="../../images/thowx.png" />
      {{ source.rate || '' }}%</span>，
    其中，<span class="company">{{ source.name }}</span><i></i>本年度增加 <span
      :class="source.companyRate > 0 ? 'up' : 'down'">
      <img class="icon"
        v-if="+source.companyRate  > 0"
        src="../../images/thows.png" />
      <img class="icon"
        v-if="+source.companyRate  < 0"
        src="../../images/thowx.png" />
      {{ source.companyRate || '' }}%</span>，<span>{{ indexLabel }}</span>为 <span
      class="value">{{ bigNumberFormat(source.companySum).value }}
    </span><span>{{  bigNumberFormat(source.companySum).unit || unitStr  }}</span>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { bigNumberFormat } from '@/utils'

@Component
export default class extends Vue {
  @Prop({ default: () => [] }) source!: Record<string, any>[]
  @Prop() private fontSize!: number
  @Prop() private indexLabel!: string
  @Prop() private unitStr!: string

  private data = {
    rate: 6.0,
    name: '城投集团',
    companyRate: 0.0,
    sum: '3430',
    companySum: '537.52'
  }

  get bigNumberFormat() {
    return (value: any) => {
      return this.unitStr === '%'
        ? {
            value,
            unit: this.unitStr
          }
        : bigNumberFormat(value)
    }
  }
}
</script>

<style scoped lang="scss">
.summarize {
  position: relative;
  width: 100%;
  // height: 110px;
  margin-bottom: 40px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 0 100px;
  font-size: 36px;
  flex-wrap: wrap;
  vertical-align: middle;
  word-break: break-all;
  .company {
    color: rgba(0, 204, 255, 1);
  }
  .value {
    color: rgba(255, 192, 22, 1);
    font-size: 58px;
    font-family: 'digital-7';
    margin: 0 4px;
  }
  .up {
    font-size: 58px;
    font-weight: bold;
    color: rgba(255, 72, 30, 1);
    font-family: 'digital-7';
    // color: rgba(255, 192, 22, 1);
    margin: 4px;
    img {
      width: 28px;
      margin-left: 6px;
    }
  }
  .down {
    font-size: 58px;
    font-weight: bold;
    color: rgba(72, 185, 28, 1);
    font-family: 'digital-7';
    // color: rgba(255, 192, 22, 1);
    margin: 4px;
    img {
      width: 28px;
      margin-left: 6px;
    }
  }
  &::after {
    content: '';
    height: 100%;
    opacity: 0.7;
    width: 100%;
    position: absolute;
    z-index: -1;
    top: 0;
    left: 0;
    border-radius: 400px/40px;
    background: linear-gradient(
      to right,
      rgba(22, 186, 255, 0.5),
      rgba(22, 186, 255, 0.6),
      rgba(64, 48, 218, 1),
      rgba(22, 186, 255, 0.6),
      rgba(22, 186, 255, 0.5)
    );
    filter: blur(20px);
  }
}
</style>