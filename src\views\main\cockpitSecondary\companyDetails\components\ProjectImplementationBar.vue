/** 项目执行概况 柱状图 */

/** 项目投资情况 */
<template>
  <div id="ProjectImplementationBar" />
</template>

<script lang='ts'>
import { Component, Prop, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { ProjectImplementationBarData } from '@/views/main/cockpitSecondary/baseData'
import { capitalProfile } from '@/api/cockpit'
import * as echarts from 'echarts'
import { deepClone } from '@/utils'

type EChartsOption = echarts.EChartsOption

@Component
export default class extends Vue {
  private loading = false
  private timer: any
  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}
  private seriesData: any[] = deepClone(ProjectImplementationBarData)
  private echartsDatas: any[] = []
  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('ProjectImplementationBar') as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    // this.initData()
    this.initEcharts()
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await capitalProfile()
    if (!Array.isArray(data) || !data.length) return
    this.echartsDatas = data
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 初始化数据
  private initEcharts() {
    let textSize = 16
    let series = this.seriesData
    this.option = {
      color: [
        {
          type: 'linear',
          x: 0,
          y: 1,
          x2: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: 'RGBA(40, 177, 255, 0)'
            },
            {
              offset: 0.4,
              color: 'RGBA(40, 177, 255, 0.3)'
            },
            {
              offset: 1,
              color: 'RGBA(40, 177, 255, 1)'
            }
          ]
        }
      ],
      legend: {
        show: true
      },
      grid: {
        left: '0%',
        right: '0%',
        top: '20%',
        bottom: '0%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 103, 177, 1)',
        borderColor: 'rgba(0, 103, 177, 1)',
        textStyle: {
          color: '#fff',
          fontSize: textSize
        }
      },
      xAxis: {
        type: 'category',
        axisLabel: {
          fontSize: textSize * 1.6,
          fontWeight: 'bold',
          color: '#5db0ea'
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(67, 154, 255, 1)'
          }
        },
        data: ['年度预算', '计划备案', '合同备案', '资金支付']
      },
      axisPointer: {
        show: false
      },
      yAxis: {
        show: true,
        type: 'value',
        axisLabel: {
          fontWeight: 'bold',
          color: '#5db0ea',
          fontSize: textSize * 1.2
        },
        axisLine: {
          lineStyle: {
            color: 'RGBA(147, 148, 149, 1)'
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0, 103, 177, 0.4)'
          }
        }
      },
      series: [
        {
          selectedMode: 'single',
          label: {
            show: true,
            position: 'top',
            fontWeight: 'bold',
            color: 'RGBA(40, 177, 255, 1)',
            fontSize: textSize * 2.2
          },
          itemStyle: {
            borderRadius: [textSize * 2, textSize * 2, 0, 0],
            shadowColor: 'RGBA(40, 177, 255, 0.6)',
            shadowBlur: 30
          },
          emphasis: {
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'RGBA(233, 60, 167, 0)'
                  },
                  {
                    offset: 0.4,
                    color: 'RGBA(233, 60, 167, 0.3)'
                  },
                  {
                    offset: 1,
                    color: 'RGBA(233, 60, 167, 1)'
                  }
                ]
              },
              shadowColor: 'RGBA(233, 60, 167, 0.6)',
              shadowBlur: 30
            },
            label: {
              fontSize: textSize * 2.2,
              color: 'RGBA(233, 60, 167, 1)'
            }
          },
          select: {
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'RGBA(233, 60, 167, 0)'
                  },
                  {
                    offset: 0.4,
                    color: 'RGBA(233, 60, 167, 0.3)'
                  },
                  {
                    offset: 1,
                    color: 'RGBA(233, 60, 167, 1)'
                  }
                ]
              },
              shadowColor: 'RGBA(233, 60, 167, 0.6)',
              shadowBlur: 30
            },
            label: {
              fontSize: textSize * 2.2,
              color: 'RGBA(233, 60, 167, 1)'
            }
          },
          showBackground: false,
          backgroundStyle: {
            color: '#081C5866',
            borderRadius: [textSize * 2, textSize * 2, 0, 0]
          },
          barWidth: textSize * 3.2,
          data: series,
          type: 'bar'
        }
      ]
    }

    this.myChart && this.myChart.setOption && this.myChart && this.myChart.setOption(this.option)
    this.change()
  }

  private change() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    this.timer = setInterval(() => {
      let index = Math.max(Math.floor(Math.random() * 4), 0)

      this.myChart &&
        this.myChart.dispatchAction({
          type: 'select',
          seriesIndex: 0,
          dataIndex: index
        })
    }, 5000)
  }

  destroyed() {
    clearInterval(this.timer)
  }
}
</script>

<style scoped lang="scss">
#ProjectImplementationBar {
  width: 100%;
  height: 100%;
}
</style>