/** 比率 */
<template>
  <section class="ratio">
    <div class="ratio__label">{{ ratioData.label }}</div>
    <div :class="['ratio__content', mode === 1 && 'ratio__content-mode1']">
      <div class="ratio__value">
        <span>{{ ratioData.value }}</span>
        <span>{{ ratioData.unit }}</span>
      </div>
      <div class="ratio__rate">
        <div class="rate__yoy">
          <span>同比</span>
          <span>
            <span :class="ratioData.YOY > 0 ? 'up' : 'down'"><img class="icon"
                v-if="+ratioData.YOY  > 0"
                src="../../../images/thows.png" />
              <img class="icon"
                v-if="+ratioData.YOY  < 0"
                src="../../../images/thowx.png" />{{ ratioData.YOY }}</span>
            <span>%</span>
          </span>
        </div>
        <div class="rate__qoq">
          <span>环比</span>
          <span>
            <span :class="ratioData.QOQ > 0 ? 'up' : 'down'"><img class="icon"
                v-if="+ratioData.QOQ  > 0"
                src="../../../images/thows.png" />
              <img class="icon"
                v-if="+ratioData.QOQ  < 0"
                src="../../../images/thowx.png" />{{ ratioData.QOQ }}</span>
            <span>%</span>
          </span>
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'

export interface RatioData {
  label: string
  // 值
  value: string
  // 同比
  YOY: number
  // 环比
  QOQ: number
  // 单位
  unit: string
}

@Component
export default class extends Vue {
  @Prop() private ratioData!: RatioData
  @Prop({ default: 0 }) private mode!: number
}
</script>


<style scoped lang="scss">
.ratio {
  position: relative;
  width: 100%;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  &__label {
    position: absolute;
    top: 10px;
    left: 10px;
    font-size: 40px;
    color: #fff;
  }
  &__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 74%;
    margin-top: 45px;
    &-mode1 {
      flex-direction: row;
      width: 100%;
      .ratio__value {
        width: 290px;
      }
      .ratio__rate {
        // flex-direction: column;
        & > div {
          display: flex;
          flex-direction: column;
          align-items: center;
          position: relative;
          & > span:first-child {
            position: absolute;
            top: -50px;
          }
          & > span {
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }
  &__value {
    display: flex;
    align-items: flex-end;
    color: rgba(255, 192, 22, 1);
    span:first-child {
      font-size: 48px;
      line-height: 48px;
      font-weight: bold;
      font-family: 'FZZZHONGHJW';
    }
    span:last-child {
      font-size: 40px;
      line-height: 40px;
      margin-left: 2px;
    }
  }
  &__rate {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    font-size: 32px;

    .up {
      font-size: 42px;
      font-weight: bold;
      color: rgba(255, 72, 30, 1);
      font-family: 'FZZZHONGHJW';
      // color: rgba(255, 192, 22, 1);
      margin: 4px;
      display: flex;
      align-items: center;
      img {
        width: 28px;
        height: 36px;
        margin-left: 6px;
        margin-right: 5px;
      }
    }
    .down {
      font-size: 58px;
      font-weight: bold;
      color: rgba(72, 185, 28, 1);
      font-family: 'FZZZHONGHJW';
      // color: rgba(255, 192, 22, 1);
      margin: 4px;
      display: flex;
      align-items: center;
      img {
        width: 28px;
        height: 36px;
        margin-left: 6px;
        margin-right: 5px;
      }
    }
    & > div {
      margin: 10px;
      display: flex;
      span {
        white-space: nowrap;
      }
      & > span {
        margin-left: 12px;
        display: flex;
        align-items: center;

        span {
          color: rgba(0, 204, 255, 1);
        }
        span:first-child {
          font-size: 42px;
          font-weight: bold;
          font-family: 'FZZZHONGHJW';
        }
      }
    }
  }
}
</style>