/* 项目管理 */

import Layout from '@/layouts/public/index.vue'

export const routes = [
  {
    path: '/projectinvestment',
    name: '项目投资监管',
    component: Layout,
    redirect: '/projectinvestment/records',
    children: [
      {
        path: 'overview',
        name: '项目投资概况',
        component: () => import('@/views/projectInvestment/Overview/index.vue')
      },
      {
        path: 'records',
        name: '投资项目计划备案',
        component: () => import('@/views/projectInvestment/Records/index.vue')
      },
      {
        path: 'adjustment',
        name: '投资项目计划调整备案',
        component: () => import('@/views/projectInvestment/RecordsAdjustment/index.vue')
      },
      {
        path: 'audit',
        name: '投资项目审批',
        component: () => import('@/views/projectInvestment/Audit/index.vue')
      },
      {
        path: 'propress',
        name: '投资项目进度报告',
        component: () => import('@/views/projectInvestment/Propress/index.vue')
      }
    ]
  }
]

export default routes
