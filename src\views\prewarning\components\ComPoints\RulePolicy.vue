<template>
  <section v-loading="loading"
    class="rule-policy-wrap">
    <div v-if="type !== 'see'"
      class="head-rule-box">
      <template v-if="ruleForm.domains.length > 1">
        <div v-for="(item,index) of ruleForm.domains"
          :key="index"
          class="mode">
          <span class="name">条件{{index+1}}</span>
          <el-select v-if="index < ruleForm.domains.length - 1"
            v-model="item.connectSymbol"
            placeholder="请选择"
            :readonly="disabled"
            class="input-mini m-r-10">
            <el-option v-for="item in dictConnectSymbol"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
      </template>
      <el-button v-if="type !=='see'"
        class="tel-btn"
        @click="validateForm('add')">添加规则</el-button>
    </div>

    <el-form :model="ruleForm"
      ref="ruleForm"
      label-width="0"
      class="form-content">
      <div v-for="(domains, index) of ruleForm.domains"
        :key="index"
        class="mode-box">
        <span class="name">条件{{index+1}}</span>
        <el-form-item :prop="'domains.' + index + '.metricIdList'"
          :rules="{
            required: true, message: '请选择指标', trigger: 'change'
          }">
          <el-cascader v-model="domains.metricIdList"
            :props="{
              label: 'name',
              value: 'id',
              children: 'children',
              expandTrigger: 'hover'
            }"
            clearable
            :readonly="disabled"
            :show-all-levels="false"
            :options="filterTmetricData"
            placeholder="选择指标"
            class="input-default"
            @change="changeMetricId(domains)" />
        </el-form-item>

        <el-form-item :prop="'domains.' + index + '.operatorSymbol'"
          :rules="{
            required: false, message: '请选择运算符', trigger: 'change'
          }">
          <el-select v-model="domains.operatorSymbol"
            clearable
            :readonly="disabled"
            placeholder="运算符"
            @change="changePperatorSymbol(domains)">
            <el-option v-for="item of getDictData('ewmOperatorSymbol')"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item v-if="domains.operatorSymbol != '0'"
          :prop="'domains.' + index + '.thresholdValue'"
          :rules="{
            required: false, message: '请输入指标值', trigger: 'blur'
          }">
          <InputNumber v-model.trim="domains.thresholdValue"
            clearable
            placeholder="指标值"
            type="decimalZero" />
        </el-form-item>

        <el-form-item>
          <span class="unit">{{domains.unitName||'--'}}</span>
        </el-form-item>

        <el-form-item v-if="type !== 'see'">
          <i class="delete el-icon-delete"
            title="删除规则"
            @click="deleteRule(index)" />
        </el-form-item>
      </div>
    </el-form>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { ElForm } from 'element-ui/types/form'
import { BusinessModule } from '@/store/modules/businessDict'
import InputNumber from '@/components/FormComment/inputNumber.vue'

export interface RulePollcy {
  ruleId?: string
  operatorSymbol: string
  connectSymbol: string
  metricIdList: number[]
  thresholdValue: string
  unitCode: string
  unitName: string
}

@Component({
  components: {
    InputNumber
  }
})
export default class extends Vue {
  @Prop() private value!: any[] // v-model 绑定的数据
  @Prop() private tmetricDicData!: any[] // 指标数据 绑定的数据
  @Prop() private metricsId?: string // 预警分类
  @Prop({ default: false }) private disabled?: boolean // 是否可编辑
  @Prop({
    validator: (value: string) => {
      return ['add', 'edit', 'see'].indexOf(value) !== -1
    }
  })
  readonly type!: string // 组件类型(新增、编辑、查看)

  // 常量数据
  private dictConnectSymbol = Object.freeze([
    {
      label: '且',
      value: '1'
    },
    {
      label: '或',
      value: '2'
    }
  ])

  private loading = false
  private isChangeMetricsId = false
  private filterTmetricData: any[] = []
  private ruleForm: any = {
    domains: []
  }

  // 监听 v-model 绑定值变化，然后重新赋值
  @Watch('value', { immediate: false, deep: true })
  changeVisible() {
    this.ruleForm.domains = this.value
  }

  // 预警分类改变时
  @Watch('metricsId', { immediate: false, deep: true })
  changeMetricsId() {
    if (this.type !== 'see' && this.isChangeMetricsId) this.ruleForm.domains = []
    this.isChangeMetricsId = true

    let find: any = this.tmetricDicData.filter((item: { id: string }) => {
      return +(this.metricsId as string) === +item.id
    })

    this.filterTmetricData = find
  }

  // 指标数据请求接口获取到数据后，需要重新渲染指标组件，保证指标选择组件不出错
  @Watch('tmetricDicData', { immediate: false, deep: true })
  changeTmetricDicData() {
    let find: any = this.tmetricDicData.filter((item: { id: string }) => {
      return +(this.metricsId as string) === +item.id
    })

    this.filterTmetricData = find
  }

  // 获取字典数据
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }

  // 指标改变时，设置对应的单位值
  private changeMetricId(domains: RulePollcy) {
    let ids = domains.metricIdList

    if (Array.isArray(ids) && ids.length) {
      let ids1 = ids[0]
      let ids2 = ids[1]

      let find: any = this.tmetricDicData.find((item: { id: number }) => {
        return +ids1 === +item.id
      })

      if (Array.isArray(find.children) && find.children.length) {
        let dictFind = find.children.find((itemDict: { id: number }) => {
          return +ids2 === +itemDict.id
        })

        domains.unitCode = dictFind.unitCode || ''
        domains.unitName = dictFind.unitName || ''
      }
    }
  }

  // 校验必填数据
  public validateForm(type: 'add' | 'save') {
    if (!this.metricsId) {
      this.$message.warning('请先选择“预警分类”')
      return
    }

    let isValidate = true
    switch (type) {
      case 'add':
        ;(this.$refs.ruleForm as ElForm).validate((valid: boolean) => {
          if (valid) {
            this.addRules()
            isValidate = true
          } else {
            isValidate = false
          }
        })
        break
      case 'save':
        ;(this.$refs.ruleForm as ElForm).validate((valid: boolean) => {
          if (valid) {
            this.submitForm()
            isValidate = true
          } else {
            isValidate = false
          }
        })
        break
    }

    return isValidate
  }

  // 添加规则
  private addRules() {
    let ruleItem: RulePollcy = {
      connectSymbol: '1',
      operatorSymbol: '',
      metricIdList: [],
      thresholdValue: '',
      unitCode: '',
      unitName: ''
    }

    this.ruleForm.domains.push(ruleItem)
    this.$emit('input', this.ruleForm.domains)
  }

  // 删除规则
  private deleteRule(index: number) {
    this.ruleForm.domains.splice(index, 1)
    this.$emit('input', this.ruleForm.domains)
  }

  // 运算符改变时，如果为“无”则要清空对应的“指标值”
  private changePperatorSymbol(item: any) {
    if (!+item.operatorSymbol) {
      item.thresholdValue = ''
    }
  }

  // 提交数据
  private async submitForm() {
    return true
  }
}
</script>

<style scoped lang="scss">
.rule-policy-wrap {
  $radius: 3px;
  $color: #b43c3c;

  position: relative;

  ::v-deep .el-form {
    .mode-box {
      .el-form-item:nth-last-child(1) {
        margin-right: 0 !important;
      }
    }
    .el-form-item {
      margin-right: 10px !important;
    }
  }

  .head-rule-box {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-bottom: 20px;
    padding: 13px;
    background: #f5f7fa;
    border-radius: $radius;
    .mode {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
    }
    .name {
      width: 67px;
      text-align: right;
      padding-right: 12px;
      box-sizing: border-box;
    }
  }

  .mode-box {
    display: flex;
    .name {
      width: 80px;
      height: 32px;
      line-height: 32px;
      text-align: right;
      padding-right: 12px;
      box-sizing: border-box;
    }
    .unit {
      display: inline-block;
      width: 20px;
    }
    .delete {
      color: $color;
      cursor: pointer;
      margin-left: 20px;
    }
  }

  .tel-btn {
    color: $color;
    letter-spacing: 0;
    border: 1px solid $color;
    border-radius: 2px;
    text-align: center;
  }

  .m-r-10 {
    margin-right: 10px;
  }

  .input-default {
    width: 170px;
  }

  .input-mini {
    width: 118px;
  }
}
</style>