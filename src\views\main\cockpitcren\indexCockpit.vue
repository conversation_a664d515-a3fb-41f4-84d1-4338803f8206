/* 驾驶舱数据面板 */

<template>
  <section class="cockpit-panel-wrapper"
    ref="wrapper">
    <div class="cockpit-panel"
      :style="{
      width: `${designWidth}px`,
      height: `${designHeight}px`,
      transform: `scale(${rate}, ${rate}) translate(-50%, -50%) !important`}">
      <!-- 时间选择  -->
      <TimeSelection />

      <!-- 内容区域 -->
      <div class="panel-content">
        <!-- 星空背景 -->
        <!-- <StarrySky /> -->

        <!-- 头部 -->
        <TitlePanel class="header-box cockipt-approach-header"
          @inWindowFullScreen="inWindowFullScreen" />

        <!-- 内容 -->
        <div class="content-box">
          <div class="mode mode-left">
            <Performance class="mode-com cockipt-approach-left-top" />
            <RealEstateprises class="mode-com cockipt-approach-left-bottom" />
          </div>
          <div class="mode mode-middel">
            <ManagementTree class="mode-com cockipt-approach-middel-top p-0" />
            <PersonInformation class="mode-com cockipt-approach-middel-bottom" />
          </div>
          <div class="mode mode-right">
            <ComprehenEvaluation class="mode-com cockipt-approach-right-top" />
            <EnterOperations class="mode-com cockipt-approach-right-bottom" />
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators/index'
import { debounce, windowFullScreen, isWindowFull } from '@/utils'
import StarrySky from '@/views/main/cockpitcren/components/Public/StarrySky.vue'
import TitlePanel from '@/views/main/cockpitcren/components/Public/TitlePanel.vue'
import TimeSelection from '@/views/main/cockpitcren/components/Public/TimeSelection.vue'
import ManagementTree from '@/views/main/cockpitcren/components/TwoPage/ManagementTree.vue'
import Performance from '@/views/main/cockpitcren/components/TwoPage/Performance.vue'
import EnterOperations from '@/views/main/cockpitcren/components/TwoPage/EnterOperations.vue'
import PersonInformation from '@/views/main/cockpitcren/components/TwoPage/PersonInformation.vue'
import ComprehenEvaluation from '@/views/main/cockpitcren/components/TwoPage/ComprehenEvaluation.vue'
import RealEstateprises from '@/views/main/cockpitcren/components/TwoPage/RealEstateprises.vue'

@Component({
  components: {
    StarrySky,
    TitlePanel,
    TimeSelection,
    ManagementTree,
    Performance,
    EnterOperations,
    PersonInformation,
    ComprehenEvaluation,
    RealEstateprises
  }
})
export default class extends Vue {
  [x: string]: any
  // 比例
  private rate = 1

  // 设计图大屏宽高
  private designWidth = 3240
  private designHeight = 1824

  // 组件数据
  private loading = false

  // 判断窗口是否处于全屏模式
  get getIsWindowFull() {
    return isWindowFull
  }

  // 组件初始化
  private mounted() {
    this.getContentWidth()

    // 页面大小改变
    window.onresize = debounce(() => {
      this.$bus.$emit('BusWindowFullScreen', this.getIsWindowFull())
      setTimeout(() => {
        this.getContentWidth()
      }, 100)
    })
  }

  // 获取内容宽度, 保证宽高比相等, 并设置
  @Loading('loading')
  private getContentWidth() {
    let contentDom = this.$refs['wrapper'] as HTMLDivElement
    let contentHeight = contentDom.clientHeight || 0
    let contentWidth = contentDom.clientWidth || 0

    // 已设计稿的宽高比做基准，用十字计算法，获取当前页面高度下应该对应的页面宽度
    let panelWidth = (this.designWidth * contentHeight) / this.designHeight

    // 用十字计算法，计算缩放比例（注意：区分计算宽度的基准值）
    if (panelWidth > contentWidth) {
      this.rate = contentWidth / this.designWidth
    } else {
      this.rate = panelWidth / this.designWidth
    }
  }

  // 进入全屏
  private inWindowFullScreen() {
    return windowFullScreen(this.$refs['wrapper'] as HTMLElement)
  }
}
</script>

<style scoped lang="scss">
$sliderWidth: 860px;
$textColor: #63f1ff;

.cockpit-panel-wrapper {
  position: relative;
  color: #fff;
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-size: 30px;
  print-color-adjust: exact;
  -webkit-print-color-adjust: exact;
  font-weight: bold !important;
  background: var(--cockpit-wrapper-bg);
}

.cockpit-panel {
  position: relative;
  top: 50%;
  left: 50%;
  transition: all 0.3s;
  overflow: hidden;
  transform-origin: 0 0;
  user-select: none;
  box-sizing: border-box;

  div {
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }
}

.panel-content {
  $ModeHeight: 135px;
  background: #021e5d;

  position: relative;
  height: 100%;
  overflow: hidden;
  .header-box {
    height: $ModeHeight;
    margin-bottom: 40px;
  }

  .content-box {
    display: flex;
    justify-content: space-between;
    height: calc(100% - 190px);

    .mode {
      position: relative;
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      margin: 0 10px;
      box-sizing: border-box;
    }
    .mode-com {
      padding: 50px 60px;
      box-sizing: border-box;
    }
    .mode-left {
      margin-left: 40px;
    }
    .mode-left,
    .mode-right {
      width: $sliderWidth;
    }
    .mode-middel {
      flex: 1;
      margin: 0;
    }
  }
}
</style>