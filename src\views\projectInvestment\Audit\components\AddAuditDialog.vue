/**
  组件描述:  新增年度计划投资弹框
*/
<template>
  <Dialog title="投资项目审批-新增"
    width="980px"
    :visible="visible"
    :close-on-click-modal="true"
    :destroy-on-close="true"
    :append-to-body="true"
    @close="closeDlg">
    <div slot="body"
      class>
      <el-form ref="AuditForm"
        :rules="auditFormRules"
        :model="auditForm"
        inline
        label-width="140px">
        <el-form-item label="项目名称"
          prop="projectName">
          <el-autocomplete v-model="auditForm.projectName"
            :fetch-suggestions="featchProject"
            value-key="projectName"
            placeholder="请输入项目名称"
            @select="handleSelect"
            @input="inputText"></el-autocomplete>
        </el-form-item>
        <el-form-item label="项目编号"
          prop="projectCode">
          <el-input placeholder="系统自动生成"
            disabled
            v-model="auditForm.projectCode" />
        </el-form-item>

        <!-- <el-form-item label="编制单位" prop="preparationUnit">
          <el-input placeholder disabled v-model="auditForm.preparationUnit" />
        </el-form-item> -->

        <!-- 项目投资主体 -->
        <el-form-item label="投资主体"
          prop="orgPath">
          <el-cascader v-model="auditForm.orgPath"
            :props="{label:'title',value:'value'}"
            placeholder="请选择投资主体"
            :options="compTree"
            filterable></el-cascader>
        </el-form-item>
        <!-- <el-form-item label="投资主体"
          prop="investSubject">
          <el-select v-model="auditForm.investSubject"
            placeholder="请选择">
            <el-option v-for="item in companyList"
              :key="item.code"
              :label="item.name"
              :value="item.code">
            </el-option>
          </el-select>
        </el-form-item>-->

        <!-- <el-form-item label="级次" prop="level">
          <el-select v-model="auditForm.level" placeholder="请选择">
            <el-option
              v-for="item in levelList"
              :key="item.value"
              :label="item.label"
              :value="item.label"
            ></el-option>
          </el-select>
        </el-form-item> -->
        <br>
        <el-form-item label="开工情况"
          prop="workStatus">
          <el-select v-model="auditForm.workStatus"
            placeholder="请选择">
            <el-option v-for="item in investCategoryList"
              :key="item.value"
              :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <br />
        <el-form-item label="项目性质"
          prop="projectProperty">
          <el-select v-model="auditForm.projectProperty"
            placeholder="请选择类型">
            <el-option v-for="(item, index) in getDictData('invest_project_property')"
              :key="index"
              :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目类型"
          prop="projectType">
          <el-select v-model="auditForm.projectType"
            placeholder="请选择项目类型">
            <el-option v-for="(item, index) in getDictData('invest_project_type')"
              :key="index"
              :label="item.label"
              :value="item.value"></el-option>
          </el-select>
          <!-- <el-input v-model="Form.projectContent" /> -->
        </el-form-item>
        <el-form-item label="项目分类"
          prop="projectCategory">
          <el-select v-model="auditForm.projectCategory"
            placeholder="请选择项目分类">
            <el-option v-for="(item, index) in getDictData('invest_project_category')"
              :key="index"
              :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-col :span="12">
          <el-form-item label="建设起止年限"
            style="width:100%"
            prop="projectDate">
            <el-date-picker style="width:100%"
              v-model="auditForm.projectDate"
              type="monthrange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy 年 MM 月 "
              value-format="yyyy.MM"
              align="right"></el-date-picker>
          </el-form-item>
        </el-col>

        <el-form-item label="年初是否已列入投资计划"
          prop="isPlan">
          <el-radio-group v-model="auditForm.isPlan"
            disabled>
            <el-radio-button :label="1">是</el-radio-button>
            <el-radio-button :label="2">否</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="是否主体投资"
          prop="isMainInvest">
          <el-radio-group v-model="auditForm.isMainInvest">
            <el-radio-button :label="1">是</el-radio-button>
            <el-radio-button :label="2">否</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="是否境外投资"
          prop="isOverInvest">
          <el-radio-group v-model="auditForm.isOverInvest">
            <el-radio-button :label="1">是</el-radio-button>
            <el-radio-button :label="2">否</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="总投资额"
          prop="whetherAmt">
          <InputNumber v-model="auditForm.whetherAmt"
            clearable
            type="decimalZero"
            placeholder="请输入">
            <template slot="append">万元</template>
          </InputNumber>
        </el-form-item>

        <el-form-item label="预计投入自有资金"
          prop="investAmount">
          <InputNumber v-model="auditForm.investAmount"
            clearable
            type="decimalZero"
            placeholder="请输入">
            <template slot="append">万元</template>
          </InputNumber>
        </el-form-item>

        <!-- <el-form-item label="预计投资期限" prop="expectedInvestPeriod">
          <InputNumber
            v-model="auditForm.expectedInvestPeriod"
            clearable
            type="decimal"
            placeholder="请输入"
          >
            <template slot="append">年</template>
          </InputNumber>
        </el-form-item> -->

        <el-form-item label="预期投资回报率"
          prop="expectedRoi">
          <InputNumber v-model="auditForm.expectedRoi"
            clearable
            type="decimal"
            placeholder="请输入">
            <template slot="append">%</template>
          </InputNumber>
        </el-form-item>

        <el-form-item label="本项目投资前公司资产负债率"
          prop="assetRatio">
          <InputNumber v-model="auditForm.assetRatio"
            clearable
            type="decimal"
            placeholder="请输入">
            <template slot="append">%</template>
          </InputNumber>
        </el-form-item>

        <el-form-item label="本项目投资后预计公司资产负债率"
          prop="afterAssetRatio">
          <InputNumber v-model="auditForm.afterAssetRatio"
            clearable
            type="decimal"
            placeholder="请输入">
            <template slot="append">%</template>
          </InputNumber>
        </el-form-item>

        <el-form-item label="本项目投资前公司非主业投资比例"
          prop="afterInvestmentRatio">
          <InputNumber v-model="auditForm.afterInvestmentRatio"
            clearable
            type="decimal"
            placeholder="请输入">
            <template slot="append">%</template>
          </InputNumber>
        </el-form-item>

        <el-form-item label="本项目投资后预计公司非主业投资比例"
          prop="beforeInvestmentRatio">
          <InputNumber v-model="auditForm.beforeInvestmentRatio"
            clearable
            type="decimal"
            placeholder="请输入">
            <template slot="append">%</template>
          </InputNumber>
        </el-form-item>

        <div class="remark m-t-12">
          <el-form-item label="项目地址"
            prop="projectAddress">
            <el-input v-model="auditForm.projectAddress"
              type="textarea"
              maxlength="100"
              placeholder="项目地址" />
          </el-form-item>
        </div>
        <div class="remark m-t-12">
          <el-form-item label="备注"
            prop="remark">
            <el-input v-model="auditForm.remark"
              type="textarea"
              maxlength="300"
              show-word-limit
              placeholder="备注" />
          </el-form-item>
        </div>
      </el-form>

      <AccessoryList v-model="auditForm.fileList"
        dict="invest_project_file_type"
        mode="upload"
        class="m-20" />
    </div>
    <div slot="footer">
      <el-button @click="closeDlg">取消</el-button>
      <el-button class="primary-buttom"
        type="primary"
        :loading="loading"
        @click="submitForm">新增</el-button>
    </div>
  </Dialog>
</template>
<script lang="ts">
import { Component, Emit, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import { Loading } from '@/decorators'
import { ElForm } from 'node_modules/element-ui/types/form'
import Uploader from '../../components/Uploader/index.vue'
import { createProjectReview, ProjectReview, selectProjectName } from '@/api/projectInvestment'
import { companyList, investCategoryList, levelList } from '../../baseData'
import InputNumber from '@/components/FormComment/inputNumber.vue'
import AccessoryList, { Accessory } from '@/views/assets/components/TradeDialog/components/AccessoryFileList.vue'
import { getCompTree } from '@/api/projectInvestment'
import { investPLanInfo } from '@/api/projectInvestment'
import { BusinessModule } from '@/store/modules/businessDict'
@Component({
  components: {
    Dialog,
    Uploader,
    InputNumber,
    AccessoryList
  }
})
export default class extends Vue {
  @Prop() private visible!: boolean
  @Prop() private title!: string

  private loading = false
  private selectInput = '' // 筛选项目名称，用于判断是否是输入的资产名称
  private companyList = companyList
  private levelList = levelList
  private investCategoryList = this.getDictData('invest_project')

  private auditForm: any = {
    orgId: '',
    projectName: '',
    afterInvestmentRatio: '',
    approvalStatus: '',
    assetRatio: '',
    auditCompletionTime: '',
    beforeInvestmentRatio: '',
    createTime: '',
    createUser: '',
    expectedInvestPeriod: '',
    expectedRoi: '',
    investAmount: '',
    workStatus: '',
    projectAddress: '',
    investSubject: '',
    isMainInvest: '',
    isOverInvest: '',
    isPlan: '2',
    level: '',
    preparationUnit: '',
    projectCode: '',
    remark: '',
    whetherAmt: '',
    reviewFeedback: '',
    decisionFile: '',
    feasibilityFile: '',
    investAdviceFile: '',
    riskFile: '',
    legalFile: '',
    necessaryFile: '',
    fileList: [],
    projectProperty: ''
  }

  private auditFormRules = {
    projectCategory: [{ required: true, trigger: 'blur', message: '请输入本项目投资后预计公司资产负债率' }],
    projectType: [{ required: true, trigger: 'blur', message: '请输入本项目投资后预计公司资产负债率' }],
    projectProperty: [{ required: true, trigger: 'blur', message: '请输入本项目投资后预计公司资产负债率' }],
    projectDate: [{ required: true, trigger: 'blur', message: '请输入本项目投资后预计公司资产负债率' }],
    afterAssetRatio: [{ required: true, trigger: 'blur', message: '请输入本项目投资后预计公司资产负债率' }],
    projectName: [{ required: true, trigger: 'change', message: '请输入项目名称' }],
    projectCode: [{ required: false, trigger: 'blur', message: '请选择项目' }],
    afterInvestmentRatio: [{ required: true, trigger: 'blur', message: '请输入本项目投资后预计公司资产负债率' }],
    assetRatio: [{ required: true, trigger: 'blur', message: '请输入本项目投资前公司资产负债率' }],
    beforeInvestmentRatio: [{ required: true, trigger: 'blur', message: '请输入本项目投资前公司非主业投资比例' }],
    expectedInvestPeriod: [{ required: true, trigger: 'blur', message: '请输入预计投资期限' }],
    expectedRoi: [{ required: true, trigger: 'blur', message: '请输入预期投资回报率' }],
    investAmount: [{ required: true, trigger: 'blur', message: '请输入预计投入自有资金' }],
    workStatus: [{ required: true, trigger: 'blur', message: '请选择投资类别' }],
    investSubject: [{ required: true, trigger: 'blur', message: '请选择投资主体' }],
    isMainInvest: [{ required: true, trigger: 'blur', message: '请选择是否主体投资' }],
    isOverInvest: [{ required: true, trigger: 'blur', message: '请选择是否境外投资' }],
    isPlan: [{ required: true, trigger: 'blur', message: '请选择年初是否已列入投资计划' }],
    level: [{ required: true, trigger: 'blur', message: '请选择次级' }],
    // preparationUnit: [{ required: true, trigger: 'blur', message: '请选择编制单位' }],
    whetherAmt: [{ required: true, trigger: 'blur', message: '请输入总投资额' }]
  }
  private compTree = []
  // 打开时获取数据
  private created() {
    this.getCompTree()
  }
  //获取机构数组
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  private async getCompTree() {
    let options = []
    let res = await getCompTree({})
    if (res.success) {
      options = res.data
    }
    this.compTree = options
  }
  // 弹窗关闭
  @Emit('close')
  private onClose() {
    //
  }

  @Loading('loading')
  private async submitForm() {
    // 基础信息校验
    ;(this.$refs.AuditForm as ElForm).validate((valid: boolean) => {
      if (!valid) return
      if (!this.validateFiles()) return
      this.onSave()
    })
  }
  private inputText(text: any) {
    if (text != this.selectInput) {
      this.auditForm.projectCode = ''
    }
  }
  // 编制单位
  get preparationUnit() {
    return ''
  }

  // 集团code
  get companyCode() {
    return '1'
  }

  private onCompanyChange() {
    //
  }

  // 保存
  private async onSave() {
    if (this.auditForm.projectDate.length == 2) {
      let time = this.auditForm.projectDate[0] + '-' + this.auditForm.projectDate[1]
      this.auditForm.projectDate = time
    }
    //  处理计划备案明细id
    this.auditForm.planDetailId = this.auditForm.id
    //处理id为空
    this.auditForm.id = ''
    let res = await createProjectReview(this.auditForm)
    if (res.success) {
      this.closeDlg()
      this.$message.success(res.msg || '保存成功!')
      this.$emit('success')
    }
  }

  // 搜索项目
  private async featchProject(queryString: string, cb: any) {
    let res = await selectProjectName({ keyword: queryString })
    if (res.success) {
      await cb(res.data)
    }
  }

  // 选择项目
  private handleSelect(project: any) {
    let auditForm = this.$refs.AuditForm as ElForm
    // auditForm.clearValidate('projectName')
    // auditForm.clearValidate('projectCode')
    // this.auditForm.projectCode = project.projectCode
    this.auditForm = Object.assign(this.auditForm, project)
    this.selectInput = project.projectName
    try {
      // 对数据处理预处理
      let time = this.auditForm.projectDate.split('-') || []
      if (time.length == 1) {
        this.auditForm.projectDate = []
      } else {
        this.auditForm.projectDate = time
      }
      // 点击之后改变计划状态
      this.auditForm.isPlan = '1'
    } catch (e) {
      //
    }
  }

  // 附件校验
  private validateFiles(): boolean {
    return true
  }

  // 关闭弹窗
  private closeDlg() {
    this.$emit('update:visible', false)
    this.onClose()
  }
}
</script>

<style lang="scss" scoped>
.uploader-list {
  display: flex;
  flex-wrap: wrap;
  ::v-deep .uploader-wrapper {
    margin: 12px 24px;
  }
}
.remark {
  width: 100%;
  ::v-deep .el-form-item__content {
    width: 100% !important;
    padding: 0 20px;
  }
}

::v-deep .el-input-group {
  .el-input__inner {
    width: 108px !important;
  }
}
::v-deep .el-radio-button__orig-radio:disabled:checked + .el-radio-button__inner {
  background: #ce4c4c !important;
  color: #fff;
}
::v-deep .el-form {
  display: flex;
  flex-wrap: wrap;
  .el-input-number--small {
    width: 160px;
  }
  .el-form-item {
    display: flex;
    align-items: center;
    .el-form-item__content {
      width: 160px;
    }
  }
  .el-input__inner {
    width: 160px;
  }
  .el-form-item__label {
    line-height: 20px;
  }
}
</style>

