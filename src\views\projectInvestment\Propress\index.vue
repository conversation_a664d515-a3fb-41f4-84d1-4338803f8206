<!-- 投资项目进度报告 -->
<template>
  <el-container class="propress"
    direction="vertical">

    <search-bar :items="searchItems"
      v-if="mode=='index'"
      :tabChange="true"
      @onSearch="handleSearch">
      <el-button v-if="isCompany"
        class="primary-buttom"
        type="primary"
        @click="onImport">导入</el-button>
    </search-bar>

    <!-- 表格 -->
    <Grid ref="grid"
      :columns="cols"
      :show-pagination="true"
      :overflow-tooltip="true"
      :remote-url="remoteUrl"
      :search-params="searchParams"
      @loadCompleted="loadCompleted">
      <template #currentProgressSlot="{row}">
        {{row.currentProgress}}
      </template>
      <template #operationSlot="{row}"
        v-if="mode=='index'">
        <Operation :list="operationList"
          :row="row" />
      </template>
    </Grid>

    <ImportForm v-if="uploaderDlgVisible"
      :visible.sync="uploaderDlgVisible"
      :projectProperty="searchParams.projectProperty"
      @success="onImportSuccess" />

    <MonthTable v-if="monthTableVisible"
      :visible.sync="monthTableVisible"
      :list="currentMonthTable" />

  </el-container>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import Grid from '@/components/Grid/index.vue'
import searchBar from '@/components/SearchBar/index.vue'
import { deleteProjectProgressReport } from '@/api/projectInvestment'
import Operation, { OperationItem } from '@/components/Operation/index.vue'
import MonthTable from './components/MonthTable.vue'
import ImportForm from './components/ImportDialog.vue'
import { Confirm } from '@/decorators'

export interface ProPressDetail {
  annualCompletedInvestment: string //本年已完成投资额
  annualCompletionRate: string //年度计划投资完成率
  annualPlannedInvestment: string //本年度计划投资额
  annualProgressTarget: string //年度进度目标
  cumulativeCompletedInvestment: string //累计完成投资额
  currentProgress: string //目前主要形象进度
  month: string //月份
  monthlyCompletedInvestment: string //本月完成投资额
  nextMonthPlan: string //下月计划安排
  orgName: string //公司名称
  problemDifficult: string //存在问题及困难
  projectCode: string //项目编号
  projectContent: string //建设规模及主要内容
  projectFullName: string //项目名称
  projectCategory: string //项目性质
  remark: string //备注
  startAndEnd: string //建设起止年限
  totalInvestment: string //总投资额
  workStatus: string //开工情况
  year: string // 年份
}

@Component({
  components: {
    Grid,
    searchBar,
    Operation,
    MonthTable,
    ImportForm
  }
})
export default class Project extends Vue {
  @Prop({ default: () => 'index' }) private mode?: 'index' | 'audit'
  @Prop() private option?: any
  private remoteUrl = '/fht-monitor/invest/progress/page'

  // 导入
  private uploaderDlgVisible = false
  private monthTableVisible = false
  private importFiles: string[] = []

  private monthTableMap: Record<string, ProPressDetail[]> = {}
  private currentMonthTable: ProPressDetail[] = []

  private projectCategoryList = [
    {
      label: '企业',
      value: 'QY',
      selected: true
    },
    {
      label: '政府',
      value: 'ZF'
    },
    {
      label: '代建',
      value: 'DJ'
    }
  ]

  private searchItems = [
    {
      type: 'tab',
      key: 'projectProperty',
      options: this.projectCategoryList
    },
    {
      type: 'text',
      key: 'keyword',
      placeholder: '请输入关键词查询',
      width: '190px'
    },
    {
      type: 'year',
      label: '年度',
      key: 'year',
      placeholder: ''
    }
  ]

  private searchParams = {
    projectProperty: 'QY'
  }

  // 企业
  private companyCols = [
    {
      prop: 'year',
      label: '年份'
    },
    {
      prop: 'month',
      label: '月份'
    },
    {
      prop: 'orgName',
      label: '公司名称'
    },
    {
      prop: 'projectCode',
      label: '项目编号',
      minWidth: 200
    },
    {
      prop: 'projectName',
      label: '项目名称',
      minWidth: 200
    },

    {
      prop: 'annualPlannedInvestment',
      label: '本年度计划投资额（万）',
      labelAlign: 'right',
      minWidth: 160
    },
    {
      prop: 'monthlyCompletedInvestment',
      label: '本月完成投资额（万）',
      labelAlign: 'right',
      minWidth: 160
    },
    {
      prop: 'annualCompletedInvestment',
      label: '本年已完成投资额（万）',
      labelAlign: 'right',
      minWidth: 160
    },
    {
      prop: 'annualCompletionRate',
      label: '年度计划投资完成率',
      labelAlign: 'right',
      minWidth: 160
    },
    {
      prop: 'cumulativeCompletedInvestment',
      label: '累计完成投资额（万）',
      labelAlign: 'right',
      minWidth: 160
    },

    {
      slotName: 'currentProgressSlot',
      label: '目前主要形象进度',
      minWidth: 160
    },
    {
      prop: 'nextMonthPlan',
      label: '下月计划安排',
      minWidth: 160
    },
    {
      prop: 'remark',
      label: '备注',
      minWidth: 260
    },
    {
      prop: '',
      label: '操作',
      width: this.mode == 'audit' ? 1 : 75,
      slotName: 'operationSlot',
      fixed: 'right',
      labelAlign: 'center'
    }
  ]

  // 操作按钮
  private operationList: OperationItem[] = [
    {
      label: '删除',
      click: this.onDelete,
      style: 1,
      visible: (row: any) => {
        return this.isCompany
      }
    },
    {
      label: '查看月度报告',
      style: 2,
      click: this.onAudit,
      visible: (row: any) => {
        return false
      }
    }
  ]

  // 是否是企业端
  get isCompany() {
    return true
  }

  get cols() {
    return this.isCompany ? this.companyCols : this.companyCols
  }

  created() {
    this.searchParams = Object.assign(this.searchParams, this.option)
    // SearchBarTab tab 切换触发
    // this.$bus.$on('onSearchBarTab', (value: any) => {
    //   this.onSearchBarTab(value)
    // })
  }

  private loadCompleted(tableList: ProPressDetail[]) {
    if (!this.isCompany) {
      this.filterData(tableList)
    }
  }

  private onImportSuccess() {
    this.refreshGrid()
  }

  // 筛选月度报表。只在国资委端筛选
  private filterData(tableList: ProPressDetail[]) {
    let tempObj: Record<string, ProPressDetail[]> = {}
    tableList.forEach((item) => {
      if (!tempObj[item.projectCode]) {
        tempObj[item.projectCode] = [item]
      } else {
        tempObj[item.projectCode].push(item)
      }
    })
    this.monthTableMap = tempObj
  }

  @Confirm({
    title: '提示',
    content: '是否确认删除该进度报告？',
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private async onDelete(row: any) {
    // 删除项目进度报告

    let res = await deleteProjectProgressReport({
      id: row.id
    })
    if (res.success) {
      this.refreshGrid()
      this.$message.success(res.msg || '删除成功!')
    }
  }

  private onAudit(row: ProPressDetail) {
    // 查看月报
    this.currentMonthTable = this.monthTableMap[row.projectCode]
    this.monthTableVisible = true
  }

  // 导入
  private onImport(row: ProPressDetail) {
    this.uploaderDlgVisible = true
  }

  // // tab切换
  // private onSearchBarTab({ value }: any) {
  //   switch (value) {
  //     case 'QY':
  //       break
  //   }
  //   this.refreshGrid()
  // }

  private handleSearch(condition: any) {
    this.searchParams = condition
    this.refreshGrid()
  }

  // 表格更新
  private refreshGrid(isCurrent = false) {
    ;(this.$refs.grid as Grid).refresh(isCurrent)
  }
}
</script>

<style lang="scss" scoped>
.propress {
  height: 100%;
}
</style>