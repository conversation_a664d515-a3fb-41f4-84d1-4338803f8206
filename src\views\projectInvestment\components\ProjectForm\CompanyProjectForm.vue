<template>
  <el-form ref="ProjectForm"
    :rules="projectFormRules"
    :model="projectForm"
    inline
    label-width="100px">
    <el-form-item label="备案编号"
      prop="recordNumber">
      <el-input disabled
        placeholder="自动生成"
        v-model="projectForm.recordNumber" />
    </el-form-item>
    <el-form-item label="填报单位"
      prop="createDept">
      <el-input v-model="projectForm.createDept" />
    </el-form-item>
    <el-form-item label="年份"
      prop="year">
      <el-date-picker v-model="projectForm.year"
        type="year"
        value-format="yyyy"
        :clearable="true" />
    </el-form-item>
  </el-form>
</template>

<script lang="ts">
import { ElForm } from 'node_modules/element-ui/types/form'
import { Component, Vue } from 'vue-property-decorator'

export interface CompanyForm {
  implementSubject: string // 实施主体
  nextYearlyFinancing: string // 本年财务数中融资
  nextYearlyOtherFunds: string // 本年财务数中其他
  nextYearlyPlannedInvestment: string // 本年计划投资额
  nextYearlyPrivateCapital: string // 本年财务数中自有资金
  nextYearlyProgressTarget: string // 本年度完成目标
  projectCategory: string // 类型
  projectCode: string // 项目编号
  projectContent: string // 项目规模及主要内容
  projectFullName: string // 项目名称
  projectProperty: string // 项目分类(经营/功能)
  remark: string // 备注
  startEndDate: string // 建设起止年限
  thisYearlyAccumulatedBudgetAmount: string // 本年财务数
  thisYearlyAccumulatedInvestment: string // 预计头年年底累计完成投资
  totalInvestment: string // 总投资额
  year: string // 本年计划年份
}

@Component
export default class extends Vue {
  public projectForm: CompanyForm = {
    implementSubject: '',
    nextYearlyFinancing: '',
    nextYearlyOtherFunds: '',
    nextYearlyPlannedInvestment: '',
    nextYearlyPrivateCapital: '',
    nextYearlyProgressTarget: '',
    projectCategory: '企业',
    projectCode: '',
    projectContent: '',
    projectFullName: '',
    projectProperty: '',
    remark: '',
    startEndDate: '',
    thisYearlyAccumulatedBudgetAmount: '',
    thisYearlyAccumulatedInvestment: '',
    totalInvestment: '',
    year: ''
  }

  private projectFormRules = {}

  public validate(): Promise<boolean> {
    let projectForm = this.$refs['ProjectForm'] as ElForm
    return projectForm.validate()
  }
}
</script>


<style scoped lang="scss">
</style>