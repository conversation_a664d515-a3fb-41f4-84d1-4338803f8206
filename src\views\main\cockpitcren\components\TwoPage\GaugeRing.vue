/* 人事信息-刻度组件 */

<template>
  <section class="cockpit-gauge-ring-wrap">
    <!-- echarts 视图 -->
    <div class="refEcharts"
      ref="refEcharts" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import * as echarts from 'echarts'

type EChartsOption = echarts.EChartsOption

@Component({
  components: {}
})
export default class extends Vue {
  @Prop({ default: 0 }) private rote!: number
  @Prop({ default: '#eee' }) private color?: string

  // echarts 视图数据
  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private option: EChartsOption = {}

  @Watch('rote')
  changeRote() {
    this.initEcharts()
  }

  // 初始化
  private mounted() {
    this.chartDom = this.$refs.refEcharts as HTMLElement
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    this.initEcharts()
  }

  // 渲染 echarts 视图
  private initEcharts() {
    let rote = this.rote
    let color = this.color

    this.option = {
      grid: {
        left: '0%',
        right: '0%',
        bottom: '0%',
        top: '0%',
        containLabel: true
      },
      series: [
        {
          type: 'gauge',
          radius: '90%',
          startAngle: 90,
          endAngle: -270,
          pointer: {
            show: false
          },
          itemStyle: {
            color: color
          },
          progress: {
            show: true,
            overlap: false,
            roundCap: true,
            clip: false,
            itemStyle: {
              borderWidth: 1,
              borderColor: color
            }
          },
          axisLine: {
            lineStyle: {
              width: 12,
              color: [[1, '#1757aa']]
            }
          },
          splitLine: {
            show: false,
            distance: 0,
            length: 10
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false,
            distance: 50
          },
          data: [
            {
              value: rote,
              name: 'Perfect',
              title: {
                show: false
              },
              detail: {
                show: false
              }
            }
          ]
        }
      ]
    }

    this.myChart && this.myChart.setOption(this.option, true)
  }
}
</script>

<style scoped lang="scss">
.cockpit-gauge-ring-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  .refEcharts {
    width: 100%;
    height: 100%;
  }
}
</style>