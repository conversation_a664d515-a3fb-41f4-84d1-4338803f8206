<template>
  <div v-loading="loading"
    style="height:100%">
    <div class="detail-content"
      v-if="!loading">
      <div class="detail-content-close m-r-15 "
        @click="closeDetail">
        <i class="el-icon-close"></i>
      </div>
      <div class="detail-top-title">
        <h5 class="table-header">
          {{ DetailData.assetName || '资产' }}
          <!-- <el-tag>自有</el-tag> -->
          <el-tag class="m-l-8"
            v-for="(item, index) in DetailData.purposes"
            :key="index">{{ item }}</el-tag>
        </h5>

        <el-link @click="loaddetail()"
          class="m-r-15">资产详情</el-link>
      </div>
      <!-- 基本信息 -->
      <el-descriptions class="m-l-8"
        size="medium"
        title
        :column="2">
        <el-descriptions-item label="房产风险等级"
          :span="2">{{ DetailData.safetyLevel || '-' }}</el-descriptions-item>
        <el-descriptions-item
          label="本年应收(万)">{{ DetailData.thisYearReceivable || '-' }}</el-descriptions-item>
        <el-descriptions-item
          label="本年实收(万)">{{ DetailData.thisYearPaid || '-' }}</el-descriptions-item>
        <el-descriptions-item
          label="出租率">{{ DetailData.occupancyRate || '-' }}%</el-descriptions-item>
        <el-descriptions-item
          label="空置率">{{ DetailData.vacancyRate || '-' }}%</el-descriptions-item>
        <el-descriptions-item
          label="建筑面积">{{ DetailData.coveredArea || '-' }}㎡</el-descriptions-item>
        <el-descriptions-item label="土地面积">{{ DetailData.landArea || '-' }}㎡</el-descriptions-item>
      </el-descriptions>
      <!-- tubiao -->
      <h5 class="table-header">历年收益情况</h5>
      <DetailChart :chartData="DetailData.incomeList"></DetailChart>
      <!--  -->
      <div class="detail-top-title">
        <h5 class="table-header">租赁记录</h5>
        <el-link @click="loadcontractManagement">查看全部</el-link>
      </div>
      <!-- <el-menu :default-active="tab" class="el-menu-demo" mode="horizontal">
      <el-menu-item index="0">全部</el-menu-item>
      <el-menu-item index="1">房产</el-menu-item>
      <el-menu-item index="2">土地</el-menu-item>
    </el-menu>-->
      <el-tabs v-model="activeName"
        @tab-click="handleClick">
        <el-tab-pane label="已出租"
          name="first">
          <leaseRecordDetail :itemData="getcontractList(1)" />
          <!-- {{this.markerinfo}} -->
        </el-tab-pane>
        <el-tab-pane label="招租中"
          name="second">
          <leaseRecordDetail :itemData="getcontractList(2)" />
        </el-tab-pane>
        <el-tab-pane label="空置中"
          name="third">
          <leaseRecordDetail :itemData="getcontractList(3)" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
import { colorSixList } from '@/views/main/cockpitcren/baseData'
import DetailChart from './components/DetailChart.vue'
import leaseRecordDetail from './components/leaseRecordDetail.vue'
import { assetsInfoProfile } from '@/api/assetsv2'

@Component({
  components: {
    DetailChart,
    leaseRecordDetail
  }
})
export default class extends Vue {
  @Prop() private visible!: boolean
  @Prop() private markerinfo!: any
  @Watch('markerinfo', { deep: true })
  private chagemarkerinfo() {
    this.getdetail()
  }
  private loaddetail() {
    this.$emit('loaddetail', this.markerinfo)
  }
  private getcontractList(type: any) {
    let list: any = []
    Array.isArray(this.DetailData.contractList) &&
      this.DetailData.contractList.forEach((item: any) => {
        if (item.status == type) {
          list.push(item)
        }
      })
    return list
  }
  private changemarkerinfo() {
    this.getdetail()
  }
  private loading = false
  private tab = '0'
  private visibleDrawer = false
  private activeNames = 99
  private activeName = 'first'
  private DetailData: any = {}
  private companyList = []
  created() {
    this.visibleDrawer = this.visible
    this.getdetail()
  }
  private async getdetail() {
    try {
      this.loading = true
      let res = await assetsInfoProfile({
        assetNo: this.markerinfo.assetNo
      })
      if (res.success) {
        this.DetailData = res.data
      } else {
        this.closeDetail()
      }
    } catch (e) {
      this.closeDetail()
    } finally {
      this.loading = false
    }
  }
  get getcolor() {
    return (index: number) => {
      return colorSixList[index]
    }
  }
  // 跳转合同
  private loadcontractManagement() {
    this.$router.push('/assets/contractManagement')
  }
  private handleClick() {
    //
  }
  private closeDetail() {
    this.$emit('update:visible', false)
  }
}
</script>

<style lang="scss" scoped>
.detail-content {
  position: relative;
  z-index: 99;
  height: 100%;
  width: auto;
  padding: 15px;
  max-height: 100%;
  box-sizing: border-box;
  background-color: #fff;
  box-shadow: 0 0 10px #bdbcbc;
  overflow: scroll;
}
.asset-type-tip {
  display: inline-block;
  padding: 3px;
  font-size: 18px;
}
.detail-top-title {
  box-sizing: border-box;
  width: 100%;
  display: flex;
  justify-content: space-between;
  justify-items: center;
}
::v-deep .el-descriptions-item__container .el-descriptions-item__content {
  font-size: 14px;
  color: #303133;
}
::v-deep .el-descriptions-item__label {
  font-size: 14px;
  color: #909399;
}
.table-header {
  color: #303133;
  font-size: 16px;
  padding: 0px 30px 0 0;
  margin: 15px 0 10px;
  display: flex;
  align-items: center;
  &::before {
    display: inline-block;
    content: '';
    width: 4px;
    height: 18px;
    margin-right: 4px;
    background: #303133;
  }
}
.detail-content-close {
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: pointer;
  transition: 0.5s all;
}
.detail-content-close:hover {
  transform: rotate(180deg);
}
</style>
