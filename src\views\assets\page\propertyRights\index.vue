<!-- 土地证书 -->
<template>
  <el-container class="container" direction="vertical">
    <search-bar :items="searchItems" @onSearch="handleSearch" >
      <el-button type="primary">导入</el-button>
      <!-- <el-button type="primary">导出</el-button> -->
    </search-bar>
    <grid
      :remote-url="remoteUrl"
      :columns="cols"
      :show-pagination="true"
      :search-params="searchParams"
      :overflow-tooltip="true"
      @row-click="loaddetail"
      show-selection="true"
      show-index="true"
      show-index-fixed="left"
      ref="grid"
    >
      <template slot="appendixs" slot-scope="scope">
        <el-link
          type="text"
          @click="uploaderDetail(scope.row.appendixs)"
        >{{ scope.row.appendixs.length }}</el-link>
      </template>
      <template slot="operatingBar" slot-scope="scope">
        <el-button type="text" @click.stop="loaddetail(scope.row)">查看</el-button>
      </template>
    </grid>

    <detail-asset-info
    :detailInfo='Diadetaillist'
      v-if="visvileDetail"
      :visible.sync="visvileDetail"
    >
    </detail-asset-info>
  </el-container>
  
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import Grid from '@/components/Grid/index.vue'
import SearchBar from '@/components/SearchBar/index.vue'
import Uploader from '@/components/Uploader/index.vue'
import { BusinessModule } from '@/store/modules/businessDict'
import DetailAssetInfo from "./DetailAssetinfo.vue"
@Component({
  components: {
    Grid,
    SearchBar,
    DetailAssetInfo,
    Uploader
  }
})
export default class HouseCertificate extends Vue {
  // 文件上传
  private showUploader = false
  private UploaderList = []
  // 详细表格数据
  private appendixsLength!: number
  private Diadetaillist: object = []
  private visvileDetail = false
  private remoteUrl = '/fht-monitor/asset/property/list'
  private searchItems = [
  
    {
      type: 'text',
      key: 'keyword',
      placeholder: '坐落/编号',
      width: '200px'
    },
      {
      type: 'select',
      key: 'certType',
      placeholder: '请选择类型',
      width: '150px',
      options: this.getDictData('cert_type')
    }
  ]
  private cols = [
    {
      label: '资产编号',
      width: 160,
      prop: 'assetNo'
    },
    {
      prop: 'location',
      label: '坐落',
      minWidth: 100
    },
    {
      label: '产权证类型',
      minWidth: 160,
      prop: 'certTypeDesc'
    },
    {
      prop: 'certNo',
      label: '产权证号'
    },

    {
      prop: 'archiveNo',
      label: '档案编号'
    },

    {
      prop: 'assetRegAddress',
      label: '产权登记地址',
      minWidth: 140
    },
    {
      prop: 'propertyOwner',
      label: '所有权人',
      minWidth: 100
    },
    {
      prop: 'certNoStatus',
      label: '产权证书状态',
      minWidth: 140
    },

    {
      prop: 'regLandPurposeDesc',
      label: '登记用途',
      minWidth: 110
    },
    {
      prop: 'landStatus',
      label: '土地状态',
      minWidth: 110
    },
    {
      prop: 'certBuildingNature',
      label: '产权证大楼性质'
    },

    {
      prop: 'certIssueDate',
      label: '发证日期',
      minWidth: 110
    },
    {
      prop: 'certEndDate',
      label: '终止日期',
      minWidth: 100
    },
    {
      prop: 'useTerm',
      label: '使用年限',
      minWidth: 100
    },
    {
      prop: 'constructionArea',
      label: '建筑面积'
    },
    {
      prop: 'landArea',
      label: '土地面积（平方米）'
    },
    {
      prop: 'tenureArea',
      label: '使用权面积'
    },
    {
      prop: 'exclusiveUseArea',
      label: '独用面积'
    },
    {
      prop: 'publicArea',
      label: '分摊面积'
    },
    {
      prop: 'frozenReason',
      label: '冻结理由'
    },
    //资产信息    分摊面积 冻结理由 注销原因  管理单位  是否抵押 抵押银行 抵押时间 贷款项目 备注

    {
      prop: 'cancelReason',
      label: '注销原因'
    },
    {
      prop: 'reasonCancellation0',
      label: '管理单位'
    },
    {
      prop: 'whetherMortgageDesc',
      label: '是否抵押'
    },
    {
      prop: 'mortgageBank',
      label: '抵押银行'
    },
    {
      prop: 'mortgageDate',
      label: '抵押时间',
      minWidth: 100
    },
    {
      prop: 'loanProject',
      label: '贷款项目',
      minWidth: 100
    },

    {
      prop: 'remark',
      label: '备注'
    },
    {
      slotName: 'operatingBar',
      label: '操作',
      minWidth: 50,
      fixed: 'right'
    }
  ]
  private searchParams = {
    type:0
  }
  private isCertificateDetailVisible = false
  private detialItems = [
    {
      label: '管理单位'
    },
    {
      labe: '使用权人'
    },
    {
      label: '土地证号'
    },
    {
      label: '土地登记地址'
    },
    {
      label: '档案编号'
    },
    {
      label: '产权证书状态'
    },
    {
      label: '产权状态'
    },
    {
      label: '登记土地用途'
    },
    {
      label: '发证日期'
    },
    {
      label: '使用年限'
    },
    {
      label: '终止日期'
    },
    {
      label: '使用权类型'
    },
    {
      label: '使用权面积(㎡)'
    },
    {
      label: '分摊面积(㎡)'
    },
    {
      label: '独有面积(㎡)'
    },
    {
      label: '冻结理由'
    },
    {
      label: '注销理由'
    },
    {
      label: '注销时间'
    }
  ]
  // 是显示附件

  private uploaderDetail(List: any) {
    this.UploaderList = List
    this.showUploader = true
  }
  // 详情附件
  private showUpdaload(list: any) {
    this.uploaderDetail = list
    this.showUploader = true
  }
  // 改变详情显示隐藏
  private changeShowDialogDetail(state: boolean) {
    this.visvileDetail = state
  }
  private handleSearch(condition: any) {
    this.searchParams = condition
    ;(this.$refs['grid'] as any).refresh()
  }
  // 字典
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  //  操作
  private loaddetail(row: any) {
    // 被排除数组
    this.Diadetaillist = row
    this.visvileDetail = true
    
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
}
</style>