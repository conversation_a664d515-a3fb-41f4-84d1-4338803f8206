<template>
  <div class="trade-management">
    <div class="sidebar">
      <el-menu :default-active="tabValue" class="menu" @select="onStatusChange">
        <el-menu-item v-for="(menu, index) in menuList" :key="index" :index="menu.value">
          <span slot="title">{{ menu.label }}</span>
        </el-menu-item>
      </el-menu>
    </div>
    <el-container class="table-wrapper" direction="vertical">
      <search-bar :items="searchItems" :tabChange="true" @onSearch="handleSearch">
        <!-- <el-button type="primary"
          @click="onExport">导出</el-button>
        <el-button type="primary"
        @click="onImport">导出</el-button>-->
      </search-bar>

      <Grid
        ref="grid"
        show-index-fixed="left"
        :show-selection="true"
        :show-index="true"
        :show-pagination="true"
        :columns="cols"
        :remote-url="remoteUrl"
        :search-params="searchParams"
        @selection-change="handleSelectionChange"
        @select-all="handleSelectionChange"
        @row-click="onSee"
      >
      
        <template #operationSlot="{ row }">
          <Operation :list="operationList" :row="row" />
        </template>
        <!-- 
        <div slot="bottomLeft">
          <el-button type="primary"
            @click="onBatchDelete">删除</el-button>
        </div>-->
      </Grid>
    </el-container>

    <!-- 审批 -->
    <AuditTradeDialog
      v-if="auditTradeDialogVisible"
      :initData="currentRow"
      :visible.sync="auditTradeDialogVisible"
    />

    <!-- 查看 -->
    <TradeDetailDialog
      v-if="tradeDetailDialogVisible"
      :assetId="assetid"
      :initData="currentRow"
      :visible.sync="tradeDetailDialogVisible"
    />
    <!-- 分配 -->
    <DialogDistribute
      v-if="visibleDistribute"
      :assetId="assetid"
      :visible.sync="visibleDistribute"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import SearchBar from '@/components/SearchBar/index.vue'
import Grid from '@/components/Grid/index.vue'
import Operation, { OperationItem } from '@/components/Operation/index.vue'
import { Confirm } from '@/decorators'
import { circulationStatusList, processNodeList } from '../../filterOptions'
import AuditTradeDialog from '../../components/TradeDialog/AuditTradeDialog.vue'
import TradeDetailDialog from '../../components/TradeDialog/TradeDetailDialog.vue'
import DialogDistribute from './DialogDistribute.vue'
import { BusinessModule } from '@/store/modules/businessDict'

@Component({
  components: { SearchBar, Grid, Operation, AuditTradeDialog, TradeDetailDialog, DialogDistribute }
})

export default class extends Vue {
  private remoteUrl = '/fht-monitor/asset/transaction/check/page'
  private tabValue = '1'
  private loading = false

  private auditTradeDialogVisible = false
  private tradeDetailDialogVisible = false
  private currentRow: any = {}

  private checkedList: number[] = []
  private visibleDistribute = false //分配显示
  private assetid = ''
  private searchParams = {
    input: '',
    tab: '1'
  }
// 监听事件，刷新表格
@Watch("auditTradeDialogVisible")
private refreshtable(){
  this.refresh()
}
@Watch("visibleDistribute")
private refreshtableVisibleDistribute(){
  this.refresh()
}
  private menuList = [
    {
      label: '待处理',
      value: '1'
    },
    {
      label: '已处理',
      value: '2'
    },
    {
      label: '交易清单',
      value: '3'
    }
  ]

  private searchItems = [
    {
      type: 'text',
      key: 'keyword',
      placeholder: '单位名称/坐落',
      width: '330px'
    }
  ]
  get cols() {
    return [
      {
        prop: 'assetNo',
        label: '资产编号',
        minWidth: 160,
        fixed: 'left'
      },
      {
        prop: 'batchNo',
        label: '申请批次号',
        fixed: 'left',
        minWidth: 160
      },
      {
        prop: 'createUserName',
        label: '提交人',
        minWidth: 120
      },
      {
        prop: 'createTime',
        label: '提交时间',
        minWidth: 160
      },

      {
        prop: 'subjectName',
        label: '标的名称',
        minWidth: 160
      },
      {
        prop: 'circulationStatusDesc',
        label: '业务状态'
      },
      {
        prop: 'location',
        label: '坐落位置',
        minWidth: 240
      },
      {
        prop: 'realEstateCertificateNo',
        label: '不动产证号',
        minWidth: 120
      },
      {
        prop: 'titleDeedNo',
        label: '房产证号',
        minWidth: 120
      },
      {
        prop: 'landCertificateNo',
        label: '土地证号',
        minWidth: 120
      },
      {
        prop: 'planCertificateNo',
        label: '规划证号',
        minWidth: 120
      },
      {
        prop: 'constructionCertificateNo',
        label: '施工证号',
        minWidth: 120
      },
      {
        prop: 'completionCertificateNo',
        label: '竣工证号',
        minWidth: 120
      },
      {
        prop: 'assetCategoryDesc',
        label: '资产类型'
      },
      {
        prop: 'rentListingPrice',
        label: '租金挂牌价'
      },
      {
        prop: 'listingPriceUnitDesc',
        label: '挂牌价单位'
      },
      {
        prop: 'bidSecurity',
        label: '保证金'
      },
      {
        prop: 'rentalArea',
        label: '拟出租面积(平方米)',
        minWidth: 160
      },
      {
        prop: 'useRequirementsDesc',
        label: '房产使用用途要求',
        minWidth: 160
      },
      {
        prop: 'leaseTermDesc',
        label: '租期',
        minWidth: 160
      },
      {
        prop: 'hasRentFreePeriodDesc',
        labelAlign: 'center',
        label: '有无免租期'
      },
      {
        prop: 'rentFreePeriod',
        label: '免租期时间'
      },
      {
        prop: 'inLeaseTermDesc',
        label: '是否包含在租期内',
        labelAlign: 'center',
        minWidth: 160
      },
      {
        label: '操作',
        slotName: 'operationSlot',
        fixed: 'right',
        minWidth: this.tabValue === '3' ? 100 : 50,
        labelAlign: 'center'
      }
    ]
  }

  // 操作按钮
  get operationList(): OperationItem[] {
    return [
      {
        label: '核实',
        click: this.onAudit,
        style: 2,
        visible: (row: any) => {
          return this.tabValue === '1'
        }
      },
      {
        label: '分配',
        click: this.onAllocation,
        style: 2,
        visible: (row: any) => {
          return this.tabValue == '3' && row.circulationStatus == "11" //限制状态待核实
        }
      },

      {
        label: '查看',
        click: this.onSee,
        visible: (row: any) => {
          return this.tabValue !== '1'
        }
      }
    ]
  }
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  private getCirculationStatus(status: number | 1) {
    return (
      this.getDictData('circulation_status').find((res: any) => {
        return res.value == status
      })||{label:""}.label
    )
  }

  private handleSearch(condition: any) {
    this.searchParams = Object.assign({}, condition)
    this.searchParams.tab = this.tabValue
    this.refresh()
  }

  private onStatusChange(tab: string) {
    this.searchParams.input = ''
    this.$set(this.searchItems, 'index', '')
    this.tabValue = tab
    this.searchParams.tab = tab
    this.refresh()
  }

  private handleSelectionChange(checkedList: any[]) {
    this.checkedList = [...checkedList]
  }

  private onSee(row: any) {
    
    this.assetid = row.id
    // this.currentRow = row
    this.tradeDetailDialogVisible = true
  }

  // 分配
  private onAllocation(row: any) {
    //
    this.assetid = row.id

    this.visibleDistribute = true
    // this.refresh()
  }
// 核实
  private onAudit(row: any) {
    this.currentRow = row
    this.auditTradeDialogVisible = true
    // this.refresh()
  }

  @Confirm({
    title: '提示',
    content: `是否删除该交易？`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private onDelete() {
    //
  }

  // 批量删除
  private onBatchDelete() {
    if (!this.checkedList.length) {
      this.$message.warning('请选择需要删除的工单！')
      return
    }

    this.$confirm(`此操作将永久删除 <strong><i>${this.checkedList.length}</i></strong> 条交易, 是否继续?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      dangerouslyUseHTMLString: true,
      type: 'warning'
    })
      .then(() => {
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
      })
      .catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
  }

  private onExport() {
    //
  }

  private onImport() {
    //
  }

  // 刷新表格
  private refresh() {
    let grid = this.$refs['grid'] as Grid
    grid && grid.refresh()
  }
}
</script>

<style lang="scss" scoped>
.trade-management {
  height: 100%;
  display: flex;

  .sidebar {
    width: 120px;
    height: 100%;
    background: #fff;
    margin-right: 8px;
    border-radius: 6px;
  }
}
.table-wrapper {
  height: 100%;
  flex: 1;
}
</style>