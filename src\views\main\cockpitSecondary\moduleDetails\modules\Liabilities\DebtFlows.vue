/* 债务金额 */

<template>
  <section class="debt-flows-wrap">
    <CommonTitle :title="title"
      :isRotuerThrow="true"
      :routerObj="{
        bizAppCode: 'finance',
        detailsPath: '/wel/index?bizAppCode=finance&path=/business/finance/debtStatistics/search/stateEntriseSum'
      }" />

    <div class="title-box">
      <span>债务类型</span>
      <span>债务余额</span>
    </div>

    <!-- 列表 -->
    <div v-loading="loading"
      class="content-box"
      ref="tableList">
      <div v-for="(item, index) of dataList"
        :key="index"
        class="mode"
        @mouseenter="mouseenterHandle"
        @mouseleave="mouseleaveHandle"
        :class="[{'mode-active': dataList.length > 4 && +index === +currentIndex}]">
        <h6>{{item.name}}</h6>
        <p>
          <span>{{item.value}}</span>
          <i>{{item.unit}}</i>
        </p>
      </div>
    </div>

    <!-- 合计 -->
    <div class="total-box">
      <div class="mode"
        v-for="(item, index) of getTotalData"
        :key="index">
        <h6 class="total">合计</h6>
        <p class="total">
          <span>{{item.value}}</span>
          <i>{{item.unit}}</i>
        </p>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { deepClone } from '@/utils'
import { liabilitiesFinSummary } from '@/api/cockpit'
import { Loading } from '@/decorators'
import CommonTitle from '@/views/main/cockpitSecondary/components/CommonTitle.vue'

@Component({
  components: {
    CommonTitle
  }
})
export default class extends Vue {
  @Prop() private title!: string // 标题
  @Prop() private orgCode!: string // 集团
  @Prop() private year!: string // 年份
  @Prop() private moon?: string // 月份

  private loading = false
  private echartsData = {}
  private dataList: any[] = []

  // 定时轮播数据
  private timer: any
  private currentIndex = 0
  private middleIndex = 2
  private listHeight = 137
  private listDom: any = {}

  // 数据变化，渲染视图
  @Watch('orgCode', { deep: true })
  @Watch('year', { deep: true })
  @Watch('moon', { deep: true })
  private changeEchartsData() {
    this.clearTimer()
    this.initData()
  }

  // 获取合计数据
  get getTotalData() {
    let data = deepClone(this.echartsData)

    if (Array.isArray(data) && data.length >= 1) {
      return data.splice(0, 1)
    } else {
      return []
    }
  }

  // 初始化
  private mounted() {
    this.listDom = this.$refs['tableList'] as HTMLDivElement
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await liabilitiesFinSummary({
      year: this.year,
      month: this.moon,
      orgCode: this.orgCode
    })

    data = data.finTypeList || []
    this.echartsData = data
    data = data.splice(1, data.length)

    this.dataList = data || []
    this.scrollTable()
  }

  // 开启定时器，让页面滚动起来
  private scrollTable() {
    let dataLen = this.dataList.length
    if (!dataLen || dataLen <= this.middleIndex) return
    this.timer = setInterval(() => {
      this.currentIndex++
      if (this.currentIndex > this.middleIndex && this.currentIndex < dataLen) {
        this.listDom.scrollTo(0, (this.currentIndex - this.middleIndex) * this.listHeight)
      } else if (this.currentIndex === dataLen) {
        this.currentIndex = 0
        this.listDom.scrollTo(0, 0)
      }
    }, 4000)
  }

  // 列表鼠标移入，停止动画
  private mouseenterHandle() {
    clearInterval(this.timer)
    this.timer = null
  }

  // 列表鼠标移出，开始动画
  private mouseleaveHandle() {
    this.scrollTable()
  }

  // 清除定时器，还原相关数据
  private clearTimer() {
    clearInterval(this.timer)
    this.timer = null
    this.currentIndex = 0
    this.listDom.scrollTo(0, 0)
  }

  // 组件销毁
  private destroyed() {
    this.clearTimer()
  }
}
</script>

<style scoped lang="scss">
.debt-flows-wrap {
  position: relative;
  height: 100%;

  h6,
  p {
    margin: 0;
  }

  .title-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    box-sizing: border-box;
    margin-top: -10px;
    span {
      color: #a5baee;
      font-size: 38px;
    }
  }

  .content-box {
    width: 100%;
    height: 55%;
    margin-top: 17px;
    overflow-y: auto;
    scroll-behavior: smooth;
    &::-webkit-scrollbar {
      display: none;
    }
    .mode-active {
      box-shadow: 0 0 20px #00ffff inset;
    }
  }

  .total-box {
    position: absolute;
    left: 0;
    bottom: -10px;
    width: 100%;
  }

  .mode {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 25px 30px;
    margin-bottom: 30px;
    border-radius: 8px;
    box-sizing: border-box;
    background: url('../../../images/mode_bj.png') no-repeat center center;
    background-size: 100% 100%;
    &:nth-last-child(1) {
      margin-bottom: 20px;
    }
    .total {
      color: #ffc73a;
    }
    h6 {
      font-size: 40px;
    }
    p {
      color: #00ffff;
      font-size: 46px;
      font-weight: normal;
      font-family: 'PangMenZhengDao';
      i {
        font-size: 34px;
        margin-left: 8px;
      }
    }
  }
}
</style>


