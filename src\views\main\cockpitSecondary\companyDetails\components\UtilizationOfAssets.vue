/** 资产利用情况 */
<template>
  <div id="UtilizationOfAssets" />
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { UtilizationOfAssetsData, companyList } from '@/views/main/cockpitSecondary/baseData'
import { capitalProfile } from '@/api/cockpit'
import * as echarts from 'echarts'
import { deepClone } from '@/utils'

type EChartsOption = echarts.EChartsOption

@Component
export default class extends Vue {
  private timer: any
  private loading = false
  private maxValue = 100
  private year = ''
  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}
  private tabList = []
  private echartsDatas: any[] = []
  private seriesData: any[] = deepClone(UtilizationOfAssetsData)
  private tabActive: {
    code: string
    name: string
  } = {
    code: '',
    name: ''
  }

  // 获取集团列表
  get getCompanyList() {
    let list: string[] = []
    companyList.forEach((item) => {
      list.push(item.name)
    })
    return list
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('UtilizationOfAssets') as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    // this.initData()
    this.initEcharts()
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await capitalProfile()
    if (!Array.isArray(data) || !data.length) return
    this.echartsDatas = data
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 初始化数据
  private initEcharts() {
    let textSize = 22
    let series = this.seriesData.map((item) => {
      // 设置圆角方向
      if (item.value < 0) {
        item.itemStyle = {
          borderRadius: [20, 0, 0, 20]
        }
        item.backgroundStyle = {
          borderRadius: [20, 0, 0, 20]
        }
      }
      return item
    })
    let yeas = ~~this.getMomentTime()
    let companyList = this.getCompanyList
    this.option = {
      color: [
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: '#835002' // 0% 处的颜色
            },
            {
              offset: 1,
              color: '#E6B607' // 100% 处的颜色
            }
          ]
        },
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: '#7E358B' // 0% 处的颜色
            },
            {
              offset: 1,
              color: '#EC342F' // 100% 处的颜色
            }
          ]
        }
      ],
      axisPointer: {
        show: false
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 103, 177, 1)',
        borderColor: 'rgba(0, 103, 177, 1)',
        textStyle: {
          color: '#fff',
          fontSize: textSize
        }
      },
      legend: {
        show: false
      },
      grid: {
        top: '0%',
        left: '8%',
        right: '6%',
        bottom: '0%',
        containLabel: true
      },
      xAxis: {
        show: false,
        type: 'value',
        axisLabel: {
          color: '#5db0ea',
          fontSize: textSize
        }
      },
      yAxis: {
        type: 'category',
        axisLabel: {
          color: '#5db0ea',
          fontSize: textSize,
          shadowBlur: 10,
          shadowColor: '#0B388F',
          backgroundColor: '#0B388F66',
          borderRadius: 6,
          formatter: (value: any, index: number) => {
            return `{a|0${series.length - index - 1}} {b|${value}}`
          },
          rich: {
            a: {
              fontStyle: 'italic',
              fontWeight: 'bold',
              color: '#4A97F8',
              fontSize: textSize * 2,
              lineHeight: textSize * 2.4,
              padding: 10
            },
            b: {
              fontWeight: 'bold',
              fontSize: textSize * 1.4,
              lineHeight: textSize * 2.4,
              padding: 10
            }
          }
        },
        data: companyList.reverse()
      },
      series: [
        {
          name: '资产利用情况',
          type: 'bar',
          stack: 'total',
          selectedMode: 'single',
          barWidth: textSize * 1.8,
          label: {
            show: true,
            fontSize: textSize * 1.4,
            fontWeight: 'bold'
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'RGBA(233, 60, 167, 0)'
                  },
                  {
                    offset: 0.4,
                    color: 'RGBA(233, 60, 167, 0.3)'
                  },
                  {
                    offset: 1,
                    color: 'RGBA(233, 60, 167, 1)'
                  }
                ]
              },
              shadowColor: 'RGBA(233, 60, 167, 0.6)',
              shadowBlur: 30
            }
          },
          select: {
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'RGBA(233, 60, 167, 0)'
                  },
                  {
                    offset: 0.4,
                    color: 'RGBA(233, 60, 167, 0.3)'
                  },
                  {
                    offset: 1,
                    color: 'RGBA(233, 60, 167, 1)'
                  }
                ]
              },
              shadowColor: 'RGBA(233, 60, 167, 0.6)',
              shadowBlur: 30
            }
          },
          itemStyle: {
            borderRadius: [0, 20, 20, 0]
          },
          showBackground: true,
          backgroundStyle: {
            color: 'rgba(0, 103, 177, 0.2)',
            borderRadius: [0, 20, 20, 0]
          },
          data: series
        }
      ]
    }

    this.myChart && this.myChart.setOption && this.myChart && this.myChart.setOption(this.option)
    this.change()
  }
  // 年份切换
  private yearHandler(year: string) {
    this.year = year
    // this.initData()
    // this.initEcharts()
  }

  private change() {
    if (this.timer) {
      clearInterval(this.timer)
    }
    this.timer = setInterval(() => {
      let index = Math.max(Math.floor(Math.random() * 4), 0)

      this.myChart &&
        this.myChart.dispatchAction({
          type: 'select',
          seriesIndex: 0,
          dataIndex: index
        })
    }, 5000)
  }

  destroyed() {
    clearInterval(this.timer)
  }
}
</script>

<style scoped lang="scss">
#UtilizationOfAssets {
  width: 100%;
  height: 100%;
}
</style>