<!-- 土地证书 -->
<template>
  <el-container class="container"
    direction="vertical">
    <search-bar :items="searchItems"
      @onSearch="handleSearch"
      v-if="isSearch">
      <!-- <el-button type="primary">导入</el-button> -->
      <!-- <el-button type="primary">导出</el-button> -->
    </search-bar>

    <Grid :remote-url="remoteUrl"
      :columns="cols"
      :show-pagination="true"
      :search-params="searchParams"
      :overflow-tooltip="true"
      @row-click="loaddetail"
      ref="grid"
      border>
      <template slot="contractLeasePeriod"
        slot-scope="scope">
        {{ scope.row.startTime + ' 至 ' + scope.row.endTime }}
      </template>
      <template slot="operatingBar"
        slot-scope="scope">
        <el-button type="text"
          size="small"
          @click.stop="loaddetail(scope.row)">查看</el-button>
      </template>
    </Grid>

    <!-- 详情信息 -->
    <DetailAssetInfo v-if="visvileDetailif"
      :detailInfo="diadetaillist"
      :visibleif.sync="visvileDetailif"
      :visible.sync="visvileDetail" />
  </el-container>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { BusinessModule } from '@/store/modules/businessDict'
import { getAstCompTree } from '@/api/projectInvestment'
import Grid from '@/components/Grid/index.vue'
import SearchBar from '@/components/SearchBar/index.vue'
import Uploader from '@/components/Uploader/index.vue'
import DetailAssetInfo from './DetailAssetinfo.vue'

@Component({
  components: {
    Grid,
    SearchBar,
    DetailAssetInfo,
    Uploader
  }
})
export default class HouseCertificate extends Vue {
  // 文件上传
  private isSearch = false
  private showUploader = false
  private UploaderList = []
  private compTree = [
    {
      value: '11',
      label: '11'
    }
  ]
  // 详细表格数据
  private appendixsLength!: number
  private diadetaillist: any = {}
  private visvileDetail = false
  private visvileDetailif = false
  private remoteUrl = '/fht-monitor/ast/contract/page'
  private searchItems = [
    {
      type: 'text',
      key: 'keyword',
      placeholder: '合同编号/承租人/资产编号',
      width: '300px'
    },
    {
      type: 'select',
      key: 'orgCode',
      placeholder: '机构名称',
      width: '150px',
      options: this.compTree
    },
    {
      type: 'select',
      key: 'contractStatus',
      placeholder: '合同状态',
      width: '150px',
      options: this.getDictData('astContractStatus')
    },
    {
      type: 'date',
      key: 'startTime',
      placeholder: '开始时间',
      width: '150px'
    },
    {
      type: 'date',
      key: 'endTime',
      placeholder: '结束时间',
      width: '150px'
    }
  ]
  // 合同编号、合同状态、合同租金、租赁总面积、承租方名称、承租方联系方式、合同租赁期限、签约时间、交易方式、所属集团
  private cols = [
    {
      prop: 'contractNo',
      label: '合同编号',
      minWidth: 120
    },
    {
      prop: 'itemNoList',
      label: '子资产编号',
      minWidth: 120
    },
    {
      label: '合同状态',
      minWidth: 120,
      prop: 'contractStatusDesc'
    },
    {
      prop: 'totalFee',
      label: '合同租金(元)',
      minWidth: 140,
      sortable: true
    },
    {
      prop: 'totalRentArea',
      label: '租赁总面积(m²)',
      minWidth: 150,
      sortable: true
    },
    {
      prop: 'renterName',
      label: '承租方名称',
      minWidth: 120
    },
    // 承租方联系方式
    {
      prop: 'renterPhone',
      label: '承租方联系方式',
      minWidth: 120
    },
    {
      slotName: 'contractLeasePeriod',
      label: '合同租赁期限',
      minWidth: 160
    },

    {
      prop: 'signTime',
      label: '签约时间',
      minWidth: 120,
      sortable: true
    },

    {
      prop: 'tradeTypeDesc',
      label: '交易方式',
      minWidth: 140
    },
    // 承租方联系方式
    {
      label: '所属集团',
      minWidth: 120,
      prop: 'orgName'
    },

    // {
    //   prop: 'renterTypeDesc',
    //   label: '承租人类型',
    //   minWidth: 110
    // },

    {
      slotName: 'operatingBar',
      label: '操作',
      minWidth: 70,
      fixed: 'right'
    }
  ]
  private searchParams = {
    type: 0
  }
  // 是显示附件
  created() {
    this.getCompTree()
  }
  private uploaderDetail(List: any) {
    this.UploaderList = List
    this.showUploader = true
  }
  // 详情附件
  private showUpdaload(list: any) {
    this.uploaderDetail = list
    this.showUploader = true
  }

  // return () => {
  //   getAstCompTree({ parentId: 0, deptCategory: 1 }).then(res => {
  //     if (res.success) {
  //       let options = res.data.map((res: any) => {
  //         return {
  //           label: res.deptName,
  //           value: res.deptCode
  //         }
  //       })
  //       return Object.assign(this.compTree, options)
  //     }
  //   })
  // }
  // }
  //获取机构数组
  private async getCompTree() {
    let options = []
    let res = await getAstCompTree({ parentId: 0, deptCategory: 1 })
    this.isSearch = false
    if (res.success) {
      let options = res.data.map((res: any) => {
        return {
          label: res.deptName,
          value: res.deptCode
        }
      })
      await this.$nextTick(() => {
        Object.assign(this.compTree, options)
        this.isSearch = true
      })
    }
  }
  // 改变详情显示隐藏
  private changeShowDialogDetail(state: boolean) {
    this.visvileDetailif = state
    this.$nextTick(() => {
      this.visvileDetail = state
    })
  }
  private handleSearch(condition: any) {
    this.searchParams = condition
    ;(this.$refs['grid'] as any).refresh()
  }
  // 字典
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  // 操作
  private loaddetail(row: any) {
    this.diadetaillist = row
    this.visvileDetailif = true
    this.$nextTick(() => {
      this.visvileDetail = true
    })
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
}
</style>
