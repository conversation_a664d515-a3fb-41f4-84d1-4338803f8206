
## 金额单位切换
- avue-crud 列表头部 menuRight 部分，添加金额单位切换组件，切换单位后，列表数据随之改变

1. 引入: import PricUnit from '@/components/pric-unit/main.vue';

2. 使用: 请查看文件 props 部分，都有注释
```
<PricUnit :value="query.amtUnit" @updataHandle="updataUnitHandle" />
```

3. 处理函数:
```
// query 为列表 search.sync 绑定值
// onLoad 为刷新列表函数

export default {
  created() {
    this.setInitData()
  },
  methods: {
    // 设置初始化默认值（表格的默认初始化赋值操作都可以放这个函数里面，方便扩展）
    setInitData() {
      this.query = {}
      this.query.amtUnit = 2
    },

    // 单位切换时执行
    updataUnitHandle(val) {
      this.query.amtUnit = val
      this.onLoad()
    },

    // 清空列表操作
    resetChange() {
      this.setInitData()
      this.onLoad()
    },
  }
}
```