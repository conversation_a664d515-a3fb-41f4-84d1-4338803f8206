/* 人事信息 */

<template>
  <section class="cockpit-entrepreneur-wrap">
    <TitleCom title="人事信息"
      :isThrow="false" />

    <div class="title-box">
      <span>员工总数</span>
      <CountTo :decimals="0"
        :startVal="0"
        :endVal="total"
        :duration="1500"
        class="num" />
      <span>人</span>
    </div>

    <div v-loading="loading"
      class="content-box">
      <div class="total">
        <i />
        <span v-if="orgActive.orgCode === '0'">中共党员(含央企)</span>
        <span v-else>中共党员</span>
        <CountTo :decimals="0"
          :startVal="0"
          :endVal="cpcNum"
          :duration="1500"
          class="num" />
        <span>人</span>
      </div>

      <div class="conter">
        <div class="mode">
          <div v-for="(item, index) of dataList"
            :key="index"
            class="contion">
            <i />
            <span>{{item.label}}</span>
            <strong>{{item.value}}</strong>
            <span>人</span>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { perHomeSummary } from '@/api/cockpit'
import CountTo from 'vue-count-to'
import TitleCom from '@/views/main/cockpitcren/components/Public/TitleCom.vue'

@Component({
  components: {
    TitleCom,
    CountTo
  }
})
export default class extends Vue {
  private loading = false
  private dataList = []
  private total = 0
  private cpcNum = 0

  // 所属集团+时间切换 选中数据
  private orgActive: {
    orgCode: string
  } = {
    orgCode: '0'
  }
  private params = {
    year: String(new Date().getFullYear()),
    month: ''
  }

  // 初始化
  private mounted() {
    this.listenerDate()
  }

  // 监听全局事件触发
  private listenerDate() {
    // 集团 切换
    this.$bus.$on('BusTabsChange', (orgActive: any) => {
      this.orgActive = orgActive
      this.initData()
    })

    // 年份 切换
    this.$bus.$on('BusYearTimeChange', (year: string) => {
      this.params.year = year
      this.initData()
    })

    // 月份 切换
    // this.$bus.$on('BusMoonTimeChange', (month: string) => {
    //   this.params.month = month
    //   this.initData()
    // })
  }

  // 获取渲染视图数据
  @Loading('loading')
  private async initData() {
    await perHomeSummary({
      orgCode: this.orgActive.orgCode,
      year: this.params.year
    })
      .then((res) => {
        if (res.data) {
          this.dataList = res.data.list || []
          this.total = +res.data.total || 0
          this.cpcNum = +res.data.cpcNum || 0
        }
      })
      .catch(() => {
        this.dataList = []
      })
  }
}
</script>

<style scoped lang="scss">
.cockpit-entrepreneur-wrap {
  position: relative;
  width: 100%;
  height: 382px;

  .title-box {
    position: absolute;
    top: 20px;
    right: 0;
    display: flex;
    align-items: end;
    span {
      font-size: 38px;
    }
    .num {
      color: #ff7a27;
      font-size: 60px;
      padding: 0 7px;
      font-weight: normal;
      font-family: 'PangMenZhengDao';
    }
  }

  .content-box {
    padding-top: 20px;
    .total {
      display: flex;
      align-items: center;
      margin-top: -11px;
      margin-bottom: 15px;
      i {
        display: inline-block;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        margin-right: 10px;
        background: #3eeeff;
      }
      span {
        font-size: 32px;
      }
      .num {
        color: #3eeeff;
        padding: 0 14px;
        font-size: 40px;
        font-weight: normal;
        font-family: 'PangMenZhengDao';
      }
    }

    .conter {
      height: 186px;
      display: flex;
      align-items: flex-start;
      overflow-y: hidden;
      &::-webkit-scrollbar {
        display: none;
      }

      .mode {
        &:nth-last-child(1) {
          margin-right: 0;
        }
        .contion {
          width: 50%;
          display: inline-block;
          box-sizing: border-box;
          margin-bottom: 24px;
          &:nth-child(1n) {
            width: 60%;
          }
          &:nth-child(2n) {
            width: 40%;
          }
          i {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
            background: #3eeeff;
          }
          span {
            font-size: 32px;
          }
          strong {
            padding: 0 6px;
            font-size: 38px;
            font-weight: normal;
            font-family: 'PangMenZhengDao';
            color: #3eeeff;
          }
        }
      }
    }
  }
}
</style>
