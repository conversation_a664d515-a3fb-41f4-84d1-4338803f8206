import request from './request'
import { RemoteResponse } from '@/types'

// 获取企业列表
export const getSuperViseTree = (params: any = {}): Promise<RemoteResponse> => {
  return request(
    '/fht-monitor/ent/supervise-tree/current',
    { ...params, isRegulated: -1 },
    {
      method: 'POST'
    }
  )
}

// 集团/单位指标接口
export const getCompanyIndicator = (params: any = {}): Promise<RemoteResponse> => {
  return request(
    '/fht-monitor/warningIndicator/companyIndicator',
    params,
    {
      method: 'POST'
    }
  )
}

// 单位指标分页接口
export const getCompanyPage = (params: any = {}): Promise<RemoteResponse> => {
  return request(
    '/fht-monitor/warningIndicator/companyPage',
    params,
    {
      method: 'POST'
    }
  )
}

// 集团指标导出
export const companyIndicatorExport = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/warningIndicator/companyIndicatorExport', params, {
    responseType: 'blob'
  })
}

// 单位单个指标导出
export const companyPageExport = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/warningIndicator/companyPageExport', params, {
    responseType: 'blob'
  })
}

// 融资查询(融资明细)导出
export const borrowExport = (params: any): Promise<RemoteResponse> => {
  return request('/fht-monitor/fin/capital/borrow/page/export', params, {
    responseType: 'blob'
  })
}

// 获取机构树(监管)
export const getCreditGroups = (params: any = {}): Promise<RemoteResponse> => {
  return request(
    '/fht-monitor/warningIndicator/getFinDeptTree',
    params,
    {
      method: 'GET'
    }
  )
}
