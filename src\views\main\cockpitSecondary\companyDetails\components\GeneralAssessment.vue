/** 资产总体情况 */

<template>
  <div id="GeneralAssessment" />
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { colorSixList, companyList } from '@/views/main/cockpitSecondary/baseData'
import * as echarts from 'echarts'

type EChartsOption = echarts.EChartsOption

@Component
export default class extends Vue {
  @Prop() private currentTabCode!: string

  @Watch('currentTabCode', {
    immediate: true
  })
  private onCurrentTabCodeChange() {
    this.filterData()
  }

  private loading = false
  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}
  private realDatas: any = {}
  private seriesData: any[] = [
    {
      name: '国资总况',
      id: 'ALL',
      type: 'line',
      symbol: 'circle',
      smooth: true,
      lineStyle: {
        width: 3
      },
      areaStyle: {
        origin: 'start',
        shadowBlur: {
          shadowColor: 'rgba(0, 0, 0, 0.5)',
          shadowBlur: 10
        },
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 1,
              color: '#EF702E00' // 0% 处的颜色
            },
            {
              offset: 0,
              color: '#EF702E44' // 100% 处的颜色
            }
          ],
          global: false // 缺省为 false
        }
      }
    },
    {
      name: '国资运营',
      type: 'line',
      symbol: 'circle',
      id: 'GYYY',
      smooth: true,
      lineStyle: {
        width: 3
      },
      areaStyle: {
        origin: 'start',
        shadowBlur: {
          shadowColor: 'rgba(0, 0, 0, 0.5)',
          shadowBlur: 10
        },
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 1,
              color: '#35AAF600' // 0% 处的颜色
            },
            {
              offset: 0,
              color: '#35AAF644' // 100% 处的颜色
            }
          ],
          global: false // 缺省为 false
        }
      }
    },
    {
      name: '城投集团',
      type: 'line',
      symbol: 'circle',
      id: 'CT',
      smooth: true,
      lineStyle: {
        width: 3
      },
      areaStyle: {
        origin: 'start',
        shadowBlur: {
          shadowColor: 'rgba(0, 0, 0, 0.5)',
          shadowBlur: 10
        },
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 1,
              color: '#00AEA500' // 0% 处的颜色
            },
            {
              offset: 0,
              color: '#00AEA544' // 100% 处的颜色
            }
          ],
          global: false // 缺省为 false
        }
      }
    },
    {
      name: '交投集团',
      type: 'line',
      symbol: 'circle',
      id: 'JT',
      smooth: true,
      lineStyle: {
        width: 3
      },
      areaStyle: {
        origin: 'start',
        shadowBlur: {
          shadowColor: 'rgba(0, 0, 0, 0.5)',
          shadowBlur: 10
        },
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 1,
              color: '#EC342F00' // 0% 处的颜色
            },
            {
              offset: 0,
              color: '#EC342F44' // 100% 处的颜色
            }
          ],
          global: false // 缺省为 false
        }
      }
    },
    {
      name: '金投集团',
      type: 'line',
      symbol: 'circle',
      id: 'KT',
      smooth: true,
      areaStyle: {
        origin: 'start',
        shadowBlur: {
          shadowColor: 'rgba(0, 0, 0, 0.5)',
          shadowBlur: 10
        },
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 1,
              color: '#E6060700' // 0% 处的颜色
            },
            {
              offset: 0,
              color: '#E6060744' // 100% 处的颜色
            }
          ],
          global: false // 缺省为 false
        }
      },
      lineStyle: {
        width: 3
      }
    },
    {
      name: '轨道集团',
      type: 'line',
      symbol: 'circle',
      id: 'GD',
      smooth: true,
      areaStyle: {
        origin: 'start',
        shadowBlur: {
          shadowColor: 'rgba(0, 0, 0, 0.5)',
          shadowBlur: 10
        },
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 1,
              color: '#06B60700' // 0% 处的颜色
            },
            {
              offset: 0,
              color: '#06B60744' // 100% 处的颜色
            }
          ],
          global: false // 缺省为 false
        }
      },
      lineStyle: {
        width: 3
      }
    }
  ]

  // 获取集团列表
  get getCompanyList() {
    let list: string[] = []
    companyList.forEach((item) => {
      list.push(item.name)
    })
    return list
  }

  created() {
    this.initData()
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('GeneralAssessment') as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    // this.initData()
    this.initEcharts()
  }

  // 筛选数据
  private filterData() {
    let currentData = this.realDatas[this.currentTabCode] || {
      ALL: [4552935, 4948077, 5354028, 5375215],
      CT: [4552935, 4948077, 5354028, 5375215],
      KT: [4552935, 4948077, 733871, 863724],
      SW: [4552935, 4948077, 1236982, 1241001],
      JT: [4552935, 4948077, 3138395, 2679731],
      GD: [4552935, 4948077, 427692, 3681453],
      GYYY: [4552935, 4948077, 427692, 3681453]
    }
    this.seriesData.forEach((item) => {
      item.data = currentData[item.id]
    })
    this.initEcharts()
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    this.realDatas = {
      // 资产总额
      ['asset_amount']: {
        CT: [4552935, 4948077, 5354028, 5375215],
        KT: [4552935, 4948077, 733871, 863724],
        SW: [4552935, 4948077, 1236982, 1241001],
        JT: [4552935, 4948077, 3138395, 2679731],
        GD: [4552935, 4948077, 427692, 3681453],
        GYYY: [4552935, 4948077, 427692, 3681453]
      },
      ['asset_net']: {
        CT: [1735860, 1774615, 1693779, 1754261],
        KT: [1735860, 1774615, 545238, 676443],
        SW: [1735860, 1774615, 858219, 858118],
        JT: [1735860, 1774615, 1154419, 1181334],
        GD: [1735860, 1774615, 1657989, 1777754],
        GYYY: [1735860, 1774615, 194877, 218360]
      },
      ['asset_count']: {
        CT: [0, 0, 0, 0],
        KT: [0, 0, 0, 0],
        SW: [0, 0, 0, 0],
        JT: [0, 0, 0, 0],
        GD: [0, 0, 0, 0],
        GYYY: [0, 0, 0, 0]
      },
      turnover: {
        CT: [107490, 246054, 381199, 37773],
        KT: [107490, 246054, 21111, 7574],
        SW: [107490, 246054, 93704, 22817],
        JT: [107490, 246054, 195116, 75720],
        GD: [107490, 246054, 21609, 6349],
        GYYY: [107490, 246054, 24763, 5137]
      },
      ['profit_amount']: {
        CT: [7249, 43354, 8132, -4855],
        KT: [7249, 43354, 13739, 1284],
        SW: [7249, 43354, 3492, 320],
        JT: [7249, 43354, -3008, 1878],
        GD: [7249, 43354, 1540, 37],
        GYYY: [7249, 43354, -12505, -3357]
      }
    }
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 初始化数据
  private initEcharts() {
    let textSize = 22
    let series = this.seriesData
    let yeas = ~~this.getMomentTime()
    let companyList = this.getCompanyList
    this.option = {
      color: colorSixList,
      legend: {
        top: '0%',
        padding: 0,
        itemHeight: 14,
        itemWidth: 14,
        itemStyle: {
          borderCap: 'butt'
        },
        lineStyle: {
          type: [0, 10],
          dashOffset: 5
        },
        textStyle: {
          color: '#999',
          fontSize: 18,
          fontWeight: 'bold'
        },
        data: companyList
      },
      grid: {
        left: '6%',
        right: '8%',
        bottom: '2%',
        top: '20%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 103, 177, 1)',
        borderColor: 'rgba(0, 103, 177, 1)',
        textStyle: {
          color: '#fff',
          fontSize: textSize
        }
      },
      xAxis: {
        type: 'category',
        name: '年',
        nameTextStyle: {
          fontSize: 30,
          fontWeight: 'bold'
        },
        boundaryGap: false,
        data: [yeas - 3, yeas - 2, yeas - 1, yeas],
        axisLabel: {
          fontSize: textSize * 1.4,
          fontWeight: 'bold',
          margin: 25
        },
        axisLine: {
          lineStyle: {
            color: '#5db0ea'
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '万元',
        max: function (value) {
          return Math.ceil(value.max) + 10
        },
        nameTextStyle: {
          fontSize: 30,
          fontWeight: 'bold'
        },
        axisLabel: {
          fontSize: textSize
        },
        axisLine: {
          lineStyle: {
            color: '#5db0ea'
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0, 103, 177, 0.4)'
          }
        }
      },
      series: series
    }

    this.myChart && this.myChart.setOption && this.myChart && this.myChart.setOption(this.option)
  }
}
</script>

<style scoped lang="scss">
#GeneralAssessment {
  width: 100%;
  height: 100%;
}
</style>