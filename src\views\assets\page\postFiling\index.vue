<template>
  <el-container class="table-wrapper"
    direction="vertical">
    <!-- 头部搜索 -->
    <SearchBar :items="searchItems"
      @onSearch="handleSearch">
    </SearchBar>

    <!-- 表格 -->
    <Grid v-loading="loadingTable"
      ref="grid"
      :columns="columns"
      :show-selection="false"
      :show-pagination="true"
      :overflow-tooltip="true"
      :search-params="searchParams"
      :show-index="true"
      show-index-fixed="left"
      :remoteUrl="remoteUrl">
      <template #operationSlot="{ row }">
        <Operationbutton :data="row"
          :items="buttonItems" />
      </template>
    </Grid>

    <!-- 弹窗：详情 -->
    <DialogDetail v-if="visibleDetail"
      :visible.sync="visibleDetail"
      :title="rowTitle"
      :rowData="rowDetail"
      :type="searchParams.yqhTab" />
  </el-container>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { PermissionModule } from '@/store/modules/permissionDict'
import { deepClone } from '@/utils'
import Grid from '@/components/Grid/index.vue'
import SearchBar from '@/components/SearchBar/index.vue'
import Operationbutton from '@/components/OperationButton/index.vue'
import DialogDetail from './components/DialogDetail.vue'

@Component({
  components: {
    Grid,
    SearchBar,
    DialogDetail,
    Operationbutton
  }
})
export default class extends Vue {
  private remoteUrl = '/fht-monitor/ast/info/page'
  private loadingTable = false
  private visibleDetail = false
  private rowTitle = ''
  private rowDetail = {}
  private searchParams = {}
  private buttonItems: any = [
    {
      label: '查看详情',
      permission: 'string',
      click: this.seeDetail,
      visible: (row: any) => {
        return true
      }
    }
  ]

  private searchItems = [
    {
      type: 'text',
      key: 'keyword',
      placeholder: '请输入企业名称',
      width: '300px'
    }
  ]
  private columns = [
    {
      label: '备案编号',
      prop: 'yqh_babh'
    },
    {
      label: '备案上报时间',
      prop: 'yqh_basbsj'
    },
    {
      label: '处置方式',
      prop: 'yqh_czfs'
    },
    {
      label: '资产编号',
      prop: 'yqh_zcbh'
    },
    {
      label: '资产类别',
      prop: 'yqh_zclb'
    },
    {
      label: '资产权利人',
      prop: 'yqh_zcqlr'
    },
    {
      label: '出让方名称',
      prop: 'yqh_crfmc'
    },
    {
      label: '受让方名称',
      prop: 'yqh_crfmc'
    },
    {
      label: '坐落地址',
      prop: 'yqh_zldz'
    },
    {
      label: '操作',
      fixed: 'right',
      width: 80,
      slotName: 'operationSlot'
    }
  ]

  // 权限方法
  get getPermission() {
    return (code: string) => {
      return PermissionModule.dictLaodData.includes(code) || false
    }
  }

  // 列表数据搜索
  private handleSearch(condition: any) {
    this.searchParams = Object.assign({}, deepClone(condition))
    this.refresh(false)
  }

  // 列表加载
  private refresh(isCurrent = false) {
    ;(this.$refs.grid as Grid).refresh(isCurrent)
  }

  // 查看详情
  private seeDetail(row = {}) {
    this.rowDetail = row
    this.rowTitle = '事后备案详情'
    this.visibleDetail = true
  }
}
</script>

<style lang="scss" scoped>
.table-wrapper {
  height: 100%;
}
</style>
