/* 负债排名 */

<template>
  <section class="debt-ratioranking-wrap">
    <CommonTitle title="负债排名"
      :isRotuerThrow="true"
      :routerObj="{
        bizAppCode: 'finance',
        detailsPath: '/wel/index?bizAppCode=finance&path=/business/finance/debtStatistics/search/stateEntriseSum'
      }"
      class="m-l-10 m-t-14 m-b-18 p-r-30" />

    <!-- <div class="info-box">
      <span>资产总额</span>
      <span>负债总额</span>
    </div> -->

    <div class="title-box">
      <span>序号</span>
      <span>集团</span>
      <span>负债总额</span>
      <span>同比</span>
      <span>资产负债率</span>
    </div>

    <div v-loading="loading"
      class="conter-wrap"
      ref="tableList">
      <ul class="series-list-box">
        <li v-for="(item,index) of echartsData"
          :key="index"
          :class="[{'active': echartsData.length > 5 && +index === +currentIndex}]"
          @mouseenter="mouseenterHandle"
          @mouseleave="mouseleaveHandle">
          <div class="mode mode-index">
            <span>{{getIndex(index)}}</span>
          </div>

          <div class="mode mode-name">
            <span>{{item.name||'-------------'}}</span>
          </div>

          <div class="mode mode-fzze">
            <span>{{getBigNumberFormat(getIfintData(item.list, 'FZZE').value)}}</span>
            <i>亿元</i>
          </div>

          <div class="mode mode-fztb">
            <div class="content"
              :class="[{'s':+getIfintData(item.list, 'TB').value > 0}, {'x':+getIfintData(item.list, 'TB').value < 0}]">
              <img v-if="+getIfintData(item.list, 'TB').value > 0"
                src="@/views/main/cockpitSecondary/images/thows.png" />
              <img v-if="+getIfintData(item.list, 'TB').value < 0"
                src="@/views/main/cockpitSecondary/images/thowx.png" />
              <span>{{getIfintData(item.list, 'TB').value}}</span>
              <i>%</i>
            </div>
          </div>

          <div class="mode mode-zcfzl">
            <span>{{getIfintData(item.list, 'FZB').value}}</span>
            <i>%</i>
          </div>
        </li>
      </ul>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { liabilitiesRateRank } from '@/api/cockpit'
import { Loading } from '@/decorators'
import { bigNumberFormat } from '@/utils'
import CommonTitle from '@/views/main/cockpitSecondary/components/CommonTitle.vue'

@Component({
  components: {
    CommonTitle
  }
})
export default class extends Vue {
  @Prop() private title!: string // 标题
  @Prop() private year!: string // 年份
  @Prop() private orgCode!: string // 集团
  @Prop() private moon?: string // 月份

  private loading = false
  private echartsData = []

  // 定时轮播数据
  private timer: any
  private currentIndex = 0
  private middleIndex = 2
  private listHeight = 80
  private listDom: any = {}

  // 筛选相应数据
  get getIfintData() {
    return (list: any[], type: string) => {
      let obj = list.filter((item) => {
        return item.type === type
      })

      return obj[0]
    }
  }

  // 万元数据转化成亿元
  get getBigNumberFormat() {
    return (num: string | number) => {
      return bigNumberFormat(+num).value || 0
    }
  }

  // 获取序号
  private getIndex(index: number) {
    return index + 1 > 9 ? index + 1 : '0' + (index + 1)
  }

  // 数据变化，渲染视图
  @Watch('year', { deep: true })
  private changeEchartsData() {
    this.clearTimer()
    this.initData()
  }

  // 初始化
  private mounted() {
    this.initData()
    this.listDom = this.$refs['tableList'] as HTMLDivElement
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await liabilitiesRateRank({
      year: this.year
    })

    this.echartsData = data || []
    // this.scrollTable()
  }

  // 开启定时器，让页面滚动起来
  private scrollTable() {
    let dataLen = this.echartsData.length
    if (!dataLen || dataLen <= this.middleIndex) return
    this.timer = setInterval(() => {
      this.currentIndex++
      if (this.currentIndex > this.middleIndex && this.currentIndex < dataLen) {
        this.listDom.scrollTo(0, (this.currentIndex - this.middleIndex) * this.listHeight)
      } else if (this.currentIndex === dataLen) {
        this.currentIndex = 0
        this.listDom.scrollTo(0, 0)
      }
    }, 4000)
  }

  // 列表鼠标移入，停止动画
  private mouseenterHandle() {
    clearInterval(this.timer)
    this.timer = null
  }

  // 列表鼠标移出，开始动画
  private mouseleaveHandle() {
    // this.scrollTable()
  }

  // 清除定时器，还原相关数据
  private clearTimer() {
    clearInterval(this.timer)
    this.timer = null
    this.currentIndex = 0
    this.listDom.scrollTo(0, 0)
  }

  // 组件销毁
  private destroyed() {
    this.clearTimer()
  }
}
</script>

<style scoped lang="scss">
.debt-ratioranking-wrap {
  position: relative;
  height: 100%;

  .conter-wrap {
    position: relative;
    height: 406px;
    overflow-y: auto;
    scroll-behavior: smooth;
    &::-webkit-scrollbar {
      display: none;
    }
  }

  .title-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 0 10px;
    color: #a5baee;
    span {
      flex: 1;
      font-size: 40px;
      box-sizing: border-box;
      &:nth-child(1) {
        flex: none;
        width: 120px;
      }
    }
  }

  .info-box {
    position: absolute;
    right: 60px;
    top: 30px;
    font-size: 36px;
    span {
      display: inline-block;
      margin-left: 65px;
      &::before {
        display: inline-block;
        content: ' ';
        width: 30px;
        height: 30px;
        margin-right: 10px;
        border-radius: 4px;
        background: #eee;
        transform: translateY(2px);
      }

      &:nth-child(1) {
        &::before {
          background: #fcb45e;
        }
      }
      &:nth-child(2) {
        &::before {
          background: #3eeeff;
        }
      }
    }
  }

  .content-box {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .series-list-box {
    $colorZe: #fcb45e;
    $colorFz: #00ffff;

    margin: 0;
    padding: 0;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    li {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 81px;
      font-size: 36px;
      box-sizing: border-box;
      .mode {
        flex: 1;
      }

      .mode-index {
        flex: none;
        width: 120px;
        span {
          color: #ffc73a;
          font-size: 40px;
          font-style: italic;
          font-weight: normal;
          font-family: 'FZZZHONGHJW';
        }
      }

      .mode-name {
        span {
          display: inline-block;
          color: #e1e1e1;
        }
      }

      .mode-fzze {
        color: $colorFz;
        span {
          font-weight: normal;
          font-family: 'FZZZHONGHJW';
        }
        i {
          font-size: 30px;
          margin-left: 8px;
        }
      }

      .mode-fztb {
        display: flex;
        align-items: center;
        img {
          width: 24px;
          margin-right: 4px;
        }
        span {
          font-weight: normal;
          font-family: 'FZZZHONGHJW';
        }
        i {
          font-size: 30px;
          margin-left: 8px;
        }
        .s {
          color: #ff6267;
        }
        .x {
          color: #00a92b;
        }
      }

      .mode-zcfzl {
        color: $colorFz;
        span {
          font-weight: normal;
          font-family: 'FZZZHONGHJW';
          font-size: 36px;
        }
        i {
          font-size: 30px;
          margin-left: 8px;
        }
      }
    }

    // .active {
    //   .mode-index {
    //     span {
    //       font-size: 46px;
    //       color: #ff368b;
    //     }
    //   }
    // }
  }

  .tabs-box {
    position: absolute;
    right: 26px;
    top: 23px;
    font-size: 36px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    span {
      margin-right: 20px;
    }

    ::v-deep .el-checkbox {
      transform: scale(3) translateX(-28px);
      .el-checkbox__label {
        color: #ccc;
        padding-left: 5px;
      }
      .el-checkbox__inner {
        background-color: #ccc;
        border-color: #ccc;
      }
      .el-checkbox__input.is-checked .el-checkbox__inner {
        background-color: #2dc4d3;
        border-color: #2dc4d3;
      }
    }

    ::v-deep .is-checked {
      .el-checkbox__label {
        color: #00ffff;
      }
    }
  }
}
</style>


