<template>
  <section v-loading="loading"
    class="echarts-dom-wrap">
    <div ref="chartDom"
      class="chartDom"
      :class="{'hide': !interfaceData.length}" />

    <el-empty description="暂无数据"
      class="empty-none-data"
      :class="{'none': interfaceData.length}" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { getPrewarningSummary } from '@/api/prewarning'
import { Loading, Throttle } from '@/decorators'
import * as echarts from 'echarts'

let chartDom = null
let myChart: any = null

@Component({})
export default class extends Vue {
  @Prop() private year!: string // 年份
  @Prop() private orgCode!: string // 集团code

  private loading = false
  private seriesData: any[] = []
  private interfaceData: any[] = []

  // 监听搜索条件改变
  @Watch('orgCode', { deep: true })
  @Watch('year', { deep: true })
  private watchFormData() {
    this.initData()
  }

  // 组件初始化
  private mounted() {
    chartDom = this.$refs.chartDom
    myChart = echarts.init(chartDom as HTMLElement)
    this.initData()
  }

  // 设置组件数据
  @Loading('loading')
  private async initData() {
    let { data } = await getPrewarningSummary({
      orgCode: this.orgCode,
      year: this.year,
      ruleNo: 'EW_R42467056972738560'
    })

    this.interfaceData = data || []

    // 组装数据
    let list: any[] = []
    if (Array.isArray(data) && data.length) {
      data.forEach((item) => {
        list.push({
          name: item.label,
          value: +item.value
        })
      })
    }

    this.seriesData = list
    this.initEcharts()
  }

  // 初始化echarts组件
  private initEcharts() {
    let seriesData = this.seriesData

    let option = {
      tooltip: {
        trigger: 'item',
        valueFormatter: function (value: string) {
          return value + ' 处'
        }
      },
      legend: { show: false },
      grid: {
        top: '0%',
        left: '1%',
        right: '1%',
        bottom: '0%',
        containLabel: true
      },
      series: [
        {
          name: '',
          type: 'pie',
          radius: '70%',
          itemStyle: {
            borderRadius: 0,
            borderColor: '#fff',
            borderWidth: 2
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            show: true,
            position: 'outside',
            fontSize: 14,
            color: '#333',
            formatter: ({ data }: { data: any }) => {
              return `{a|${data.name}} \n {b|${data.value}处}`
            },
            rich: {
              a: {
                fontSize: 14,
                color: '#333'
              },
              b: {
                fontSize: 14,
                color: '#999'
              }
            }
          },
          data: seriesData
        },
        {
          type: 'pie',
          radius: '70%',
          itemStyle: {
            borderRadius: 0,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: true,
            position: 'inside',
            formatter: (parmes: any) => {
              return parmes.percent ? `${parmes.percent}%` : '0%'
            },
            color: '#333',
            fontSize: 14
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          data: seriesData
        }
      ]
    }

    myChart && myChart.setOption && myChart.setOption(option)

    this.clickEchartsItem()
  }

  // 点击 eacharts 图表
  private clickEchartsItem() {
    myChart &&
      myChart.getZr().on('click', (event: any) => {
        this.$router.push('/assets/limitToMoreThanThreeMonths')
      })
  }
}
</script>

<style scoped lang="scss">
.chartDom {
  width: 100%;
  height: 100%;
}
</style>