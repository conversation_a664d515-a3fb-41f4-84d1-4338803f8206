/**
  组件描述:  排名组件
*/
<template>
  <section class="ranking-list-wrapper"
    ref="Wrapper">
    <transition-group tag="div"
      name="slide-fade"
      class="ranking-list">
      <div :class="['ranking-list__item', itemIndex === index && 'ranking-list__item--current']"
        v-for="(item, index) in realList"
        :key="index"
        @mouseenter="mouseenterHandle"
        @mouseleave="mouseleaveHandle">
        <div :class="[
          {'item__active': realList.length > 4 && +index === +currentIndex},
        ]">
          <div class="item__info">
            <div class="info__left">
              <div class="index">{{ getIndex(index) }}</div>
              <el-tooltip v-if="item[realProp.name].length > 5"
                :content="item[realProp.name]"
                placement="top">
                <div class="name">
                  {{ item[realProp.name] }}
                </div>
              </el-tooltip>
              <div v-else
                class="name">
                {{ item[realProp.name] }}
              </div>
            </div>
            <div class="info__right">
              <div class="value">
                {{ item[realProp.value] }}
                <i class="unit">{{ item[realProp.unitStr] || '亿元' }}</i>
              </div>
            </div>
          </div>
          <div class="item__progress">
            <div
              :class="['rate', item[realProp.rate] > 0 && 'up', item[realProp.rate] < 0 && 'down']">
              <i v-if="item[realProp.rate] > 0">+</i>{{ item[realProp.rate] }}%
            </div>

            <div class="item__progress-bar"
              :style="{ width: getBarWidth(item.indicatorRate) }" />
          </div>
        </div>
      </div>
    </transition-group>
  </section>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'

interface RankingItem {
  name: string
  rate: string | string
  value: string | string
  unitStr: string
}

@Component
export default class extends Vue {
  @Prop() private rankingList!: RankingItem[]
  @Prop({ default: () => ({}) }) private propMap!: RankingItem
  @Prop({ default: 'value' }) private rankingProp!: keyof RankingItem // 进行排序的属性
  @Prop({ default: 1 }) private rankingType!: 0 | 1 // 排序方式 0-由小到大 1-由大到小

  @Watch('rankingList', { deep: true })
  private onRankingListChange() {
    this.init()
  }

  private realProp = {
    name: 'name',
    rate: 'rate',
    value: 'value',
    unitStr: 'unitStr'
  }

  private itemIndex = 0

  // 定时轮播数据
  private timer: any
  private currentIndex = 0
  private middleIndex = 2
  private listHeight = 120
  private listDom: any = {}
  private realList: RankingItem[] = []

  created() {
    this.init()
  }

  // 初始化
  private init() {
    this.realProp = Object.assign(this.realProp, this.propMap)

    // 排序
    this.realList = [...this.rankingList]

    if (this.$refs['Wrapper']) {
      this.listDom = this.$refs['Wrapper'] as HTMLDivElement
      this.clearTimer()
      this.scrollTable()
    }
  }

  // 获取序号
  private getIndex(index: number) {
    return index + 1 > 9 ? index + 1 : '0' + (index + 1)
  }

  // 获取进度条宽度
  private getBarWidth(val: number) {
    return `${val}%`
  }

  // 开启定时器，让页面滚动起来
  private scrollTable() {
    let dataLen = this.realList.length
    if (!dataLen || dataLen <= this.middleIndex) return

    this.timer = setInterval(() => {
      this.currentIndex++
      if (this.currentIndex > this.middleIndex && this.currentIndex < dataLen) {
        this.listDom.scrollTo(0, (this.currentIndex - this.middleIndex) * this.listHeight)
      } else if (this.currentIndex === dataLen) {
        this.currentIndex = 0
        this.listDom.scrollTo(0, 0)
      }
    }, 4000)
  }

  // 列表鼠标移入，停止动画
  private mouseenterHandle() {
    clearInterval(this.timer)
    this.timer = null
  }

  // 列表鼠标移出，开始动画
  private mouseleaveHandle() {
    this.scrollTable()
  }

  // 清除定时器，还原相关数据
  private clearTimer() {
    clearInterval(this.timer)
    this.timer = null
    this.currentIndex = 0
    this.listDom.scrollTo(0, 0)
  }

  // 组件销毁
  private destroyed() {
    this.clearTimer()
  }
}
</script>

<style scoped lang="scss">
.ranking-list-wrapper {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  font-size: 36px;
  padding: 18px 19px;
  box-sizing: border-box;
  scroll-behavior: smooth;
  &::-webkit-scrollbar {
    display: none;
  }
}
.ranking-list {
  &__item {
    .item {
      &__info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 42px;
        & > div {
          display: flex;
          align-items: center;
        }

        .info {
          flex: 1;
        }

        .index,
        .value {
          font-style: italic;
          color: #ffc62c;
          font-size: 38px;
          line-height: 52px;
          font-weight: 400;
          font-family: 'FZZZHONGHJW';
          white-space: nowrap;
        }
        .name {
          width: 500px;
        }
        .name {
          font-size: 38px;
          line-height: 52px;
          font-weight: 400;
          margin-left: 18px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          font-family: '';
        }
      }

      &__progress {
        position: relative;
        width: 100%;
        height: 54px;
        border-radius: 7px;
        margin: 16px 0 36px;
        overflow: hidden;
        background: #093091;
        .rate {
          position: absolute;
          right: 0;
          font-size: 30px;
          line-height: 52px;
          font-weight: 400;
          margin-left: 18px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          font-family: '';
        }
        .up {
          color: #ff368b;
        }
        .down {
          color: #42ff5b;
        }
      }
      &__progress-bar {
        height: 100%;
        background-image: linear-gradient(to right, #47efff22 0%, #47efff99 20%, #47efff 100%);
      }
    }
    .item__active {
      .index {
        font-size: 50px;
        color: #ff368b;
      }
    }
  }
}

.unit {
  font-size: 30px;
  font-style: inherit;
  margin-left: -7px;
}

/* 可以设置不同的进入和离开动画 */
/* 设置持续时间和动画函数 */
.slide-fade-enter-active {
  transition: all 0.3s ease;
}
.slide-fade-leave-active {
  transition: all 0.8s cubic-bezier(1, 0.5, 0.8, 1);
}
.slide-fade-enter, .slide-fade-leave-to
/* .slide-fade-leave-active for below version 2.1.8 */ {
  transform: translateX(10px);
  opacity: 0;
}
</style>