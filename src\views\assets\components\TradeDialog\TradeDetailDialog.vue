// 新增资产交易

<template>
  <Dialog title="挂牌申请资料" width="1100px" :visible="visible" :append-to-body="true" @close="closeDlg" v-loading="loading">
    <div slot="body" v-if="detailCom">
      <div :class="['status', 'status--success']">状态:{{ getStateValue(detail.currentAsset.auditStatus) }}</div>
      <div class="content">
        <el-tabs v-model="activeValue" @tab-click="handleClick">
          <el-tab-pane label="标的基本信息" name="1">
            <!-- 出租信息 -->
            <RentalInfoDetail :initData="detail" />
          </el-tab-pane>
          <el-tab-pane label="出租方信息" name="2">
            <!-- 出租方信息 -->
            <LessorInfoDetail :initData="detail.lessor" />
          </el-tab-pane>
          <el-tab-pane label="交易条件与承租方条件" name="3">
            <LesseeQualificationsDetail :initData="detail" />
          </el-tab-pane>
          <el-tab-pane label="审核意见" name="4">
            <AuditOpinion :initData="detail" :mode="AuditOpinionMode" />
          </el-tab-pane>
          <el-tab-pane label="挂牌信息" name="5">
            <ListingInfo :initData="detail" />
          </el-tab-pane>
          <el-tab-pane label="成交结果" name="6">
            <TradeResult :initData="detail" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <div slot="footer" class="footer">
      <div class="footer-left">
        <!-- <el-button @click="onPrint">打印</el-button> -->
      </div>
      <div class="footer-right">
        <el-button @click="closeDlg">关闭</el-button>
      </div>
    </div>
  </Dialog>
</template>
<script lang="ts">
import { Component, Emit, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import RentalInfoDetail from './components/RentalInfoDetail.vue'
import LessorInfoDetail from './components/LessorInfoDetail.vue'
import LesseeQualificationsDetail from './components/LesseeQualificationsDetail.vue'
import ListingInfo from './components/ListingInfo.vue'
import TradeResult from './components/TradeResult.vue'
import AuditOpinion from './components/AuditOpinion.vue'
import AuditFlow from '@/views/projectInvestment/components/AuditFlow.vue'
import { AttachmentFile } from './components/AccessoryFileList.vue'
import { AssetForm } from './components/AssetForm.vue'
import { LessorInfo } from './components/LessorInfoForm.vue'
import { LesseeQualifications } from './components/LesseeQualificationsForm.vue'
import { BusinessModule } from '@/store/modules/businessDict'
import { DetailAsset } from '@/api/assets'

export interface Basic {
  approvalFile: string //	批准文件名称	string
  approvalOrgan: string //		批准单位名称	string
  attachmentFile1: AttachmentFile[] // 附件-授权委托人的身份证明
  attachmentFile2: AttachmentFile[] // 附件-授权委托书
  attachmentFile3: AttachmentFile[] // 附件-内部决策文件
  attachmentFile4: AttachmentFile[] // 附件-审批文件
  attachmentFile5: AttachmentFile[] // 附件-资产权属证明
  attachmentFile6: AttachmentFile[] // 附件-资产评估报告
  attachmentFile7: AttachmentFile[] // 附件-其他资产相关权利人的意思表示
  attachmentFile8: AttachmentFile[] // 附件-其他具有法律效力的权属证明文件
  batchNo: string //		申请批次号	string
  competentDepartment: string //		国家出租企业或主管部门名称	string
  createDept: number //		创建部门	integer
  id: number //			integer
  internalDecision: number | string //		内部决策情况,0：无，1:股东会决议，2：董事会决议，3：总经理办公会决议，4：党委会决议，9：其他	integer
  internalDecisionDesc: string //			string
  otherInternalDecision: string //		内部决策情况其它说明	string
  stateAsset: number //		是否国有资产,1:是，0：否	integer
  stateAssetDesc: string //			string
  status: number //		有效状态，1-正常，0-作废	integer
  statusDesc: string //
}

export interface Verify {
  attachmentFile1: AttachmentFile[] // 附件-受托机构核实意见
  attachmentFile2: AttachmentFile[] // 附件-挂牌项目信息反馈函
  attachmentFile3: AttachmentFile[] // 附件-挂牌项目信息反馈函
  result: number //	核实结果
  verifyOpinions: string //	受托机构核实意见
}

export interface TradeDetail {
  basic: Basic
  assetList: AssetForm[]
  currentAsset: AssetForm
  lessee: LesseeQualifications
  lessor: LessorInfo
  verify: Verify
}

@Component({
  name: 'Container',
  components: {
    Dialog,
    RentalInfoDetail,
    LessorInfoDetail,
    LesseeQualificationsDetail,
    AuditFlow,
    ListingInfo,
    TradeResult,
    AuditOpinion
  }
})
export default class Container extends Vue {
  @Prop({ default: () => ({}) }) private initData!: TradeDetail
  @Prop({ default: () => '' }) private assetId!: string
  @Prop() private visible!: boolean

  @Emit('close')
  private onClose() {
    return
  }
  private detailCom = false //拿到数据时才显示
  private loading = false
  private activeValue = '1'
  private detail: Partial<TradeDetail> = {}
  private AuditOpinionMode="see" //详情时的状态
  created() {
    // this.detail = Object.assign({}, this.initData)
    if(this.assetId!=""){
    this.getdetail(this.assetId)
    }
  }
  //
  private async getdetail(id: string | ''|number) {
    this.loading = true
    try {
      let res = await DetailAsset({
        id: id
      })
      if (res.success) {
        this.loading = false
        this.detailCom = true
        this.detail = Object.assign({}, res.data)
      }
    } catch (e) {
         this.loading = false
      this.closeDialog()
    }
  }
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  private getStateValue(values: any = '') {
    let dict=this.getDictData('asset_out_side_status').find((res: any) => {
      return res.value == values
    })||{label:""}
    return dict.label
  }

  // 点击关闭
  private closeDlg() {
    this.$emit('update:visible', false)
    this.onClose()
  }

  private handleClick(tab: any) {
    //
  }
  private closeDialog() {
    this.$emit('update:visible', false)
  }
  private onPrint() {
    //
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0 !important;
  padding-bottom: 20px !important;
  .dialog-body {
    padding: 0 !important;
  }
}
::v-deep .el-step.is-simple:not(:last-of-type) .el-step__title {
  max-width: 70%;
}

::v-deep .el-select {
  width: 100%;
}

::v-deep .el-step__title {
  cursor: pointer;
}

::v-deep .el-descriptions-item__container {
  align-items: center;
}

.status {
  // line-height: 46px;
  padding: 12px;
  background: #909399;
  color: #fff;
  &--success {
    background: rgb(248, 236, 236);
    color: #67c23a;
  }
}

.content {
  padding: 10px 24px;
}

.footer {
  display: flex;
  justify-content: space-between;
}
</style>
