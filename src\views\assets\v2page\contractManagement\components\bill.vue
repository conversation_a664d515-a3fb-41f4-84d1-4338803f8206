<template>
  <section class="bill-detail-wrap">
    <div v-loading="loading"
      class="content-box">
      <Grid ref="grid"
        border
        :data="gridData"
        :columns="columns"
        :show-pagination="false"
        :overflow-tooltip="true"
        :search-params="searchParams">
        <template slot="slotBillCycle"
          slot-scope="{row}">
          <span>{{row.startDate}} 至 {{row.endDate}}</span>
        </template>
      </Grid>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { getAstBillList } from '@/api/assets'
import { Loading } from '@/decorators'
import Grid from '@/components/Grid/index.vue'

@Component({
  components: {
    Grid
  }
})
export default class extends Vue {
  @Prop() private contractNo!: string

  private loading = false
  private gridData: any[] = []
  private searchParams: any = {}
  private columns = [
    {
      prop: 'periodDesc',
      label: '账单期数'
    },
    {
      prop: 'billCycle',
      label: '账单周期',
      slotName: 'slotBillCycle'
    },
    {
      prop: 'assetName',
      label: '资产名称'
    },
    {
      prop: 'itemNo',
      label: '子资产编号'
    },
    {
      prop: 'billFee',
      label: '应收金额(元)'
    },
    {
      prop: 'paidFee',
      label: '实收金额(元)'
    },
    {
      prop: 'unpaidFee',
      label: '待收金额(元)'
    },
    {
      prop: 'overdueFee',
      label: '欠缴金额(元)'
    },
    {
      prop: 'adjustFee',
      label: '调整金额(元)'
    },
    {
      prop: 'reduceFee',
      label: '减免金额(元)'
    },
    {
      prop: 'billStatusDesc',
      label: '账单状态'
    }
  ]

  private created() {
    this.getGridData()
  }

  // 获取账单信息
  @Loading('loading')
  private async getGridData() {
    let data = await getAstBillList({
      contractNo: this.contractNo
    })

    if (Array.isArray(data.data) && data.data.length) {
      this.gridData = data.data
    }
  }
}
</script>

<style scoped lang="scss">
.content-box {
  height: calc(100vh - 134px);
}
</style>