/**
  组件描述: 表格操作按钮
*/
<template>
  <section class="operation">
    <template v-for="(btn, index) in list">
      <el-button v-if="btn.visible ? btn.visible(row) : true"
        v-permission="btn.permissionId || ''"
        @click.stop="onBtnClick(btn)"
        type="text"
        size="medium"
        v-bind="btn"
        :key="index">
        {{ btn.label }}
      </el-button>
    </template>
  </section>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'

type OperationClick = (row: any, index: number, params: any) => void
type OperationVisible = (row: any) => void
export interface OperationItem {
  label: string // 按钮名称
  style?: 1 | 2 | 3 // 用于区分样式
  params?: object // 按钮区分参数
  click?: OperationClick
  visible?: OperationVisible
}

@Component
export default class Operation extends Vue {
  @Prop({ default: () => [] }) private list!: OperationItem[]
  @Prop({ default: () => ({}) }) private row!: any
  @Prop() private index!: number

  // 点击按钮
  private onBtnClick(item: OperationItem) {
    item.click && item.click(this.row, this.index, item.params)
  }
}
</script>


<style scoped lang="scss">
</style>