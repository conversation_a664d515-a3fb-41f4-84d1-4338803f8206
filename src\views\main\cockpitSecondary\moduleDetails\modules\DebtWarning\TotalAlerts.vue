/* 预警总数 */

<template>
  <section class="total-alerts-wrap">
    <CommonTitle :title="title"
      :isRotuerThrow="true"
      :routerObj="{
        bizAppCode: 'alert',
        detailsPath: '/prewarning/events'
      }"
      class="title-box m-b-30" />

    <!-- 头部tabs切换 -->
    <div class="tabs-box">
      <span v-for="(item,index) of tabsList"
        :key="index"
        :class="{'active':+tabsActive === index+1}"
        @click="onChangeTabs(item.value)">{{item.label}}</span>
    </div>

    <!-- 视图 -->
    <div class="content-box">
      <div v-loading="loadingEarlyWarning"
        class="early-warning-info">
        <div class="layout-left">
          <p>预警总数</p>
          <CountTo :decimals="0"
            :startVal='0'
            :endVal='~~earlyWarningData.total'
            :duration='2000'
            class="cunot" />
        </div>

        <div class="layout-right">
          <div class="clscation">
            <div v-for="(item,index) of earlyWarningData.list"
              :key="index">
              <p>{{item.label}}</p>
              <i :class="`color${index+1}`">{{item.value}}</i>
            </div>
          </div>

          <div class="notice">
            <template v-if="+tabsActive === 1">
              <p>
                <span>监管留查</span>
                <i>{{earlyWarningData.stayCount}}</i>
              </p>
              <p>
                <span>督办</span>
                <i>{{earlyWarningData.superviseCount}}</i>
              </p>
            </template>
          </div>
        </div>
      </div>

      <div v-show="groupData.length"
        v-loading="loadingGroup"
        class="company-list">
        <template v-if="!!interval">
          <img src="@/views/main/cockpitcren/images/thow2.png"
            class="img prev"
            @click="changCarousel('prev')" />
          <img src="@/views/main/cockpitcren/images/thow1.png"
            class="img next"
            @click="changCarousel('next')" />
        </template>

        <el-carousel ref="carousel"
          :autoplay="false"
          :interval="interval"
          :initial-index="initialIndex"
          type="card"
          height="400px"
          indicator-position="none">
          <el-carousel-item v-for="(item,index) of groupData"
            :key="index"
            class="carousel-item">
            <p class="name">{{item.groupName}}</p>
            <div class="dispose-item">
              <p>已处置 <i class="disposed">{{item.disposed}}</i></p>
              <p>未处置 <i class="not-disposed">{{item.unDisposed}}</i></p>
            </div>

            <WaterCharts :key="index"
              :width="400"
              :height="400"
              :title="!item.rote ? 0 : parseInt(item.rote)" />
          </el-carousel-item>
        </el-carousel>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { ElCarousel } from 'element-ui/types/carousel'
import { debtInfaceAlertTotal, debtInfaceAlertTotalByCompany } from '@/api/cockpit'
import CountTo from 'vue-count-to'
import CommonTitle from '@/views/main/cockpitSecondary/components/CommonTitle.vue'
import WaterCharts from '@/views/main/cockpitSecondary/components/WaterCharts.vue'

@Component({
  components: {
    CountTo,
    CommonTitle,
    WaterCharts
  }
})
export default class extends Vue {
  @Prop() private title!: string // 标题
  @Prop() private year!: string // 年份
  @Prop() private moon!: string // 月份
  @Prop() private tabActive!: string // 选中集团

  private carouselDom: any = null
  private loadingEarlyWarning = false
  private loadingGroup = false
  private earlyWarningData = {}
  private groupData: any[] = []

  private tabsActive = 1
  private tabsList = Object.freeze([
    {
      label: '总况',
      value: 1
    },
    {
      label: '已处置',
      value: 2
    },
    {
      label: '未处置',
      value: 3
    }
  ])

  private interval = 0
  private initialIndex = 0

  // 数据变化，渲染视图
  @Watch('tabActive', { deep: true })
  private changeTabActive() {
    this.carousel()
    this.infaceEarlyWarning()
  }

  // 组件初始化
  private mounted() {
    this.carouselDom = this.$refs['carousel'] as ElCarousel
    this.infaceGroupInit()
  }

  // 是否开启轮播动画，或者手动切换
  private carousel() {
    if (+this.tabActive === 0) {
      this.interval = 5000
      this.initialIndex = 0
    } else {
      this.interval = 0
      let findIndex =
        Array.isArray(this.groupData) &&
        this.groupData.findIndex((item) => {
          return item.orgCode === this.tabActive
        })

      this.initialIndex = +findIndex
      this.carouselDom.setActiveItem(findIndex)
    }
  }

  // 头部 tabs 切换
  private onChangeTabs(value: number) {
    this.tabsActive = value
    this.infaceEarlyWarning()
  }

  // 获取预警相关数据
  @Loading('loadingEarlyWarning')
  private async infaceEarlyWarning() {
    let { data } = await debtInfaceAlertTotal({
      dealStatus: this.tabsActive,
      orgCode: this.tabActive || '0'
    })

    this.earlyWarningData = data || {}
  }

  // 各集团数据
  @Loading('loadingGroup')
  private async infaceGroupInit() {
    let { data } = await debtInfaceAlertTotalByCompany({
      orgCode: this.tabActive || '0'
    })

    this.groupData = data || []
  }

  // 手动切换各集团显示
  private changCarousel(str: 'prev' | 'next') {
    if (str === 'prev') this.carouselDom.prev()
    if (str === 'next') this.carouselDom.next()
  }
}
</script>

<style scoped lang="scss">
.total-alerts-wrap {
  $colorNum: #00f6ff;
  $colorH: #fb3f3f;
  $colorR: #ff7500;
  $colorW: #f5b331;
  $colorL: #249df7;

  $colorText: rgba(
    $color: #fff,
    $alpha: 0.8
  );
  $borderImage: linear-gradient(
    90deg,
    rgba(5, 186, 253, 0) 0%,
    rgba(5, 186, 253, 0.1) 10%,
    rgba(5, 186, 253, 0.3) 20%,
    rgba(5, 186, 253, 0.5) 30%,
    rgba(5, 186, 253, 0.8) 40%,
    rgba(5, 186, 253, 1) 50%,
    rgba(5, 186, 253, 0.8) 60%,
    rgba(5, 186, 253, 0.5) 70%,
    rgba(5, 186, 253, 0.3) 80%,
    rgba(5, 186, 253, 0.1) 90%,
    rgba(5, 186, 253, 0) 100%
  );

  @keyframes keyMoveThow1 {
    0% {
      opacity: 1;
      transform: translateX(0);
    }
    50% {
      opacity: 0.5;
      transform: translateX(-20px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }
  @keyframes keyMoveThow2 {
    0% {
      opacity: 1;
      transform: translateX(0);
    }
    50% {
      opacity: 0.5;
      transform: translateX(20px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  position: relative;
  height: 100%;
  p {
    margin: 0;
  }

  .color1 {
    color: $colorH;
  }
  .color2 {
    color: $colorR;
  }
  .color3 {
    color: $colorW;
  }
  .color4 {
    color: $colorL;
  }

  .disposed {
    color: $colorNum;
  }
  .not-disposed {
    color: $colorH;
  }

  .content-box {
    width: 100%;
    height: 88%;
  }

  .tabs-box {
    position: absolute;
    right: 0;
    top: 62px;
    font-size: 38px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    span {
      padding: 4px 0 4px 0;
      margin-right: 50px;
      cursor: pointer;
      &:nth-last-child(1) {
        margin-right: 0;
      }
    }
    .active {
      color: #41eeff;
      border-bottom: 3px solid #41eeff;
    }
  }

  .early-warning-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 30px;
    padding: 30px 20px 10px;
    border-radius: 7px;
    border: 3px solid #0938a2;
    transform: translateY(22px);
    background: linear-gradient(0deg, #0939a0, rgba(8, 45, 133, 0));
    .layout-left {
      width: 170px;
      margin-right: 20px;
      text-align: left;
      span {
        display: inline-block;
        color: $colorText;
        font-size: 34px;
        margin-bottom: 20px;
      }
      .cunot {
        height: 60px;
        line-height: 60px;
        font-size: 56px;
        color: $colorNum;
        font-weight: normal;
        font-family: 'PangMenZhengDao';
      }
    }
    .layout-right {
      flex: 1;
      .clscation {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 10px;
        margin-bottom: 10px;
        border-bottom: 3px solid;
        border-image: $borderImage 1;
        p {
          color: $colorText;
          text-align: center;
        }
        i {
          font-size: 36px;
          font-weight: normal;
          font-family: 'PangMenZhengDao';
        }
      }
      .notice {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 50px;
        span {
          margin-right: 10px;
          color: rgba($color: #fff, $alpha: 0.8);
        }
        i {
          color: $colorNum;
          font-size: 36px;
        }
      }
    }
  }

  .company-list {
    position: relative;
    top: 10px;
    .img {
      position: absolute;
      z-index: 1;
      top: 135px;
      width: 100px;
      cursor: pointer;
    }
    .prev {
      left: -40px;
      animation: keyMoveThow2 5s infinite;
    }
    .next {
      right: -40px;
      animation: keyMoveThow1 5s infinite;
    }
    .name {
      position: absolute;
      top: 200px;
      z-index: 2;
      width: 400px;
      font-size: 34px;
      text-align: center;
      color: rgba($color: #fff, $alpha: 0.7);
    }
    .dispose-item {
      position: absolute;
      bottom: 20px;
      z-index: 2;
      width: 400px;
      padding-left: 143px;
      color: rgba($color: #fff, $alpha: 0.7);
      box-sizing: border-box;
      p {
        line-height: 34px;
      }
      i {
        font-size: 34px;
      }
    }
    ::v-deep .el-carousel {
      .el-carousel__arrow {
        width: 0;
        overflow: hidden;
      }
      .el-carousel__mask {
        background: none;
      }
      .el-carousel__item {
        opacity: 0.4;
      }
      .is-active {
        opacity: 1;
        .name {
          color: #fff;
        }
        .dispose-item {
          color: #fff;
        }
      }
    }
  }
}
</style>


