/* 数智监管系统：标题文案 */

<template>
  <div id="cockpitTitle" />
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import * as echarts from 'echarts'

type EChartsOption = echarts.EChartsOption

@Component
export default class extends Vue {
  @Prop({ default: 150 }) private height?: number
  @Prop({ default: 100 }) private fontSize?: number
  @Prop({ default: '#75edff' }) private color?: string
  @Prop({ default: '数智监管系统' }) private title?: string

  private chartDom: HTMLElement | null = null
  private myChart: any = null
  private option: EChartsOption = {}

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('cockpitTitle') as HTMLElement
    this.chartDom.style.height = `${this.height}px`
    this.myChart = echarts.init(this.chartDom as HTMLElement)
    this.initEcharts()
  }

  // 初始化数据
  private initEcharts() {
    let title = '金华市国资委数智监管系统'
    let color = this.color
    let fontSize = this.fontSize

    this.option = {
      graphic: {
        elements: [
          {
            type: 'text',
            left: 'center',
            top: 'center',
            style: {
              text: title,
              fontSize: fontSize,
              fontFamily: 'FZZZHONGHJW',
              fontWeight: 'normal',
              lineDash: [0, 200],
              lineDashOffset: 0,
              fill: 'transparent',
              stroke: color,
              lineWidth: 1,
              shadowBlur: 30,
              shadowColor: '#000'
            },
            keyframeAnimation: {
              duration: 3000,
              loop: false,
              keyframes: [
                {
                  percent: 0.7,
                  style: {
                    fill: 'transparent',
                    lineDashOffset: 200,
                    lineDash: [200, 0]
                  }
                },
                {
                  percent: 0.8,
                  style: {
                    fill: 'transparent'
                  }
                },
                {
                  percent: 1,
                  style: {
                    fill: color,
                    fontWeight: 'normal'
                  }
                }
              ]
            }
          }
        ]
      }
    }

    this.myChart && this.myChart.setOption && this.myChart && this.myChart.setOption(this.option, true)
  }

  // 销毁相关数据
  private destroyed() {
    this.chartDom = null
    this.myChart = null
  }
}
</script>

<style scoped lang="scss">
#cockpitTitle {
  width: 100%;
}
</style>

