/**
  组件描述:  新增年度计划投资弹框
*/
<template>
  <Dialog :title="`投资项目计划备案-${mode === 'see' ? '详细' : '审批'}`"
    width="980px"
    :visible="visible"
    :close-on-click-modal="true"
    :destroy-on-close="true"
    :append-to-body="true"
    @close="closeDlg">
    <div slot="body"
      class="">

      <el-descriptions title="">
        <el-descriptions-item label="备案编号">{{ investProjectPlanFiling.planNo }}
        </el-descriptions-item>
        <el-descriptions-item label="填报单位">{{ investProjectPlanFiling.orgId }}
        </el-descriptions-item>
        <el-descriptions-item label="年份">{{ investProjectPlanFiling.year }}</el-descriptions-item>
      </el-descriptions>

      <div class="plan-table-wrapper">
        <PlanTable ref="PlanTable"
          v-if="investProjectPlanFiling.filingDetailList!=0"
          :list="investProjectPlanFiling.filingDetailList"
          :loading="loading"
          :recordNumber="investProjectPlanFiling.id"
          mode="see" />
      </div>

      <AccessoryList :list="oldAccessoryList"
        title="附件列表"
        mode="download"
        class="m-20" />

      <!-- 审批流 -->
      <!-- <div class="m-l-20"
        style="font-weight: bold; color: #303133;font-size: 16px;">原审核情况</div>
      <AuditFlow mode="see" />

      <AccessoryList :list="accessoryList"
        title="原附件列表"
        mode="upload"
        class="m-20" />

      审批流
      <AuditFlow :mode="mode" /> -->

    </div>
    <div slot="footer">
      <el-button v-if="mode === 'see'"
        @click="closeDlg">关闭</el-button>
      <el-button v-if="mode === 'audit'"
        @click="closeDlg">取消</el-button>
      <el-button v-if="mode === 'audit'"
        class="primary-buttom"
        type="primary"
        :loading="loading"
        @click="submitForm">确认</el-button>
    </div>
  </Dialog>
</template>
<script lang="ts">
import { Component, Emit, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import { Loading } from '@/decorators'
import { ElForm } from 'node_modules/element-ui/types/form'
import { createPlanIni, createProject } from '@/api/projectInvestment'
import PlanTable from '../../components/PlanTable/PlanGrid.vue'
import Uploader from '../../components/Uploader/index.vue'
import AuditFlow from '../../components/AuditFlow.vue'
import AccessoryList, { Accessory } from '@/views/assets/components/TradeDialog/components/AccessoryFileList.vue'

interface InvestProjectPlanFiling {
  boardResolution: string
  businessSubmissionTime: string
  companyCode: string
  enterpriseSubmitter: string
  investmentPlanReport: string
  otherDecisionDocuments: string
  projectSchedule: string
  recordNumber: string
  year: string
}

@Component({
  components: {
    Dialog,
    Uploader,
    PlanTable,
    AuditFlow,
    AccessoryList
  }
})
export default class extends Vue {
  @Prop() private visible!: boolean
  @Prop() private title!: string
  @Prop() private mode!: 'audit' | 'see'
  @Prop() private detail!: InvestProjectPlanFiling

  private loading = false

  private tableList: {} = {
    investProjectAgentDataExlVO: [], // 代建
    investProjectDataExlVO: [], // 企业
    investProjectGovDataExlVO: [] // 政府
  }

  private investProjectPlanFiling: InvestProjectPlanFiling = {
    boardResolution: '',
    businessSubmissionTime: '',
    companyCode: '',
    enterpriseSubmitter: '',
    investmentPlanReport: '',
    otherDecisionDocuments: '',
    projectSchedule: '',
    recordNumber: '',
    year: String(new Date().getFullYear() - 1)
  }

  private oldAccessoryList: Accessory[] = [
    {
      fileName: '投资计划报告',
      isRequired: true,
      prop: 'investmentPlanReport',
      fileList: []
    },
    {
      fileName: '项目计划表',
      isRequired: true,
      prop: 'projectSchedule',
      fileList: []
    },
    {
      fileName: '董事会决议',
      isRequired: true,
      prop: 'boardResolution',
      fileList: []
    },
    {
      fileName: '其他决策文件',
      isRequired: true,
      prop: 'otherDecisionDocuments',
      fileList: []
    }
  ]

  private accessoryList: Accessory[] = [
    {
      fileName: '投资计划报告',
      isRequired: true,
      prop: 'investmentPlanReport',
      fileList: []
    },
    {
      fileName: '项目计划表',
      isRequired: true,
      prop: 'projectSchedule',
      fileList: []
    },
    {
      fileName: '董事会决议',
      isRequired: true,
      prop: 'boardResolution',
      fileList: []
    },
    {
      fileName: '其他决策文件',
      isRequired: true,
      prop: 'otherDecisionDocuments',
      fileList: []
    }
  ]

  // 弹窗关闭
  @Emit('close')
  private onClose() {
    //
  }

  created() {
    if (this.mode == 'see') {
      this.investProjectPlanFiling = Object.assign({}, this.detail)
    }

    // let investProjectPlanFiling = this.investProjectPlanFiling
  }

  @Loading('loading')
  private async submitForm() {
    // 审批只需对审核流进行校验
  }

  // 附件校验
  private validateFiles(): boolean {
    return true
  }

  // 关闭弹窗
  private closeDlg() {
    this.$emit('update:visible', false)
    this.onClose()
  }
}
</script>

<style lang="scss" scoped>
.uploader-list {
  display: flex;
  flex-wrap: wrap;
  ::v-deep .uploader-wrapper {
    margin: 12px 24px;
  }
}
::v-deep .el-descriptions {
  padding: 0 20px;
}
</style>

