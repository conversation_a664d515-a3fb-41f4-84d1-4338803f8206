<template>
  <el-container class="container"
    direction="vertical">
    <!-- 头部搜索 -->
    <el-form :inline="true"
      :model="formData"
      ref="ruleForm"
      label-width="84px"
      class="form-box">
      <el-form-item label="所属集团"
        prop="orgCode">
        <el-select v-model="formData.orgCode"
          clearable
          placeholder="请选择"
          class="mode-input">
          <el-option v-for="item of compTree"
            :key="item.deptCode"
            :label="item.deptName"
            :value="item.deptCode" />
        </el-select>
      </el-form-item>

      <el-form-item label="直属单位"
        prop="bizCodeList">
        <OrgTree v-model="formData.bizCodeList"
          ref="orgTree"
          width="260"
          :deptCode="formData.orgCode"
          class="mode-input" />
      </el-form-item>

      <el-form-item label="主资产编号"
        prop="assetNo">
        <el-input v-model.trim="formData.assetNo"
          clearable
          placeholder="请输入"
          class="mode-input" />
      </el-form-item>

      <el-form-item label="产权证号"
        prop="cerNo">
        <el-input v-model.trim="formData.certNo"
          clearable
          placeholder="房产证/土地证/不动产证号"
          class="mode-input" />
      </el-form-item>

      <el-form-item label=" ">
        <el-button type="primary"
          icon="el-icon-search"
          @click="searchForm">查询</el-button>
        <el-button icon="el-icon-refresh"
          @click="resetForm">重置</el-button>
        <el-button :loading="loadingExport"
          icon="el-icon-download"
          @click="exportForm">导出列表</el-button>
      </el-form-item>
    </el-form>

    <Grid ref="grid"
      :show-pagination="true"
      :overflow-tooltip="true"
      :remote-url="remoteUrl"
      :columns="columns"
      :search-params="searchParams"
      @row-click="rowClickHandle"
      @onLoaded="onLoadedHandle">
      <template slot="slotNameCertCat"
        slot-scope="scope">
        <span v-if="scope.row.certCat == 1">甲方自有</span>
        <span v-else-if="scope.row.certCat == 2">受乙委托</span>
        <span v-else>-</span>
      </template>

      <template slot="operationSlot"
        slot-scope="scope">
        <Operation :list="operationItems"
          :row="scope.row" />
      </template>
    </Grid>

    <!-- 查看详情 -->
    <DetailCom v-if="visibelDetail"
      :visible.sync="visibelDetail"
      :rowData="rowData" />
  </el-container>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { downloadXls } from '@/utils'
import { astInfoExport } from '@/api/assets'
import { Loading } from '@/decorators'
import { getAstCompTree } from '@/api/projectInvestment'
import { ElForm } from 'element-ui/types/form'
import Grid from '@/components/Grid/index.vue'
import OrgTree from '@/components/FormComment/orgTree.vue'
import DetailCom from './DetailCom.vue'

@Component({
  components: {
    Grid,
    OrgTree,
    DetailCom
  }
})
export default class HouseCertificate extends Vue {
  @Prop() private assetType!: string // 1 房产；2 土地

  private loadingExport = false
  private visibelDetail = false
  private rowData = {}
  private compTree = []
  private formData = {
    orgCode: '',
    bizCodeList: [],
    assetNo: '',
    certNo: ''
  }

  // 详细表格数据
  private gridData: any[] = []
  private remoteUrl = '/fht-monitor/ast/cert/astInfo/page'
  private searchParams: any = {
    assetType: this.assetType
  }
  private searchItems: any[] = []
  private operationItems: any[] = [
    {
      label: '查看',
      params: { type: 'see' },
      click: this.operateHandel
    }
  ]
  private columns = [
    {
      label: '所属集团',
      prop: 'orgName'
    },
    {
      label: '直属单位',
      prop: 'bizCodeName'
    },
    {
      label: '主资产编号',
      prop: 'assetNo'
    },
    {
      label: '主资产名称',
      prop: 'mainAssetName'
    },
    {
      label: '不动产证号',
      prop: 'realEstateCertNo'
    },
    {
      label: '房产证号',
      prop: 'houseCertNo'
    },
    {
      label: '土地证号',
      prop: 'landCertNo'
    },
    {
      label: '建筑面积(㎡)',
      prop: 'coveredArea'
    },
    {
      label: '土地面积(㎡)',
      prop: 'landArea'
    },
    {
      label: '产权类型',
      prop: 'certCat',
      slotName: 'slotNameCertCat'
    },
    {
      label: '使用期限',
      prop: 'useTerm'
    },
    {
      label: '开始时间',
      prop: 'startDate'
    },
    {
      label: '结束时间',
      prop: 'endDate'
    },
    {
      label: '操作',
      fixed: 'right',
      width: 60,
      slotName: 'operationSlot'
    }
  ]

  // 初始化
  created() {
    this.getCompTree()
  }

  // 获取直属单位
  private async getCompTree() {
    let { data } = await getAstCompTree({ parentId: 0, deptCategory: 1 })
    this.compTree = data || []
  }

  // 查询
  private searchForm() {
    this.handleSearch(this.formData)
  }

  // 重置
  private resetForm() {
    ;(this.$refs.ruleForm as ElForm).resetFields()
    ;(this.$refs.orgTree as any).clearHandle()

    this.formData.orgCode = ''
    this.formData.assetNo = ''
    this.formData.certNo = ''
    this.formData.bizCodeList = []
    this.handleSearch()
  }

  // 列表搜索
  private handleSearch(condition: any = {}) {
    this.searchParams = Object.assign(
      {
        assetType: this.assetType
      },
      condition
    )
    ;(this.$refs['grid'] as any).refresh()
  }

  // 表格数据加载完成后触发
  private onLoadedHandle(data: any) {
    this.gridData = data.records || []
  }

  // 导出
  @Loading('loadingExport')
  private async exportForm() {
    let objData = Object.assign(
      {
        assetType: this.assetType
      },
      this.formData
    )

    let res = await astInfoExport(objData)
    let text = +this.assetType === 1 ? '资产管理（房产列表）' : '资产管理（土地列表）'
    let time = new Date().getTime()

    downloadXls(res.data, `${text}_${time}.xlsx`)
    this.$message.success(res.msg || '导出成功')
  }

  // 点击行触发查看详情操作
  private rowClickHandle(row: any) {
    this.rowData = row
    this.visibelDetail = true
  }

  // 列表操作
  private operateHandel(row: any = {}, index: number, params: any) {
    this.rowData = row

    switch (params.type) {
      case 'see':
        this.visibelDetail = true
        break
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: calc(100vh - 156px);
}

.form-box {
  margin-bottom: 14px;
  padding: 10px 0 0;
  border-radius: 4px;
  background: rgb(172 196 221 / 10%);
  border: 1px solid rgb(172 196 221 / 20%);
  .el-form-item {
    margin-bottom: 10px;
  }
  .mode-input {
    width: 260px;
  }
}
</style>
