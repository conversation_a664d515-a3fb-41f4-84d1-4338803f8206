/* 债务到期 */

<template>
  <section class="debt-flows-wrap">
    <CommonTitle :title="title"
      :isRotuerThrow="true"
      :routerObj="{
        bizAppCode: 'finance',
        detailsPath: '/wel/index?bizAppCode=finance&path=/business/finance/debtStatistics/search/stateEntriseSum'
      }" />

    <!-- 货币资金余额 -->
    <div class="balance-box">
      <span>货币资金余额</span>
      <CountTo :decimals="0"
        :startVal="0"
        :endVal="+surplusData.value || 0"
        :duration="1500"
        class="num" />
      <span>{{surplusData.unit}}元</span>
    </div>

    <!-- 头部tabs切换 -->
    <div class="tabs-box">
      <span v-for="(item,index) of echartsData"
        :key="index"
        :class="{'active':+tabsActive === index+1}"
        @click="onChangeTabs(item.value)">{{item.name|| '——'}}</span>
    </div>

    <div class="title-box">
      <span>债务类型</span>
      <span>债务金额</span>
    </div>

    <!-- 列表 -->
    <div v-loading="loading"
      class="content-box"
      ref="tableList">
      <div v-for="(item, index) of dataList"
        :key="index"
        class="mode"
        @mouseenter="mouseenterHandle"
        @mouseleave="mouseleaveHandle"
        :class="[{'mode-active': dataList.length > 3 && +index === +currentIndex}]">
        <h6>{{item.name}}</h6>
        <p>
          <span>{{item.value}}</span>
          <i>{{item.unit}}元</i>
        </p>
      </div>
    </div>

    <!-- 合计 -->
    <div class="total-box">
      <div v-for="(item, index) of totalList"
        :key="index"
        class="mode">
        <h6 class="total">合计</h6>
        <p class="total">
          <span>{{item.value}}</span>
          <i>{{item.unit}}元</i>
        </p>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { deepClone } from '@/utils'
import { Loading } from '@/decorators'
import { liabilitiesFinSummary, liabilitiesFinSurplus } from '@/api/cockpit'
import CountTo from 'vue-count-to'
import CommonTitle from '@/views/main/cockpitSecondary/components/CommonTitle.vue'

@Component({
  components: {
    CountTo,
    CommonTitle,
    liabilitiesFinSurplus
  }
})
export default class extends Vue {
  @Prop() private title!: string // 标题
  @Prop() private year!: string // 年份
  @Prop() private orgCode!: string // 集团
  @Prop() private moon?: string // 月份

  private loading = false
  private dataList: any[] = []
  private totalList: any[] = []
  private surplusData = {}
  private echartsData = []

  // 头部tabs切换
  private tabsActive = '1'

  // 定时轮播数据
  private timer: any
  private currentIndex = 0
  private middleIndex = 2
  private listHeight = 116
  private listDom: any = {}

  // 数据变化，渲染视图
  @Watch('orgCode', { deep: true })
  @Watch('year', { deep: true })
  @Watch('moon', { deep: true })
  private changeEchartsData() {
    this.tabsActive = '1'
    this.clearTimer()
    this.initSurplus()
    this.initData()
  }

  // 初始化
  private mounted() {
    this.listDom = this.$refs['tableList'] as HTMLDivElement
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await liabilitiesFinSummary({
      year: this.year,
      month: this.moon,
      orgCode: this.orgCode
    })

    this.echartsData = data.dueVOList || {}
    this.filterData()
  }

  // 筛选数据
  private filterData() {
    let data = deepClone(this.echartsData)

    let find = data.find((item: { value: string }) => {
      return +item.value === +this.tabsActive
    })

    if (Array.isArray(find.finTypeList) && find.finTypeList.length) {
      let list = find.finTypeList.splice(1, find.finTypeList.length)

      this.totalList = find.finTypeList.splice(0, 1)
      this.dataList = list
      this.scrollTable()
    } else {
      this.dataList = []
    }
  }

  // 获取货币资金余额
  private async initSurplus() {
    let { data } = await liabilitiesFinSurplus({
      year: this.year,
      moon: this.moon,
      orgCode: this.orgCode
    })

    this.surplusData = data
  }

  // 头部 tabs 切换
  private onChangeTabs(value: string) {
    this.tabsActive = value
    this.clearTimer()
    this.filterData()
  }

  // 开启定时器，让页面滚动起来
  private scrollTable() {
    let dataLen = this.dataList.length
    if (!dataLen || dataLen <= this.middleIndex) return
    this.timer = setInterval(() => {
      this.currentIndex++
      if (this.currentIndex > this.middleIndex && this.currentIndex < dataLen) {
        this.listDom.scrollTo(0, (this.currentIndex - this.middleIndex) * this.listHeight)
      } else if (this.currentIndex === dataLen) {
        this.currentIndex = 0
        this.listDom.scrollTo(0, 0)
      }
    }, 4000)
  }

  // 列表鼠标移入，停止动画
  private mouseenterHandle() {
    clearInterval(this.timer)
    this.timer = null
  }

  // 列表鼠标移出，开始动画
  private mouseleaveHandle() {
    this.scrollTable()
  }

  // 清除定时器，还原相关数据
  private clearTimer() {
    clearInterval(this.timer)
    this.timer = null
    this.currentIndex = 0
    this.listDom.scrollTo(0, 0)
  }

  // 组件销毁
  private destroyed() {
    this.clearTimer()
  }
}
</script>

<style scoped lang="scss">
.debt-flows-wrap {
  position: relative;
  height: 100%;

  h6,
  p {
    margin: 0;
  }

  .tabs-box {
    font-size: 36px;
    margin-bottom: 20px;
    padding: 0 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    span {
      padding: 4px 0 4px 0;
      margin-right: 50px;
      cursor: pointer;
      &:nth-last-child(1) {
        margin-right: 0;
      }
    }
    .active {
      color: #00e8ff;
      border-bottom: 3px solid #00e8ff;
    }
  }

  .balance-box {
    position: absolute;
    top: 62px;
    right: 0;
    display: flex;
    align-items: end;
    span {
      font-size: 36px;
    }
    .num {
      color: #ff7a27;
      font-size: 56px;
      padding: 0 7px 0 10px;
      font-weight: normal;
      font-family: 'PangMenZhengDao';
    }
  }

  .title-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    box-sizing: border-box;
    span {
      color: #a5baee;
      font-size: 38px;
    }
  }

  .content-box {
    width: 100%;
    height: 47%;
    margin-top: 17px;
    overflow-y: auto;
    scroll-behavior: smooth;
    &::-webkit-scrollbar {
      display: none;
    }
    .mode-active {
      box-shadow: 0 0 20px #00ffff inset;
    }
  }

  .total-box {
    position: absolute;
    left: 0;
    bottom: -9px;
    width: 100%;
  }

  .mode {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 21px 30px;
    margin-bottom: 17px;
    border-radius: 8px;
    box-sizing: border-box;
    background: url('../../../images/mode_bj.png') no-repeat center center;
    background-size: 100% 100%;
    &:nth-last-child(1) {
      margin-bottom: 20px;
    }
    .total {
      color: #ffc73a;
    }
    h6 {
      font-size: 40px;
    }
    p {
      color: #00ffff;
      font-size: 46px;
      font-weight: normal;
      font-family: 'PangMenZhengDao';
      i {
        font-size: 34px;
        margin-left: 8px;
      }
    }
  }
}
</style>


