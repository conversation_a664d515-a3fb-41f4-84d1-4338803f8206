<template>
  <Dialog title="查看详情"
    width="80vw"
    :visible="visible"
    @close="handleClose">
    <div slot="body"
      v-loading="loading">
      <div class="header-box">
        <el-descriptions :column="3"
          size="medium">
          <el-descriptions-item label="主资产编号">{{rowData.assetNo}}</el-descriptions-item>
          <el-descriptions-item label="所属集团">{{rowData.orgName}}</el-descriptions-item>
          <el-descriptions-item label="直属单位">{{rowData.bizCodeName}}</el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="content-box">
        <el-tabs v-model="activeName">
          <el-tab-pane label="资产详情"
            name="astDetails">
            <AstDetails :rowData="rowData" />
          </el-tab-pane>

          <el-tab-pane label="资产明细"
            name="astDetailed">
            <AstDetailed :rowData="rowData" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <div slot="footer">
      <el-button @click="handleClose">关 闭</el-button>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import AstDetails from './AstDetails.vue'
import AstDetailed from './AstDetailed.vue'

@Component({
  components: {
    Dialog,
    AstDetails,
    AstDetailed
  }
})
export default class extends Vue {
  @Prop() private visible!: boolean
  @Prop() private rowData!: object

  private loading = false
  private activeName: 'astDetails' | 'astDetailed' = 'astDetails'

  // 关闭弹窗
  private handleClose() {
    this.$emit('update:visible', false)
  }
}
</script>

<style scoped lang="scss">
.header-box {
  padding: 0 0 10px;
  p {
    margin: 0;
  }
}

.content-box {
  position: relative;
}
</style>
