<!-- 投资项目计划调整备案 -->
<template>
  <el-container class="records" direction="vertical">
    <search-bar :items="searchItems" @onSearch="handleSearch" v-if="mode=='index'">
      <el-button v-if="isCompany" class="primary-buttom" type="primary" @click="onAdd">新增</el-button>
    </search-bar>

    <!-- 表格 -->
    <Grid
      ref="grid"
      :columns="getcol"
      :show-pagination="true"
      :overflow-tooltip="true"
      :show-index="true"
      :remote-url="remoteUrl"
      :search-params="searchParams"
      @row-click="onSee"
    >
      <template #approvalStatusSlot="{row}">
        <div
          :class="[row.approvalStatus === 1 && 'audit-success', row.approvalStatus === 1 && 'audit-pending', row.approvalStatus === 1 && 'audit-danger']"
        >{{ row.approvalStatus }}</div>
      </template>
      <template #planType='{row}'>
        <span v-if="row.planType==1">计划备案</span>
        <span v-if="row.planType==2">调整备案</span>
      </template>
      <template #operationSlot="{row}" v-if="mode=='index'">
        <Operation :list="operationList" :row="row" />
      </template>
    </Grid>

    <!-- 新增 -->

    <AdjustRecordDialog
      v-if="AdjustRecordDialogVisible"
      :visible.sync="AdjustRecordDialogVisible"
      @success="refreshGrid"
      :mode="detailMode"
      :detail="currentRow"
    />

    <!-- 查看或审批 -->
    <RecordDetailDialog
      v-if="recordDetailDaologVisible"
      :visible.sync="recordDetailDaologVisible"
      :mode="recordDetailMode"
      :detail="currentRow"
      @success="refreshGrid"
    />

    <!--附件查看 -->
    <Uploader
      v-model="accessoryList"
      :title="accessoryTitle"
      :uploadable="false"
      :is-private="false"
      :show-cover="false"
      :visible.sync="accessoryVisible"
    />
  </el-container>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import Grid from '@/components/Grid/index.vue'
import searchBar from '@/components/SearchBar/index.vue'
import Operation, { OperationItem } from '@/components/Operation/index.vue'
import AdjustRecordDialog from './components/AdjustRecordDialog.vue'
import RecordDetailDialog from './components/RecordDetailDialog.vue'
import Uploader from '@/components/Uploader/index.vue'
import { cond } from 'lodash'

interface RecordItem {
  approvalStatus: string // 审批状态
  auditCompletionTime: string // 审批完成时间
  boardResolution: string // 董事会决议
  businessSubmissionTime: string // 企业提交时间
  createDept: number // 填报单位名称
  createTime: string // 创建时间
  createUser: number // 创建人
  enterpriseSubmitter: string // 企业提交人
  id: number
  investmentPlanReport: string // 投资计划报告
  isDeleted: number // 是否删除
  orgCode: string
  orgName: string
  otherDecisionDocuments: string // 其他决策文件
  projectSchedule: string // 项目计划
  recordNumber: string // 备案编号
  remark: string // 备注
  reviewFeedback: string // 反馈
  status: number
  updateTime: string
  updateUser: number
  year: string // 年份
}

@Component({
  components: {
    Grid,
    searchBar,
    Operation,
    Uploader,
    AdjustRecordDialog,
    RecordDetailDialog
  }
})
export default class Project extends Vue {
  @Prop({ default: () => 'index' }) private mode?: 'index' | 'audit'
  @Prop() private option?: any
  private readonly remoteUrl = '/fht-monitor/invest/plan/page'

  private AdjustRecordDialogVisible = false
  private detailMode = 'add' // 模式
  // 查看或审批
  private recordDetailDaologVisible = false
  private recordDetailMode = 'see'

  private currentRow: any = {}

  // 附件列表
  private accessoryList: string[] = []
  private accessoryTitle = ''
  private accessoryVisible = false

  private searchItems = [
    {
      type: 'text',
      key: 'keyword',
      placeholder: '请输入项目编码/项目名称进行查询',
      width: '330px'
    },
    {
      type: 'year',
      key: 'year',
      label: '年度',
      placeholder: ''
    }
  ]

  private searchParams = {
    planType: 2,
    ...this.option
  }

  // 操作按钮
  private operationList: OperationItem[] = [
    {
      label: '查看',
      click: this.onSee,
      style: 3,
      visible: (row: any) => {
        return true
      }
    },
    {
      label: '审批',
      style: 2,
      click: this.onAudit,
      visible: (row: any) => {
        return !this.isCompany
      }
    }
  ]
  private created() {
    this.searchParams = Object.assign(this.searchParams, this.option)
  }
  get getcol(){
    return this.mode=='index'?this.cols:this.cols_audit
  }
  private onSee(row: any) {
    // 查看
    this.currentRow = row
    this.detailMode = 'see'
    this.AdjustRecordDialogVisible = true
  }

  private onAudit(row: any) {
    // 审批
    this.recordDetailMode = 'see'
    this.currentRow = row
    this.recordDetailDaologVisible = true
  }

  private cols = [
    {
      prop: 'planNo',
      label: '备案编号',
      minWidth: 160
    },
    {
      prop: 'circulationStatusSlot',
      label: '业务状态'
    },
    {
      prop: 'year',
      label: '年份'
    },

    {
      prop: 'orgName',
      label: '填报单位名称',
      minWidth: 200
    },

    {
      prop: 'createUserName',
      label: '提交人',
      minWidth: 160
    },
    {
      prop: 'createTime',
      label: '提交时间',
      minWidth: 160
    },
    {
      prop: '',
      label: '操作',
      labelAlign: 'center',
      slotName: 'operationSlot',
      width: this.mode == 'audit' ? 1 : 75,
      fixed: 'right'
    }
  ]
  private cols_audit = [
    {
      prop: 'planNo',
      label: '备案编号',
      minWidth: 160
    },
    {
      slotName: 'planType',
      label: '备案类型',
      minWidth: 160
    },
    {
      prop: 'circulationStatusSlot',
      label: '业务状态'
    },
    {
      prop: 'year',
      label: '年份'
    },

    {
      prop: 'orgName',
      label: '填报单位名称',
      minWidth: 200
    },

    {
      prop: 'createUserName',
      label: '提交人',
      minWidth: 160
    },
    {
      prop: 'createTime',
      label: '提交时间',
      minWidth: 160
    },
    {
      prop: '',
      label: '操作',
      labelAlign: 'center',
      slotName: 'operationSlot',
      width: this.mode == 'audit' ? 1 : 75,
      fixed: 'right'
    }
  ]

  // 是否是企业端
  get isCompany() {
    return true
  }

  // 新增
  private onAdd() {
    this.detailMode = 'add'
    this.AdjustRecordDialogVisible = true
  }

  private handleSearch(condition: any) {
    this.searchParams = Object.assign(condition, this.searchParams)
    this.refreshGrid()
  }

  // 表格更新
  private refreshGrid(isCurrent = false) {
    ;(this.$refs.grid as Grid).refresh(isCurrent)
  }

  // 账单展示
  private onAccessoryShow(accessoryList: any, accessoryTitle: string) {
    this.accessoryList = accessoryList
    this.accessoryTitle = accessoryTitle
    this.accessoryVisible = true
  }

  // 分割附件url
  private splitUrl(url = '') {
    return (url || '').split(',').filter(item => item !== '') || []
  }
}
</script>

<style lang="scss" scoped>
.records {
  height: 100%;

  .accessory {
    color: #409eff;
    cursor: pointer;
  }
}
</style>