/* 资产财务 */

<template>
  <section class="assets-finance-wrap">
    <AssetSupervision class="m-b-76" />
    <FinanceSupervision />
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import AssetSupervision from '@/views/main/cockpitcren/components/OnePage/AssetSupervision.vue'
import FinanceSupervision from '@/views/main/cockpitcren/components/OnePage/FinanceSupervision.vue'

@Component({
  components: {
    AssetSupervision,
    FinanceSupervision
  }
})
export default class extends Vue {}
</script>

<style scoped lang="scss">
.assets-finance-wrap {
  position: relative;
  width: 100%;
  height: 1010px;
  background: url('../../images/panel_bg1.png') no-repeat left top;
  background-size: 100% 100%;
  .m-b-76 {
    margin-bottom: 56px;
  }
}
</style>