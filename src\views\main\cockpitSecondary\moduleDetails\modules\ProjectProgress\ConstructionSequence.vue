/**
  组件描述: 建设时序占比
*/
<template>
  <section v-loading="loading"
    class="construction-sequence">
    <ProportionOfRing chartId="ConstructionSequence"
      :seriesData="seriesData"
      :individuationOptions="individuationOptions"
      :ringWidth="310" />
  </section>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import { EChartsOption } from 'echarts'
import { echartConfigure } from '@/views/main/cockpitcren/baseData'
import { getProjectTimingRatio, ProjectProgressDataItem } from '@/api/cockpit'
import { Loading } from '@/decorators'
import ProportionOfRing from '@/views/main/cockpitSecondary/charts/ProportionOfRing.vue'

@Component({
  components: {
    ProportionOfRing
  }
})
export default class extends Vue {
  @Prop({ default: '0' }) private activeTabs!: number

  @Watch('activeTabs', { immediate: true })
  private activeTabsChange() {
    this.params.orgCode = this.activeTabs
    this.fetchData()
  }

  private loading = false

  // 参数
  private params: any = {
    orgCode: '0',
    year: String(new Date().getFullYear())
  }

  private seriesData = [
    {
      name: '经营',
      value: 0,
      num: 0
    },
    {
      name: '功能',
      value: 0,
      num: 0
    }
  ]

  private projectNum = 0
  private totalInvestment = 0
  private colorList = ['#FFAD2C', '#00A0E9']

  // 个性化配置
  get individuationOptions(): EChartsOption {
    let tooltipData: any = Object.assign(
      {
        trigger: 'item',
        valueFormatter: (value: number) => `${value}亿元`
      },
      echartConfigure.tooltip
    )

    return {
      title: [
        {
          text: '数量',
          top: '55%',
          left: 'center',
          textStyle: {
            fontSize: 30,
            fontWeight: 'normal',
            fontFamily: 'PangMenZhengDao',
            color: '#fff'
          }
        },
        {
          text: this.projectNum + '',
          top: '40%',
          left: 'center',
          textStyle: {
            fontSize: 48,
            fontWeight: 'normal',
            fontFamily: 'PangMenZhengDao',
            color: '#FFC42C'
          }
        }
      ],
      tooltip: tooltipData,
      color: this.colorList,
      series: [
        {
          radius: ['40%', '60%'],
          label: {
            formatter: (param: any) => {
              let { data, dataIndex } = param
              let styleName = dataIndex === 0 ? 'c' : 'a'

              return `{b|${data.name}} {b|${((data.value / this.totalInvestment) * 100 || 0).toFixed(1)}%}\n{${styleName}|${
                data.num
              }个}\n{${styleName}|${data.value}亿元}`
            },
            rich: {
              a: {
                fontSize: 36,
                lineHeight: 40,
                padding: [30, 0, 0, 0],
                color: '#00FFFF',
                fontWeight: 'normal',
                fontFamily: 'PangMenZhengDao'
              },
              b: {
                fontSize: 32,
                lineHeight: 40,
                color: '#fff',
                fontWeight: 'normal',
                fontFamily: 'PangMenZhengDao'
              },
              c: {
                fontSize: 36,
                lineHeight: 40,
                padding: [30, 0, 0, 0],
                color: '#FFAD2C',
                fontWeight: 'normal',
                fontFamily: 'PangMenZhengDao'
              },
              hr: {
                borderColor: '#8C8D8E44',
                width: '100%',
                borderWidth: 1,
                height: 0
              }
            }
          }
        }
      ]
    }
  }

  mounted() {
    this.listenerDate()
  }

  // 监听时间变化
  private listenerDate() {
    this.$bus.$on('BusYearTimeChange', (year: string) => {
      this.params.year = year
      this.fetchData()
    })

    this.$bus.$on('BusMoonTimeChange', (month: string) => {
      this.params.month = month
      this.fetchData()
    })
  }

  @Loading('loading')
  private async fetchData() {
    let res = await getProjectTimingRatio(this.params)
    if (res.success) {
      this.processingData(res.data)
    }
  }

  // 处理数据
  private processingData(data: Array<ProjectProgressDataItem>) {
    this.totalInvestment = +data[0].indicatorValue
    this.projectNum = +data[0].indicatorCount

    this.seriesData = [
      {
        name: data[0].indicatorName,
        value: +data[0].indicatorValue,
        num: +data[0].indicatorCount
      },
      {
        name: data[1].indicatorName,
        value: +data[1].indicatorValue,
        num: +data[1].indicatorCount
      }
    ]

    this.totalInvestment = this.seriesData.reduce((sum: any, currentValue: any) => {
      return (sum += currentValue.value || 0)
    }, 0)
    this.projectNum = this.seriesData.reduce((num: any, currentValue: any) => {
      return (num += currentValue.num || 0)
    }, 0)
  }
}
</script>


<style scoped lang="scss">
.construction-sequence {
  width: 100%;
  height: 100%;
}
</style>