/**
  组件描述:  重大项目进度
*/
<template>
  <section v-loading="loading"
    class="project-progress">
    <RankingList :rankingList="rankingList"
      rankingProp="indicatorValue"
      :propMap="propMap" />
  </section>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import { getProjectProgress, ProjectProgressDataItem } from '@/api/cockpit'
import { Loading } from '@/decorators'
import RankingList from '@/views/main/cockpitSecondary/components/RankingList.vue'

@Component({
  components: {
    RankingList
  }
})
export default class extends Vue {
  @Prop({ default: '0' }) private activeTabs!: string

  @Watch('activeTabs', { immediate: true })
  private activeTabsChange() {
    this.params.orgCode = this.activeTabs
    this.fetchData()
  }

  private loading = false

  // 参数
  private params: any = {
    orgCode: '0',
    year: String(new Date().getFullYear()),
    projectProperty: ''
  }

  private propMap = {
    name: 'indicatorName',
    value: 'indicatorValue',
    rate: 'indicatorRate'
  }

  private rankingList: ProjectProgressDataItem[] = []

  mounted() {
    this.listenerDate()
    this.$bus.$on('projectInvestmentTypeChange', (projectProperty: string) => {
      this.params.projectProperty = projectProperty
      this.fetchData()
    })
  }

  // 监听时间变化
  private listenerDate() {
    this.$bus.$on('BusYearTimeChange', (year: string) => {
      this.params.year = year
      this.fetchData()
    })

    this.$bus.$on('BusMoonTimeChange', (month: string) => {
      this.params.month = month
      this.fetchData()
    })
  }

  // 请求数据
  @Loading('loading')
  private async fetchData() {
    let { data } = await getProjectProgress(this.params)

    this.rankingList = data
  }
}
</script>


<style scoped lang="scss">
.project-progress {
  width: 100%;
  height: 100%;
}
</style>