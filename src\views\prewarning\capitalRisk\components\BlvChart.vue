<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts'

export default {
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
  },

  data () {
    return {
      chart: null,
      option: {
        title: {
          text: '带息负债比率',
          left: 'center',
          top: '0%',
          textStyle: {
            color: '#333',
            fontSize: 14,
            fontWeight: 'normal'
          }
        },
        grid: {
          top: "25%",  // 调整了top值以给标题留出空间
          bottom: "0%",
          left: "5%",
          right: "5%",
          containLabel: true
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: 'line',
            lineStyle: {
              color: '#000',
              width: 1,
              fontWeight: '700'
            }
          }
        },
        legend: {
          left: 'center',
          top: '12%',      // 调整位置更靠上
          itemGap: 30,    // 减小间距
          itemWidth: 12,  // 减小图例标记的宽度
          itemHeight: 8, // 减小图例标记的高度
          icon: 'roundRect'
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,  // 让线条从起点开始
          axisLine: {
            show: true,
            lineStyle: {
              color: '#999'
            }
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          data: ['2019', '2020', '2021', '2022', '2023']
        },
        yAxis: {
          type: 'value',
          // min: 0,
          // max: 120,
          // interval: 20,
          axisLine: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#eee',
              type: 'solid'
            }
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: '实际值',
            type: "line",
            symbol: "circle",
            symbolSize: 4,
            itemStyle: {
              color: 'rgba(136, 155, 216,1)'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0,
                  color: 'rgba(136, 155, 216,1)'
                }, {
                  offset: 1,
                  color: 'rgba(136, 155, 216,1)'
                }]
              }
            },
            data: [78, 88, 80, 105, 70]
          },
          {
            name: '五年内平均值',
            type: "line",
            symbol: "none",
            itemStyle: {
              color: '#ff0000'
            },
            lineStyle: {
              type: 'solid'
            },
            data: [83, 83, 83, 83, 83]
          }
        ]
      }
    }
  },

  beforeDestroy () {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },

  methods: {

    preInitChart () {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
      this.$nextTick(() => {
        this.initChart()
      })
    },

    initChart () {
      this.chart = echarts.init(this.$el, 'macarons')

      this.chart.setOption(this.option)
    }
  }
}
</script>
