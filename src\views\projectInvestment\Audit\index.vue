<!-- 投资项目审批 -->
<template>
  <el-container class="audit"
    direction="vertical">

    <search-bar :items="searchItems"
      @onSearch="handleSearch">
      <el-button v-if="isCompany"
        class="primary-buttom"
        type="primary"
        @click="onAdd">新增</el-button>
    </search-bar>

    <!-- 表格 -->
    <Grid ref="grid"
      :columns="cols"
      :show-pagination="true"
      :overflow-tooltip="true"
      show-index-fixed="left"
      :show-index="true"
      :remote-url="remoteUrl"
      :search-params="searchParams"
      @row-click="onSee">

      <template #approvalStatusSlot="{row}">
        {{ row.approvalStatus }}
      </template>
      <template #isPlanSlot="{row}">
        {{getStateValue("invest_project_property",row.isPlan)}}
      </template>

      <template #boardResolutionSlot="{row}">
        <div v-if="row.boardResolution"
          class="accessory"
          @click="onAccessoryShow(splitUrl(row.boardResolution), '董事会决议')">
          附件（{{ splitUrl(row.boardResolution).length }}）</div>
      </template>

      <template #operationSlot="{row}">
        <Operation :list="operationList"
          :row="row" />
      </template>
    </Grid>

    <AddAuditDialog v-if="addAuditDialogVisivle"
      :visible.sync="addAuditDialogVisivle"
      @success="refreshGrid"
      title="项目审批-新增" />

    <AuditDetailDialog v-if="auditDetailDialogVisivle"
      :visible.sync="auditDetailDialogVisivle"
      :mode="recordDetailMode"
      :detail="currentRow"
      title="项目审批-查看" />
  </el-container>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import Grid from '@/components/Grid/index.vue'
import searchBar from '@/components/SearchBar/index.vue'
import AddAuditDialog from './components/AddAuditDialog.vue'
import AuditDetailDialog from './components/AuditDetailDialog.vue'
import Operation, { OperationItem } from '@/components/Operation/index.vue'
import { ProjectReview } from '@/api/projectInvestment'
import { BusinessModule } from '@/store/modules/businessDict'
@Component({
  components: {
    Grid,
    searchBar,
    AddAuditDialog,
    AuditDetailDialog,
    Operation
  }
})
export default class Project extends Vue {
  private remoteUrl = '/fht-monitor/invest/project/page'

  private addAuditDialogVisivle = false
  private auditDetailDialogVisivle = false
  private recordDetailMode = 'see'
  private currentRow: Partial<ProjectReview> = {}

  // 附件列表
  private accessoryList: string[] = []
  private accessoryTitle = ''
  private accessoryVisible = false
  private projectCategoryList = [
    {
      label: '企业',
      value: 'QY',
      selected: true
    },
    {
      label: '政府',
      value: 'ZF'
    },
    {
      label: '代建',
      value: 'DJ'
    }
  ]
  private searchItems = [
    {
      type: 'tab',
      key: 'projectProperty',
      options: this.projectCategoryList
    },
    {
      type: 'text',
      key: 'keyword',
      placeholder: '请输入关键词查询',
      width: '190px'
    }
    // {
    //   type: 'year',
    //   label: '年度',
    //   key: 'year',
    //   placeholder: ''
    // }
  ]

  private searchParams = {
    year: '',
    projectProperty: 'QY'
  }

  private cols = [
    {
      prop: 'projectCode',
      label: '项目编号',
      minWidth: 180,
      fixed: 'left'
    },

    {
      prop: 'projectName',
      label: '项目名称',
      fixed: 'left',
      minWidth: 200
    },
    {
      slotName: 'approvalStatusSlot',
      label: '业务状态',
      fixed: 'left'
    },
    {
      prop: 'orgName',
      label: '投资主体'
    },

    // {
    //   prop: 'isPlanSlot',
    //   label: '年初是否已列入计划',
    //   width: 200
    // },
    // {
    //   prop: 'isMainInvest',
    //   label: '是否主体投资',
    //   width: 200
    // },
    // {
    //   prop: 'isOverInvest',
    //   label: '是否境外投资',
    //   width: 200
    // },
    {
      prop: 'whetherAmt',
      label: '总投资额(万)',
      width: 120
    },
    {
      prop: 'cumulativeCompletedInvestment',
      label: '累计完成投资额(万)',
      width: 160
    },
    {
      prop: 'cumulativeCompletedRate',
      label: '累计完成投资率(%)',
      width: 150
    },
    {
      prop: 'investAmount',
      label: '预计投入自有资金',
      width: 200
    },
    {
      prop: 'projectDate',
      label: '建设起止年限',
      width: 200
    },
    {
      prop: 'expectedRoi',
      label: '预期投资回报率',
      width: 200
    },
    {
      prop: 'assetRatio',
      label: '本项目投资前公司资产负债率',
      width: 200
    },
    {
      prop: 'afterAssetRatio',
      label: '本项目投资后预计公司资产负债率',
      width: 210
    },
    {
      prop: 'afterInvestmentRatio',
      label: '本项目投资前公司非主业投资比例',
      width: 210
    },
    {
      prop: 'beforeInvestmentRatio',
      label: '本项目投资后预计公司非主业投资比例',
      width: 250
    },
    // {
    //   prop: 'reviewFeedback',
    //   label: '审核反馈意见',
    //   width: 200
    // },
    // {
    //   prop: 'auditCompletionTime',
    //   label: '审核完成时间',
    //   width: 160
    // },
    // {
    //   prop: 'createUser',
    //   label: '企业编制人'
    // },
    // {
    //   prop: 'createTime',
    //   label: '编制提交时间',
    //   width: 200
    // },
    {
      prop: '',
      label: '操作',
      width: this.isCompany ? 75 : 145,
      slotName: 'operationSlot',
      fixed: 'right',
      labelAlign: 'center'
    }
  ]

  // 操作按钮
  private operationList: OperationItem[] = [
    {
      label: '查看',
      click: this.onSee,
      style: 3,
      visible: (row: any) => {
        return true
      }
    },
    {
      label: '审批',
      style: 2,
      click: this.onAudit,
      visible: (row: any) => {
        return !this.isCompany
      }
    }
  ]

  // 是否是企业端
  get isCompany() {
    return true
  }

  private onSee(row: any) {
    // 查看
    this.recordDetailMode = 'see'
    this.currentRow = row
    this.auditDetailDialogVisivle = true
  }

  private onAudit(row: any) {
    // 审批
    this.recordDetailMode = 'audit'
    this.currentRow = row
    this.auditDetailDialogVisivle = true
  }

  // 新增
  private onAdd() {
    this.addAuditDialogVisivle = true
  }

  // 账单展示
  private onAccessoryShow(accessoryList: any, accessoryTitle: string) {
    this.accessoryList = accessoryList
    this.accessoryTitle = accessoryTitle
    this.accessoryVisible = true
  }

  private handleSearch(condition: any) {
    this.searchParams = Object.assign(this.searchParams, condition)
    this.refreshGrid()
  }

  // 表格更新
  private refreshGrid(isCurrent = false) {
    ;(this.$refs.grid as Grid).refresh(isCurrent)
  }

  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }
  // 筛选字典项
  private getStateValue(dictname: string, values: any = '') {
    let list = this.getDictData(dictname).find((res: any) => {
      return res.value == values
    }) || { label: '', value: '' }
    return list.label
  }
}
</script>

<style lang="scss" scoped>
.audit {
  height: 100%;
}
</style>