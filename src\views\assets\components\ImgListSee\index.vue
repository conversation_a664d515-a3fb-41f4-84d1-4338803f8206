<template>
<div style="width:100%;height:100%">
    <iframe v-if='src.split(".")[src.split(".").length-1]=="pdf"' marginwidth="0" framespacing="0" marginheight="0"  allowtransparency="true"  class="iframe" style="width:100%;height:100%" @load="loadFrame" :src="src" frameborder="0"></iframe>
    <el-image style="width:100%;height:100%" fit="scale-down" v-else :src="src" alt="">
    </el-image>
</div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
//  import Pdfh5 from "pdfh5";
@Component({
  components: {
  }
})
export default class extends Vue {
    @Prop({default:"http://fh-mjgy-test.oss-cn-hangzhou.aliyuncs.com/upload/20220811/a36eb2656e9afb11ad0a6a7e096e432b.pdf"}) private src?:string
    private pdfh5:any=""
   created () {
    //    	  this.pdfh5 = new Pdfh5("#demo", {
	// 	// pdfurl: "http://fh-mjgy-test.oss-cn-hangzhou.aliyuncs.com/upload/20220811/a36eb2656e9afb11ad0a6a7e096e432b.pdf"
	//   });
   }
   private loadFrame(){
        //  let imgs:any = document.getElementsByTagName('img');
        //  imgs.setAttribute("style","width:100vw;");
   }
}
</script>

<style lang="scss" scoped>
.iframe{
    .img{
        width: 40vw;
    }
}
</style>