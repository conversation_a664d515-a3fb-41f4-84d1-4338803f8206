<template>
  <div class="assets-map-tool">
    <header class="map-tools-header">
      <el-select v-model="searchform.orgCode"
        placeholder="请选择"
        @change="changeorg">
        <el-option v-for="item in compTree"
          :key="item.id"
          :label="item.deptName"
          :value="item.deptCode" />
      </el-select>

      <!-- <el-select  v-model="searchform.companyCode" @change="changeMarkerPoint(searchform)" placeholder="请选择" >
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" @change="changSelect" />
      </el-select>-->

      <el-input v-model="searchform.keyword"
        clearable
        placeholder="请输入关键词"
        @keydown.enter="changeorg"
        @input="changSelect(searchform)"
        class="search-box">
        <el-button v-if="isSearch"
          class="bcfff"
          slot="append"
          icon="el-icon-search"
          @click="changeorg" />
      </el-input>

      <el-button v-show="!showPutAway"
        type="text"
        class="m-l-10 m-r-10"
        @click="btnputAway(true)">
        <span class="updown">
          <i class="el-icon-arrow-down" />
          <i class="el-icon-arrow-down" />
        </span>
      </el-button>

      <el-button v-show="showPutAway"
        type="text"
        class="m-l-10 m-r-10"
        @click="btnputAway(false)">
        <span class="updown">
          <i class="el-icon-arrow-up" />
          <i class="el-icon-arrow-up" />
        </span>
      </el-button>
    </header>

    <transition name="el-zoom-in-left">
      <section v-if="showPutAway"
        v-loading="LoaningInquire"
        style="background: #fff; padding: 20px 5px; box-shadow: 0 0 10px #d8dce6;">
        <div>
          <span class="table-header m-0">街道地址</span>
          <div class="p-l-15">
            <el-select v-model="searchform.poi"
              style="width:100%;"
              clearable
              filterable
              remote
              reserve-keyword
              placeholder="搜地点"
              :remote-method="remotePOIMethod"
              :loading="loadingPOI"
              @change="changPOI()">
              <el-option v-for="item in POIList"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </el-select>
          </div>
        </div>
        <div>
          <span class="table-header m-0">资产类型</span>
          <div class="p-l-15">
            <el-checkbox-group v-model="searchform.assetTypes">
              <el-checkbox v-for="(city, index) in assetTypesDict"
                :label="city.value"
                :key="index">{{ city.label }}</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <div class>
          <div class="table-header">
            <span class="m-0">资产用途</span>
            <el-button type="text"
              @click="handleCheckAllChange(true)">全选</el-button>
          </div>
          <div class="p-l-15">
            <!-- <el-checkbox
              :indeterminate="isTypeCheckAll"
              v-model="TypeCheckAll"
              @change="handleCheckAllChange"
            >全选</el-checkbox>-->
            <el-checkbox-group v-model="searchform.purposes"
              @change="changSelect">
              <el-checkbox v-for="(city, index) in types"
                :label="city.value"
                :key="index">{{ city.label }}</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>

        <div>
          <!-- <span class="table-header">区域</span> -->
          <span class="table-header">
            <span class="m-0">区域</span>
            <el-button type="text"
              @click="handleCheckAllChangeCode()">全选</el-button>
          </span>
          <div class="p-l-15">
            <el-checkbox-group v-model="searchform.districtIds"
              @change="changSelect">
              <el-checkbox class="p-t-10"
                v-for="(item, index) in area"
                :label="item.value"
                :key="index">{{ item.label }}</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <div>
          <span class="table-header">面积（㎡）</span>
          <div class="p-l-15 tool-area">
            <input-numeber v-model="searchform.minArea"
              placeholder="最小面积" />
            <span class="p-l-5 p-r-5">-</span>
            <input-numeber v-model="searchform.maxArea"
              placeholder="最大面积" />
          </div>
        </div>
        <!-- <div>
          <span class="table-header">详情数据</span>
          <el-table :data="tableData" style="width: 100%" v-loading="loadingTable">
            <el-table-column prop="propertyTypeDesc" label="类型" width="60"></el-table-column>
            <el-table-column prop="assetNum" label="不动产(处)" width="80"></el-table-column>
            <el-table-column prop="landArea" label="土地面积(㎡)" width="85"></el-table-column>
            <el-table-column prop="constructionArea" label="建筑面积(㎡)" width="85"></el-table-column>
            <el-table-column prop="rentRate" label="出租率" width="80"></el-table-column>
          </el-table>
        </div>-->
        <!-- <el-button slot="prepend" icon=""></el-button> -->
        <div style="text-align:end;"
          class="m-t-15">
          <el-button type
            @click="repossess">重置</el-button>
          <el-button type="primary"
            @click="inquire">查询</el-button>
        </div>
      </section>
    </transition>
  </div>
</template>

<script lang="ts">
import { Component, Emit, Prop, Vue } from 'vue-property-decorator'
import { Throttle } from '@/decorators'
import { BusinessModule } from '@/store/modules/businessDict'
import { getAstCompTree } from '@/api/projectInvestment'
import { assetsInfoMap, mapSuggestion } from '@/api/assetsv2'
import { deepClone } from '../../../../../utils'
import InputNumeber from '@/components/FormComment/inputNumber.vue'

@Component({
  components: {
    InputNumeber
  }
})
export default class Container extends Vue {
  @Prop({ default: true }) private isSearch?: boolean // 是否显示搜索按钮

  private assetTypesDict = [
    {
      label: '房产',
      value: 1
    },
    {
      label: '土地',
      value: 2
    }
  ]
  private showPutAway = false
  private showType = false
  private loadingTable = false
  private LoaningInquire = false
  private tableData: any = []
  private TypeCheckAll = false
  private isTypeCheckAll = false
  private loadingPOI = false //远程搜索加载
  private POIList: any = [] //poi列表
  private searchform: any = {
    purposes: [],
    assetTypes: [1, 2],
    districtIds: [],
    keyword: '',
    orgCode: '',
    poi: ''
  }
  private options = []
  // types = ['住宅', '楼宇', '园区', '商铺', '土地', '其他']
  // private types = ['住宅', '楼宇', '园区', '商铺', '土地', '其他']
  private types = this.getDictData('asset_purpose')
  private area = this.getDictData('areaCode')
  private searchvalue = '' //搜索值
  // 组织机构值
  private compTree = []
  mounted() {
    this.assetsInfoMap()
  }
  created() {
    this.getCompTree()
    // this.handleCheckAllChange(true)
    // this.handleCheckAllChangeCode()
    // this.isTypeCheckAll=true
    this.TypeCheckAll = true
  }
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }

  // 拉取接口远程数据
  @Throttle
  private async assetsInfoMap() {
    this.LoaningInquire = true
    let params: any = deepClone(this.searchform)
    if (params.poi != undefined) {
      params.lat = params.poi.split(',')[0] || ''
      params.lon = params.poi.split(',')[1] || ''
    }

    params.raidus = 1
    delete params.poi

    // params.deptCode = this.searchform.deptCode[this.searchform.deptCode.length - 1]
    try {
      let res = await assetsInfoMap(params)
      if (res.success) {
        // this.tableData = res.data
        this.LoaningInquire = false
        this.loadmark(res.data)
      }
    } catch (e) {
      this.LoaningInquire = false
    }
  }
  // type 筛选
  private assetsInfoMapbyType(type: string) {
    //
    this.$set(this.searchform, 'type', type)
    this.assetsInfoMap()
  }
  private changPOI(data: any) {
    let labelData = this.POIList.find((res: any) => {
      return this.searchform.poi == res.location.lat + ',' + res.location.lng
    })
    if (labelData !== undefined) {
      this.$emit('addMapLabel', labelData)
    }
  }
  //百度POI服务
  @Throttle
  private async remotePOIMethod(value: string) {
    let query: any = {
      keyword: value
    }
    try {
      this.loadingPOI = true
      let res = await mapSuggestion(query)
      if (res.success) {
        this.POIList = res.data.map((res: any) => {
          return {
            label: res.name,
            value: res.location.lat + ',' + res.location.lng,
            ...res
          }
        })
      }
      this.loadingPOI = false
    } catch (e) {
      this.loadingPOI = false
      console.error(e)
    }
  }
  // 添加点
  @Emit('loadmark')
  private loadmark(List: any) {
    // this.removeOverlay()
    return List
  }

  // 触发全选 资产用途
  private handleCheckAllChange(val: any) {
    if (this.searchform.purposes.length == this.types.length) {
      // this.searchform.districtIds=[]
      this.$set(this.searchform, 'purposes', [])
    } else {
      this.searchform.purposes = this.types.map((item: any) => {
        return item.value
      })
    }
  }
  // 选择机构时触发
  private changeorg() {
    this.showPutAway = true
  }
  private handleCheckAllChangeCode() {
    if (this.searchform.districtIds.length == this.area.length) {
      // this.searchform.districtIds=[]
      this.$set(this.searchform, 'districtIds', [])
    } else {
      this.searchform.districtIds = this.area.map((item: any) => {
        return item.value
      })
    }
  }
  //获取机构数组
  private async getCompTree() {
    let options = []
    let res = await getAstCompTree({})
    if (res.success) {
      options = res.data
      this.compTree = options
    }
  }
  // 改变列表数据之后切换数据
  private changSelect() {
    // this.btnputAway(true)
    // this.changeShowType(false)
    // this.getfrontPageList()
    // this.changeMarkerPoint(this.searchform)
    this.btnputAway(true)
  }
  private btnputAway(state: boolean) {
    this.showPutAway = state
  }
  private changeShowType(state: boolean) {
    this.showType = state
  }
  @Emit('changeMarkerinfo')
  private changeMarkerPoint(searchform: any) {
    let params: any = deepClone(searchform)

    params.deptCode = this.searchform.deptCode[this.searchform.deptCode.length - 1]
    return params
  }
  // 查询按钮
  // @Loading("LoaningInquire")
  private inquire() {
    ;(this.$parent as any).$parent.ischeck = 10
    this.assetsInfoMap()
    this.searchform.type = ''
  }
  // 重置
  private repossess() {
    this.searchform = { purposes: [], assetTypes: [1, 2], districtIds: [], keyword: '', orgCode: '' }
    this.LoaningInquire = false
  }
}
</script>

<style lang="scss" scoped>
.el-input-group__prepend {
  background-color: #fff !important;
  border: none !important;
}

::v-deep .search-box {
  .el-input-group__append {
    border: 0px !important;
  }
  .el-input-group__append,
  .el-input-group__prepend {
    border: 0px !important;
  }
}

.bcfff {
  background-color: #fff !important;
  border: 0px;
}
.assets-map-tool {
  ::v-deep .el-input__inner {
    border: 1px solid #fff;
  }
  position: relative;
  .table-header {
    border-left: 5px solid #303133;
    padding-left: 5px;
    padding-right: 5px;
    // line-height: 200%;
    height: 25px;
    color: #303133;
    display: flex;
    justify-content: space-between;
    justify-items: center;
  }
  border-radius: 5px;
  width: 420px;

  .map-tools-header {
    background: #fff;
    box-shadow: 0 0 10px #d8dce6;
    height: 36px;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .search-section {
    display: flex;
    justify-content: space-around;
  }
  section {
    position: absolute;
    top: 40px;
    margin: 0 0;
    line-height: 25px;
    div {
      margin: 5px 0;
    }
    ::v-deep .el-table__header-wrapper {
      font-size: 12px;
    }
    ::v-deep .el-table th.el-table__cell > .cell {
      font-size: 12px;
      padding: 0 5px;
    }
    ::v-deep .el-descriptions-item__label {
      line-height: 30px;
      font-size: 14px;
    }
    ::v-deep .el-select .el-input__inner {
      height: 30px !important;
      border-radius: 0px;
      // font-size: 26px;
      border-bottom: 1px solid #ce4c4c;
    }
  }
  .updown {
    display: flex;
    flex-wrap: wrap;
    margin: 0;
    i:nth-child(2) {
      transform: translateY(-4px);
    }
  }
}
.el-descriptions-item__container .el-descriptions-item__content {
  color: #303133 !important;
}
.tool-area {
  display: flex;
  justify-content: center;
  align-items: center;
  ::v-deep .el-input__inner {
    border: 1px solid #d8dce6;
  }
}
</style>
