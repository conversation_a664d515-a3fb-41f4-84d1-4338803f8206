<template>
  <el-container class="table-wrapper" direction="vertical">
    <!-- 头部搜索 -->
    <SearchBar :items="searchItems" @onSearch="handleSearch"></SearchBar>

    <!-- 表格 -->
    <Grid
      v-loading="loadingTable"
      ref="grid"
      :columns="columns"
      :show-selection="false"
      :show-pagination="true"
      :overflow-tooltip="true"
      :search-params="searchParams"
      :show-index="true"
      show-index-fixed="left"
      :remoteUrl="remoteUrl"
      border
    >
      <template #operationSlot="{ row }">
        <Operationbutton :data="row" :items="buttonItems" />
      </template>
    </Grid>
    <el-dialog title="" top="30vh" :modal="true" :visible.sync="centerDialogVisible" width="30%" center>
      <h3>
        <i class="el-icon-info"></i>
        暂未开放
      </h3>
      <span slot="footer" class="dialog-footer"></span>
    </el-dialog>
    <!-- 弹窗：详情 -->
    <DialogDetail v-if="visibleDetail" :visible.sync="visibleDetail" :title="rowTitle" :rowData="rowDetail" />
  </el-container>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { PermissionModule } from '@/store/modules/permissionDict'
import { getAstCompTree } from '@/api/projectInvestment'
import { deepClone } from '@/utils'
import Grid from '@/components/Grid/index.vue'
import SearchBar from '@/components/SearchBar/index.vue'
import Operationbutton from '@/components/OperationButton/index.vue'
import DialogDetail from './components/DialogDetail.vue'

@Component({
  components: {
    Grid,
    SearchBar,
    DialogDetail,
    Operationbutton
  }
})
export default class extends Vue {
  private remoteUrl = '/fht-monitor/ast/recordation/before/page'
  private loadingTable = false
  private centerDialogVisible = true
  private visibleDetail = false
  private rowTitle = '事前备案详情信息'
  private rowDetail = {}
  private searchParams = {
    bizType: 1
  }
  private buttonItems: any = [
    {
      label: '查看详情',
      permission: 'string',
      click: this.seeDetail,
      visible: (row: any) => {
        return true
      }
    }
  ]
  private searchItems = [
    {
      type: 'select',
      key: 'orgCode',
      placeholder: '所属集团',
      options: []
    },
    {
      type: 'select',
      key: 'disposalType',
      placeholder: '处置方式',
      options: [
        {
          label: '出租',
          value: 1
        },
        {
          label: '转让',
          value: 2
        },
        {
          label: '其他',
          value: 3
        }
      ]
    },
    {
      type: 'date',
      key: 'reportTime',
      placeholder: '备案时间'
    },
    {
      type: 'text',
      key: 'keyword',
      placeholder: '请输入企业名称',
      width: '300px'
    }
  ]
  private columns: any[] = [
    {
      label: '所属集团',
      prop: 'orgName'
    },
    {
      label: '资产类别',
      prop: 'assetTypeDesc'
    },
    {
      label: '处置方式',
      prop: 'disposalTypeDesc'
    },
    {
      label: '备案编号',
      prop: 'recordNo',
      minWidth: 120
    },
    {
      label: '备案上报时间',
      prop: 'reportTime',
      minWidth: 120
    },
    {
      label: '资产编号',
      prop: 'assetNo',
      minWidth: 120
    },
    {
      label: '资产明细编号',
      prop: 'itemNo',
      minWidth: 120
    },
    {
      label: '资产权利人',
      prop: 'rightsOwner',
      minWidth: 120
    },
    {
      label: '出租/转让方名称',
      prop: 'contactName',
      minWidth: 120
    },
    {
      label: '联系电话',
      prop: 'contactPhone',
      minWidth: 120
    },
    {
      label: '坐落位置',
      prop: 'location',
      minWidth: 200
    },
    {
      slotName: 'operationSlot',
      label: '操作',
      width: 80,
      fixed: 'right'
    }
  ]

  // 权限方法
  get getPermission() {
    return (code: string) => {
      return PermissionModule.dictLaodData.includes(code) || false
    }
  }

  // 数据初始化
  private created() {
    this.getCompTree()
  }

  // 获取“机构名称”字典数据
  private async getCompTree() {
    let { data } = await getAstCompTree({ parentId: 0, deptCategory: 1 })

    let list: any[] = []
    if (Array.isArray(data)) {
      data.forEach(item => {
        list.push({
          label: item.deptName,
          value: item.deptCode
        })
      })
    }

    let index = this.searchItems.findIndex(item => {
      return item.key === 'orgCode'
    })

    this.$set(this.searchItems, index, {
      type: 'select',
      key: 'orgCode',
      placeholder: '所属集团',
      options: list || []
    })
  }

  // 列表数据搜索
  private handleSearch(condition: any) {
    this.searchParams = Object.assign({ bizType: 1 }, deepClone(condition))
    this.refresh(false)
  }

  // 列表加载
  private refresh(isCurrent = false) {
    ;(this.$refs.grid as Grid).refresh(isCurrent)
  }

  // 查看详情
  private seeDetail(row = {}) {
    this.rowDetail = row
    this.rowTitle = '事前备案详情信息'
    this.visibleDetail = true
  }
}
</script>

<style lang="scss" scoped>
.table-wrapper {
  height: 100%;
}
</style>
