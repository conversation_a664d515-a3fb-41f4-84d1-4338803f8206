<template>
  <el-select v-loading="loading"
    popper-class="select_box"
    clearable
    :value="valueTitle"
    :placeholder="placeholder"
    :popper-append-to-body="true"
    :style="{ width: width ? width + 'px' : '100%' }"
    @clear="clearHandle"
    @visible-change="changevalueTitle">
    <el-option :value="valueTitle"
      :label="valueTitle">
      <div class="select_tree_header">
        <div>
          <el-radio v-model="typeradio"
            :label="true"
            @click.stop.native="stopradio">本级</el-radio>
          <el-radio v-model="typeradio"
            :label="false"
            @click.stop.native="stopradio">所有下级</el-radio>
        </div>

        <el-input v-model="filterText"
          clearable
          placeholder="输入关键字进行过滤"
          class="inputFilter"
          @click.stop.native="stopstop" />
      </div>
      <!-- <el-radio label="所有" @click.stop.native="stopstop">所有</el-radio> -->
      <!-- <el-radio label="本级" @click.stop.native="stopstop">本级</el-radio> -->

      <el-tree class="filter-tree"
        :data="data"
        :props="defaultProps"
        :filter-node-method="filterNode"
        ref="tree"
        node-key="creditCode"
        show-checkbox
        :check-strictly="typeradio">
        <!-- <span class="custom-tree-node" slot-scope="{ node, data }">
              <span>{{ node.label }}</span>
              <el-button v-show="node.isLeaf == false" type="text" size="small" @click.stop.native="changeour(node, data)">
                选择所有
              </el-button>
            </span> -->
      </el-tree>
    </el-option>
  </el-select>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import { getOrgTreeByEnt } from '@/api/public'
import { Loading } from '@/decorators'

@Component({
  components: {}
})
export default class extends Vue {
  @Prop() private value?: Array<any>
  @Prop() private deptCode?: string
  @Prop() private width?: number
  @Prop({ default: '请选择' }) placeholder?: string

  private childrenlist: any = []
  private loading = false
  private typeradio = false //是否父子节点
  private filterText = '' //筛选数据
  private valueTitle = '' //显示数据
  private clearable = false //清理
  private data: any = []
  private defaultProps = {
    children: 'children',
    label: 'name'
  }

  @Watch('filterText', { deep: true })
  private changefilterText(val: any) {
    ;(this.$refs.tree as any).filter(val)
  }

  @Watch('deptCode')
  private changeOrg(val: any) {
    this.getOrgTreeList()
  }

  mounted() {
    this.getOrgTreeList()
    this.$nextTick(() => {
      let selectDropDownNode: any = document.getElementsByClassName('el-select-dropdown__wrap')
      for (let i = 0; i < selectDropDownNode.length; i++) {
        selectDropDownNode[i].style.maxHeight = '600px'
      }
    })
  }

  // 获取机构树
  @Loading('loading')
  private async getOrgTreeList() {
    let res = await getOrgTreeByEnt()
    this.data = [res.data]
  }

  //   更改绑定值
  private changevalueTitle() {
    let list = (this.$refs.tree as any).getCheckedNodes()
    if (list.length > 0) {
      let valueTitlelist = list.map((res: any) => {
        return res.name
      })
      //   更新v-model
      let valuecodelist = list.map((res: any) => {
        return res.creditCode
      })
      this.$emit('input', valuecodelist)
      // 显示值
      this.valueTitle = `${valueTitlelist[0]}${valueTitlelist.length == 1 ? '' : '等'}${
        valueTitlelist.length == 1 ? '' : valueTitlelist.length
      }${valueTitlelist.length == 1 ? '' : '个部门'}`
      this.$emit('nodeChange', valuecodelist)
    } else {
      this.$emit('input', [])
      this.valueTitle = ''
    }
  }
  private handleNodeClick(data: any) {
    //
  }
  private filterNode(value: any, data: any) {
    if (!value) return true
    return data.name.indexOf(value) !== -1
  }
  // 清除选中
  private clearHandle() {
    this.resetChecked()
  }
  private stopstop() {
    ;(this.$refs.tree as any).filter(this.filterText)
  }
  private stopradio(val: any) {
    this.resetChecked()
  }
  private changeour(node: any, data: any) {
    // ;(this.$refs.tree as any).setChecked(data.id, true)
  }
  //   设置某一个节点
  private AddTreeKey() {
    let treeRef: any = this.$refs.tree as any
    let ischeck = this.getArrayInclude(treeRef.getCheckedKeys(), this.childrenlist)
    this.childrenlist.forEach((item: any) => {
      treeRef.setChecked(item, !ischeck)
    })
    this.childrenlist = []
  }
  //   递归所有字节点
  private getchildren(data: any) {
    this.childrenlist.push(data.id)
    if (data.children != undefined) {
      data.children.forEach((item: any) => {
        this.getchildren(item)
      })
    }
  }
  //判断数组包含关系
  private getArrayInclude(arrall: Array<any>, arrmin: Array<any>) {
    let temp = []
    for (const item of arrmin) {
      arrall.indexOf(item) !== -1 ? temp.push(item) : ''
    }
    return temp.length ? true : false
  }
  //  清空数组
  private resetChecked() {
    ;(this.$refs.tree as any).setCheckedKeys([])
    this.changevalueTitle()
    this.$emit('clearChange')
  }
}
</script>

<style lang="scss" scoped>
.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
  height: auto;
  padding: 0;
  padding: 10px;
  background: #fff;
}
.el-select-dropdown__item.selected {
  font-weight: normal;
  min-width: 600px;
  height: 800px;
}

ul li >>> .el-tree .el-tree-node__content {
  height: auto;
  padding: 0 20px;
}
.el-tree-node__label {
  font-weight: normal;
}
.el-tree >>> .is-current .el-tree-node__label {
  color: #409eff;
  font-weight: 700;
}
.el-tree >>> .is-current .el-tree-node__children .el-tree-node__label {
  color: #606266;
  font-weight: normal;
}
.inputFilter {
  width: 100%;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 0px;
}
.select_tree_header {
  display: flex;
  justify-items: center;
  flex-direction: column;
}
::v-deep .select_box .el-select-dropdown,
.el-scrollbar,
.el-scrollbar__wrap {
  min-height: 900px !important;
}
</style>
