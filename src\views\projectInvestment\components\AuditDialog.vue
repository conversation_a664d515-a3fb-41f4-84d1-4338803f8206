/** 
  审批弹框
*/
<template>
  <Dialog title="投资项目计划备案-新增"
    width="980px"
    :visible="visible"
    :close-on-click-modal="true"
    :destroy-on-close="true"
    :append-to-body="true"
    @close="closeDlg"
    @open="openDiaHandle">
    <div slot="body"
      class="">
      <div class="detail"></div>

    </div>
    <div slot="footer">
      <el-button @click="closeDlg">取消</el-button>
      <el-button class="primary-buttom"
        type="primary"
        :loading="loading"
        @click="submitForm">新增</el-button>
    </div>
  </Dialog>
</template>
<script lang="ts">
import { Component, Emit, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import Uploader from '@/components/Uploader/index.vue'

@Component({
  components: {
    Dialog,
    Uploader
  }
})
export default class extends Vue {
  @Prop() private visible!: boolean
  @Prop() private title!: string
  @Prop() private mode!: 'see' | 'audit' // 查看、审批模式，审批只是可以操作审批流

  // 弹窗关闭
  @Emit('close')
  private onClose() {
    //
  }

  private openDiaHandle() {
    // 
  }

  private submitForm() {
    // 校验
  }

  // 关闭弹窗
  private closeDlg() {
    this.$emit('update:visible', false)
    this.onClose()
  }
}
</script>

<style lang="scss" scoped>
</style>
