declare module "*.vue" {
  import Vue from "vue";
  export default Vue;
}

// elementUi 国际化 ts 声明
declare module 'element-ui/lib/locale/lang/*' {
  export const elementLocale: any
}

// 图片预览插件 ts 声明
declare module 'v-viewer'

// axios 中 qs 的 ts 声明
declare module 'qs' {
  const qs: any;
  export default qs;
}

// 数字滚动插件
declare module 'vue-count-to';

// 元素拖拽
declare module 'vuedraggable'

declare module 'compressorjs'

// 数据脑图
declare module 'vue-jsmind'

// 打印页面
declare module 'vue-print-nb'