/**
 * 货币单位、
 * 货币单位,1:元，2:万元，3:亿]不能为空
 */
 import { BusinessModule } from '@/store/modules/businessDict'
 function getDictData(key: string) {
     let dictionary: any = BusinessModule.dictLaodData || {}
     return dictionary[key] ? dictionary[key] : []
 }
export const currencyUnit=[
    {
        value:1,
        label:"元"
    },
    {
        value:2,
        label:"万元"
    },
    {
        value:3,
        label:"亿元"
    }
]
/**
 * issuanceCurrency发行币种
 * 发行币种,1:人民币，2：美元，3：其他")
 */
export const issuanceCurrency=getDictData("issuance_currency")
/**
 *  不良资产类型
 * 发行币种,1:人民币，2：美元，3：其他")
 */
export const badAssetType=[
    {
        value:1,
        label:"实物资产类"
    },
    {
        value:2,
        label:"往来款项类"
    },
    {
        value:3,
        label:"对外投资类"
    },
    {
        value:4,
        label:"资产核销类"
    }
]
/**
 * 与被担保人关系
 * 1：全资子公司，2：控股子公司，3：参股公司，4：无股权关系业务关联企业,5:其他")
 */
 export const equityRelationship=[
    {
        value:1,
        label:"全资子公司"
    },
    {
        value:2,
        label:"控股子公司"
    },
    {
        value:3,
        label:"参股公司"
    },
    {
        value:4,
        label:"无股权关系业务关联企业"
    },
    {
        value:5,
        label:"其他"
    },
]
/**
 * 捐赠次数
 * 捐赠次数，1：一次，2：多次"),5:其他")
 */
export const donationTimesType=[
{
    value:1,
    label:"一次"
},
{
    value:2,
    label:"多次"
},
]
/**
 * 发行形式
 * 捐赠次数，发行形式，1：境内，2：境外)
 */
export const issuanceFormType=[
{
    value:1,
    label:"境内"
},
{
    value:2,
    label:"境外"
},
]
/**
 * 列支途径
 * 列支途径,1:预算内，2:预算外)
 */
export const disbursementWayType=[
{
    value:1,
    label:"预算内"
},
{
    value:2,
    label:"预算外"
},
]
/**
 * 发行对象
 * 发行对象，1：个人，2：机构，3：其他)
 */
export const issuanceTargetType=[
{
    value:1,
    label:"个人"
},
{
    value:2,
    label:"机构"
},
{
    value:3,
    label:"其他"
},
]
/**
 * 发行次数
 * 发行次数，1:一次，2：多次)
 */
export const issuanceTimeNumberType=[
{
    value:1,
    label:"一次"
},
{
    value:2,
    label:"多次"
},
]

// 企业与被担保人关系