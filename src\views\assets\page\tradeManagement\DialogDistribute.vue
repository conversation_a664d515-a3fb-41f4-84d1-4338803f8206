// 分配详情
<template>
    <Dialog title="分配" width="600px" :visible="visible" @close="handleClose" center>
      <div slot="body" class="center">
        <el-form :model="FromTransaction" ref="ruleForm" label-width="80px" class="demo-ruleForm">
          <el-form-item label="分配人员" :rules="[{ required: true, message: '请选择分配人', trigger: 'blur' }]">
            <el-select filterable v-model="FromTransaction.assignUserId" placeholder="请选择分配人" @change="selectUser">
              <el-option v-for="(item, index) in peopleList" :value="item.id" :label="item.name" :key="index"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="DetermineAllocation">确定</el-button>
      </div>
    </Dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import { transactionAssign,getUserList } from '@/api/assets'
import { api } from 'v-viewer'
export interface Transaction {
  assetId: number | string
  assignUserId: number | string
  assignUserName: string
}
@Component({
  components: {
    Dialog
  }
})
export default class extends Vue {
  @Prop() private visible!: boolean
  @Prop() private assetId!: string
  private peopleList = [] //人员列表
  private FromTransaction: Transaction = {
    assetId: this.assetId,
    assignUserId: '',
    assignUserName: ''
  }
  private mounted(){
      this.getUserList()
  }
  // 选择人员时加载数据
  private selectUser(id:any){
    
      let list:any=this.peopleList.find((res:any)=>{
        return res.id==id
      })
      this.FromTransaction.assignUserId=list.id
      this.FromTransaction.assignUserName=list.name
      this.FromTransaction.assetId=this.assetId
  }
//  加载人员树
private async getUserList(){
     try {
          let res = await getUserList({})
          if (res.success) {
              this.peopleList=res.data
              
          }
        } catch (e) {
          console.error(e)
        }
}
  private handleClose() {
    this.$emit('update:visible', false)
  }
  //   验证表单
  private async DetermineAllocation() {
    ;(this.$refs['ruleForm'] as any).validate((valid: boolean) => {
      if (valid) {
          this.sendApi()
      } else {
        return false
      }
    })
  }
//   发送请求
  private async  sendApi() {
    try {
      let res:any = await transactionAssign({
        ...this.FromTransaction
      })
      if (res.success) {
        this.$message.success('分配成功')
        this.handleClose()
      }
    } catch (e) {
      console.error(e)
    }
  }
}
</script>

<style lang="scss" scoped>
.center {
  text-align: center;
}
</style>