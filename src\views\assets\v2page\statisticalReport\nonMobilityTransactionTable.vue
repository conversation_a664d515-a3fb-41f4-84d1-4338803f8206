<!-- 土地证书 -->
<template>
  <el-container class="container"
    direction="vertical">
    <SearchBar :items="searchItems"
      @onSearch="handleSearch"
      v-if="isSearch">
      <el-button v-loading="loadingExport"
        :disabled="!gridData.length"
        type="primary"
        @click="exportHandle">导 出</el-button>
    </SearchBar>

    <Grid ref="grid"
      border
      :remote-url="remoteUrl"
      :columns="columns"
      :search-params="searchParams"
      :show-pagination="true"
      :overflow-tooltip="true"
      @row-click="loaddetail"
      @onLoaded="onLoadedHandle">
      <template slot="contractLeasePeriod"
        slot-scope="scope">
        {{ scope.row.startTime + ' 至 ' + scope.row.endTime }}
      </template>
      <template slot="operatingBar"
        slot-scope="scope">
        <el-button type="text"
          @click.stop="loaddetail(scope.row)">查看</el-button>
      </template>
    </Grid>
  </el-container>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { BusinessModule } from '@/store/modules/businessDict'
import { getAstCompTree } from '@/api/projectInvestment'
import { downloadXls } from '@/utils'
import { ExportOuttrans } from '@/api/assetsv2'
import { Loading, Confirm } from '@/decorators'
import Grid from '@/components/Grid/index.vue'
import SearchBar from '@/components/SearchBar/index.vue'
import Uploader from '@/components/Uploader/index.vue'

@Component({
  components: {
    Grid,
    SearchBar,
    Uploader
  }
})
export default class HouseCertificate extends Vue {
  // 文件上传
  private loadingExport = false
  private isSearch = false
  private showUploader = false
  private UploaderList = []
  private compTree = []

  // 详细表格数据
  private appendixsLength!: number
  private Diadetaillist: any = {}
  private gridData: any[] = []
  private visvileDetail = false
  private visvileDetailif = false
  private remoteUrl = '/fht-monitor/ast/report/out-trans'
  private searchParams: any = {}
  private searchItems: any[] = [
    {
      type: 'text',
      key: 'keyword',
      placeholder: '资产名称/合同编号/资产编号/承租人/出租人',
      width: '400px'
    },
    {
      type: 'select',
      key: 'orgCode',
      placeholder: '直属单位',
      width: '150px',
      options: this.compTree
    },
    {
      type: 'select',
      key: 'assetPurpose',
      placeholder: '房产用途',
      width: '150px',
      options: this.getDictData('asset_purpose')
    }
  ]
  private columns = [
    {
      label: '直属单位',
      minWidth: 120,
      prop: 'orgName'
    },
    {
      prop: 'itemName',
      label: '房产名称',
      minWidth: 120
    },
    {
      label: '房产用途',
      minWidth: 120,
      prop: 'assetPurposeStr'
    },
    {
      label: '合同编号',
      minWidth: 120,
      prop: 'contractNo'
    },
    {
      label: '资产编号',
      minWidth: 120,
      prop: 'itemNo'
    },
    {
      prop: 'totalRentArea',
      label: '出租总面积(m²)',
      minWidth: 120
    },
    {
      prop: 'totalFee',
      label: '租赁价格(元)',
      minWidth: 120
    },
    {
      prop: 'leasePeriod',
      label: '租期',
      minWidth: 120
    },
    {
      prop: 'isEvaDesc',
      label: '是否评估',
      minWidth: 120
    },
    {
      prop: 'leaseEvaluateValue',
      label: '资产评估价值(元)',
      minWidth: 140
    },
    {
      prop: 'originalValue',
      label: '账面价值(元)',
      minWidth: 120
    },
    {
      prop: 'houseCertNo',
      label: '房产证编号',
      minWidth: 120
    },
    {
      prop: 'realEstateCertNo',
      label: '不动产证编号',
      minWidth: 120
    },
    {
      prop: 'landCertNo',
      label: '土地证编号',
      minWidth: 120
    },
    {
      prop: 'useStatusStr',
      label: '使用情况',
      minWidth: 120
    },
    {
      prop: 'reason',
      label: '未公开交易原因',
      minWidth: 150
    }
  ]

  // 初始化
  created() {
    this.initQuery()
    this.getCompTree()
  }

  // 获取字典
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }

  // 根据链接赋值相关字段
  private initQuery() {
    let { orgCode } = this.$route.query
    if (orgCode) {
      let findIndex = this.searchItems.findIndex((item) => {
        return item.key === 'orgCode'
      })
      this.searchItems[findIndex].value = orgCode
      this.searchParams.orgCode = orgCode
    }
  }

  // 详情
  private uploaderDetail(List: any) {
    this.UploaderList = List
    this.showUploader = true
  }

  // 详情附件
  private showUpdaload(list: any) {
    this.uploaderDetail = list
    this.showUploader = true
  }

  //获取机构数组
  private async getCompTree() {
    let options = []
    let res = await getAstCompTree({ parentId: 0, deptCategory: 1 })
    this.isSearch = false
    if (res.success) {
      let options = res.data.map((res: any) => {
        return {
          label: res.deptName,
          value: res.deptCode
        }
      })
      await this.$nextTick(() => {
        Object.assign(this.compTree, options)
        this.isSearch = true
      })
    }
  }

  // 改变详情显示隐藏
  private changeShowDialogDetail(state: boolean) {
    this.visvileDetailif = state
    this.$nextTick(() => {
      this.visvileDetail = state
    })
  }

  // 列表搜索
  private handleSearch(condition: any) {
    this.searchParams = condition
    ;(this.$refs['grid'] as any).refresh()
  }

  // 操作
  private loaddetail(row: any) {
    this.Diadetaillist = row
    this.visvileDetailif = true
    this.$nextTick(() => {
      this.visvileDetail = true
    })
  }

  // 表格数据加载完成后触发
  private onLoadedHandle(data: any) {
    this.gridData = data.records || []
  }

  // 导出
  @Loading('loadingExport')
  @Confirm({
    title: '提示',
    content: `是否确认导出数据`,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
  private async exportHandle() {
    let res = await ExportOuttrans(this.searchParams)
    let time = new Date().getTime()
    downloadXls(res.data, `未公开交易_${time}.xlsx`)
    this.$message.success(res.msg || '导出成功')
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
}
</style>
