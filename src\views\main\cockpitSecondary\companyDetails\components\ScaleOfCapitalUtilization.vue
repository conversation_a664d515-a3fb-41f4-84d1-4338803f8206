/** 资金利用规模 */
<template>
  <div id="ScaleOfCapitalUtilization" />
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { ScaleOfCapitalUtilizationData, colorSixList, companyList } from '@/views/main/cockpitSecondary/baseData'
import { capitalProfile } from '@/api/cockpit'
import * as echarts from 'echarts'
import { deepClone } from '@/utils'

type EChartsOption = echarts.EChartsOption

@Component
export default class extends Vue {
  private loading = false
  private maxValue = 100
  private year = ''
  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}
  private tabList = []
  private echartsDatas: any[] = []
  private seriesData: any[] = deepClone(ScaleOfCapitalUtilizationData)
  private tabActive: {
    code: string
    name: string
  } = {
    code: '',
    name: ''
  }

  // 获取集团列表
  get getCompanyList() {
    let list: string[] = []
    companyList.forEach((item) => {
      list.push(item.name)
    })
    return list
  }

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById('ScaleOfCapitalUtilization') as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    // this.initData()
    this.initEcharts()
  }

  // 请求数据接口
  @Loading('loading')
  private async initData() {
    let { data } = await capitalProfile()
    if (!Array.isArray(data) || !data.length) return
    this.echartsDatas = data
  }

  // 获取当前年份
  private getMomentTime() {
    let that = this as any
    return that.$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 初始化数据
  private initEcharts() {
    let textSize = 22
    let series = this.seriesData
    let yeas = ~~this.getMomentTime()
    let companyList = this.getCompanyList

    this.option = {
      color: colorSixList,
      legend: {
        top: 0,
        padding: 0,
        itemHeight: 14,
        itemWidth: 14,
        itemStyle: {
          borderCap: 'butt'
        },
        lineStyle: {
          type: [0, 10],
          dashOffset: 5
        },
        textStyle: {
          color: '#999',
          fontSize: 18
        },
        data: companyList
      },
      grid: {
        left: '4%',
        right: '5%',
        bottom: '0%',
        top: '16%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 103, 177, 1)',
        borderColor: 'rgba(0, 103, 177, 1)',
        textStyle: {
          color: '#fff',
          fontSize: textSize
        }
      },
      xAxis: {
        type: 'category',
        name: '年',
        boundaryGap: false,
        data: [yeas - 3, yeas - 2, yeas - 1, yeas],
        axisLabel: {
          fontSize: textSize
        },
        axisLine: {
          lineStyle: {
            color: '#5db0ea'
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '万',
        max: function (value) {
          return Math.ceil(value.max) + 10
        },
        axisLabel: {
          fontSize: textSize
        },
        axisLine: {
          lineStyle: {
            color: '#5db0ea'
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0, 103, 177, 0.4)'
          }
        }
      },
      series: series
    }

    this.myChart && this.myChart.setOption && this.myChart && this.myChart.setOption(this.option)
  }
  // 年份切换
  private yearHandler(year: string) {
    this.year = year
    // this.initData()
  }

  // tabs切换
  private tabsHandler(item: { code: string; name: string }) {
    this.tabActive = item

    if (Array.isArray(this.echartsDatas) && this.echartsDatas.length) {
      //
    }
  }
}
</script>

<style scoped lang="scss">
#ScaleOfCapitalUtilization {
  width: 100%;
  height: 100%;
}
</style>