// 获取权限字典项数据
import Vue from 'vue'
import store from '@/store/index'
import { VuexModule, Module, Action, Mutation, getModule } from 'vuex-module-decorators'
import { infacePermissionDictData } from '@/api/public'
import { getLocalStorage, setLocalStorage } from '@/utils/cache'

export interface PermissionDict {
  dictLaodData: string[]
}

@Module({ dynamic: true, store, name: 'Permission' })
class Permission extends VuexModule implements PermissionDict {
  public dictLaodData: string[] = []

  // 赋值权限字典项数据
  @Mutation
  private SET_PERMISSION_DICT(params: any[]) {
    let dictLaodData: string[] = []

    params.forEach((item) => {
      dictLaodData.push(item.code)
      if (Array.isArray(item.children)) {
        item.children.forEach((child: { code: string }) => {
          dictLaodData.push(child.code)
        })
      }
    })

    this.dictLaodData = dictLaodData
    // console.log("🚀 奇怪", dictLaodData)

    // 保存到 localStorage
    setLocalStorage('permission_dict', JSON.stringify({
      content: dictLaodData,
      dataType: 'array',
      dateTime: new Date().getTime()
    }))
  }

  // 获取权限字典项数据
  @Action
  public async getPermissionDict() {
    // 先从 localStorage 获取
    // const cachedData = getLocalStorage('permission_dict')
    // if (cachedData) {
    //   const { content } = JSON.parse(cachedData)
    //   this.SET_PERMISSION_DICT(content)
    //   return
    // }

    // 如果没有缓存数据，则从接口获取
    let { data } = await infacePermissionDictData()
    data = data || []
    this.SET_PERMISSION_DICT(data)
  }
}

export const PermissionModule = getModule(Permission)