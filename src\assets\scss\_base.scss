/*-- 单独的公共样式文件 --*/

#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}

body {
  margin: 0;
  overflow-x: hidden;
}

i {
  font-style: normal;
}

ul,
li {
  margin: 0;
  padding: 0;
  list-style: none;
}

// 清除点击出现虚拟框
a {
  outline: none;
  text-decoration: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);

  &:focus {
    outline: 0;
  }

  &:link,
  &:visited {
    color: #2c3e50;
    text-decoration: none;
  }
}

a img {
  border: none;
}

// 清浮动
.clearfix {
  *zoom: 1;

  &:after {
    content: ".";
    height: 0;
    display: block;
    visibility: hidden;
    clear: both;
  }
}

// 圆角 50%
.border-radius-circle {
  -moz-border-radius: 50% !important;
  -webkit-border-radius: 50% !important;
  border-radius: 50% !important;
}

// 清除边框
.border-none {
  border: none !important;
}

.border-block {
  border: block !important;
}

// 隐藏滚动条 
.scrollbar-none {
  -ms-overflow-style: none;
  overflow: -moz-scrollbars-none;

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

// 暂无数据样式
.custom-data-none {
  color: #999;
  font-size: 40px;
  text-align: center;
  line-height: 200px;
}

// el-dialog 出现动画
@keyframes dialogAppear {
  0% {
    top: -20px;
  }

  100% {
    top: 0px;
  }
}

.el-dialog {
  top: 0px;
  animation: dialogAppear 0.3s linear;
}

.el-dialog__wrapper {
  top: -20px !important;
}

// end
// 全局修改表单错误提示样式
// .el-form {
//     .el-form-item.is-error .el-input__inner,
//     .el-form-item.is-error .el-input__inner:focus,
//     .el-form-item.is-error .el-textarea__inner,
//     .el-form-item.is-error .el-textarea__inner:focus,
//     .el-message-box__input input.invalid,
//     .el-message-box__input input.invalid:focus {
//         border-color: #f2b969 ;
//     }
//     .el-form-item__error {
//         color: #e6a23c ;
//     }
//     .is-required .el-form-item__label::before,
//     .is-required .el-form-item__label::after {
//         color: #f2b969 ;
//     }
// }

// end
// 驾驶舱：自定义 Cascader 级联选择器样式
.cockpit-cascader-wrap {
  $color: #5db0ea;
  $bg: #010e18;
  background: rgba($color: $bg, $alpha: 0.9) !important;
  border: 1px solid $bg !important;
  box-shadow: 0 0 2px $color !important;

  .el-cascader-menu__list {
    li {
      &:hover {
        background: rgba($color: $color, $alpha: 0.2) !important;
      }
    }
  }

  .el-radio__inner {
    background: none;
    border-color: rgba($color: $color, $alpha: 0.6);
  }

  .el-cascader-menu {
    color: rgb(186, 185, 185);
    border-right: 1px solid rgba($color: $color, $alpha: 0.3);
  }

  .popper__arrow {
    border-bottom-color: rgba($color: $color, $alpha: 0.4) !important;

    &::after {
      border-bottom-color: rgba($color: $color, $alpha: 0.4) !important;
    }
  }
}

// end
// 驾驶舱：自定义 DatePicker 日期选择器样式
.cockpit-picker-box {
  $color: #5db0ea;
  $bg: #010e18;
  $textColor: #a3a6ac;
  width: 280px !important;
  background: rgba($color: $bg, $alpha: 0.9) !important;
  border: 1px solid $bg !important;
  box-shadow: 0 0 2px $color !important;

  .el-year-table td .cell {
    color: $textColor;
  }

  .el-date-picker__header {
    margin: 10px !important;
    padding-bottom: 5px !important;
  }

  .el-date-picker__header-label {
    color: $textColor;
  }

  .el-date-picker__header--bordered {
    border-bottom: 1px solid rgba($color: $color, $alpha: 0.3);
  }

  .el-picker-panel__icon-btn {
    color: $textColor;
    font-size: 18px;
    margin-top: 5px;
  }

  .el-year-table td {
    padding: 0;
  }

  .el-picker-panel__content {
    width: auto !important;
    margin-bottom: 0;
  }

  .popper__arrow {
    border-bottom-color: rgba($color: $color, $alpha: 0.4) !important;

    &::after {
      border-bottom-color: rgba($color: $color, $alpha: 0.4) !important;
    }
  }
}

// end
// 下拉选择框
.cockpit-selector {
  $color: #5db0ea;
  $bg: #010e18;
  $textColor: #a3a6ac;
  background: rgba($color: $bg, $alpha: 0.9) !important;
  border: 1px solid $bg !important;
  box-shadow: 0 0 2px $color !important;

  .el-select-dropdown__item.hover {
    background: #5db0ea;
    color: #fff;
  }
}

// 平面图弹框
.ichnography-dialog {
  .el-dialog {
    width: 100% !important;
    background-image: url('../../views/main/cockpitcren/images/cockipt_dialog_bg.png');
    background-color: transparent !important;
    background-size: 100% 100%;
    position: relative;
    box-shadow: none;
  }

  .el-dialog__body {
    background: none !important;

    .dialog-body {
      height: 100%;

      &>div {
        height: 100%;
      }
    }
  }

  .el-dialog__header {
    background: none !important;
    border: none;

    &:after {
      background: none !important;
    }

    .el-dialog__title {
      padding-top: 15px;
      color: #fff;
      padding-left: 45px;
    }

    .el-dialog__headerbtn {
      right: 45px !important;
      top: 15px !important;
    }

    .el-dialog__close {
      color: #fff;
    }
  }
}

// 平面图弹框呼吸灯
@keyframes ichnographyBLN {
  0% {
    box-shadow: 0px 0px 40px 0px rgba(29, 39, 107, 0.8);
  }

  50% {
    box-shadow: 0px 0px 10px 0px rgba(29, 39, 107, 0.3);
  }

  100% {
    box-shadow: 0px 0px 4px 0px rgba(29, 39, 107, 0.8);
  }
}

// 驾驶舱：iframe 自定义弹窗样式
.custom-view-iframe-wrap {
  .el-dialog__header {
    padding: 0 !important;
  }

  .el-dialog__body {
    padding: 0 !important;

    .dialog-body {
      padding: 0 !important;
    }
  }

  .el-dialog__close {
    position: relative;
    z-index: 100;
    font-size: 30px;
    margin-top: 2px;
    margin-right: -14px;
    background-color: #fff;
    border: 1px solid #999;
  }
}


/* 驾驶舱进场动画 */

$cockiptMoveTime: 2s;
$cockiptMoveDeep: ease;

// 头部
@keyframes CockiptHeaderMove {
  0% {
    transform: translateY(-160px);
  }

  100% {
    transform: translateY(0);
  }
}

// 左侧-上部
@keyframes CockiptLeftTopMove {
  0% {
    transform: translateX(-800px);
  }

  100% {
    transform: translateY(0);
  }
}

// 左侧-下部
@keyframes CockiptLeftBottomMove {
  0% {
    transform: translateX(-800px) translateY(800px);
  }

  100% {
    transform: translateX(0) translateY(0);
  }
}

// 中间-上部
@keyframes CockiptMidTopMove {
  0% {
    transform: scale(0.5);
  }

  100% {
    transform: translateY(1);
  }
}

// 中间-下部
@keyframes CockiptMidBottomMove {
  0% {
    transform: translateY(500px);
  }

  100% {
    transform: translateY(0);
  }
}

// 右侧-上部
@keyframes CockiptRightTopMove {
  0% {
    transform: translateX(800px);
  }

  100% {
    transform: translateY(0);
  }
}

// 右侧-下部
@keyframes CockiptRightBottomMove {
  0% {
    transform: translateX(800px) translateY(800px);
  }

  100% {
    transform: translateX(0) translateY(0);
  }
}

.cockipt-approach-header {
  animation: CockiptHeaderMove $cockiptMoveTime $cockiptMoveDeep;
}

.cockipt-approach-left-top {
  animation: CockiptLeftTopMove $cockiptMoveTime $cockiptMoveDeep;
}

.cockipt-approach-left-bottom {
  animation: CockiptLeftBottomMove $cockiptMoveTime $cockiptMoveDeep;
}

.cockipt-approach-middel-top {
  animation: CockiptMidTopMove $cockiptMoveTime $cockiptMoveDeep;
}

.cockipt-approach-middel-bottom {
  animation: CockiptMidBottomMove $cockiptMoveTime $cockiptMoveDeep;
}

.cockipt-approach-right-top {
  animation: CockiptRightTopMove $cockiptMoveTime $cockiptMoveDeep;
}

.cockipt-approach-right-bottom {
  animation: CockiptRightBottomMove $cockiptMoveTime $cockiptMoveDeep;
}

/* 驾驶舱进场动画 end */

/* 驾驶舱背景闪烁 start */

@keyframes CockiptBgFicker {
  0% {
    opacity: 1;
  }

  15% {
    opacity: 0;
  }

  30% {
    opacity: 1;
  }

  45% {
    opacity: 0;
  }

  60% {
    opacity: 1;
  }

  75% {
    opacity: 0;
  }

  90% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.cockipt-bg-flicker {
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  z-index: 0;
  width: 100%;
  height: 100%;
  animation: CockiptBgFicker 3s linear;
}

/* 驾驶舱背景闪烁 end */

// 财务监管样式
// 路由切换动画

/* fade-transform */

.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all .3s;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

// 子系统 重要按钮样式
.primary-buttom {
  background: #b43c3c !important;
  border-radius: 3px !important;
  border-color: transparent !important;
  color: #fff !important;
}


// 审核状态颜色
.audit-success {
  color: #67C23A;

}

.audit-pending {
  color: #409EFF;

}

.audit-danger {
  color: #fb3f3f;
}

// 财务监管样式
.finance_red_btn {
  background: #b43c3c;
  border-radius: 2px;
  border-radius: 2px;
  color: #fff;
  width: 82px;
  height: 36px;
}

.finance_white_btn {
  background: #fff;
  border-radius: 2px;
  border-radius: 2px;
  color: #b43c3c;
  width: 82px;
  height: 36px;
}