/** 通用折线图 */

<template>
  <div :id="chartId"
    style="width: 100%; height: 420px;" />
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import { colorSixList, echartConfigure } from '@/views/main/cockpitSecondary/baseData'
import * as echarts from 'echarts'

type EChartsOption = echarts.EChartsOption

@Component
export default class extends Vue {
  @Prop() private chartId!: string
  @Prop({ default: () => [] }) seriesData!: any[]
  @Prop({ default: () => [] }) legendData!: any[]
  @Prop({ default: () => [] }) xData!: any[]
  @Prop({ default: '年' }) private xName!: string
  @Prop({ default: '%' }) private yName!: string

  private chartDom = {}
  private myChart: any = {}
  private option: EChartsOption = {}

  // 组件初始化
  private mounted() {
    this.chartDom = document.getElementById(this.chartId) as any
    this.myChart = echarts.init(this.chartDom as HTMLElement)

    this.change()
    this.initEcharts()
  }

  // 初始化数据
  private initEcharts() {
    let textSize = 22
    let series = this.seriesData

    this.option = {
      color: colorSixList,
      title: {
        text: '历年增长',
        top: 10,
        left: 10,
        textStyle: {
          color: '#fff',
          fontWeight: 'bold',
          fontSize: 40
        }
      },

      legend: {
        top: 20,
        right: '5%',
        padding: 0,
        itemHeight: 14,
        itemWidth: 14,
        itemStyle: {
          borderCap: 'butt'
        },
        lineStyle: {
          type: [0, 10],
          dashOffset: 5
        },
        textStyle: {
          color: '#fff',
          fontSize: 30,
          fontWeight: 'bold'
        },
        data: ['土地面积', '建筑面积']
      },
      grid: {
        left: '4%',
        right: '10%',
        bottom: '3%',
        top: '35%',
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 103, 177, 1)',
        borderColor: 'rgba(0, 103, 177, 1)',
        textStyle: {
          color: '#fff',
          fontSize: textSize * 1.4
        }
      },
      xAxis: {
        type: 'category',
        name: this.xName,
        nameTextStyle: {
          fontWeight: 'bold',
          fontSize: 28
        },
        boundaryGap: false,
        data: [2018, 2019, 2020, 2021, 2022],
        axisLabel: {
          fontSize: textSize * 1.4,
          fontWeight: 'bold',
          fontFamily: echartConfigure.fontFamilyNumber,
          margin: 25
        },
        axisLine: {
          lineStyle: {
            color: '#5db0ea'
          }
        }
      },
      yAxis: {
        type: 'value',
        name: 'KM²',
        nameTextStyle: {
          color: '#5db0ea',
          fontSize: 24,
          lineHeight: 40,
          fontWeight: 'bold'
        },
        max: function (value) {
          return Math.ceil(value.max) + 10
        },
        axisLabel: {
          fontSize: textSize * 1.2,
          fontWeight: 'bold',
          fontFamily: echartConfigure.fontFamilyNumber
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#5db0ea'
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(0, 103, 177, 0.4)'
          }
        }
      },
      series: [
        {
          name: '土地面积',
          type: 'line',
          symbol: 'circle',
          smooth: true,
          lineStyle: {
            width: 3
          },
          areaStyle: {
            origin: 'start',
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 1,
                  color: '#E6B60700' // 0% 处的颜色
                },
                {
                  offset: 0,
                  color: '#E6B60744' // 100% 处的颜色
                }
              ],
              global: false // 缺省为 false
            }
          },
          data: this.getData()
        },
        {
          name: '建筑面积',
          type: 'line',
          symbol: 'circle',
          smooth: true,
          lineStyle: {
            width: 3
          },
          areaStyle: {
            origin: 'start',
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 1,
                  color: '#E6B60700' // 0% 处的颜色
                },
                {
                  offset: 0,
                  color: '#E6B60744' // 100% 处的颜色
                }
              ],
              global: false // 缺省为 false
            }
          },
          data: this.getData()
        }
      ]
    }

    this.myChart && this.myChart.setOption && this.myChart && this.myChart.setOption(this.option)
  }

  private getData() {
    return [
      +(Math.random() * 1000).toFixed(2),
      +(Math.random() * 1000).toFixed(2),
      +(Math.random() * 1000).toFixed(2),
      +(Math.random() * 1000).toFixed(2),
      +(Math.random() * 1000).toFixed(2),
      +(Math.random() * 1000).toFixed(2)
    ]
  }

  private change() {
    this.$emit('change')
  }
}
</script>

<style scoped lang="scss">
</style>