<template>
  <section class="company-details-wrap">
    <!-- 原始企业画像 -->
    <!-- <CompanyDetails /> -->

    <!-- 最新企业画像：数字金华提供 -->
    <iframe :src="src"
      ref="iframe"
      width="100%"
      height="100%"
      frameborder="0"
      allowfullscreen
      class="iframe-box" />
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { setLocalStorage } from '@/utils/cache'
import CompanyDetails from '@/views/main/cockpitSecondary/companyDetails/index.vue'

@Component({
  components: {
    CompanyDetails
  }
})
export default class extends Vue {
  private src = ''
  // 初始化
  private mounted() {
    // 像iframe里面传递参数
    let { orgCode } = this.$route.query

    setLocalStorage('orgCode', orgCode)

    // 区分政务网和常规域名
    let { origin } = window.location
    let path = '/jinhuaguozi/cockpit'

    if (origin.includes('http://localhost:8081')) {
      this.src = `http://localhost:8086${path}`
    } else if (origin.includes('https://szjg.gzw.jinhua.gov.cn:6443')) {
      this.src = `https://szjg.gzw.jinhua.gov.cn:6443${path}`
    } else if (origin.includes('http://*************:9990')) {
      this.src = `http://*************:9990${path}`
    }

    window.addEventListener(
      'message',
      (event) => {
        const data = event.data
        console.log('收到1-企业画像-event:', event)
        if (data.type === 'navigateTo') {
          console.log('收到2-企业画像-导航请求:', data)
          this.$bus.$emit('BustoBusiness', {
            code: data.code,
            path: data.path
          })
        }
      },
      false
    )
  }
}
</script>

<style scoped lang="scss">
.company-details-wrap {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .iframe-box {
    border: none;
    transform: translateX(-22px);
  }
}
</style>