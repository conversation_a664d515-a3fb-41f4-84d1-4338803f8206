<template>
  <section v-loading="loading"
    class="assets-astDisposal-wrap">
    该功能暂未开放
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'

@Component({})
export default class extends Vue {
  private loading = false
}
</script>

<style scoped lang="scss">
.assets-astDisposal-wrap {
  position: relative;
  background: #fff;
  text-align: center;
  line-height: 100%;
  min-height: 100%;
  overflow-y: auto;
  padding-top:50vh ;
  // padding: 50px 14px;
  box-sizing: border-box;
}
</style>