<template>
  <section class="company-summary-wrap">
    <div class="summary-box">
      <div v-for="(item, index) of companyData"
        :key="index"
        :class="`mode${index+1}`"
        class="mode">
        <i class="icon"
          :class="[
          {'el-icon-files':index==0},
          {'el-icon-document-copy':index==1},
          {'el-icon-office-building':index==2},
          {'el-icon-receiving':index==3}
        ]" />
        <p class="info">
          <strong>{{item.total}}{{item.unit}}</strong>
          <span>{{item.name}}</span>
        </p>
      </div>
    </div>

    <div class="conter-box">
      <h4 class="til">
        <i class="el-icon-s-marketing" />
        <span>各集团经营性资产统计情况</span>
        <el-tooltip effect="dark"
          content="仅包含经营性资产统计数据"
          placement="top-start">
          <i class="el-icon-warning" />
        </el-tooltip>
      </h4>
      <div v-loading="loadingOperational"
        class="pre-box">
        <div v-for="(item, index) of operationalData"
          :key="index"
          class="mode">
          <SummaryPre v-if="!loadingOperational"
            :infoData="{ 
              title: item.name,
              unit: item.unit
            }"
            :echarsData="item.list" />
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { Loading } from '@/decorators'
import { assetsGeneral, assetsOperationalGeneral } from '@/api/assetsv2'
import SummaryPre from './SummaryPre.vue'

@Component({
  components: {
    SummaryPre
  }
})
export default class extends Vue {
  @Prop() private orgCode!: string // 集团code
  @Prop() private year!: string // 年份

  private loading = false
  private loadingOperational = false
  private companyData: any[] = []
  private operationalData: any[] = []

  // 监听搜索条件改变
  @Watch('orgCode', { deep: true })
  @Watch('year', { deep: true })
  private watchFormData() {
    this.initData()
    this.assetsInitData()
  }

  // 组件初始化
  private mounted() {
    this.initData()
    this.assetsInitData()
  }

  // 获取总况数据
  @Loading('loading')
  private async initData() {
    let { data } = await assetsGeneral({
      orgCode: this.orgCode,
      year: this.year
    })

    this.companyData = data || []
  }

  // 获取经营性资产数据
  @Loading('loadingOperational')
  private async assetsInitData() {
    let { data } = await assetsOperationalGeneral({
      orgCode: this.orgCode,
      year: this.year
    })

    this.operationalData = data || []
  }
}
</script>

<style scoped lang="scss">
.company-summary-wrap {
  position: relative;
  margin-bottom: 8px;
  margin-top: -10px;
  h4,
  h6,
  p {
    margin: 0;
  }
  .summary-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    background: #fff;
    border-bottom: 8px solid #f0f2f5;
    .mode1 {
      background: linear-gradient(180deg, #ffe5e5 0%, #fff4f4 100%);
    }
    .mode2 {
      background: linear-gradient(180deg, #f3d6e3 0%, #f5e4ec 100%);
    }
    .mode3 {
      background: linear-gradient(180deg, #e9e5ff 0%, #f6f4ff 100%);
    }
    .mode4 {
      background: linear-gradient(180deg, #dae1ff 0%, #eff2ff 100%);
    }
    .mode5 {
      background: linear-gradient(180deg, #cee6d9 0%, #e9f5ee 100%);
    }
    .mode6 {
      background: linear-gradient(180deg, #ffeeda 0%, #fff9ef 100%);
    }
    .mode7 {
      background: linear-gradient(180deg, #d6eaf6 0%, #ecf9f9 100%);
    }

    .mode {
      flex: 1;
      display: flex;
      align-items: end;
      justify-content: space-between;
      min-width: 183px;
      margin-right: 50px;
      padding: 10px;
      color: #303133;
      border-radius: 4px;
      box-sizing: border-box;
      &:nth-last-child(1) {
        margin-right: 0;
      }
      .icon {
        color: #909399;
        font-size: 40px;
      }
      .info {
        display: flex;
        align-items: end;
        flex-direction: column;
        font-size: 14px;
        strong {
          font-size: 18px;
          font-weight: 500;
        }
        span {
          color: #909399;
          font-size: 13px;
        }
      }
    }
  }

  .conter-box {
    padding: 10px 10px 0;
    background: #fff;
    .til {
      display: flex;
      align-items: center;
      font-weight: normal;
      margin: 10px 0;
      color: rgba(245, 108, 108, 0.8);
      i {
        font-size: 20px;
        margin-right: 4px;
      }
      span {
        font-size: 17px;
      }
    }

    .pre-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .mode {
        flex: 1;
        min-width: 183px;
        height: 240px;
        margin-right: 50px;
        box-sizing: border-box;
        &:nth-last-child(1) {
          margin-right: 0;
        }
      }
    }
  }
}
</style>