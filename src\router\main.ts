/* 主页面 */

import Layouts from '@/layouts/public/index.vue'

export const routes = [
  {
    path: '/',
    name: '/',
    component: Layouts,
    meta: { title: '首页' },
    redirect: { name: 'Home' },
    children: [
      {
        path: 'home',
        name: 'Home',
        meta: { title: '首页' },
        component: () => import('@/views/main/home/<USER>')
      }
    ]
  },
  {
    path: '/myiframe',
    name: 'myiframe',
    component: Layouts,
    props: (route: any) => ({ name: route.query.name, url: route.query.url }),
    meta: { title: '' },
    children: [
      {
        path: 'urlPath',
        name: 'urlPath',
        meta: { title: '' },
        component: () => import('@/views/myiframe/index.vue')
      },
    ]
  },
  {
    path: '/',
    name: '/',
    component: Layouts,
    meta: { title: '驾驶舱' },
    redirect: { name: 'Cockpit' },
    children: [
      {
        path: 'cockpit',
        name: 'Cockpit',
        meta: { title: '驾驶舱', hasAside: true },
        component: () => import('@/views/main/cockpitcren/indexCockpit.vue')
      },
      {
        path: 'moduledetail',
        name: 'moduleDetail',
        props: (route: any) => ({ moduleName: route.query.module }),
        meta: { title: '模块详情', hasAside: true },
        component: () => import('@/views/main/cockpitSecondary/moduleDetails/index.vue')
      },
      {
        path: 'companydetail',
        name: 'companyDetail',
        props: (route: any) => ({ id: route.query.id }),
        meta: { title: '公司详情', hasAside: true },
        component: () => import('@/views/main/cockpitSecondary/companyDetails/index.vue')
      }
    ]
  },
  {
    path: '/assetmap',
    name: 'assetMap',
    props: (route: any) => ({ id: route.query.id }),
    meta: { title: '资产地图', hasAside: true },
    component: () => import('@/views/main/cockpitSecondary/charts/AssetMapIframe.vue')
  },

  {
    path: '/assetDistributionMap',
    name: 'assetDistributionMap',
    meta: { title: '资产地图' },
    props: (route: any) => ({ id: route.query.id }),
    component: () => import('@/views/main/map/index.vue')
  }
]

export default routes