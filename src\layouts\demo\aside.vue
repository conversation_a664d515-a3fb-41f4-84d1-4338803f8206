<template>
  <el-aside class="aside-wrap">
    <el-menu :default-active="defaultActive"
      class="el-menu-vertical-wrap"
      text-color="#8C8EA4"
      active-text-color="#2F3038">
      <el-menu-item v-for="(item, index) of menuList"
        :key="index"
        :index="String(index + 1)"
        @click="routerPath(item.path, index + 1)">
        <img class="menu-ic"
          :src="require('@/assets/images/finance/'+ item.icon +'.png')" />
        <span>{{ item.name }}</span>
      </el-menu-item>
    </el-menu>
  </el-aside>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'

@Component({
  name: 'Aside'
})
export default class Aside extends Vue {
  // 侧边栏数据
  private menuList = Object.freeze([
    {
      icon: 'menu-zai',
      name: '企业发债审批',
      path: 'debtIssuanceApproval'
    },

    {
      icon: 'menu-zhichan',
      name: '资产减值备案',
      path: 'assetImpairment'
    },
    {
      icon: 'menu-juan',
      name: '对外捐赠事项',
      path: 'abroad'
    },
    {
      icon: 'menu-qing',
      name: '清产核资审批',
      path: 'nuclearApproval'
    },
    {
      icon: 'menu-da',
      name: '大额资金出借',
      path: 'largeFunds'
    },
    {
      icon: 'menu-dui',
      name: '对外担保备案',
      path: 'externalGuarantee'
    }
  ])

  private defaultActive = '1'

  // 组件初始化
  private mounted() {
    this.$router.push(this.menuList[Number(this.defaultActive) - 1].path)
  }

  // 侧边栏路由跳转
  private routerPath(path: string, index: number) {
    this.$router.push(path)
  }
}
</script>

<style scoped lang="scss">
$color: #b43c3c;
$activeBg: rgba(
  $color: #b43c3c,
  $alpha: 0.2
) !important;

.aside-wrap {
  position: relative;
  width: 220px !important;
  &::-webkit-scrollbar {
    display: none;
  }
}

.el-menu-vertical-wrap {
  height: 100%;
  overflow-y: auto;
  border-right: none;
  &::-webkit-scrollbar {
    display: none;
  }
  .el-menu-item {
    height: 54px;
    font-size: 16px;
    padding: 0 30px;
    padding-left: 15px !important;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    &:hover {
      background: #ebf3ff;
    }
    .menu-ic {
      width: 20px;
      height: 20px;
      margin-right: 16px;
      margin-top: -2px;
    }
  }
  .is-active {
    font-weight: bold;
    border-right: 4px solid $color;
    background: #ebf3ff;
  }
}
</style>
