/**
  组件描述: 企业计划表
*/

<template>
  <section class="p-10"
    style="height:300px">
    <section v-if="mode !== 'see'"
      class="operation-bar">
      <h3 class="title">年度计划表</h3>
      <div>
        <el-button class="primary-buttom"
          type="primary"
          :loading="loading"
          @click="onAdd">新增一条</el-button>
        <el-button class="primary-buttom"
          type="primary"
          :loading="loading"
          @click="onImport">导入</el-button>
      </div>
    </section>

    <!-- 表格 -->
    <Grid ref="grid"
      :border="true"
      :columns="cols"
      :show-pagination="false"
      :overflow-tooltip="true"
      :pageSizes="[99999]"
      :data="tableList"
      :search-params="searchParams"
      @loadCompleted="loadCompleted">
      <template #projectPropertySlot="{row}">
        {{getStateValue("invest_project_property",row.projectProperty)}}
      </template>
      <template #projectCategorySlot="{row}">
        <template v-if="!Number(row.projectCategory)">
          {{row.projectCategory}}
        </template>
        <template v-else>
          {{getStateValue("invest_project_category",row.projectCategory)}}
        </template>
      </template>
      <template #projectCategorySlot="{row}">
        {{getStateValue("invest_project_type",row.projectCategory)}}
      </template>
      <template #operationSlot="{ row, $index }">
        <Operation :list="operationList"
          :row="row"
          :index="$index" />
      </template>
    </Grid>

    <!-- 导入 -->
    <ImportDialog v-if="uploaderDlgVisible"
      :visible.sync="uploaderDlgVisible"
      :recordNumber="recordNumber"
      @addForm="addForm" />

    <!-- 新增 -->
    <AddForm v-if="visibleAdd"
      ref="refadd"
      :visible.sync="visibleAdd"
      :form-data="formData"
      :model="FormModel"
      @updateForm="updateForm"
      @addForm="addForm" />
  </section>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import { ElForm } from 'node_modules/element-ui/types/form'
import { Confirm } from '@/decorators/index'
import { BusinessModule } from '@/store/modules/businessDict'
import Grid from '@/components/Grid/index.vue'
import Operation, { OperationItem } from '@/components/Operation/index.vue'
import Uploader from '@/components/Uploader/index.vue'
import ImportDialog from './ImportDialog.vue'
import AddForm from './PlanForm.vue'

@Component({
  components: {
    Grid,
    Operation,
    Uploader,
    ImportDialog,
    AddForm
  }
})
export default class extends Vue {
  @Prop({ default: 'see' }) private mode!: 'see' | 'edit' | 'add' // 查看、编辑、新增模式
  @Prop() private list!: any
  @Prop() private loading!: boolean
  @Prop() private recordNumber!: string
  private FormModel: 'edit' | 'see' | 'add' = 'add'
  @Watch('loading')
  private loadingChange() {
    if (this.loading) {
      this.refreshGrid()
    }
  }

  @Watch('list', { deep: true })
  private recordNumberChange() {
    this.tableList = this.list
  }
  // 新增事件
  private visibleAdd = false
  private formData = {} //编辑数据
  private searchParams = {
    recordNumber: ''
  }

  public tableList: any[] = []

  // 表单
  private CompanyTableForm: Record<string, any> = {}

  // 导入列表
  private importFiles: string[] = []
  private uploaderDlgVisible = false

  // 操作按钮
  private operationList: OperationItem[] = [
    {
      label: '编辑',
      click: this.onEdit,
      style: 1,
      visible: (row: any) => {
        return this.mode == 'add'
      }
    },
    {
      label: '删除',
      click: this.onDelete,
      style: 3,
      visible: (row: any) => {
        return this.mode == 'add'
      }
    },
    {
      label: '查看',
      click: this.onSee,
      style: 3,
      visible: (row: any) => {
        return true
      }
    }
  ]

  private seeCols = [
    {
      slotName: 'projectPropertySlot',
      label: '项目性质'
    },
    // {
    //   prop: 'projectCode',
    //   label: '项目编号',
    //   minWidth: 100
    // },
    {
      prop: 'projectName',
      label: '项目名称'
    },
    // {
    //   prop: 'orgCode',
    //   label: '公司名称'
    // },
    {
      prop: 'totalInvestment',
      label: '总投资额'
    },
    {
      slotName: 'projectCategorySlot',
      label: '项目分类',
      minWidth: 80
    },

    {
      prop: 'projectDate',
      label: '建设起止年限',
      minWidth: 140
    },
    {
      prop: 'projectAddress',
      label: '建设地址',
      minWidth: 140
    },
    {
      prop: 'projectContent',
      label: '项目规模及主要内容',
      minWidth: 160
    },
    {
      prop: 'implementSubject',
      label: '实施主体',
      minWidth: 140
    },
    {
      prop: 'remark',
      label: '备注',
      minWidth: 160
    },
    {
      prop: '',
      label: '操作',
      labelAlign: 'center',
      slotName: 'operationSlot',
      width: this.mode == 'see' ? 50 : 130,
      fixed: 'right'
    }
  ]

  get getTableList() {
    return this.tableList
  }
  get cols() {
    return this.mode === 'see' ? this.seeCols : this.seeCols
  }

  private onInputChange(row: any, prop: string, index: number) {
    row[prop] = this.CompanyTableForm[`${prop}${index}`]
  }

  private baseCompanyTableFormRules: Record<string, any> = {
    projectFullName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }]
  }

  get remoteUrl() {
    return this.mode === 'add' ? '' : this.recordNumber ? '/fht-monitor/invest/selectPlanFilingCompany' : ''
  }

  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }

  get CompanyTableFormRules() {
    let rules: Record<string, any> = {}
    let form: Record<string, any> = {}
    this.tableList.forEach((item, index) => {
      for (let prop in this.baseCompanyTableFormRules) {
        rules[prop + index] = this.baseCompanyTableFormRules[prop]
        form[prop + index] = ''
      }
    })
    this.CompanyTableForm = form
    return rules
  }

  created() {
    if (this.mode == 'see') {
      this.tableList = this.list
    }
  }

  private onDateChange(date: any) {
    this.CompanyTableForm.startEndDate = date.join('-')
  }

  // 列表接口请求完成
  private loadCompleted({ records }: any) {
    this.tableList = [...records]
  }

  // 删除 表格数据
  @Confirm({
    title: ' ',
    content: '是否删除这段数据？'
  })
  private async onDelete({ projectCode, eidt }: { projectCode: string; eidt: boolean }, index: number) {
    this.tableList.splice(index, 1)
  }

  // 编辑
  private onEdit(row: any) {
    this.FormModel = 'edit'
    this.formData = row
    this.visibleAdd = true
  }

  // 查看
  private onSee(row: any) {
    this.FormModel = 'see'
    this.formData = row
    this.visibleAdd = true
  }

  // 新增数据到对应表单
  private addForm(data: any) {
    if (Array.isArray(data) && data.length) {
      data.forEach((item) => {
        setTimeout(() => {
          item.key = String(new Date().getTime())
          this.tableList.push(item)
        }, 30)
      })
    } else {
      data.key = String(new Date().getTime())
      this.tableList.push(data)
    }
  }

  // 更新数据
  private updateForm(data: any) {
    this.tableList.forEach((item: any, index: number) => {
      if (item.id && item.id === data.id) {
        this.tableList.splice(index, 1, data)
        return
      }

      if (item.key == data.key) {
        this.tableList.splice(index, 1, data)
        return
      }
    })
  }

  // 新增
  private onAdd() {
    this.formData = {}
    this.FormModel = 'add'
    this.visibleAdd = true
  }

  // 导入
  private onImport() {
    this.uploaderDlgVisible = true
  }

  // 表单验证
  public validate(): Promise<boolean> {
    let companyTableForm = this.$refs['CompanyTableForm'] as ElForm
    return companyTableForm.validate()
  }

  // 表格更新
  private refreshGrid(isCurrent = false) {
    ;(this.$refs.grid as Grid).refresh(isCurrent)
  }

  // 筛选项目字典
  private getStateValue(dictname: string, values: any = '') {
    let list = this.getDictData(dictname).find((res: any) => {
      return res.value == values
    }) || { label: '', value: '' }
    return list.label
  }
}
</script>


<style scoped lang="scss">
.operation-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  justify-items: center;
  margin-bottom: 10px;
  .title {
    font-size: 15px;
    font-weight: 500;
    margin: 0;
    color: #535353;
  }
}
</style>