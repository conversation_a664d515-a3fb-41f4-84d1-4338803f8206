/**
组件描述 企业新增表单
 */
<template>
  <Dialog title="新增-年度计划"
    width="980px"
    append-to-body
    :visible="visible"
    @close="handleClose">
    <div slot="body">
      <el-form ref="Form"
        :rules="projectFormRules"
        :disabled="model == 'see' ? true : false"
        :model="Form"
        label-width="120px">
        <el-descriptions title
          :column="2"
          :colon="false">
          <el-descriptions-item>
            <el-form-item label="项目性质">
              <el-select v-model="Form.projectProperty"
                placeholder="请选择类型">
                <el-option v-for="(item, index) in typeOpt"
                  :key="index"
                  :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <el-form-item label="项目编号">
              <el-input disabled
                placeholder="保存后自动生成"
                v-model="Form.planNo" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <el-form-item label="项目名称"
              prop="projectName">
              <el-input v-model="Form.projectName"
                placeholder="请输入项目名称" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <el-form-item label="公司名称"
              prop="orgPath">
              <el-cascader v-model="Form.orgPath"
                :props="{label:'title',value:'value'}"
                placeholder="公司名称"
                :options="compTree"
                :emitPath="true"
                filterable
                ref="cascader"
                @expand-change="changeOrg"></el-cascader>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <el-form-item label="总投资额"
              prop="totalInvestment">
              <InputNumber type="decimalZero"
                v-model="Form.totalInvestment"
                placeholder="请输入总投资额">
                <template slot="append">万元</template>
              </InputNumber>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <el-form-item label="建设起止年限"
              prop="projectDate">
              <div style>
                <el-date-picker v-model="Form.projectDate"
                  type="monthrange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="yyyy 年 MM 月 "
                  value-format="yyyy.MM"
                  align="right"></el-date-picker>
              </div>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <el-form-item label="项目类型"
              prop="projectType">
              <el-select v-model="Form.projectType"
                placeholder="请选择项目类型">
                <el-option v-for="(item, index) in getDictData('invest_project_type')"
                  :key="index"
                  :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <el-form-item label="建设地址"
              prop="projectAddress">
              <el-input v-model="Form.projectAddress"
                placeholder="请输入建设地址" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <el-form-item label="项目分类"
              prop="projectCategory">
              <el-select v-model="Form.projectCategory"
                placeholder="请选择项目分类">
                <el-option v-for="(item, index) in getDictData('invest_project_category')"
                  :key="index"
                  :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item>
            <el-form-item label="实施主体"
              :span="1">
              <el-input v-model="Form.implementSubject"
                placeholder="请输入" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item :span="2">
            <el-form-item label="规模及主要内容">
              <el-input v-model="Form.projectContent"
                placeholder="请输入" />
            </el-form-item>
          </el-descriptions-item>

          <!-- <el-descriptions-item>
            <el-form-item label="本年计划年份">
              <el-date-picker style=""
                v-model="Form.year"
                type="year"
                value-format="yyyy"
                :clearable="true" />
            </el-form-item>
          </el-descriptions-item> -->

        </el-descriptions>
        <el-descriptions title="本年完成投资额"
          :column="2"
          :colon="false">
          <el-descriptions-item :span="1">
            <el-form-item label="投资额"
              prop="thisYearlyAccumulatedInvestment">
              <InputNumber type="decimalZero"
                v-model="Form.thisYearlyAccumulatedInvestment">
                <template slot="append">万元</template>
              </InputNumber>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item v-if="!isshow(['ZF'])"></el-descriptions-item>
          <el-descriptions-item v-if="isshow(['ZF'])">
            <el-form-item label="财务数">
              <InputNumber type="decimal"
                v-model="Form.thisYearlyAccumulatedBudgetAmount">
                <template slot="append">万元</template>
              </InputNumber>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
        <!-- 明年 -->
        <el-descriptions title="明年投资计划"
          :column="2"
          :colon="false">
          <el-descriptions-item>
            <el-form-item label="计划投资额"
              prop="nextYearlyPlannedInvestment">
              <InputNumber v-model="Form.nextYearlyPlannedInvestment"
                clearable
                type="decimalZero"
                placeholder="请输入">
                <template slot="append">万元</template>
              </InputNumber>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item v-if="isshow(['QY', 'ZF'])">
            <el-form-item label="财务数(预算金额)">
              <InputNumber v-model="Form.nextYearlyPlannedBudgetAmount"
                clearable
                type="decimalZero"
                placeholder="请输入">
                <template slot="append">万元</template>
              </InputNumber>
            </el-form-item>
          </el-descriptions-item>

          <!-- 企业专有 -->
          <el-descriptions-item v-if="isshow(['QY'])">
            <el-form-item label="自有资金">
              <InputNumber v-model="Form.nextYearlyPrivateCapital"
                clearable
                type="decimalZero"
                placeholder="请输入">
                <template slot="append">万元</template>
              </InputNumber>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item v-if="isshow(['QY'])">
            <el-form-item label="融资">
              <InputNumber v-model="Form.nextYearlyFinancing"
                clearable
                type="decimalZero"
                placeholder="请输入">
                <template slot="append">万元</template>
              </InputNumber>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item v-if="isshow(['QY'])">
            <el-form-item label="其他资金">
              <InputNumber v-model="Form.nextYearlyOtherFunds"
                clearable
                type="decimalZero"
                placeholder="请输入">
                <template slot="append">万元</template>
              </InputNumber>
            </el-form-item>
          </el-descriptions-item>
          <!-- 政府专属 -->

          <el-descriptions-item v-if="isshow(['ZF'])">
            <el-form-item label="中央预算内专项">
              <InputNumber v-model="Form.nextYearlyCentralBudgetSpecial"
                clearable
                type="decimalZero"
                placeholder="请输入">
                <template slot="append">万元</template>
              </InputNumber>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item v-if="isshow(['ZF'])">
            <el-form-item label="市级政性资金">
              <InputNumber v-model="Form.nextYearlyCityFiscalFunds"
                clearable
                type="decimalZero"
                placeholder="请输入">
                <template slot="append">万元</template>
              </InputNumber>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item v-if="isshow(['ZF'])">
            <el-form-item label="省级财政性资金">
              <InputNumber v-model="Form.nextYearlyProvincialFiscalFunds"
                clearable
                type="decimalZero"
                placeholder="请输入">
                <template slot="append">万元</template>
              </InputNumber>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item v-if="isshow(['ZF'])">
            <el-form-item label="负债">
              <InputNumber v-model="Form.nextYearlyDebt"
                clearable
                type="decimalZero"
                placeholder="请输入">
                <template slot="append">万元</template>
              </InputNumber>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item v-if="isshow(['ZF'])">
            <el-form-item label="区财政性资金">
              <InputNumber v-model="Form.nextYearlyDistrictFiscalFunds"
                clearable
                type="decimalZero"
                placeholder="请输入">
                <template slot="append">万元</template>
              </InputNumber>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item v-if="isshow(['ZF'])">
            <el-form-item label="其他资金">
              <InputNumber v-model="Form.nextYearlyOtherFunds"
                clearable
                type="decimalZero"
                placeholder="请输入">
                <template slot="append">万元</template>
              </InputNumber>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item v-if="!isshow(['ZF'])"></el-descriptions-item>
          <el-descriptions-item :span="2">
            <el-form-item label="建设进度目标">
              <el-input type="textarea"
                style="width: 100%"
                maxlength="200"
                show-word-limit
                :autosize="{ minRows: 2, maxRows: 4 }"
                v-model="Form.nextYearlyProgressTarget"
                placeholder="请输入" />
            </el-form-item>
          </el-descriptions-item>
          <br />
          <el-descriptions-item :span="2">
            <el-form-item label="备注">
              <el-input placeholder="请输入备注信息"
                type="textarea"
                style="width: 100%"
                maxlength="300"
                show-word-limit
                :autosize="{ minRows: 3, maxRows: 5 }"
                v-model="Form.remark" />
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
    </div>
    <div slot="footer">
      <div>
        <el-button @click="handleClose">关 闭</el-button>
        <el-button type="primary"
          v-if="model == 'edit'"
          @click="sureAdd">更 新</el-button>
        <el-button type="primary"
          v-if="model == 'add'"
          @click="sureAdd">确 定</el-button>
      </div>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import { cloneDeep } from 'lodash'
import { BusinessModule } from '@/store/modules/businessDict'
import InputNumber from '@/components/FormComment/inputNumber.vue'
import { getCompTree } from '@/api/projectInvestment'
@Component({
  components: {
    Dialog,
    InputNumber
  }
})
export default class extends Vue {
  @Prop() private visible!: boolean
  @Prop() private formData!: any
  @Prop({ default: 'add' }) private model!: 'see' | 'edit' | 'add'
  private projectFormRules = {
    projectProperty: [{ required: true, message: '请选择项目类型', trigger: 'blur' }],
    orgPath: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
    projectName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
    projectContent: [{ required: true, message: '项目规模及内容', trigger: 'blur' }],
    totalInvestment: [{ required: true, message: '总投资额', trigger: 'blur' }],
    year: [{ required: true, message: '本年计划年份', trigger: 'blur' }],
    projectDate: [{ required: true, message: '请输入建设起止年限', trigger: 'blur' }],
    thisYearlyAccumulatedInvestment: [{ required: true, message: '请输入预计头年年底累计完成投资', trigger: 'blur' }],
    nextYearlyPlannedInvestment: [{ required: true, message: '请输入本年计划投资额', trigger: 'blur' }],
    thisYearlyAccumulatedBudgetAmount: [{ required: true, message: '本年财务数', trigger: 'blur' }],
    nextYearlyPrivateCapital: [{ required: true, message: '本年财务数中自有资金', trigger: 'blur' }],
    nextYearlyOtherFunds: [{ required: true, message: '本年财务数中融资', trigger: 'blur' }],
    nextYearlyFinancing: [{ required: true, message: '本年财务数中其他', trigger: 'blur' }],
    nextYearlyProgressTarget: [{ required: true, message: '本年度完成目标', trigger: 'blur' }],
    implementSubject: [{ required: true, message: '请输入实施主体', trigger: 'blur' }],
    projectType: [{ required: true, message: '请选择项目类型', trigger: 'blur' }],
    projectCategory: [{ required: true, message: '请选择项目分类', trigger: 'blur' }],
    projectAddress: [{ required: true, message: '请输入项目地址', trigger: 'blur' }]
  }
  private typeOpt = this.getDictData('invest_project_property')

  //项目分类： 1-经营/2-功能）
  private typepProject = [
    { label: '功能', value: 2 },
    { label: '经营', value: 1 }
  ]
  private type: 'gov' | 'com' | 'agent' = 'com'
  private compTree = [] //机构结构树

  // 数据初始化
  created() {
    this.Form = Object.assign(this.Form, this.formData)
    this.getCompTree()
    try {
      // 对时间预处理
      let time = this.Form.projectDate.split('-') || []
      if (time.length == 1) {
        this.Form.projectDate = []
      } else {
        this.Form.projectDate = cloneDeep(time)
      }
    } catch (e) {
      //
    }
  }

  private Form: any = {
    projectProperty: '',
    id: '',
    projectCategory: '',
    orgPath: '',
    remark: '', //备注
    implementSubject: '', //	实施主体
    nextYearlyProgressTarget: '', //建设进度目标
    recordNumber: '',
    createDept: '',
    projectDate: '',
    projectName: '',
    projectContent: '',
    year: '',
    nextYearlyPlannedInvestment: '',
    nextYearlyFinancing: '',
    thisYearlyAccumulatedBudgetAmount: '',
    nextYearlyOtherFunds: '',
    nextYearlyPrivateCapital: '',
    thisYearlyAccumulatedInvestment: '',
    totalInvestment: '',
    nextYearlyCentralBudgetSpecial: '',
    projectAddress: ''
  }
  // 字典筛选
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }

  // 拉取远程机构树结构
  private changeOrg(data: any) {
    let getCheckedNodes = (this.$refs.cascader as any).getCheckedNodes()
  }
  private async getCompTree() {
    let options = []
    let res = await getCompTree({})
    if (res.success) {
      options = res.data
    }
    this.compTree = options
  }
  public isshow(list: number[]) {
    return list.includes(this.Form.projectProperty)
  }
  private sureAdd() {
    ;(this.$refs.Form as any).validate((isPass: boolean) => {
      if (isPass) {
        if (this.model !== 'add') {
          this.$message.success('更新成功')
          this.FormdataProcessing()
          this.$emit('updateForm', this.Form)
        } else {
          this.$message.success('添加成功')
          this.FormdataProcessing()
          this.Form.id = String(new Date().getTime())
          this.$emit('addForm', this.Form)
        }
        this.handleClose()
      } else {
        return false
      }
    })
  }

  // 对提交的数据预处理
  private FormdataProcessing() {
    if (this.Form.projectDate.length == 2) {
      let time = this.Form.projectDate[0] + '-' + this.Form.projectDate[1]
      this.Form.projectDate = cloneDeep(time)
    }
  }

  private handleClose() {
    this.$emit('update:visible', false)
  }

  // 筛选项目字典
  private getStateValue(dictname: string, values: any = '') {
    let list = this.getDictData(dictname).find((res: any) => {
      return res.value == values
    }) || { label: '', value: '' }
    return list.label
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-input.is-disabled .el-input__inner,
.el-range-editor.is-disabled,
.el-textarea.is-disabled .el-textarea__inner {
  color: #2f2f2f !important;
}
::v-deep .el-textarea.is-disabled .el-textarea__inner {
  color: #2f2f2f !important;
}
::v-deep.el-form .el-form-item__label {
  line-height: 20px;
}
::v-deep.el-form-item__content {
  width: 100%;
}
::v-deep.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  width: 100%;
}
::v-deep.el-select,
.el-range-editor.el-input__inner {
  width: 100%;
}
.el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 5px;
}
</style>