// 月度报告

<!-- 投资项目审批 -->
<template>
  <Dialog title="月度报告"
    width="980px"
    :visible="visible"
    :close-on-click-modal="true"
    :destroy-on-close="true"
    :append-to-body="true"
    @close="closeDlg">

    <!-- 表格 -->
    <Grid slot="body"
      ref="grid"
      :data="list"
      :columns="cols"
      :show-pagination="false"
      :overflow-tooltip="true">
    </Grid>
  </Dialog>
</template>

<script lang="ts">
import { Component, Emit, Prop, Vue } from 'vue-property-decorator'
import Grid from '@/components/Grid/index.vue'
import Dialog from '@/components/Dialog/index.vue'
import { ProPressDetail } from '../index.vue'

@Component({
  components: {
    Grid,
    Dialog
  }
})
export default class Project extends Vue {
  @Prop() private visible!: boolean
  @Prop() private list!: ProPressDetail[]

  // 弹窗关闭
  @Emit('close')
  private onClose() {
    //
  }

  private cols = [
    {
      prop: 'month',
      label: '月份'
    },
    {
      prop: 'monthlyCompletedInvestment',
      label: '本月完成投资额',
      minWidth: 160
    },
    {
      prop: 'annualCompletedInvestment',
      label: '本年已完成投资额',
      minWidth: 160
    },
    {
      prop: 'annualCompletionRate',
      label: '年度计划投资完成率',
      minWidth: 160
    },
    {
      prop: 'cumulativeCompletedInvestment',
      label: '累计完成投资额',
      minWidth: 160
    },
    {
      prop: 'workStatus',
      label: '开工情况'
    },
    {
      prop: 'currentProgress',
      label: '目前主要形象进度',
      minWidth: 160
    },
    {
      prop: 'problemDifficult',
      label: '存在问题及困难',
      minWidth: 160
    },
    {
      prop: 'nextMonthPlan',
      label: '下月计划安排',
      minWidth: 160
    },
    {
      prop: 'remark',
      label: '备注',
      minWidth: 260
    }
  ]

  // 关闭弹窗
  private closeDlg() {
    this.$emit('update:visible', false)
    this.onClose()
  }
}
</script>

<style lang="scss" scoped>
.month-table {
  height: 100%;
}

::v-deep .el-table__body-wrapper {
  height: 400px;
}
</style>