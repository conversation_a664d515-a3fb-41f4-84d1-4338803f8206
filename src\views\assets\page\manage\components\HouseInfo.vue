<template>
  <section class="house-land-info-wrap">
    <h4 class="title">房产统计</h4>
    <div class="content">
      <div class="mode">
        <h6>资产总数</h6>
        <strong>
          <span>{{ getDetailData('astTotal', 'value1') }}</span>
          <i>处</i>
        </strong>
        <p>资产总面积：{{ getDetailData('astTotal', 'value2') }} m²</p>
        <!-- <p>房间总数：{{getDetailData('coveredAreaTotal', 'value3')}} 间</p> -->
      </div>

      <div class="mode point" @click="pierceThrough({ manageType: '1' })">
        <h6>经营性资产总数</h6>
        <strong>
          <span>{{ getDetailData('bizAstTotal', 'value1') }}</span>
          <i>处</i>
        </strong>
        <!-- <p>出租房间数：{{getDetailData('occupancyRateTotal', 'value2')}} 个</p> -->
        <p>经营性资产总面积：{{ getDetailData('bizAstTotal', 'value2') }} m²</p>
      </div>

      <div class="mode point" @click="pierceThrough({ manageType: '1', useStatus: '3' })">
        <h6>已出租资产总数</h6>
        <strong>
          <span>{{ getDetailData('occupancyAstTotal', 'value1') }}</span>
          <i>处</i>

          <span class="mini_title">出租率：{{ getDetailData('occupancyAstTotal', 'value3') }}%</span>
        </strong>

        <p>已出租资产总面积：{{ getDetailData('occupancyAstTotal', 'value2') }} ㎡</p>
        <!-- <p>闲置总面积：{{getDetailData('vacancyRateTotal', 'value3')}} m²</p> -->
      </div>

      <div class="mode point" @click="pierceThrough({ manageType: '1', useStatus: '1' })">
        <h6>闲置资产总数</h6>
        <strong>
          <span>{{ getDetailData('vacancyAstTotal', 'value1') }}</span>
          <i>处</i>
          <span class="mini_title">闲置率：{{ getDetailData('vacancyAstTotal', 'value3') }}%</span>
        </strong>

        <p>闲置资产总面积：{{ getDetailData('vacancyAstTotal', 'value2') }} ㎡</p>
        <!-- <p>本年剩余应收：{{getDetailData('thisYearReceivableTotal', 'value3')}} 万</p> -->
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

@Component({})
export default class extends Vue {
  @Prop() private detailData!: any

  get getDetailData() {
    return (type: string, value: string) => {
      if (this.detailData[type] && this.detailData[type][value]) {
        return this.detailData[type][value]
      } else {
        return '-'
      }
    }
  }
  private pierceThrough(res: any) {
    this.$emit('pierceThrough', res)
  }
}
</script>
<style scoped>
.mini_title {
  font-size: 12px !important;
  margin-left: 10px;
}
.point {
  cursor: pointer;
}
</style>
