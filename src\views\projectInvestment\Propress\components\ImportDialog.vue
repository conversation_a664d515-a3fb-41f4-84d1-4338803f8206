// 导入弹框

<!-- 投资项目审批 -->
<template>
  <Dialog :title="importTitle"
    width="480px"
    :visible="visible"
    :close-on-click-modal="true"
    :destroy-on-close="true"
    :append-to-body="true"
    @close="closeDlg">

    <div slot="body">
      <el-form ref="ImportForm"
        :rules="importFormRules"
        :model="importForm"
        label-width="100px">
        <!-- <el-form-item label="公司名称"
          prop="orgName">
          <el-input disabled
            placeholder="公司名称"
            v-model="importForm.orgName" />
        </el-form-item> -->

        <el-form-item label="导入时间"
          prop="date"
          required>
          <el-date-picker v-model="importForm.date"
            type="month"
            @change="onDateChange"
            value-format="yyyy-M"
            :clearable="false"
            class="input-mode" />
        </el-form-item>

        <el-form-item label="项目性质"
          prop="projectProperty"
          required>
          <el-select v-model="importForm.projectProperty"
            placeholder="请选择"
            class="input-mode">
            <el-option v-for="item in categoryList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 导入 -->
        <el-form-item label="导入文件"
          required>
          <el-upload class="upload-demo"
            ref="upload"
            accept=".xls,.xlsx"
            :headers="aosHeader"
            :action="aosActionUrl"
            :on-change="handleChange"
            :file-list="fileList"
            :on-success="handleAOSSuccess"
            :on-error="handleAOSError"
            :limit="1"
            :data="importForm"
            :multiple="false"
            :auto-upload="false">
            <el-button slot="trigger"
              size="small"
              type="primary">选取文件</el-button>
            <div slot="tip"
              class="el-upload__tip">只能上传excel文件，且不超过3M <el-button type="text"
                @click="downMobel">下载模版</el-button>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>

    <div slot="footer">
      <el-button @click="closeDlg">取消</el-button>
      <el-button class="primary-buttom"
        type="primary"
        :loading="loading"
        @click="submitUpload">确认</el-button>
    </div>
  </Dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { ElUpload } from 'element-ui/types/upload'
import { Loading } from '@/decorators'
import { getToken } from '@/components/Uploader/config'
import { BusinessModule } from '@/store/modules/businessDict'
import Dialog from '@/components/Dialog/index.vue'

@Component({
  components: {
    Dialog
  }
})
export default class Project extends Vue {
  @Prop() private visible!: boolean
  @Prop() private recordNumber!: string
  @Prop({ default: '' }) private projectProperty?: string

  private aosActionUrl = `${process.env.VUE_APP_URL}fht-monitor/invest/progress/import`
  private loading = false
  private aosHeader = {}
  private fileList: File[] = []
  private categoryList: any[] = []
  private uploaderDlgVisible = false

  private handleAOSError(error: object, file: File, fileList: Array<File>) {
    this.$message.warning('上传失败')
  }

  private importForm = {
    projectProperty: 'QY',
    month: new Date().getMonth() + 1 + '',
    year: String(new Date().getFullYear() - 1),
    date: `${new Date().getFullYear()} ${new Date().getMonth() + 1}`
  }

  private importFormRules = {}

  // 获取字典数据
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }

  // 设置标题
  get importTitle() {
    return `计项目进度表导入-${(this.categoryList.find((item) => item.value === this.importForm.projectProperty) || { label: '' }).label}`
  }

  // 数据初始化
  created() {
    this.categoryList = this.getDictData('invest_project_property')
    this.aosHeader = {
      Authorization: `Basic c2FiZXI6c2FiZXJfc2VjcmV0`,
      'Fht-Auth': `bearer ${getToken()}`
    }

    // 如果没有传prop类型数据，则默认取字典第一个值
    if (this.projectProperty) {
      this.importForm.projectProperty = this.projectProperty
    } else {
      let categoryList: any[] = this.categoryList
      if (Array.isArray(categoryList) && categoryList.length) {
        this.importForm.projectProperty = categoryList[0].value
      }
    }
  }

  // 下载模板
  private downMobel() {
    let time = new Date().getTime()
    let projectProperty = this.importForm.projectProperty

    window.open(process.env.VUE_APP_URL + `fht-monitor/invest/progress/export?projectProperty=${projectProperty}`)
  }

  private onDateChange() {
    let arr = this.importForm.date.split('-')
    this.importForm.month = arr[1]
    this.importForm.year = arr[0]
  }

  handleChange(file: File, fileList: File[]) {
    this.fileList = [...fileList]
  }

  @Loading('loading')
  submitUpload() {
    if (this.fileList.length > 0) {
      ;(this.$refs.upload as ElUpload).submit()
    } else {
      this.$message.warning('请选择文件')
    }
  }

  handleAOSSuccess(res: any) {
    this.$message.success(res.msg || '导入成功！')
    this.updataHandle()
    this.closeDlg()
  }

  // 关闭弹窗
  private closeDlg() {
    this.$emit('update:visible', false)
  }

  // 触发父组件更新
  private updataHandle() {
    this.$emit('success')
  }
}
</script>

<style lang="scss" scoped>
.month-table {
  height: 100%;
}

.input-mode {
  width: 220px;
}

.url-text {
  cursor: pointer;
  color: #409eff;
  div {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

::v-deep .el-table__body-wrapper {
  height: 400px;
}
</style>