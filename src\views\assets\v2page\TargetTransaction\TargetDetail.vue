<template>
  <Dialog width="1000px" :visible="visible" @close="handleClose">
    <div slot="header">
      <div style="font-size:18px;">
        {{ detaildataInfo.subjectNo }}
        <span style="font-size:13px;color:#666666;">({{ detaildataInfo.subjectName }})</span>
      </div>
    </div>
    <div slot="body" v-loading="loading" class="dialog_content">
      <el-tabs v-model="activeName" v-if="showTable">
        <el-tab-pane label="标的信息" name="1">
          <detailtargetInfo :detail="detaildataInfo"></detailtargetInfo>
        </el-tab-pane>
        <el-tab-pane label="出租方信息" name="2">
          <detailLessorInfo :detail="detaildataInfo"></detailLessorInfo>
        </el-tab-pane>
        <el-tab-pane label="承租方资格条件" name="3">
          <detailLesseeInfo :detail="detaildataInfo"></detailLesseeInfo>
        </el-tab-pane>
        <el-tab-pane label="审核与结果" name="4">
          <DetailReviewResults :detail="detaildataInfo" @handleClose="handleClose"></DetailReviewResults>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div slot="footer">
      <el-button type="" @click="handleClose">关闭</el-button>
    </div>
  </Dialog>
</template>
<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import Dialog from '@/components/Dialog/index.vue'
import detailtargetInfo from './components/DetailTargetInfo.vue'
import detailLessorInfo from './components/DetailLessorInfo.vue'
import detailLesseeInfo from './components/DetailLesseeInfo.vue'
import DetailReviewResults from './components/DetailReviewResults.vue'
import { targetDetail } from '@/api/assetsv2'
@Component({
  components: {
    Dialog,
    detailtargetInfo,
    detailLessorInfo,
    detailLesseeInfo,
    DetailReviewResults
  }
})
export default class extends Vue {
  @Prop() private visible!: boolean
  @Prop() private detaildata!: any
  @Prop() private mode!: any
  private detaildataInfo = {}
  private loading = false
  private activeName = '1'
  private handleClose() {
    this.$emit('update:visible', false)
  }
  private showTable = false
  mounted() {
    if (this.mode == 'see') {
      this.getDetail()
    }
  }
  private async getDetail() {
    try {
      this.loading = true
      let res = await targetDetail({
        id: this.detaildata.id
      })
      if (res.success) {
        Object.assign(this.detaildataInfo, res.data)
        // this.detaildataInfo = res.data
        this.showTable = true
        this.loading = false
      }
    } catch (e) {
      this.$emit('update:visible', false)
      this.loading = false
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog_content {
  padding: 0 !important;
  min-height: 400px;
}
</style>
