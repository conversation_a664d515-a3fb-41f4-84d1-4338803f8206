<template>
  <el-tooltip v-bind="$attrs"
    v-on="$listeners"
    :disabled="disabled"
    popper-class="grid-tooltip">
    <span slot="content">{{ $attrs['content'] }}</span>
    <span class="tooltip-text"
      ref="tooltipText">{{ $attrs['content'] }}</span>
  </el-tooltip>

</template>

<script lang="ts">
import { Component, Vue, Prop, Emit } from 'vue-property-decorator'

@Component
export default class extends Vue {
  private disabled = true

  mounted() {
    let tooltipText = this.$refs.tooltipText as HTMLSpanElement
    if (!tooltipText) {
      return
    }
    this.disabled = tooltipText.offsetHeight / 20 <= 1
  }
}
</script>

<style lang="scss" scoped>
.tooltip-text {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>

<style lang="scss">
.grid-tooltip {
  max-width: 300px !important;
}
</style>