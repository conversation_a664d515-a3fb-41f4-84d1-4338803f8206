<template>
  <section class="echarts-dom-wrap">
    <div v-loading="loading"
      ref="chartDom"
      class="chartDom"
      :class="{'hide': !interfaceData.length}" />

    <el-empty description="暂无数据"
      class="empty-none-data"
      :class="{'none': interfaceData.length}" />
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { assetsReceivablesOverYear } from '@/api/assetsv2'
import { Loading } from '@/decorators'
import * as echarts from 'echarts'

let chartDom = null
let myChart: any = null

@Component({})
export default class extends Vue {
  @Prop() private orgCode!: string // 集团code
  @Prop() private year!: string // 年份

  private loading = false
  private legendData: string[] = []
  private xAxisData: string[] = []
  private seriesData: any[] = []
  private interfaceData = []

  // 监听搜索条件改变
  @Watch('orgCode', { deep: true })
  @Watch('year', { deep: true })
  private watchFormData() {
    this.initData()
  }

  // 组件初始化
  private mounted() {
    this.$nextTick(() => {
      chartDom = this.$refs.chartDom
      myChart = echarts.init(chartDom as HTMLElement)

      this.initData()
    })
  }

  // 获取数据
  @Loading('loading')
  private async initData() {
    let { data } = await assetsReceivablesOverYear({
      orgCode: this.orgCode,
      year: this.year
    })

    this.interfaceData = data || []

    // 组装数据
    let legendData: string[] = []
    let xAxisData: string[] = []
    let seriesData: any[] = []
    Array.isArray(data) &&
      data.forEach((item, index) => {
        legendData.push(item.name)

        if (!index && Array.isArray(item.list) && item.list.length) {
          item.list.forEach((itemList: { year: string }) => {
            xAxisData.push(itemList.year)
          })
        }

        let unit = '万'
        let line = 'bar'
        let yAxisIndex = null
        let list: number[] = []

        if (+item.type === 3) unit = '%'
        if (+item.type === 3) line = 'line'
        if (+item.type === 3) yAxisIndex = 1
        if (Array.isArray(item.list) && item.list.length) {
          item.list.forEach((ide: { value: string }) => {
            list.push(+ide.value)
          })
        }

        seriesData.push({
          name: item.name,
          type: line,
          barWidth: 30,
          yAxisIndex: yAxisIndex,
          tooltip: {
            valueFormatter: function (value: number) {
              return value + ` ${unit}`
            }
          },
          data: list
        })
      })

    this.legendData = legendData
    this.xAxisData = xAxisData
    this.seriesData = seriesData
    this.initEcharts()
  }

  // 初始化echarts组件
  private initEcharts() {
    let legendData = this.legendData
    let xAxisData = this.xAxisData
    let seriesData = this.seriesData

    let option = {
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        top: '18%',
        left: '2%',
        right: '2%',
        bottom: '0%',
        containLabel: true
      },
      legend: {
        data: legendData
      },
      xAxis: [
        {
          type: 'category',
          data: xAxisData,
          axisPointer: {
            type: 'shadow'
          },
          axisLabel: {
            formatter: '{value} 年'
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: '万',
          nameTextStyle: {
            align: 'left',
            fontSize: 10,
            lineHeight: 0
          }
        },
        {
          type: 'value',
          name: '%',
          max: 100,
          interval: 20,
          splitLine: {
            show: false
          }
        }
      ],
      series: seriesData
    }

    myChart && myChart.setOption && myChart.setOption(option)
  }
}
</script>

<style scoped lang="scss">
.chartDom {
  width: 100%;
  height: 100%;
}
</style>