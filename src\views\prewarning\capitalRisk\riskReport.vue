<template>
  <div class="grid">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px" class="formClass">
      <el-form-item label="单位名称" prop="corpCode">
        <el-cascader v-model="queryParams.corpCode" :options="dwmcOption" clearable :show-all-levels="false"
          :props="{ checkStrictly: true, value: 'deptCode', label: 'deptName', emitPath: false }" filterable
          placeholder="单位名称" />
      </el-form-item>
      <el-form-item label="时间选择" prop="chooseTime">
        <el-date-picker v-if="queryParams.timeFlag == 1" v-model="queryParams.chooseTime" type="month"
          placeholder="时间选择" value-format="yyyy-MM" />
        <el-date-picker v-else v-model="queryParams.chooseTime" type="year" placeholder="时间选择" value-format="yyyy" />
      </el-form-item>
      <el-form-item label="" prop="timeFlag">
        <el-radio-group v-model="queryParams.timeFlag" @input="timeFlagChange">
          <el-radio :label="1">月度</el-radio>
          <el-radio :label="2">年度</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="" prop="mergeFlag" style="margin-left: 20px;">
        <el-checkbox v-model="queryParams.mergeFlag" :true-label="1" :false-label="0">是否为合并表</el-checkbox>
      </el-form-item>
      <el-form-item style="margin-left: 16px;">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div v-show="pageObj">
      <div class="breadcrumb">
        <el-button type="primary" icon="el-icon-view" size="mini" @click="handlePreview">预览</el-button>
      </div>

      <div class="reportPage">
        <div class="title">{{ pageObj?.corpName }}{{ pageObj?.year }}年<span v-show="pageObj?.month">{{ pageObj?.month
            }}月</span>资金风险预警报告</div>
        <div class="sectionOne">
          <div class="secondTitle">一、企业概况</div>
          <div class="content p_1">{{ pageObj?.reportIntro }}</div>
        </div>

        <div class="sectionOne">
          <div class="secondTitle">二、投入链分析</div>
          <div class="flex_1">
            <div class="flex_2 w_1">
              <div class="thirdTitle">（一）偏离度分析</div>
              <div class="content p_1">
                截至<span v-if="pageObj?.month">{{ pageObj?.month }}月</span><span v-else>{{
                  pageObj?.year
                }}年</span>末，公司现金流动负债比率{{
                    pageObj?.indicatorList[0].actualValue
                  }}%，预测值{{
                  pageObj?.indicatorList[0].predictiveValue
                }}%，偏离{{
                  pageObj?.indicatorList[0].predictiveDeviateValue
                }}%；资产负债率{{
                  pageObj?.indicatorList[1].actualValue
                }}%，预测值{{
                  pageObj?.indicatorList[1].predictiveValue
                }}%，偏离{{
                  pageObj?.indicatorList[1].predictiveDeviateValue
                }}%；带息负债比率{{
                  pageObj?.indicatorList[2].actualValue
                }}%，预测值{{
                  pageObj?.indicatorList[2].predictiveValue
                }}%，偏离{{
                  pageObj?.indicatorList[2].predictiveDeviateValue
                }}%；已获利息倍数{{
                  pageObj?.indicatorList[3].actualValue
                }}，预测值{{
                  pageObj?.indicatorList[3].predictiveValue
                }}，偏离{{
                  pageObj?.indicatorList[3].predictiveDeviateValue
                }}；速动比率{{
                  pageObj?.indicatorList[4].actualValue
                }}%，预测值{{
                  pageObj?.indicatorList[4].predictiveValue
                }}%，偏离{{
                  pageObj?.indicatorList[4].predictiveDeviateValue
                }}%；净资产收益率{{
                  pageObj?.indicatorList[5].actualValue
                }}%，预测值{{
                  pageObj?.indicatorList[5].predictiveValue
                }}%，偏离{{
                  pageObj?.indicatorList[5].predictiveDeviateValue
                }}%；具体如右图：
              </div>
            </div>
            <div class="chartWrapOne w_1">
              <TllfxChart ref="tllfxChart" />
            </div>
          </div>
          <div class="thirdTitle m_1">（二）纵向分析</div>
          <div class="content p_1">
            截至<span v-if="pageObj?.month">{{ pageObj?.month }}月</span><span v-else>{{
              pageObj?.year
            }}年</span>末，公司现金流动负债比率{{
                pageObj?.indicatorList[0].actualValue
              }}%，较五年内年均值{{
              pageObj?.indicatorList[0].riskWarningValue
            }}%，同比{{
              pageObj?.indicatorList[0].yearIncreaseRate > 0 ? '增加' : '减少'
            }}{{
              Math.abs(pageObj?.indicatorList[0].yearIncreaseRate)
            }}个百分点；资产负债率{{
              pageObj?.indicatorList[1].actualValue
            }}%，较五年内年均值{{
              pageObj?.indicatorList[1].riskWarningValue
            }}%，同比{{
              pageObj?.indicatorList[1].yearIncreaseRate > 0 ? '增加' : '减少'
            }}{{
              Math.abs(pageObj?.indicatorList[1].yearIncreaseRate)
            }}个百分点；带息负债比率{{
              pageObj?.indicatorList[2].actualValue
            }}%，较五年内年均值{{
              pageObj?.indicatorList[2].riskWarningValue
            }}%，同比{{
              pageObj?.indicatorList[2].yearIncreaseRate > 0 ? '增加' : '减少'
            }}{{
              Math.abs(pageObj?.indicatorList[2].yearIncreaseRate)
            }}个百分点；已获利息倍数{{
              pageObj?.indicatorList[3].actualValue
            }}，较五年内年均值{{
              pageObj?.indicatorList[3].riskWarningValue
            }}，同比{{
              pageObj?.indicatorList[3].yearIncreaseRate > 0 ? '增加' : '减少'
            }}{{
              Math.abs(pageObj?.indicatorList[3].yearIncreaseRate)
            }}；速动比率{{
              pageObj?.indicatorList[4].actualValue
            }}%，较五年内年均值{{
              pageObj?.indicatorList[4].riskWarningValue
            }}%，同比{{
              pageObj?.indicatorList[4].yearIncreaseRate > 0 ? '增加' : '减少'
            }}{{
              Math.abs(pageObj?.indicatorList[4].yearIncreaseRate)
            }}个百分点；净资产收益率{{
              pageObj?.indicatorList[5].actualValue
            }}%，较五年内年均值{{
              pageObj?.indicatorList[5].riskWarningValue
            }}%，同比{{
              pageObj?.indicatorList[5].yearIncreaseRate > 0 ? '增加' : '减少'
            }}{{
              Math.abs(pageObj?.indicatorList[5].yearIncreaseRate)
            }}个百分点。具体如下图：
          </div>
          <div class="swipWrap m_1">
            <Carouse :autoplay="false" type="card" arrow="always" height="200px" indicator-position="none">
              <CarouselItem v-for="item in firstZxfx" :key="item.indicatorName" style="width:33%;">
                <BlvChart :ref="`blvChartTwo${item.indicatorName}`" />
              </CarouselItem>
            </Carouse>
          </div>
          <div class="flex_1 m_1">
            <div class="flex_2 w_1">
              <div class="thirdTitle">（三）横向分析</div>
              <div class="content p_1">
                截至<span v-if="pageObj?.month">{{ pageObj?.month }}月</span><span v-else>{{
                  pageObj?.year
                }}年</span>末，公司现金流动负债比率{{
                    pageObj?.indicatorList[0].comparisonResultDecimal
                  }}分<span v-show="pageObj?.indicatorList[0].comparisonResult !== '/'">，{{
                  pageObj?.indicatorList[0].comparisonResult
                }}</span>；资产负债率{{
                    pageObj?.indicatorList[1].comparisonResultDecimal
                  }}分<span v-show="pageObj?.indicatorList[1].comparisonResult !== '/'">，{{
                  pageObj?.indicatorList[1].comparisonResult
                }}</span>；带息负债比率{{
                    pageObj?.indicatorList[2].comparisonResultDecimal
                  }}分<span v-show="pageObj?.indicatorList[2].comparisonResult !== '/'">，{{
                  pageObj?.indicatorList[2].comparisonResult
                }}</span>；已获利息倍数{{
                    pageObj?.indicatorList[3].comparisonResultDecimal
                  }}分<span v-show="pageObj?.indicatorList[3].comparisonResult !== '/'">，{{
                  pageObj?.indicatorList[3].comparisonResult
                }}</span>；速动比率{{
                    pageObj?.indicatorList[4].comparisonResultDecimal
                  }}分<span v-show="pageObj?.indicatorList[4].comparisonResult !== '/'">，{{
                  pageObj?.indicatorList[4].comparisonResult
                }}</span>；净资产收益率{{
                    pageObj?.indicatorList[5].comparisonResultDecimal
                  }}分<span v-show="pageObj?.indicatorList[5].comparisonResult !== '/'">，{{
                  pageObj?.indicatorList[5].comparisonResult
                }}</span>；具体如右图：
              </div>
            </div>
            <div class="chartWrapOne w_1">
              <FxChart ref="fxChart" />
            </div>
          </div>
        </div>

        <div class="sectionOne">
          <div class="secondTitle">三、运营链分析</div>
          <div class="flex_1">
            <div class="flex_2 w_1">
              <div class="thirdTitle">（一）偏离度分析</div>
              <div class="content p_1">
                截至<span v-if="pageObj?.month">{{ pageObj?.month }}月</span><span v-else>{{
                  pageObj?.year
                }}年</span>末，公司<span v-for="(item, index) in yylFx" :key="index"><span v-show="item.actualValue">{{
                    item.indicatorDesc }}{{ item.actualValue
                    }}{{ item.indicatorUnit }}<span v-show="item.predictiveValue">，预测值{{
                      item.predictiveValue
                    }}{{ item.indicatorUnit }}</span>，偏离{{
                        item.predictiveDeviateValue
                      }}{{ item.indicatorUnit }}；</span></span>具体如右图：
              </div>
            </div>
            <div class="chartWrapOne w_1">
              <TllfxChart ref="tllfxChartTwo" />
            </div>
          </div>
          <div class="thirdTitle m_1">（二）纵向分析</div>
          <div class="content p_1">
            截至<span v-if="pageObj?.month">{{ pageObj?.month }}月</span><span v-else>{{
              pageObj?.year
            }}年</span>末，公司<span v-for="(item, index) in yylFx" :key="index"><span v-show="item.actualValue">{{
                item.indicatorDesc }}{{
                  item.actualValue
                }}{{ item.indicatorUnit }}，较五年内年均值{{
                  item.riskWarningValue
                }}{{ item.indicatorUnit }}，同比{{
                  item.yearIncreaseRate > 0 ? '增加' : '减少'
                }}{{
                  Math.abs(item.yearIncreaseRate)
                }}{{ item.indicatorUnit == '%' ? '个百分点' : '万元' }}；</span></span>具体如下图：
          </div>
          <div class="swipWrap m_1">
            <Carouse :autoplay="false" type="card" arrow="always" height="200px" indicator-position="none">
              <CarouselItem v-for="item in yylFx" :key="item.indicatorName" style="width:33%;">
                <BlvChart :ref="`blvChartTwo${item.indicatorName}`" />
              </CarouselItem>
            </Carouse>
          </div>
          <div class="flex_1 m_1">
            <div class="flex_2 w_1">
              <div class="thirdTitle">（三）横向分析</div>
              <div class="content p_1">
                截至<span v-if="pageObj?.month">{{ pageObj?.month }}月</span><span v-else>{{
                  pageObj?.year
                }}年</span>末，公司<span v-for="(item, index) in yylFx" :key="index"><span
                    v-show="item.comparisonResultDecimal">{{
                      item.indicatorDesc }}{{
                      item.comparisonResultDecimal
                    }}分<span v-show="item.comparisonResult !== '/'">，{{
                      item.comparisonResult
                    }}</span>；</span></span>具体如右图：
              </div>
            </div>
            <div class="chartWrapOne w_1">
              <FxChartTwo ref="fxChartTwo" />
            </div>
          </div>
        </div>

        <div class="sectionOne">
          <div class="secondTitle">四、回笼链分析</div>
          <div class="content p_1">
            截至<span v-if="pageObj?.month">{{ pageObj?.month }}月</span><span v-else>{{
              pageObj?.year
            }}年</span>末。销售回款率{{ hllFx.actualValue }}%<span v-show="hllFx.predictiveValue">，预测值{{
                hllFx.predictiveValue
              }}</span>%，偏离{{
                hllFx.predictiveDeviateValue
              }}%，较五年内年均值{{
              hllFx.riskWarningValue
            }}%，同比{{
              hllFx.yearIncreaseRate > 0 ? '增加' : '减少'
            }}{{
              Math.abs(hllFx.yearIncreaseRate)
            }}个百分点<span v-show="pageObj?.indicatorList[5].comparisonResult !== '/'">，{{
              hllFx.comparisonResult
            }}</span>，{{ hllFx.remark }}。
          </div>
        </div>

      </div>
    </div>

    <PreviewDialog ref="previewDialog" />
  </div>
</template>

<script>
import { getCompanyIndicator, getCreditGroups } from '@/api/risk'
import Carouse from '@/views/prewarning/capitalRisk/components/Carousel.vue'
import CarouselItem from '@/views/prewarning/capitalRisk/components/CarouselItem.vue'
import TllfxChart from '@/views/prewarning/capitalRisk/components/TllfxChart.vue'
import BlvChart from '@/views/prewarning/capitalRisk/components/BlvChart.vue'
import FxChart from '@/views/prewarning/capitalRisk/components/FxChart.vue'
import FxChartTwo from '@/views/prewarning/capitalRisk/components/FxChartTwo.vue'
import PreviewDialog from '@/views/prewarning/capitalRisk/dialog/PreviewDialog.vue'

export default {

  components: {
    Carouse,
    CarouselItem,
    TllfxChart,
    BlvChart,
    FxChart,
    FxChartTwo,
    PreviewDialog
  },

  data () {
    return {
      pageObj: null,
      queryParams: {
        corpCode: "",
        timeFlag: 1,
        mergeFlag: 0,
        reportFlag: 1,
        chooseTime: this.$moment(new Date()).add('month', -1).format("YYYY-MM")
      },
      dwmcOption: [],
      firstZxfx: [],
      yylFx: [],
      hllFx: [],
      currentParams: null,
    }
  },

  created () {
    this.getOption()
    this.queryParams.corpCode = '913307012549743237'
    this.handleQuery()
  },

  methods: {

    timeFlagChange (label) {
      this.queryParams.chooseTime = this.queryParams.timeFlag == 1 ? this.$moment(new Date()).add('month', -1).format("YYYY-MM") : this.$moment(new Date()).add('year', -1).format("YYYY")
      // console.log('时间类型变化了', this.queryParams.chooseTime)

    },

    getOption () {
      getCreditGroups().then(res => {
        // console.log('单位树', res)
        this.dwmcOption = res.data
      })
    },

    getList () {
      getCompanyIndicator(this.queryParams).then(res => {
        this.pageObj = res.data
        this.firstZxfx = this.pageObj.indicatorList.slice(0, 6)
        this.yylFx = this.pageObj.indicatorList.slice(6, -1)
        this.hllFx = this.pageObj.indicatorList[this.pageObj.indicatorList.length - 1]
        // console.log('列表', res, this.yylFx)
        this.$nextTick(() => {
          this.$refs.tllfxChart.option.series[0].data = this.firstZxfx.map(item => item.predictiveDeviateValue)
          // this.$refs.tllfxChart.option.series[0].data = this.firstZxfx.map(item => item.predictiveValue)
          // this.$refs.tllfxChart.option.series[1].data = this.firstZxfx.map(item => item.actualValue)
          this.$refs.tllfxChart.preInitChart()

          this.firstZxfx.forEach(el => {
            this.$refs[`blvChartTwo${el.indicatorName}`][0].option.title.text = el.indicatorName
            this.$refs[`blvChartTwo${el.indicatorName}`][0].option.xAxis.data = el.yearList.map(it => it.year)
            this.$refs[`blvChartTwo${el.indicatorName}`][0].option.series[0].data = el.yearList.map(it => it.valueDecimal)
            this.$refs[`blvChartTwo${el.indicatorName}`][0].option.series[1].data = el.yearList.map(it => el.riskWarningValue)
            this.$refs[`blvChartTwo${el.indicatorName}`][0].preInitChart()
          })
          this.$refs.fxChart.option.series[5].data = this.firstZxfx.map((item, index) => [index, item.comparisonResultDecimal != null ? Number(item.comparisonResultDecimal) : '-'])
          this.$refs.fxChart.preInitChart()




          this.$refs.tllfxChartTwo.option.series[0].data = this.yylFx.map(item => item.predictiveDeviateValue)
          // this.$refs.tllfxChartTwo.option.series[0].data = this.yylFx.map(item => item.predictiveValue)
          // this.$refs.tllfxChartTwo.option.series[1].data = this.yylFx.map(item => item.actualValue)
          this.$refs.tllfxChartTwo.option.xAxis[0].data = this.yylFx.map(item => item.indicatorDesc)
          this.$refs.tllfxChartTwo.preInitChart()

          this.yylFx.forEach(el => {
            this.$refs[`blvChartTwo${el.indicatorName}`][0].option.title.text = el.indicatorName
            this.$refs[`blvChartTwo${el.indicatorName}`][0].option.xAxis.data = el.yearList.map(it => it.year)
            this.$refs[`blvChartTwo${el.indicatorName}`][0].option.series[0].data = el.yearList.map(it => it.valueDecimal)
            this.$refs[`blvChartTwo${el.indicatorName}`][0].option.series[1].data = el.yearList.map(it => el.riskWarningValue)
            this.$refs[`blvChartTwo${el.indicatorName}`][0].preInitChart()
          })

          let newYylFx = this.yylFx.slice(2, -1)
          if (this.queryParams.timeFlag === 1) { // 月度
            newYylFx = newYylFx.filter(item => item.indicatorDesc !== '投资收益')
          }
          // console.log('列表2', newYylFx)
          this.$refs.fxChartTwo.option.xAxis.data = newYylFx.map(item => item.indicatorDesc)
          this.$refs.fxChartTwo.option.series[0].data = newYylFx.map(item => 0)
          this.$refs.fxChartTwo.option.series[1].data = newYylFx.map(item => 40)
          this.$refs.fxChartTwo.option.series[2].data = newYylFx.map(item => 20)
          this.$refs.fxChartTwo.option.series[3].data = newYylFx.map(item => 20)
          this.$refs.fxChartTwo.option.series[4].data = newYylFx.map(item => 20)
          this.$refs.fxChartTwo.option.series[5].data = newYylFx.map((item, index) => [index, item.comparisonResultDecimal != null ? Number(item.comparisonResultDecimal) : '-'])
          this.$refs.fxChartTwo.preInitChart()

        })
      })
    },

    handleQuery () {
      if (!this.queryParams.corpCode) {
        this.$message({ message: '请选择集团或者单位！', type: 'warning' })
        return
      }
      this.currentParams = Object.assign({}, this.queryParams)
      this.getList()
    },

    handlePreview () {
      this.$refs.previewDialog.showDialog(this.currentParams)
    },

    resetQuery () {
      this.queryParams = {
        corpCode: "",
        timeFlag: 1,
        mergeFlag: 0,
        reportFlag: 1,
        chooseTime: this.$moment(new Date()).add('month', -1).format("YYYY-MM")
      }
      this.pageObj = null
    },
  }
}
</script>

<style scoped lang="scss">
.breadcrumb {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
}

.reportPage {
  display: flex;
  flex-direction: column;
  border-radius: 6px;
  background-color: #fff;
  padding: 24px 36px;
  box-sizing: border-box;

  .title {
    width: 100%;
    display: flex;
    justify-content: center;
    font-size: 29px;
    // font-weight: bolder;
    box-sizing: border-box;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 16px;
    font-family: fzxbsjt;
  }
}

.sectionOne {
  box-sizing: border-box;
  padding: 16px 0;
  border-bottom: 1px solid #e8e8e8;
}

.secondTitle {
  font-size: 21px;
  // font-weight: bolder;
  margin-bottom: 16px;
  font-family: ht;
}

.thirdTitle {
  font-size: 21px;
  margin-bottom: 8px;
  font-family: kt;
}

.content {
  text-align: justify;
  text-indent: 2em;
  line-height: 28px;
  font-size: 21px;
  box-sizing: border-box;
  font-family: fsGB2312;
}

.flex_1 {
  display: flex;
}

.flex_2 {
  display: flex;
  flex-direction: column;
}

.w_1 {
  width: 50%;
}

.p_1 {
  padding: 0 24px;
}

.m_1 {
  margin-top: 16px;
}

.chartWrapOne {
  height: 250px;
  // background-color: antiquewhite;
}

.swipWrap {
  width: 100%;
  height: 200px;
  // background-color: aqua;
}

.medium {
  // background-color: aqua;
  height: 200px;
}
</style>

<style lang="scss">
.el-radio__original {
  display: none !important;
  /* 隐藏原生 radio 输入，但仍然允许交互 */
}

.el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
  box-shadow: none !important;
}
</style>
