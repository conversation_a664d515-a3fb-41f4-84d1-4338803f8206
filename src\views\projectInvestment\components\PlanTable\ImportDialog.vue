// 导入弹框

<!-- 投资项目审批 -->
<template>
  <Dialog :title="importTitle"
    width="480px"
    :visible="visible"
    :close-on-click-modal="true"
    :destroy-on-close="true"
    :append-to-body="true"
    @close="closeDlg">

    <div slot="body"
      class="">
      <el-form ref="ImportForm"
        :rules="importFormRules"
        :model="importForm"
        label-width="100px">
        <!-- <el-form-item label="备案编号"
          prop="recordNumber">
          <el-input disabled
            placeholder="备案编号"
            v-model="importForm.recordNumber" />
        </el-form-item> -->

        <!-- 先去掉，如果要加上，那要在下面的数据中也加上对应字段 -->
        <!-- <el-form-item label="公司名称"
          prop="orgPath"
          required>
          <el-cascader v-model="importForm.orgPath"
            :props="{label:'title',value:'value'}"
            placeholder="公司名称"
            :options="compTree"
            :emitPath="true"
            filterable
            ref="cascader" />
        </el-form-item> -->

        <el-form-item label="项目性质"
          prop="projectProperty"
          required>
          <el-select v-model="importForm.projectProperty"
            placeholder="请选择">
            <el-option v-for="item in projectPropertyList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="导入文件"
          prop=""
          required>
          <el-upload class="upload-demo"
            ref="upload"
            accept=".xls,.xlsx"
            :headers="aosHeader"
            :action="aosActionUrl"
            :on-change="handleChange"
            :file-list="fileList"
            :on-success="handleAOSSuccess"
            :limit="1"
            :data="importForm"
            :multiple="false"
            :auto-upload="false">
            <el-button slot="trigger"
              size="small"
              type="primary">选取文件</el-button>
            <div slot="tip"
              class="el-upload__tip">只能上传excel文件，且不超过3M <el-button type="text"
                @click="downMobel">下载模版</el-button>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>

    <div slot="footer">
      <el-button @click="closeDlg">取 消</el-button>
      <el-button class="primary-buttom"
        type="primary"
        :loading="loading"
        @click="validateForm">确 认</el-button>
    </div>

  </Dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { getToken } from '@/components/Uploader/config'
import { BusinessModule } from '@/store/modules/businessDict'
import { getCompTree } from '@/api/projectInvestment'
import { ElForm } from 'element-ui/types/form'
import Dialog from '@/components/Dialog/index.vue'

@Component({
  components: {
    Dialog
  }
})
export default class Project extends Vue {
  @Prop() private visible!: boolean
  @Prop() private recordNumber!: string
  @Prop({ default: '' }) private projectProperty?: string

  private aosActionUrl = `${process.env.VUE_APP_URL}fht-monitor/invest/plan/import`
  private loading = false
  private aosHeader = {}
  private fileList: File[] = []
  private projectPropertyList = []
  private compTree = []
  private uploaderDlgVisible = false
  private importForm = {
    projectProperty: '',
    recordNumber: ''
  }
  private importFormRules = {
    projectProperty: [{ required: true, message: '请选择', trigger: 'change' }]
  }

  // 获取字典数据
  get getDictData() {
    return (key: string) => {
      let dictionary: any = BusinessModule.dictLaodData || {}
      return dictionary[key] ? dictionary[key] : []
    }
  }

  // 获取标题
  get importTitle() {
    return `计划表导入-${
      (this.projectPropertyList.find((item: any) => item.value === this.importForm.projectProperty) || { label: '' }).label
    }`
  }

  // 数据初始化
  private created() {
    this.importForm.recordNumber = this.recordNumber
    this.projectPropertyList = this.getDictData('invest_project_property')
    this.aosHeader = {
      Authorization: `Basic c2FiZXI6c2FiZXJfc2VjcmV0`,
      'Fht-Auth': `bearer ${getToken()}`
    }

    // 如果没有传prop类型数据，则默认取字典第一个值
    if (this.projectProperty) {
      this.importForm.projectProperty = this.projectProperty
    } else {
      let projectPropertyList: any[] = this.projectPropertyList
      if (Array.isArray(projectPropertyList) && projectPropertyList.length) {
        this.importForm.projectProperty = projectPropertyList[0].value
      }
    }

    this.getCompTree()
  }

  // 获取机构数组
  private async getCompTree() {
    let { data } = await getCompTree({ parentId: 0, deptprojectProperty: 1 })
    this.compTree = data || []
  }

  // 下载模板
  private downMobel() {
    let projectProperty = this.importForm.projectProperty
    window.open(process.env.VUE_APP_URL + `fht-monitor/invest/plan/export?projectProperty=${projectProperty}`)
  }

  // 导入文件触发
  private handleChange(file: File, fileList: File[]) {
    this.fileList = [...fileList]
  }

  // 提交前验证必填数据
  private validateForm() {
    ;(this.$refs.ImportForm as ElForm).validate((valid: boolean) => {
      valid && this.submitUpload()
    })
  }

  // 提交
  private submitUpload() {
    if (!this.fileList.length) {
      this.$message.warning('请选取文件')
    } else {
      this.loading = true
      ;(this.$refs.upload as any).submit()
      setTimeout(() => {
        this.loading = false
      }, 1000)
    }
  }

  // 导入成功后执行
  private handleAOSSuccess(res: any) {
    if (res && Array.isArray(res.data) && res.data.length) {
      this.$emit('addForm', res.data)
      this.closeDlg()
    } else {
      this.$message.warning('导入失败，请重新上传！')
    }
  }

  // 关闭弹窗
  private closeDlg() {
    this.$emit('update:visible', false)
  }
}
</script>

<style lang="scss" scoped>
.month-table {
  height: 100%;
}

.url-text {
  cursor: pointer;
  color: #409eff;
  div {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

::v-deep .el-table__body-wrapper {
  height: 400px;
}
</style>