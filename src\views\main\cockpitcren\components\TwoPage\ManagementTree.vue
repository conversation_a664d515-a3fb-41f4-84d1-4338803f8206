/* 管理树 */

<template>
  <section v-loading="loading"
    class="management-tree-wrap">
    <!-- 树结构 -->
    <div class="content-box">
      <div class="map-box">
        <video v-show="isMapContent"
          autoplay
          muted
          id="videoMode"
          class="video-box">
          <source :src="videoSource"
            type="video/webm">
        </video>
        <video v-show="isMapContent && isVideoLoop"
          autoplay
          muted
          loop
          id="videoModeLoop"
          class="video-box">
          <source :src="videoSource"
            type="video/webm">
        </video>
      </div>
    </div>

    <!-- 悬浮区域 -->
    <div class="modules-box">
      <SuspensionMode @tabsCilckHandle="tabsCilckHandle" />
    </div>

    <!-- 底部 -->
    <div class="footer-box">
      <transition name="run">
        <div v-if="isCarousel"
          class="carousel-box">
          <el-carousel ref="carousel"
            :autoplay="false"
            :interval="4000"
            :initial-index="initialIndex"
            type="card"
            height="80px"
            indicator-position="none"
            @change="toggleTab">
            <el-carousel-item v-for="(item, index) in tabList"
              :key="index">
              <div class="medium">{{ item.orgName }}</div>
            </el-carousel-item>
          </el-carousel>

          <img src="@/views/main/cockpitcren/images/thow2.png"
            class="img prev"
            @click="changCarousel('prev')" />
          <img src="@/views/main/cockpitcren/images/thow1.png"
            class="img next"
            @click="changCarousel('next')" />
        </div>
      </transition>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { isWindowFull, replaceOssStr } from '@/utils'
import { ElCarousel } from 'element-ui/types/carousel'
import { getGroupList, getReadOnlySignUrl } from '@/api/public'
import { Loading } from '@/decorators'
import SuspensionMode from './SuspensionMode.vue'

type typeOrgItem = {
  orgName: string
  orgCode: string
}

@Component({
  components: {
    SuspensionMode
  }
})
export default class extends Vue {
  private loading = false
  private isCarousel = true
  private initialIndex = 0

  // tabs 集团切换数据
  private tabList: Array<typeOrgItem> = []

  // 中部内容数据
  private isMapContent = true
  private isVideoLoop = false
  private videoMode: HTMLVideoElement | null = null
  private videoModeLoop: HTMLVideoElement | null = null
  private videoSource = ''
  private activeItem: {
    orgCode: string
    orgName: string
  } = {
    orgCode: '',
    orgName: ''
  }
  $bus: any

  // 判断窗口是否处于全屏模式
  get getIsWindowFull() {
    return isWindowFull
  }

  private async created() {
    // 根据访问路径动态匹配视频播放地址
    // let videoSource = ''
    let  videoSource = '/monitor/cdn/tree.webm'

    // if (process.env.NODE_ENV === 'development') {
    //   videoSource = 'https://fh-ka.oss-cn-hangzhou.aliyuncs.com/form.webm'
    // } else {
    //   if (window.location.origin.includes('dpm1-jh-fht-admin.mdguanjia.com')) {
    //     // 生产环境1
    //     videoSource = 'https://fh-ka.oss-cn-hangzhou.aliyuncs.com/form.webm'
    //   } else if (window.location.origin.includes('szjg.gzw.jinhua.gov.cn')) {
    //     // 生产环境2
    //     videoSource = 'http://jhsgzw-jhgzszjgxt-oss.oss-cn-jinhua-jhzwy-d01-a.inner.jhszwy.net/upload/20250530/3c662ffb0bc520a8f31e1c5e14b8f941.webm'
    //   } else {
    //     // 生产环境3
    //     videoSource = 'http://oss.oss-cn-jinhua-jhzwy-d01-a.inner.jhszwy.net/upload/20230626/bea3785e74bb9c3fe76dff0d7024c12a.webm'
    //   }
    // }

    this.videoSource = videoSource
    console.log(123)
    // 树结构背景 video，链接做一个加签处理
    // if(!window.origin.includes('//szjg.gzw.jinhua.gov.cn') && !window.origin.includes('*************')) {
    //   console.log(2)
    //   this.videoSource = videoSource
    // } else {
      // console.log(3)
      // getReadOnlySignUrl({
      //   fileName: videoSource,
      //   zipFlag: false,
      // }).then((res:any) => {
      //   if (res.code != 200) return;

      //   let urlReplace = JSON.parse(JSON.stringify(res.data));
      //   urlReplace = replaceOssStr(urlReplace);

      //   urlReplace = urlReplace.replace('/oss/oss/', '/oss/');

      //   console.log(urlReplace, 'urlReplace')

      //   this.videoSource = urlReplace
      // })
    // }
    // end
  }

  // 组件初始化
  private mounted() {
    this.videoMode = document.getElementById('videoMode') as HTMLVideoElement
    this.videoModeLoop = document.getElementById('videoModeLoop') as HTMLVideoElement
    this.getOrgList()
    this.videoPalyHandle()
  }

  // 获取 tabs 数据
  @Loading('loading')
  private async getOrgList() {
    let { data } = await getGroupList()

    // data = data.concat([
    //   {
    //     orgName: '尖峰集团',
    //     orgCode: '91330000704202954L'
    //   },
    // ])

    // 按展示要求，数据倒叙排列
    let dataDeep = JSON.parse(JSON.stringify(data))
    let oneData = data.splice(0, 1)
    let otherData = dataDeep.splice(1, data.length)
    otherData = otherData.reverse()
    let newData = oneData.concat(otherData)
    // end

    if (Array.isArray(newData) && newData.length) {
      this.tabList = newData || []
      this.$bus.$emit('BusTabsChange', newData[0])
    }
  }

  // 切换 tab，触发全局方法，让其他模块更新数据
  private toggleTab(index = 0) {
    let tabCode = this.tabList[index]
    let activeItem = this.tabList.find((item) => {
      return item.orgCode === tabCode.orgCode
    })

    if (activeItem) {
      this.activeItem = activeItem
      this.$bus.$emit('BusTabsChange', this.activeItem)
    }
  }

  // 手动切换tabs
  private changCarousel(str: 'prev' | 'next') {
    let carouselDom = this.$refs['carousel'] as ElCarousel

    if (str === 'prev') carouselDom.prev()
    if (str === 'next') carouselDom.next()
  }

  // 点击树上的按钮，切换tabs
  private tabsCilckHandle(item: { orgCode: string; value: number }) {
    this.isCarousel = false
    let activeItem = this.tabList.find((itemFind) => {
      return itemFind.orgCode === item.orgCode
    })

    if (activeItem) {
      this.initialIndex = this.tabList.length - +item.value + 1

      this.activeItem = activeItem
      this.$bus.$emit('BusTabsChange', this.activeItem)
    }

    setTimeout(() => {
      this.isCarousel = true
    }, 100)
  }

  // 视频播放
  private videoPalyHandle() {
    setTimeout(() => {
      this.isVideoLoop = true
    }, 8000)
  }

  // 组件销毁
  private destroyed() {
    this.$bus.$off('BusTabsChange')
  }
}
</script>

<style scoped lang="scss">
.management-tree-wrap {
  position: relative;
  display: flex;
  justify-content: space-between;
  flex-direction: column;

  width: 100%;
  height: 1200px;

  .content-box {
    position: relative;
    flex: 1;
    height: 800px;
    .map-box {
      position: relative;
      width: 110%;
      height: 110%;
      transform: translateX(-5%) translateY(-5%);

      .video-box {
        position: absolute;
        left: 0;
        top: 0;
        z-index: 1;
        width: 100%;
        height: 100%;
        filter: hue-rotate(206.087deg) saturate(1) brightness(1.7);
      }

      .modes-list {
        position: relative;
        left: -5%;
        top: 58px;
        z-index: 2;
      }

      .modes {
        $cor: #3eeeff;
        $cobg1: #021e5d;
        $cobg2: #0090fe;

        position: absolute;
        padding: 14px;
        border: 2px solid rgba($color: $cor, $alpha: 0.6);
        border-radius: 50%;
        cursor: pointer;
        font-weight: normal;
        font-family: 'FZZZHONGHJW';
        background: rgba($color: $cobg1, $alpha: 0.7);
        &:hover {
          z-index: 100;
          box-shadow: 0 0 30px $cor;
          animation-play-state: paused;
          background: rgba($color: $cobg1, $alpha: 0.8);
          border: 2px solid rgba($color: $cor, $alpha: 1);
          .text {
            width: 140px;
            height: 140px;
            color: #eeb500;
            font-size: 48px;
            line-height: 48px;
            border: 2px solid rgba($color: $cobg2, $alpha: 1);

            // transform: scale(1.2);
            // color: #eeb500;
            // font-size: 48px;
            // line-height: 48px;
            // border: 1px solid rgba($color: $cobg2, $alpha: 1);
          }
          .info {
            z-index: 30;
            opacity: 1;
            width: inherit;
            height: inherit;
          }
        }
        .text {
          position: relative;
          display: flex;
          align-items: center;
          text-align: center;
          flex-direction: column;
          justify-content: center;
          width: 110px;
          height: 110px;
          padding: 10px;
          font-size: 42px;
          line-height: 46px;
          border-radius: 50%;
          margin: 0;
          transition: 0.5s ease;
          border: 2px solid rgba($color: $cobg2, $alpha: 0.4);
          & span:nth-child(1) {
            margin-top: 10px;
          }
        }
        .info {
          width: 0;
          height: 0;
          opacity: 0;
          position: absolute;
          right: -544px;
          top: -100px;
          z-index: 10;
          overflow: hidden;
          //transition: 1s ease;
          img {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
          }
          .conter-box {
            padding: 15px 30px 15px 124px;
          }
          .conter {
            position: relative;
            display: flex;
            align-items: center;
            text-align: left;
            flex-direction: column;
            justify-content: left;
            min-width: 500px;
            padding: 20px 20px 20px 40px;
            box-sizing: border-box;
            border: 2px solid $cor;
            border-radius: 10px;
            box-shadow: 0 0 30px $cor;
            background: rgba($color: $cobg1, $alpha: 0.8);
            i {
              position: absolute;
              color: $cor;
            }
            .icontop {
              left: -4px;
              top: -4px;
              transform: rotate(45deg);
            }
            .iconbtom {
              right: -4px;
              bottom: -4px;
              transform: rotate(45deg);
            }
            .topit {
              margin: 0;
              width: 100%;
              display: flex;
              align-items: center;
              justify-content: space-between;
              strong {
                display: inline-block;
                vertical-align: middle;
                color: #eeb500;
                font-size: 40px;
                min-width: 100px;
                font-weight: normal;
                font-family: none;
                margin-right: 16px;
              }
              span {
                display: inline-block;
                vertical-align: middle;
                font-weight: bold;
                font-size: 40px;
                font-family: none;
              }
            }
          }
        }
        .info-inner {
          right: 81px;
          top: -85px;
          img {
            transform: rotateY(180deg);
          }
          .conter-box {
            padding: 15px 124px 15px 30px;
          }
        }
      }
      .modes0 {
        left: 746px;
        top: 403px;
        animation: keyModes0 8s ease-in-out, keyModesLanbel2 8s linear infinite;
      }
      .modes1 {
        left: 477px;
        top: 492px;
        animation: keyModes1 8s ease-in-out, keyModesLanbel1 10s linear infinite;
      }
      .modes2 {
        left: 330px;
        top: 290px;
        animation: keyModes2 7s ease-in-out, keyModesLanbel2 10s linear infinite;
      }
      .modes3 {
        left: 484px;
        top: 81px;
        animation: keyModes3 5s ease-in-out, keyModesLanbel2 10s linear infinite;
      }
      .modes4 {
        left: 780px;
        top: 87px;
        animation: keyModes4 5s ease-in-out, keyModesLanbel2 6s linear infinite;
      }
      .modes5 {
        left: 1063px;
        top: 122px;
        animation: keyModes5 6s ease-in-out, keyModesLanbel1 10s linear infinite;
      }
      .modes6 {
        left: 1186px;
        top: 286px;
        animation: keyModes6 7s ease-in-out, keyModesLanbel2 10s linear infinite;
      }
      .modes7 {
        left: 1086px;
        top: 493px;
        animation: keyModes7 7s ease-in-out, keyModesLanbel1 10s linear infinite;
        .text {
          font-size: 34px;
        }
      }
      .modes8 {
        left: 778px;
        top: 502px;
        animation: keyModes8 6s ease-in-out, keyModesLanbel2 7s linear infinite;
        .text {
          font-size: 36px !important;
        }
      }

      .active-modes {
        $cor: #3eeeff;
        $cobg1: #021e5d;
        $cobg2: #0090fe;

        z-index: 100;
        box-shadow: 0 0 30px $cor;
        animation-play-state: paused;
        background: rgba($color: $cobg1, $alpha: 0.8);
        border: 2px solid rgba($color: $cor, $alpha: 1);
        .text {
          width: 140px;
          height: 140px;
          color: #eeb500;
          font-size: 48px;
          line-height: 48px;
          border: 2px solid rgba($color: $cobg2, $alpha: 1);
        }
        .info {
          z-index: 30;
          opacity: 1;
          width: auto;
          height: auto;
          overflow: inherit;
        }
      }
    }
  }

  .modules-box {
    position: absolute;
    left: 50%;
    top: 56px;
    width: 1172px;
    height: 680px;
    transform: translate(-50%, 0);
  }

  .footer-box {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 200px;
    display: flex;
    justify-content: center;

    .carousel-box {
      position: relative;
      width: 75%;
      .img {
        position: absolute;
        top: 107px;
        width: 100px;
        cursor: pointer;
      }
      .prev {
        left: -64px;
        animation: keyMoveThow2 5s infinite;
      }
      .next {
        right: -64px;
        animation: keyMoveThow1 5s infinite;
      }

      .medium {
        font-size: 50px;
        text-align: center;
        line-height: 140px;
        margin-top: 68px;
        background: url('../../images/tree_btn_active.png') no-repeat center center;
        background-size: 100% 100%;
      }

      & .medium:nth-child(2) {
        transform: rotateX(-10deg);
      }
    }

    ::v-deep .el-carousel {
      height: 100%;
      .el-carousel__container {
        height: 100% !important;
        transform-style: preserve-3d;
        .el-carousel__mask {
          background: none;
        }
        .el-carousel__item {
          opacity: 0.6;
        }
        .is-active {
          opacity: 1;
        }
      }
    }
  }
}
</style>

<style lang="scss" scoped>
// 元素开始进入的状态 | 元素离开结束的状态
.run-enter-from,
.run-leave-to {
  opacity: 0;
}
// 元素进入结束的状态 ｜ 元素开始离开的状态。     这里不写也可以！！！！！！
.run-enter-to,
.run-leave-from {
  opacity: 1;
}
// 元素进入 ｜ 结束时，过渡的效果
.run-enter-active,
.run-leave-active {
  // 过渡动画的使用
  transition: opacity 2s linear 0s;
}
</style>