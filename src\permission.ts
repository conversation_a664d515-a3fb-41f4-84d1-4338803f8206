/*
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-08-14 15:53:47
 * @LastEditors: wjb
 * @LastEditTime: 2025-08-14 16:16:57
 */
import router from './router'
import { Route } from 'vue-router'
import { getLocalStorage } from '@/utils/cache'

// 免登路由
const whiteList = ['/login']
let VUE_APP_BASE_URL = ''

if (process.env.NODE_ENV === 'production') {
  if (window.location.origin.includes('dpm1-jh-fht-admin.mdguanjia.com')) {
    VUE_APP_BASE_URL = 'http://dpm1-jh-fht-admin.mdguanjia.com/'
  } else if (window.location.origin.includes('szjg.gzw.jinhua.gov.cn')) {
    VUE_APP_BASE_URL = 'https://szjg.gzw.jinhua.gov.cn:6443/'
  } else if (window.location.origin.includes('*************')) {
    VUE_APP_BASE_URL = 'http://*************:9990/'
  }
}

// 路由拦截
router.beforeEach(async (to: Route, _: Route, next: any) => {
  let token = ''
  let tokenJson = getLocalStorage('saber-token')
  if (tokenJson) token = JSON.parse(tokenJson).content

  if (token) {
    next()
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      if (process.env.NODE_ENV === 'development') {
        next('/login')
      } else {
        window.location.href = VUE_APP_BASE_URL + '#/login'
      }
    }
  }
})



