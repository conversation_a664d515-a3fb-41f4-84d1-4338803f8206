<template>
  <section v-loading="loading"
    class="assets-prewarning-wrap">
    <div class="header-box">
      <el-form ref="form"
        :inline="true"
        :model="formData">
        <el-form-item>
          <el-select v-model="formData.orgCode"
            :clearable="false"
            placeholder="公司名称">
            <el-option v-for="(item, index) in compTree"
              :key="index"
              :label="item.deptName"
              :value="item.deptCode">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-date-picker v-model="formData.year"
            :clearable="false"
            type="year"
            placeholder="年份"
            value-format="yyyy">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </div>

    <el-divider class="divider-box" />

    <div v-if="isShow"
      class="conter-box">
      <div class="modules">
        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>租期超5年</span>
          </h4>
          <div class="cter">
            <EchartsLeasePie :orgCode="formData.orgCode"
              :year="formData.year" />
          </div>
        </div>

        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>闲置超3个月</span>
          </h4>
          <div class="cter">
            <EchartsIdlesPie :orgCode="formData.orgCode"
              :year="formData.year" />
          </div>
        </div>
      </div>

      <div class="modules">
        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>未公开交易</span>
            <el-tooltip effect="dark"
              content="未进公开交易场所挂牌的资产"
              placement="top-start">
              <i class="el-icon-warning" />
            </el-tooltip>
          </h4>
          <div class="cter">
            <EchartsMobilePie :orgCode="formData.orgCode"
              :year="formData.year" />
          </div>
        </div>

        <div class="mode">
          <h4 class="til">
            <i class="el-icon-s-marketing" />
            <span>未评估</span>
            <el-tooltip effect="dark"
              content="资产已出租但没有相关评估报告"
              placement="top-start">
              <i class="el-icon-warning" />
            </el-tooltip>
          </h4>
          <div class="cter">
            <EchartsEvaluatedPie :orgCode="formData.orgCode"
              :year="formData.year" />
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { getGroupList } from '@/api/public'
import EchartsLeasePie from './components/EchartsLeasePie.vue'
import EchartsIdlesPie from './components/EchartsIdlesPie.vue'
import EchartsMobilePie from './components/EchartsMobilePie.vue'
import EchartsEvaluatedPie from './components/EchartsEvaluatedPie.vue'

@Component({
  components: {
    EchartsLeasePie,
    EchartsIdlesPie,
    EchartsMobilePie,
    EchartsEvaluatedPie
  }
})
export default class extends Vue {
  private isShow = true
  private loading = false
  private timer: any = null
  private compTree = []
  private formData: {
    orgCode: string | null
    year: string | null
  } = {
    orgCode: '',
    year: ''
  }

  // 数据初始化
  private created() {
    this.getCompTree()
    this.formData.year = (this as any).$moment(new Date()).add('year', 0).format('YYYY')
  }

  // 组件初始化
  private mounted() {
    window.addEventListener('resize', () => {
      clearTimeout(this.timer)
      this.isShow = false
      this.loading = true
      this.timer = setTimeout(() => {
        this.isShow = true
        this.loading = false
      }, 500)
    })
  }

  // 获取机构数据
  private async getCompTree() {
    let { data } = await getGroupList()

    if (Array.isArray(data) && data.length) {
      this.formData.orgCode = data[0].deptCode
    }

    this.compTree = data || []
  }

  // 组件销毁
  private destroyed() {
    clearTimeout(this.timer)
    this.timer = null
  }
}
</script>

<style scoped lang="scss">
.assets-prewarning-wrap {
  position: relative;
  height: 100%;
  background: #fff;
  overflow-y: auto;
  padding: 10px 14px;
  box-sizing: border-box;
  h4,
  p {
    margin: 0;
  }
  .header-box {
    text-align: right;
  }
  .divider-box {
    margin: 0 0 20px;
  }
  .conter-box {
    position: relative;
    .modules {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      height: 22vw;
      margin-bottom: 20px;
      &:nth-last-child(1) {
        margin-bottom: 0;
      }
      .mode {
        flex: 1;
        height: 100%;
        margin-right: 20px;
        display: flex;
        padding: 10px;
        border-radius: 4px;
        overflow: hidden;
        box-sizing: border-box;
        justify-content: space-between;
        flex-direction: column;
        background: rgba(172, 196, 221, 0.1);
        &:nth-last-child(1) {
          margin-right: 0;
        }
        .til {
          display: flex;
          align-items: center;
          font-weight: normal;
          margin-bottom: 10px;
          color: rgba(245, 108, 108, 0.8);
          i {
            font-size: 20px;
            margin-right: 4px;
          }
          span {
            font-size: 17px;
          }
        }
        .cter {
          flex: 1;
        }
      }
    }
  }

  ::v-deep .echarts-dom-wrap {
    position: relative;
    height: 100%;
    .hide {
      opacity: 0;
    }
    .none {
      display: none !important;
    }
    .empty-none-data {
      position: absolute;
      top: 0;
      width: 100%;
      height: 100%;
      padding: 0;
    }
  }
}
</style>